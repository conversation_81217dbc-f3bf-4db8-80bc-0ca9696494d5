{"login": "login", "ui_login": "เข้าสู่ระบบ", "fild_username": "ชื่อบัญชี", "fild_password": "รหัสผ่าน", "btn_signin": "เข้าสู่ระบบ", "btn_signup": "สมัครเข้าใช้งาน", "ui_login_app": "ยืนยันการเข้าสู่ระบบ", "home": "home", "ui_hello": "สวัสดี", "ui_morning_y": "คุณลงเวลาเข้างานเรียบร้อย", "ui_morning_n": "คุณยังไม่ได้ลงเวลาเข้างาน", "ui_bye_y": "คุณลงเวลาออกเรียบร้อย", "ui_bye_n": "คุณยังไม่ได้ลงเวลาออกงาน", "btn_peopleProcess": "People\nProcess", "btn_4C": "4C", "btn_tools": "เครื่องมือ\nทำงาน", "btn_bitrix": "ประเด็นงาน\nของฉัน", "btn_CuGtc": "CU / GTC", "btn_seeAll": "ดูทั้งหมด", "ui_peopleProcess": "People Process", "ui_4C": "4C", "ui_CuGtc": "CU / GTC", "ui_tools": "เครื่องมือทำงาน", "btn_issue": "ประเด็นหลัก", "btn_toolsPMS": "เครื่องมือ\nทำงาน PMS", "btn_toolsAAM": "เครื่องมือ\nทำงาน AAM", "btn_toolsCS": "เครื่องมือ\nทำงาน CS24", "btn_ta": "ลงเวลาทำงาน", "btn_welfare": "สวัสดิการ", "btn_callcenter": "ติดต่อสอบถาม", "btn_buyGTC": "หุ้น GTC", "btn_scanQR": "สแกน QR", "btn_cew": "บันทึก CEW", "btn_idea": "PKG\nไอเดีย", "btn_Leave": "ใบลา/\nอนุมัติใบลา", "ui_statistics": "สถานะความมั่งคั่ง", "btn_Income": "รายได้", "btn_LikeWallet": "ไลค์วอลเลท", "btn_gtc": "หุ้น GTC", "btn_cu": "หุ้น CU", "btn_meta_cu": "หุ้น Meta CU", "btn_Insurance_CU": "เงินค้ำประกัน", "ui_Salary": "เงินเดือน", "ui_Incentive": "Incentive", "ui_dateUpdate": "วันที่อัพเดท", "ui_Reward": "เงินรางวัล", "ui_PFS": "(PFS)", "ui_Bath": "บาท", "ui_Totally": "ทั้งหมด", "ui_Locked": "ล็อคอยู่", "ui_Unlock": "ใช้ได้", "ui_LIKE": "ไลค์", "ui_BV": "มูลค่าหุ้น BV ", "ui_sharing": "จำนวนหุ้น", "ui_TotallyGTC": "มูลค่า", "ui_TotallyGTCcoin": "GTCcoin", "ui_Unit": "หุ้น", "ui_Collateralized": "ติดค้ำประกัน", "ui_TotallyCU": "มูลค่าหุ้น", "ui_SavingCU": "เงินออม", "ui_InsuranceCU": "เงินประกันการทำงาน", "ui_DebtCU": "หนี้สิน", "ui_reward": "ผลตอบแทน", "ui_rewardCu": "ผลตอบแทนหุ้นถือครอง", "ui_reward2": "จากการลงทุน", "ui_Conditions": "*รอถือครองกรรมสิทธิ์หุ้นตามเงื่อนไข", "ui_ConditionsCu": "รอถือครอง / หุ้น", "ui_userConditionsCu": "ถือครอง / หุ้น", "ui_ContractCU": "สัญญาที่", "ui_value": "มูลค่า(บาท)", "ui_bath": "บาท", "ui_likevalue": "ไลค์", "ui_cu": "CU", "ui_withcu": "กับ CU", "ui_MetaCu": "Meta CU", "ui_Availble": "ใช้ได้", "ui_gtc": "GTC", "ui_debt": "หนี้สิน", "ui_like": "ไลค์พอยท์", "ui_likecredit": "ไลค์ เครดิต", "ui_detailFull": "ดูข้อมูลเพิ่มเติม", "ui_Savingmoney": "เงินออม", "ui_position": "ตำแหน่ง", "ui_purpose": "จุดมุ่งหมายทีม", "ui_borrowcu": "สัญญาเงินกู้สามัญ", "ui_nondisclosure": "สัญญาไม่เปิดเผยข้อมูล", "ui_passcode": "รหัสความปลอดภัย", "ui_create_passcode": "ตั้งรหัสความปลอดภัย", "ui_confirm_passcode": "ยืนยันรหัสความปลอดภัย", "ui_languages": "ตั้งค่าภาษา", "ui_feedback": "ข้อเสนอแนะ", "ui_guid": "คู่มือ MS24", "ui_talk": "พูดคุย", "ui_supporter": "ผู้สนับสนุน", "ui_logout": "ออกจากระบบ", "ui_logoutalert": "ออกจากระบบ", "ui_logoutCF": "คุณต้องการออกจากระบบ\nMS24 ใช่หรือไม่?", "btn_cancel": "ยกเลิก", "btn_logout": "ออกจากระบบ", "ui_update": "แจ้งอัพเดทเวอร์ชั่น", "ui_updateDetail": "MS24 Mobile Application\nได้ทำการปรับปรุงแก้ไข ฟังก์ชั่นการใช้งานใหม่\nเพื่อการใช้งานที่ดีขึ้น กรุณาทำการอัพเดทเวอร์ชั่น\n\nกดปุ่ม “อัพเดท” ด้านล่าง เพื่อทำการอัพเดท", "btn_update": "อัพเดท", "btn_After": "ภายหลัง", "ui_titleAlert": "แจ้งเตือน", "ui_detailAlert": "เมนูนี้กำลังอยู่ในระหว่าง\nการพัฒนา", "ui_detailAlert2": "ไฟล์ที่ท่านเลือกมีขนาดใหญ่เกินไป\nกรุณาปรับไฟล์ให้เล็กลง", "ui_titleAlert2": "ผิดพลาด", "ui_detailAlert3": "ไม่พบข้อมูลลูกค้า\nลองค้นหาใหม่", "btn_okAlert": "ตกลง", "modify": "modify", "ui_modify": "แจ้งปิดปรังปรุง", "ui_modifyDetail": "MS24 Mobile Application\nปิดปรับปรุงแก้ไขและงดให้บริการเมนูนี้ชัวคราว\n\nขออภัยในความไม่สะดวกค่ะ", "passscode": "passcode", "ui_enter": "ใส่รหัสความปลอดภัย", "ui_wrong": "รหัสของคุณไม่ถูกต้อง ลองอีกครั้ง", "ui_lostpin": "หากลืมพิน กดที่นี่ เพื่อออกจากระบบบ", "changepasscode": "changepasscode", "ui_setting": "ตั้งค่ารหัสความปลอดภัย", "ui_resetpasscode": "เปลี่ยนรหัสใหม่", "ui_putpasscodedefals": "ใส่รหัสเดิม", "ui_putpasscodenew": "ใส่รหัสใหม่", "ui_confirmpasscodenew": "ยันยืนรหัสใหม่อีกครั้ง", "ui_passwrong": "รหัสของคุณไม่ถูกต้อง", "ui_alertpass": "เปลี่ยนรหัสเรียบร้อย", "ui_alertdata": "คุณได้เปลี่ยนรหัส\nความปลอดภัยใหม่แล้วเรียบร้อย\nสามารถเข้าใช้งานได้เลยค่ะ", "btn_ok": "ตกลง", "ui_alerthome": "ยกเลิกตั้งค่ารหัส", "ui_datahome": "คุณต้องการจะยกเลิก\nการตั้งค่ารหัสความปลอดภัยใหม่\nใช่หรือไม่?", "btn_continew": "ดำเนินการต่อ", "btn_okhome": "ตกลง", "changlanguagessetting": "changlanguagessetting", "ui_selectlanguages": "เลือกภาษา", "btn_eng": "อังกฤษ", "btn_th": "ไทย", "ui_LanguageSetting": "ตั้งค่าภาษา", "ui_settingCFf": "คุณต้องการยืนยัน\nตั้งค่าเป็นภาษา", "ui_settingCFl": "ใช่หรือไม่", "ui_selecteng": "อังกฤษ", "ui_selectth": "ไทย", "ui_selectkm": "กัมพูชา", "ui_selectlao": "ลาว", "btn_settingcancel": "ยกเลิก", "ui_settingchange": "เปลี่ยนภาษา", "feedback": "feedback", "ui_headFeedback": "ข้อเสนอแนะ", "ui_subject": "หัวข้อ / เรื่อง", "tf_typingSj": "พิมพ์หัวข้อที่จะเสนอแนะ", "ui_detailFeedback": "รายละเอียด / คำอธิบาย", "tf_detailFeedback": "พิมพ์รายละเอียด", "ui_imgUpload": "อัพโหลดรูปภาพ", "btn_imgUpload": "อัพโหลดรูปภาพ", "btn_saveFeedback": "บันทึกข้อมูล", "ui_feedbacOK": "บันทึกเรียบร้อย", "ui_feedbackDe": "ทางเราได้รับข้อเสนอแนะ\nของคุณแล้วเรียบร้อย\nขอบคุณมากค่ะ", "ui_feedbackOK": "ตกลง", "contact": "contact", "ui_headcontact": "ติดต่อสอบถาม", "ui_subjectcontact": "เรื่องติดต่อ", "tf_typingSjcontact": "พิมพ์เรื่องที่ติดต่อ", "ui_detailcontact": "รายละเอียด", "tf_detailcontact": "พิมพ์รายละเอียด", "ui_imgUploadcontact": "อัพโหลดรูปภาพ", "btn_imgUploacontact": "อัพโหลดรูปภาพ", "btn_savecontact": "ส่งข้อมูล", "ui_contactOK": "ส่งข้อมูล เรียบร้อย", "ui_contactDe": "ทางเราได้รับการติดต่อ\nของคุณแล้วเรียบร้อย\nขอบคุณมากค่ะ", "ui_contactkOK": "ตกลง", "ta": "ta", "ui_ta": "ลงเวลาทำงาน", "ui_date": "วันที่", "ui_timeNow": "ขณะนี้เวลา", "btn_checkin": "มาทำงาน", "btn_checkin2": "มาทำงาน(บ่าย)", "btn_checkout": "ลงเวลาออก", "ui_problem": "สามารถแจ้งปัญหาได้ที่Telegram ห้องรับใช้", "reasonforlate": "reasonforlate", "ui_why": "เหตุผลที่สาย", "tf_message": "พิมพ์รายละเอียด...", "btn_send": "ส่งข้อมูล", "sendproblem": "sendproblem", "ui_what": "แจ้งปัญหาการลงเวลา", "ui_notWorking": "ระบบแอพมีปัญหา", "ui_notInternet": "สัญญาณอินเทอร์เน็ตใช้การไม่ได้", "reciveProblem": "reciveProblem", "ui_wait": "* ได้รับข้อมูลปัญหา การลงเวลาของคุณแล้ว \nกรุณารอการติดต่อกลับจากทีมงาน\nภายใน 5 นาที", "notification": "notification", "ui_notify": "การแจ้งเตือน", "ui_claim": "เคลม LIKE", "btn_claim": "รับคะแนน", "btn_claimNow": "เคลม เลย!", "ui_act": "กิจกรรม", "ui_news": "ข่าวสาร", "pkgWelfare": "pkgWelfare", "ui_select": "เลือกรายการที่ต้องการ", "btn_basic": "สวัสดิการพื้นฐาน", "btn_flexible": "สวัสดิการยืดหยุ่น", "btn_health": "สวัสดิการสุขภาพ", "ui_basic": "พื้นฐาน", "ui_flexible": "ยืดหยุ่น", "ui_health": "สุขภาพ", "ui_contact": "สอบถามรายละเอียดกับทีมงาน", "pkgwelfarebasic": "pkgwe<PERSON><PERSON>bas<PERSON>", "ui_headBasic": "สวัสดิการพื้นฐาน", "ui_detail": "รายละเอียดสวัสดิการพื้นฐาน", "ui_welfarecity": "สวัสดิการสิทธิตามกฏหมายแรงงานของแต่ละประเทศ", "ui_welfareout": "สวัสดิการนอกเหนือจากที่กฎหมายกำหนด", "ui_welfareout1": "สวัสดิการช่วยเหลือพิธีมงคลสมรส", "ui_welfareout2": "สวัสดิการช่วยเหลือคลอดบุตร", "ui_welfareout3": "สวัสดิการช่วยเหลือเพื่อการอุปสมบท", "ui_welfareout4": "สวัสดิการช่วยเหลืองานศพ : ครอบครัวสมาชิก / เครือญาติสมาชิก", "ui_welfareout5": "สวัสดิการประกันอุบัติเหตุกลุ่ม", "ui_welfareout6": "สวัสดิการช่วยเหลือประกันกลุ่มสุขภาพ", "ui_welfareout7": "สวัสดิการประกันชีวิตกลุ่ม", "ui_welfareout8": "สวัสดิการช่วยเหลือตรวจสุขภาพประจำปี ( เฉพาะกลุ่มงาน )", "ui_welfareout9": "สวัสดิการช่วยเหลือทุนการศึกษาสมาชิก", "ui_welfareout10": "สวัสดิการรางวัลอายุงาน", "ui_welfareout11": "สวัสดิการเครื่องแบบสมาชิก \n( ไม่รวมชุดฟอร์มช่าง )", "ui_welfareout12": "สวัสดิการงบเลี้ยงส่งสมาชิกลาออก", "ui_welfareout13": "สวัสดิการเงินสมทบอายุงานกรณีลาออก", "ui_welfareout14": "สวัสดิการเบี้ยเลี้ยงปฏิบัติงานต่างประเทศ / ", "ui_welfareout15": "ปฏิบัติงานนอกสถานที่ / ฝึกอบรม", "ui_welfareout16": "สวัสดิการค่าที่พัก", "ui_helpbudget": "ส่วนที่องค์กรมีงบช่วยเหลือ", "pkgwelfarebasicform": "pkgwelfarebasicform", "ui_headBasicform": "สวัสดิการพื้นฐาน", "ui_status": "สถานะ", "dp_select": "เลือก", "dp_member": "สมาชิกประจำ", "dp_test": "สมาชิกทดลองงาน", "dp_hire": "สมาชิกสัญญาจ้าง", "dp_Advisor": "ที่ปรึกษา", "ui_datework": "กรุณาเลือกวันที่", "ui_remarkdatework": "ระบุวันที่ จัดงานต่างๆ เช่น งานบวช งานแต่ง ฯลฯ ตามเงื่อนไขที่แจ้งไว้ และถ้ากรณีลาคลอดใส่วันที่เริ่มทำการลาคลอดด้วยทุกครั้ง", "tf_datework": "วว/ดด/ปปปป", "ui_selectwelfare": "เลือกสวัสดิการ", "ui_remarkwelfare": "กรณีลาบวชเกิน15วันขึ้นไป ให้เลือก >>>>> งานบวช (บวชเกิน15วัน) และใส่วันที่กลับมาทำงานในช่องวันที่กลับมาทำงานสำหรับคนลาบวชเดิน15วันด้วย!!", "dp_welfareselect": "เลือกรายการสวัสดิการ", "dp_marri": "งานมงคลสมรส", "dp_monk": "งานบวช", "dp_monk15": "งานบวช (บวชเกิน15วัน)", "dp_baby": "คลอดบุตร", "dp_gran": "งานศพ เครือญาติสมาชิก (บิดา - มารดาของคู่สมรส และ พี่น้องร่วมบิดา-มารดาเดียวกัน ปู่ ย่า ตา ยาย )", "dp_father": "งานศพ ครอบครัวสมาชิก (บิดา - มารดา บุตร คู่สมรส )", "dp_advisor": "งานศพ สมาชิกกลุ่มที่ปรึกษารวมถึง บิดา - มารดา คู่สมรส และ บุตร ของที่ปรึกษา เสียชีวิต", "ui_link": "แนบ link การ์ดงานพิธีต่างๆ/ใบเกิด", "ui_remarklink": "(ถ้ายังไม่มีการ์ดให้ไปใส่อีกข้อนึง)", "ui_showlink": "link ที่อัพโหลด", "ui_card": "สำหรับยังไม่มีการ์ดงานพิธี", "dp_cardselect": "เลือก", "dp_nocard": "ยังไม่มีการ์ด", "ui_bank": "เลขบัญชีในการรับเงิน (เฉพาะกสิกรไทยเท่านั้น)", "ui_remarkbank": "ถ้ามี 0 นำหน้าให้ใส่ - เช่น 0-********* *", "tf_typingbank": "พิมพ์เลขที่บัญชี", "ui_remarkbasic": "หมายเหตุ", "tf_typingnote": "พิมพ์หมายเหตุ", "ui_monk": "สำหรับหัวข้อ*งานบวช (บวชเกิน15วัน)*", "ui_remarkmonk": "*กรุณาใส่วันที่กลับมาทำงานด้วยคะ*", "btn_savebasic": "บันทึกข้อมูล", "btn_nextbasic": "ถัดไป", "btn_beforebasic": "ก่อนหน้า", "pkgwelfareflexible": "pkgwelfareflexible", "ui_headFlexible": "สวัสดิการยืดหยุ่น", "ui_amount": "ยอดเงินที่ต้องการเบิก", "ui_balance": "ยอดคงเหลือสุทธิ", "btn_apply": "คีย์เบิกสวัสดิการ", "ui_history": "รายการเบิก", "ui_recive": "เบิกไปแล้ว", "ui_withdraw_money": "เบิก", "pkgwelfareflexiblewithdraw": "pkgwelfareflexiblewithdraw", "ui_headFlexibleWithdraw": "สวัสดิการยืดหยุ่น", "ui_amountWithdrow": "วงเงินคงเหลือที่เบิกได้", "ui_amount2": "จำนวนเงินที่ต้องการเบิก", "tf_amount": "พิมพ์จำนวนเงิน", "ui_grouptype": "หมวดหมู่ใบเสร็จ", "dr_select": "เลือกรายการ", "ui_uploadimmage": "อัพโหลดใบเสร็จ", "ui_exImage": "ตัวอย่างใบเสร็จ", "ui_linkimmage": "ลิงค์รูป", "btn_uploadimmage": "อัพโหลดรูปภาพ", "btn_save": "บันทึกข้อมูล", "btn_details": "ยืนยันการเบิกสวัสดิการ", "btn_details2": "คุณต้องการเบิกสวัสดิการยืดหยุ่น", "btn_details3": "จำนวน ", "btn_details4": " บาท", "btn_details5": " กีบ", "btn_details6": "ใช่หรือไม่?", "btn_back": "กลับหน้าหลัก", "pkgwelfareflexibleconfirm": "pkgwelfareflexibleconfirm", "ui_headFlexibleConfirm": "สวัสดิการยืดหยุ่น", "pkgwelfarehealth": "pkgwelfarehealth", "ui_headhealth": "สวัสดิการสุขภาพ", "ui_selectinsurance": "เลือกรายการที่ต้องการ", "btn_selectIPD": "สวัสดิการสุขภาพ IPD\n( ผู้ป่วยใน )", "btn_selectOPD": "สวัสดิการสุขภาพ OPD\n( ผู้ป่วยนอก )", "ui_conditionIPD": "เงื่อนไขสวัสดิการสุขภาพ IPD...", "ui_conditionOPD": "เงื่อนไขสวัสดิการสุขภาพ OPD...", "ui_messageIPD_thai1": "ค่ารักษาพยาบาลไม่เกิน 10,000 บาท/ครั้ง ตามเงื่อนไขการเบิกดังนี้ (รวมค่าห้อง/ค่าหมอ/ค่ายา)\n- จ่ายค่าห้อง 1,500 บาท/วัน\n- ค่าปรึกษาแพทย์เชี่ยวชาญเฉพาะโรค 2,000 บาท/ครั้ง\n- ค่ายาตามจริง ไม่เกินวงเงินรวม 10,000 บาท/ครั้ง\nค่าผ่าตัด 5,000 บาท/ครั้ง", "ui_messageIPD_thai": "เงื่อนไขสวัสดิการสุขภาพผู้ป่วยในและผู้ป่วยนอก \nเป็นไปตามเงื่อนไขตามประกาศของทีม PAO", "ui_messageIPD_rplc1": "ค่ารักษาพยาบาลไม่เกิน 4,500,000 กีบ/ครั้ง ตามเงื่อนไขการเบิกดังนี้ (รวมค่าห้อง/ค่าหมอ/ค่ายา)\n- จ่ายค่าห้อง 450,000 กีบ/วัน\n- ค่าปรึกษาแพทย์เชี่ยวชาญเฉพาะโรค 600,000 กีบ/ครั้ง\n- ค่ายาตามจริง ไม่เกินวงเงินรวม 4,500,000 กีบ/ครั้ง\nค่าผ่าตัด 4,500,000 กีบ/ครั้ง", "ui_messageIPD_rafco1": "ค่ารักษาพยาบาลไม่เกิน 500 $/ครั้ง ตามเงื่อนไขการเบิกดังนี้ (รวมค่าห้อง/ค่าหมอ/ค่ายา)\n- จ่ายค่าห้อง 50 $/วัน\n- ค่าปรึกษาแพทย์เชี่ยวชาญเฉพาะโรค 66.66 $/ครั้ง\n- ค่ายาตามจริง ไม่เกินวงเงินรวม 500 $/ครั้ง\nค่าผ่าตัด 500 $/ครั้ง", "ui_messageOPD_thai1": "ค่ารักษาผู้ป่วยนอก วงเงิน 3,600 บาท ต่อปี", "ui_messageOPD_rplc1": "ค่ารักษาผู้ป่วยนอก วงเงิน 1,080,000 กีบ ต่อปี", "ui_messageOPD_rafco1": "ค่ารักษาผู้ป่วยนอก วงเงิน 110 $ ต่อปี", "ui_messageNOTE": "***ใช้สิทธิ์ได้เฉพาะสมาชิกประจำเท่านั้น\n***รักษาที่คลีนิคหรือโรงพยาบาลเท่านั้นกรณีซื้อยาตามร้านขายยาไม่สามารถเบิกได้นะจ๊ะ\n***ใบเสร็จที่ห้ามนำมาเบิก เช่น ใบเสร็จตรวจการตั้งครรภ์ไม่สามารถเบิกได้\nสามารถติดต่อสอบถามรายละเอียดได้ที่ PAO หรือสอบถามเบอร์ 152 153 154 และ 158", "btn_statusWithdrow": "สถานะการเบิก", "pkgwelfarehealthwithdraw": "pkgwelfarehealthwithdraw", "ui_headhealthwithdraw": "สวัสดิการสุขภาพ", "ui_statusWithdrawPKG": "สถานะการเบิกใช้ประกัน PKG", "ui_Used": "ใช้ไป", "ui_Balance": "คงเหลือ", "ui_IPD": "ผู้ป่วยใน IPD", "ui_OPD": "ผู้ป่วยนอก OPD", "ui_amountWithdraw": "จำนวนเงินรวมที่ใช้ไป", "ui_listWithdraw": "รายการเบิกเคลม", "ui_claimtypeIPD": "เคลมประเภท IPD", "ui_claimtypeOPD": "เคลมประเภท OPD", "btn_key": "คีย์เบิกประกัน", "pkgwelfarehealthfrom": "pkgwelfarehealthfrom", "ui_headhealthfrom": "สวัสดิการสุขภาพ", "ui_headamount": "จำนวนเงินที่ให้ PAO โอนสำรองให้ กรณีเร่งด่วน", "tf_healthamount": "พิมพ์จำนวนเงิน", "ui_healthremark": "*กรณีที่ไม่ได้ให้ PAO สำรองเงินให้ กรุณาใส่ 0", "ui_headtype": "ประเภทการเบิกเคลม", "dp_selecthealth": "เลือกรายการ", "dp_out": "ผู้ป่วยนอก", "dp_in": "ผู้ป่วยใน", "ui_headhealthamountnet": "ค่าใช้จ่ายรวม", "tf_amountnet": "พิมพ์จำนวนเงิน", "ui_amountnetremark": "*จำนวนเงิน ทั้งหมด ที่จ่ายจริง ตามใบเสร็จ", "ui_headamountroom": "ค่าห้องพัก ที่นอนรักษา", "tf_amountroom": "พิมพ์จำนวนเงิน", "ui_roomremark": "*ค่าห้องพัก ที่นอนรักษา ที่ถูกคิดตามใบเสร็จ ( ถ้าไม่มีให้ใส่ 0 )", "ui_headamountday": "จำนวนวันที่นอนรักษาตัว", "tf_amounttday": "พิมพ์จำนวนวัน", "ui_dayremark": "* จำนวนวัน ที่ถูกคิดเงินตามใบเสร็จ (ถ้าไม่มียอดเงินให้ใส่ 0)", "btn_next": "ถัดไป", "pkgwelfarehealthfrom2": "pkgwelfarehealthfrom2", "ui_headhealthfrom2": "สวัสดิการสุขภาพ", "tf_remark": "*กรณีถ้าไม่มี กรุณาใส่ 0", "tf_typingamount": "พิมพ์จำนวนเงิน", "ui_visitingdoctor": "ค่าเยี่ยมเเพทย์ค่าตรวจรักษาของผู้ประกอบวิชาชีพ", "ui_amountday": "จำนวนวัน ในใบเสร็จ ที่จ่ายค่า “ แพทย์เยี่ยม ”", "tf_amountday": "พิมพ์จำนวนวัน", "ui_treatmentcost": "ค่ารักษาพยาบาลอื่นๆ", "ui_surgerycost": "ค่าการผ่าตัด", "ui_medication": "ค่ายากลับบ้าน", "btn_before": "ก่อนหน้า", "btn_next2": "ถัดไป", "pkgwelfarehealthfrom3": "pkgwelfarehealthfrom3", "ui_headhealthfrom3": "สวัสดิการสุขภาพ", "tf_remarknamebank": "*เช่น ศิริวรรณ ส่งศรี เป็นต้น", "tf_remarknumbank": "*ตย. **********", "tf_remarktel": "*ตย. ***********", "ui_doc": "เอกสารแนบ หรือ เอกสารตัวจริง", "btn_image": "อัพโหลดรูปภาพ", "ui_remark": "หมายเหตุ", "tf_typingremark": "พิมพ์รายละเอียด", "ui_namebank": "ข้อมูลการโอนเงินชื่อบัญชี", "tf_naembank": "พิมพ์รายละเอียด", "ui_numbank": "เลขที่บัญชี ธนาคารกสิกร ตามเลขที่บัญชีเงินเดือน", "tf_numbank": "พิมพ์เลขที่บัญชี", "ui_tel": "เบอร์โทรติดต่อผู้เบิก", "tf_tel": "พิมพ์เบอร์โทร", "btn_before3": "ก่อนหน้า", "btn_next3": "ถัดไป", "pkgwelfarehealthfrom4": "pkgwelfarehealthfrom4", "ui_headhealthfrom4": "สวัสดิการสุขภาพ", "ui_historymodify": "ข้อมูลประวัติการรักษา ชื่อสถานพยาบาล", "tf_historymodify": "พิมพ์รายละเอียด", "ui_namedoc": "ชื่อแพทย์ที่รักษา / แพทย์เจ้าของไข้", "tf_namedoc": "พิมพ์รายละเอียด", "ui_becurse": "สาเหตุของการเข้ารักษา", "tf_becurse": "พิมพ์รายละเอียด", "ui_drug": "ยาที่ได้รับ", "tf_drug": "พิมพ์รายละเอียด", "ui_typedrug": "*ระบุชนิดของยาที่ได้รับในการรักษาครั้งนี้", "btn_savefrom": "บันทึกข้อมูล", "btn_before4": "ก่อนหน้า", "pkgwelfarehealthconfirm": "pkgwelfarehealthconfirm", "ui_headwelfarehealthConfirm": "สวัสดิการสุขภาพ", "GTCbuyORsell": "GTCbuyORsell", "ui_GTCbuyORsell": "หุ้น GTC", "ui_GTCbuy": "ซื้อ", "ui_GTCsell": "ขาย", "ui_GTCbuycondition": "ดูเงื่อนไขการซื้อ", "ui_GTCbuycondition2": "ดูเงื่อนไขการขาย", "ui_GTCbuyRule": "เงื่อนไขการซื้อหุ้น GTC", "ui_GTCbuyRuleDetail": "สำหรับสมาชิกอายุงานบรรจุเป็นสมาชิกประจำ\nซื้อหุ้นต่อคนได้ตามที่ คกก. GTC แจ้งเงื่อนไข\nการซื้อในแต่ละครั้ง\nสำหรับสมาชิกแจ้งซื้อแล้ว แนบหลักฐาน\nการโอน likepoint ณ วันที่แจ้งด้วยค่ะ\n\nหากตรวจสอบแล้วยังไม่ได้โอนมา \nทีมงานขอยกเลิกหุ้นที่แจ้งซื้อ \nงานนี้ใครโอนก่อนมีสิทธิ์ก่อน", "btn_GTCbuyRuleOK": "ตกลง", "ui_GTCsellRule": "เงื่อนไขการขายหุ้น GTC", "ui_GTCsellRuleDetail": "1.สามารถขายได้ไม่เกิน 20.0%\nของหุ้นที่คุณถือครองอยู่\n2.สามารถขายได้ไม่เกิน 380.0 หุ้น\n( หรือประมาณ 10,000 บาท )\n3.สามารถขายได้ไม่เกินจำนวนหุ้นที่ GTC\nเปิดขายคงเหลืออยู่", "btn_GTCsellRuleOK": "ตกลง", "ui_GTCunit": "หุ้น", "ui_GTCbath": "บาท", "btn_GTCbuy": "ซื้อ", "ui_GTCheadBuy": "ซื้อหุ้น GTC", "ui_GTCbv": "ราคาต่อหุ้น BV", "ui_GTCnamesale": "ชื่อผู้ขาย", "ui_GTCnumsale": "จำนวนหุ้นที่เสนอขาย", "ui_GTCnumbuy": "จำนวนหุ้นต้องการซื้อ", "ui_GTCenterOnlyNum": "", "tf_GTCnumbuy": "กรอกจำนวนหุ้น", "ui_GTCrecive": "จำนวนหุ้นที่จะได้รับ", "ui_GTCbuyMoney": "จำนวนเงินที่ซื้อ", "ui_GTCcashout": "จำนวนเงินที่ต้องโอนจ่าย", "btn_GTCbuyindetail": "ซื้อ GTC", "ui_GTCdetail": "* รายละเอียดการซื้อ", "ui_GTCdetailtime": "ระยะเวลาในการทำรายการภายใน 30 นาที", "ui_GTCchanal": "ช่องทางการโอนจ่ายเงิน", "ui_GTCcashing": "กำลังชำระเงิน", "ui_GTCdetailcancle": "รายการจะถูกยกเลิกภายใน", "ui_GTCdetailcancle2": "30", "ui_GTCdetailcancle3": "นาที", "ui_GTCkeybuy": "เลขที่ธุรกรรมการซื้อ", "ui_GTCdetailbuy": "รายละเอียดการซื้อ GTC", "ui_GTCnumunit": "จำนวนหุ้นที่ซื้อ", "ui_GTCpriceperunit": "ราคาต่อหุ้น", "ui_GTCremark": "หมายเหตุ", "ui_GTCremarkdetail": "กรุณาสอบถามรายละเอียดการโอนเงินกับผู้ขายก่อนชำระเงินได้ผ่านทางช่องทางการติดต่อ\n", "ui_GTCremarkdetail2": "เมื่อดำเนินการโอนเงินแล้ว ทำการกดปุ่ม “ โอนเงินแล้ว ”", "btn_GTCcancel": "ยกเลิก", "btn_GTCmove": "โอนเงินแล้ว", "ui_GTCstatus": "สถานะ", "ui_GTCremarkdetail3": "กรณีโอนเงินแล้วไม่ได้รับการโอนหุ้น GTC จากผู้ขาย\n", "ui_GTCremarkdetail4": "ให้กดปุ่ม “ ร้องเรียน ” เพื่อติดต่อทีมคกก.GTC", "btn_GTCreport": "ร้องเรียน", "ui_GTCreport": "ร้องเรียน", "btn_GTCAppealcancle": "ยกเลิก", "ui_GTCAppealdetail": "รายละเอียด", "tf_GTCAppealType": "กรุณาพิมพ์รายละเอียด..", "btn_GTCAppealSend": "ส่งข้อมูล", "ui_GTCAppealSuccess": "ยืนยันเรียบร้อย", "ui_GTCAppealDetail": "ทางคกก. GTC ได้รับข้อมูล\nการร้องเรียนของคุณแล้ว\nเรียบร้อยค่ะ", "btn_GTCAppealok": "ตกลง", "ui_GTCtransuccess": "ทำรายการสำเร็จ", "ui_GTCdetailsuccess": "GTC ได้โอนเข้าบัญชีของคุณแล้ว\nสามารถตรวจสอบได้ที่ หน้าหลัก", "ui_GTCsuccess": "เรียบร้อย", "ui_GTCconnectsale": "ติดต่อผู้ขาย", "ui_GTCconnectsale2": "คุณสามารถติดต่อกับผู้ขายหุ้น\nได้ที่เบอร์", "btn_GTCconnectOK": "ตกลง", "ui_GTCnum": "จำนวนหุ้นที่ถือครอง", "ui_GTCguarantee": "หุ้นที่ถือครองติดค้ำ CU", "ui_GTCfree": "หุ้นที่ถือครองปลอดภาระ", "ui_GTCover": "จำนวนหุ้นขายได้ไม่เกิน", "ui_GTCforsale": "จำนวนหุ้นที่เสนอขาย", "tf_GTCforsale": "ใส่จำนวนหุ้นที่จะขาย", "ui_GTCtel": "เบอร์โทรติดต่อ", "tf_GTCtel": "กรอกเบอร์โทรสำหรับให้ผู้ซื้อติดต่อ", "ui_GTCforsaleDetail": "* ข้อมูลรายละเอียดการขาย", "ui_GTCsale": "ราคาขาย (BV)", "ui_GTCforsaleunit": "จำนวนหุ้นที่เสนอขาย", "ui_GTCforsalebath": "มูลค่าที่เสนอขาย", "ui_GTCdate": "วันที่เสนอขาย", "ui_GTCcheck": "* กรุณาตรวจสอบรายละเอียดให้ถูกต้องทุกครั้ง ก่อนกดยืนยันการขาย", "btn_GTCok": "ยืนยัน", "ui_GTCconfirmSuccess": "ยืนยันเรียบร้อย", "ui_GTCreciveData": "ทางเราได้รับข้อมูลการขายหุ้น GTC\nของคุณ ", "ui_GTCreciveData2": "\nเรียบร้อยแล้วค่ะ", "btn_GTCsellSuccess": "ตกลง", "ui_GTCheadTransfre": "แจ้งการยืนยันโอนขายหุ้น GTC", "ui_GTCtransferDetail": "รายละเอียดการซื้อ GTC", "ui_GTCtransfersale": "ชื่อผู้ซื้อ", "ui_GTCtransfernumbuy": "จำนวนหุ้นที่ขอซื้อ", "ui_GTCtransferConfirm": "ยืนยันโอนหุ้น", "ui_GTCtransferunit": "ราคาต่อหุ้น", "ui_GTCtransfermoney": "จำนวนเงินที่คุณจะได้รับ", "btn_GTCtransfercancle": "ยกเลิก", "btn_GTCtransferOK": "ยืนยันโอนหุ้น", "ui_GTCheadersuccess": "ทำรายการสำเร็จ", "ui_GTCtransferdetail": "GTC ได้โอนหุ้นที่คุณขาย เข้าบัญชีของผู้ซื้อแล้วเรียบร้อย\nสามารถตรวจสอบยอดเงินได้ที่ หน้าหลัก", "btn_GTCtransferSuccess": "เรียบร้อย", "Leave": "Leave", "ui_ABSENCE": "อนุมัติ / ใบลา", "btn_approve": "อนุมติใบลา", "btn_leave": "สร้างใบลา", "ui_typeleavecreate": "ประเภทใบลา", "dp_selectleavecreate": "เลือกประเภทใบลา", "ui_dateleavecreate": "เริ่มวันที่", "ui_dateleavetocreate": "ถึงวันที่", "sl_dateleave": "เลือกวันที่", "ui_timestart": "ช่วงเวลา", "ui_timestop": "ถึงเวลา", "tf_detail": "ใส่รายละเอียด", "ui_sumdate": "รวม / วัน", "ui_detailleave": "รายละเอียดการลา", "tf_typingdetailcreate": "พิมพ์รายละเอียด", "ui_personApprove": "ผู้อนุมัติใบลา", "dp_personApprove": "เลือกผู้อนุมัติ", "btn_createleave": "สร้างใบลา", "btn_cancelcreate": "ยกเลิก", "noti_leave": "อนุมัติใบลาเรียบร้อย", "LeaveDetail": "LeaveDetail", "ui_LeaveDetail": "รายละเอียดใบลา", "ui_name": "ชื่อ : ", "ui_typeleave": "ประเภทใบลา : ", "ui_dateleave": "วันที่ : ", "ui_dateleaveto": "ถึงวันที่ : ", "ui_sumdateleave": "รวมจำนวนวันที่ลา : ", "ui_DetailLeave": "รายละเอียด : ", "ui_datekey": "วันที่คีย์ข้อมูล : ", "btn_approveDetail": "อนุมัติ", "btn_notApproveDetail": "ไม่อนุมัติ", "ui_approvesuccess": "อนุมัติเรียบร้อย", "ui_approvedetail": "คุณได้อนุมัติใบลา ของ\nคุณ ", "ui_approvedetailsuccess": "\nเรียบร้อยแล้วค่ะ", "ui_approvesuccessCan": "ไม่อนุมัติเรียบร้อย", "ui_approvedetailCan": "คุณไม่อนุมัติใบลา ของ\nคุณ ", "ui_approvedetailsuccessCan": "\nโปรดแจ้งสมาชิกด้วย", "ui_createLeaveSuccess": "สร้างใบลาเรียบร้อย", "ui_createdetail": "ใบลาของคุณได้ถูกสร้าง\nและส่งให้ผู้รับใช้ทีม เรียบร้อยแล้ว\nโปรดรอการอนุมัติ", "ui_approveok": "ตกลง", "timmi": "ติ๋มมี่ :", "leaveconfirm": "leaveconfirm", "ui_leaveconfirm": "ยืนยันการอนุมัติ", "ui_leaveYN": "คุณต้องการยืนยันการอนุมัติใช่หรือไม่", "ui_remarkleave": "หมายเหตุ :", "btn_leaveOK": "ตกลง", "btn_leaveCancel": "ยกเลิก", "createleaveconfirm": "createleaveconfirm", "ui_createleaveconfirm": "ยืนยันการสร้างใบลา", "ui_createleaveYN": "คุณต้องการสร้างใบลาใช่หรือไม่", "btn_createleaveOK": "ตกลง", "btn_createleaveCancel": "ยกเลิก", "mainidea": "mainidea", "ui_mainideahead": "PKG IDEA", "btn_newsidea": "ข่าวสารไอเดีย", "btn_vdo": "วีดีโอ", "btn_addidea": "ออกไอเดีย", "ui_by": "BY", "btn_sendidea": "ออกไอเดีย", "addidea": "addidea", "ui_headaddidea": "ออกไอเดีย", "ui_nameidea": "ชื่อไอเดีย", "tf_typingidea": "พิมพ์ชื่อไอเดีย", "ui_detailidea": "รายละเอียด / คำอธิบาย", "tf_typingdetail": "พิมพ์รายละเอียด", "btn_saveidea": "บันทึกข้อมูล", "ui_savesuccess": "บันทึกเรียบร้อย", "ui_thx": "ขอบคุณสำหรับการร่วมออกไอเดีย\nของคุณด้วยนะครับ", "btn_okidea": "ตกลง", "detailidea": "detailidea", "ui_headdetail": "รายละเอียด", "ui_post": "โพสต์โดย", "ui_vote": "ให้ดาว", "btn_savevote": "บันทึกข้อมูล", "ui_votesuccess": "บันทึกเรียบร้อย", "ui_votethx": "ขอบคุณสำหรับคะแนนโหวต\nของคุณด้วยนะครับ", "ui_voteok": "ตกลง", "ui_comment": "แสดงความคิดเห็น", "tf_Write": "พิมพ์ข้อความ...", "btn_hidecomments": "ซ่อนความคิดเห็น", "btn_comments": "ความคิดเห็น", "QRscan": "QRscan", "ui_QRscanhead": "สแกนคิวอาร์โค๊ด", "ui_QRscanSuccess": "เรียบร้อย", "ui_QRscanSuccessDetail": "ทางเราได้สร้าง กิจกรรม กดเคลม\nของคุณ", "ui_QRscanSuccessDetail2": "\nเรียบร้อยแล้วค่ะ", "ui_QRscanok": "ตกลง", "ui_QRscanSuccessDetail3": "เข้าสู่ระบบ WebConnect", "cew": "cew", "ui_cewhead": "บันทึก CEW", "btn_cewPerson": "บุคคล", "ui_cewTeam": "ทีม", "ui_cewTeamRec": "ทีมที่ได้รับ", "dp_cewTeam": "เลือกทีม", "ui_cewPersonRec": "สมาชิกที่ได้รับ", "tf_cewIdPerson": "ใส่รหัสสมาชิก / พิมพ์ชื่อ", "tf_cewBU": "ใส่ BU", "ui_cewType": "เลือกประเภท CEW", "btn_cewRoadmap": "แผนงาน", "btn_cewAtmosphere": "บรรยากาศ", "btn_cewGeneral": "ทั่วไป", "tf_cewTyping": "พิมพ์รายละเอียด..", "tf_cewNumLike": "ใส่จำนวนไลค์พอยท์", "dp_cewCategory": "เลือกหมวดหมู่", "ui_cewDetailAAM": "* กรณี AAM กรุณาเลือกเขต", "ui_cewDetailAAM2": "ส่วน FU BU คณะกรรมการ และ\nRPLC ให้เลือก “ ไม่มี ”", "dp_cewCounty": "เลือกเขต", "ui_cewGiveCew": "ผู้ออก CEW", "ui_cewDetailCew": "ข้อมูลรายละเอียดการ CEW", "ui_cewPersonRec2": "ผู้รับ CEW", "ui_cewNumLike": "จำนวนไลค์พอยท์", "ui_cewUnitLike": "ไลค์", "ui_cewTypeCategory": "ประเภท / หมวดหมู่", "ui_cewDate": "วันที่บันทึก CEW", "ui_cewConfirm": "ยืนยัน", "ui_cewSuccess": "บันทึกเรียบร้อย", "ui_cewSuccessDetail": "ทางเราได้รับข้อมูลการบันทึก CEW\nของคุณ", "ui_cewSuccessDetail2": "\nเรียบร้อยแล้วค่ะ", "ui_cewSuccessok": "ตกลง", "noit_saveCew": "บันทึก Cew สำเร็จ", "mainBitrix": "mainBitrix", "ui_mainBitrixhead": "ประเด็นงานของฉัน", "btn_mainBitrixRecJob": "ผู้รับงาน", "btn_mainBitrixSendJob": "ผู้จ่ายงาน", "ui_mainBitrixNumJob": "คุณมีประเด็นงานทั้งหมด", "ui_mainBitrixList": "รายการ", "ui_mainBitrixOverDeadline": "ยังไม่มีการรายงาน\nความก้าวหน้าเพิ่มเติมมา", "ui_mainBitrixOverDeadline2": "วันแล้ว", "btn_mainBitrixHideComment": "ซ่อนความคิดเห็น", "btn_mainBitrixComment": "ความคิดเห็น", "btn_mainBitrixAddComment": "รายงานความก้าวหน้า", "btn_mainBitrixTyping": "พิมพ์ข้อความ..", "btn_mainBitrixUploadImg": "แนบรูป", "btn_mainBitrixEdit": "แก้ไข", "ui_mainBitrixNotSuccess": "ดำเนินการไม่สำเร็จ", "ui_mainBitrixNotSuccessDetail": "1. กรุณากดปุ่ม ", "ui_mainBitrixNotSuccessDetail2": " ก่อนทุกครั้ง\nเพื่อทำการรับประเด็นงานของคุณ\n2. กรุณาพิมพ์รายงานความก้าวหน้า\n3. หากประเด็นงานของคุณแล้วเสร็จ\nเรียบร้อย กรุณากดปุ่ม Finish", "btn_mainBitrixNotSuccessok": "ตกลง", "ui_mainBitrixSuccess": "บันทึกเรียบร้อย", "ui_mainBitrixSuccessDetail": "ได้รับข้อมูลการส่งรายงาน\nการปิดประเด็นงานนี้\nของคุณแล้ว เรียบร้อยค่ะ", "btn_mainBitrixSuccessok": "ตกลง", "ui_mainBitrixCloseIssueSuccess": "บันทึกเรียบร้อย", "ui_mainBitrixCloseIssueSuccessDetail": "ได้รับข้อมูลการรายงาน\nการปิดประเด็นงานนี้ ของสมาชิก\n", "ui_mainBitrixCloseIssueSuccessDetail2": "\nเรียบร้อยแล้วค่ะ", "btn_mainBitrixCloseIssueSuccessok": "ตกลง", "ui_mainBitrixReturnSuccess": "บันทึกเรียบร้อย", "ui_mainBitrixReturnSuccessDetail": "ได้รับข้อมูลการส่ง Return\nประเด็นงานนี้ ของสมาชิก\n", "ui_mainBitrixReturnSuccessDetail2": "\nเรียบร้อยแล้วค่ะ", "btn_mainBitrixReturnSuccessok": "ตกลง", "ui_mainBitrixShowDetailHead": "คำอธิบายเพิ่มเติม", "ui_mainBitrixShowDetai": "1.ตรวจสอบประเด็นงานแล้วถูกต้อง\nกดปุ่ม ", "ui_mainBitrixShowDetai2": " ปิดงานได้เลย\n2.ตรวจสอบประเด็นงานแล้วไม่ถูกต้อง\nกดปุ่ม ", "ui_mainBitrixShowDetai3": " พร้อม ", "ui_mainBitrixShowDetai4": "แจ้งสาเหตุและข้อมูลที่ต้องการ\n3.คลิกเปลี่ยนกำหนดวันที่แล้วเสร็จใหม่\nทุกครั้งด้วย", "btn_mainBitrixShowDetaiContinue": "ดำเนินการต่อ", "likeCredit": "likeCredit", "ui_likeCredithead": "ไลค์ เครดิต", "ui_likeCreditPoint": "คะแนนของคุณ", "ui_likeCreditLastUpdate": "อัพเดทล่าสุด", "ui_likeCreditChange": "ใช้คะแนนแลกสิทธิพิเศษ", "ui_likeCreditUnitPoint": "คะแนน", "btn_likeCreditChange": "แลก", "ui_likeCreditConfirmChange": "ยืนยันการแลกคะแนน", "ui_likeCreditConfirmChangeDetail": "คุณต้องการใช้คะแนนแลก\n", "ui_likeCreditConfirmChangeDetail2": "เพิ่ม ใช่หรือไม่?\n", "ui_likeCreditConfirmChangeDetail3": "ใช้คะแนน", "ui_likeCreditConfirmChangeDetail4": "คะแนน", "btn_likeCreditConfirmok": "ตกลง", "btn_likeCreditConfirmcancel": "ยกเลิก", "NDA": "NDA", "ui_NDAhead": "สัญญาการรักษาข้อมูลที่เป็นความลับ", "ui_NDAtitle": "สัญญาการรักษาข้อมูลที่เป็นความลับ\n(Non-disclosure Agreement)", "ui_NDAdetail": "“ข้อมูลที่เป็นความลับ” หมายความถึง ข้อมูลใดๆ รวมทั้งข้อมูลของบุคคลภายนอกที่ฝ่าย ผู้ให้ข้อมูลได้เปิดเผยแก่ฝ่ายผู้รับข้อมูล และฝ่ายผู้ให้ข้อมูลประสงค์ให้ฝ่ายผู้รับข้อมูลเก็บรักษาข้อมูลดังกล่าวไว้เป็นความลับและ/หรือความลับทางการค้าของฝ่ายผู้ให้ข้อมูล โดยข้อมูลดังกล่าวจะเกี่ยวข้องกับผลงานวิจัยเดิม ผลงานวิจัยของโครงการ และ/หรือการดำเนินงานโครงการ แผนและ/หรือแนวทางการวิจัย และ/หรือแผนทางการตลาด ซึ่งรวมถึงแต่ไม่จำกัดเฉพาะกระบวนการ ขั้นตอนวิธี โปรแกรมคอมพิวเตอร์ (รหัสต้นฉบับ รหัสจุดหมาย โปรแกรมปฏิบัติการ และฐานข้อมูลที่ใช้เชื่อมต่อโปรแกรมคอมพิวเตอร์) แบบ ต้นแบบ ภาพวาด สูตร เทคนิค การพัฒนาผลิตภัณฑ์ ข้อมูลการทดลอง และข้อมูลอื่นใดที่เกี่ยวข้องกับผลงานวิจัยเดิม ผลงานวิจัยของโครงการ และ/หรือการดำเนินงานโครงการ ขอให้ท่านอ่านสัญญาอย่างถี่ถ้วน พร้อมลงนามยินยอมตามข้อตกลง", "btn_ReadDoc": "ตรวจสอบเอกสาร", "btn_sign": "เซ็นเอกสาร", "btn_signP1": "เซ็นเอกสาร P1", "btn_signP2": "เซ็นเอกสาร P2", "more": "more", "ui_morehead": "เมนูทั้งหมด", "ui_toolsWork": "เครื่องมือทำงาน / งาน", "ui_HWWTF": "HWWTF", "btn_ToolsBU": "เครื่องมือ\nทำงาน BU", "btn_TA": "ลงเวลา\nทำงาน", "btn_Cew": "บันทึก\nCEW", "ui_agreement": "ข้อมูลสัญญา", "btn_loanCU": "สัญญา\nเงินกู้สามัญ", "btn_nonAgreement": "สัญญา\nไม่เปิดเผย\nข้อมูล", "ui_MVP": "เมนูทดสอบ", "btn_nitroSign": "อนุมัติเอกสาร\nการเงิน", "btn_CS24": "แอพ CS24", "Nitrosign": "เซ็นเอกสาร", "ui_NitroListhead": "การอนุมัติเอกสาร", "ui_Nitrohead": "เซ็นเอกสาร", "ui_Nitroreference": "เลขอ้างอิง", "ui_Nitrostatus": "สถานะ", "btn_NitroSign": "เซ็น", "ui_NitroSelect": "เลือก", "ui_NitroSelectDetail": "คุณจะใช้ลายเซ็น\nที่มีอยู่หรือสร้างใหม่", "ui_NitroSelectExist": "ที่มีอยู่", "ui_NitroSelectNew": "สร้างใหม่", "ui_NitroAgree": "ยินยอม", "ui_NitroAgreeDetail": "คุณยินยอมที่จะให้ใช้\nลายเซ็น อิเล็กทรอนิกส์\nที่มีอยู่ หรือไม่", "ui_NitroAgreeOK": "ยินยอม", "ui_NitroAgreeNO": "ไม่ ยินยอม", "CS24": "CS24", "ui_CS24head": "รายชื่อที่ค้นพบ", "ui_CS24head2": "รายละเอียดข้อมูลลูกค้า", "btn_seeDetail": "ดูข้อมูลส่วนตัว", "ui_detailBU": "ข้อมูลแต่ละ BU", "btn_buPMS": "PMS", "btn_buAAM": "AAM", "btn_buPMG": "PMG", "btn_buPCC": "PCC", "btn_buRPLC": "RPLC", "btn_buRAFCO": "RAFCO", "alert_not_found_likewallet": " Phone number not found like wallet", "test": "test", "ui_totalHeldShares": "หุ้นถือครอง", "ui_holdShareReturns": "จากหุ้นที่ถือครอง", "ui_waitHeldShares": "หุ้นรอถือครอง", "ui_titleAlert3": "แจ้งเตือน", "ui_detailAlert4": "กรุณาเซ็นเอกสาร", "btn_HRSign": "อนุมัติเอกสาร\nบุคคลากร", "pending_sign": "รอเซ็นยืนยัน", "document_detail": "รายละเอียดเอกสาร", "report_invalid_document": "กดที่นี่! หากเอกสารไม่ถูกต้อง", "report_invalid_document_detail": "รายละเอียด", "btn_report_invalid_document": "ยืนยัน", "ui_here": "ที่นี่!", "ui_regis_signature": "ไม่พบลายเซ็นในฐานข้อมูล\nกรุณาขึ้นทะเบียนลายเซ็นของท่าน", "ui_click_here_to_sign": "กดที่นี่! เพื่อลงนามเอกสาร", "ui_register_Esign": "ลงทะเบียนลายเซ็น", "ui_register_twoFA": "ลงทะเบียน 2FA", "not_support_NFC": "เครื่องของท่านไม่รองรับระบบ NFC\n สำหรับการใช้งาน YubiKey\n กด ถัดไป เพื่อลงทะเบียนการใช้งาน OTP", "support_NFC": "เว็บแอพยังไม่รองรับระบบ NFC\n สำหรับการใช้งาน YubiKey\n กด ถัดไป เพื่อลงทะเบียนการใช้งาน OTP", "not_support_NFC_verify": "เครื่องของท่านไม่รองรับระบบ NFC\n สำหรับการใช้งาน YubiKey\n กด ถัดไป เพื่อใช้งาน OTP", "btn_register_twoFA": "ลงทะเบียน", "btn_register_OTP": "ลงทะเบียน OTP", "ui_success_yubikey": "คุณได้ลงทะเบียน YubiKey เรียบร้อยแล้ว", "ui_success_yubikey_verify": "ใช้งาน YubiKey เรียบร้อยแล้ว", "ui_success_otp": "คุณได้ลงทะเบียน OTP เรียบร้อยแล้ว", "ui_success_otp_sending": "ระบบกำลังทำการส่ง OTP", "ui_success_otp_send": "ระบบทำการส่ง OTP ไปยัง Telegram ของคุณเรียบร้อยแล้ว", "ui_success_otp_verify_header": "ยืนยันรหัส OTP", "ui_otp_here": "กรอก OTP ที่ได้รับจาก Telegram", "ui_success_otp_title": "ยืนยันการใช้งาน OTP สำเร็จ", "ui_success_otp_verify": "เข้าใช้งานระบบ OTP เรียบร้อยแล้ว", "ui_waiting": "รอสักครู่", "ui_duplicate_otp": "เครื่องของท่านไม่รองรับระบบ NFC\n สำหรับการใช้งาน YubiKey", "ui_duplicate_otp_detail": "ท่านได้ทำการลงทะเบียน OTP ไปแล้ว", "ui_touch_yubikey": "แตะ YubiKey ของท่าน", "ui_duplicate_yubikey": "YubiKey นี้ได้มีการลงทะเบียนไปแล้ว", "ui_register_twoFA_header": "ระบบความปลอดภัย กลุ่มประชากิจ", "ui_scan_yubikey": "สแกน <PERSON><PERSON><PERSON><PERSON>", "ui_change_to_otp": "กดที่นี่! เพื่อเปลี่ยนเป็น OTP", "ui_please_unlock": "กรุณาทำการปลดล็อค", "ui_unlock": "ปลดล็อค", "ui_to_sign_in": " เพื่อเข้าใช้งาน MS24", "privacy_policy": "นโยบายความเป็นส่วนตัว", "btn_PMSSign": "ยืนยันรายการ\nทำงาน", "ui_notification_list": "ไม่มีรายการแจ้งเตือน", "ui_delete_l": "ลบข่าวสารทั้งหมด", "ui_delete_1": "กรุณาอ่านข่าวสารของท่านให้ครบถ้วน", "ui_delete_2": "ก่อนทำการลบข้อมูลข่าวทั้งหมด", "ui_delete_3": "ลบทั้งหมด", "ui_delete_4": "ยกเลิก", "ui_delete_5": "มีข่าวสารที่คุณยังไม่ได้อ่าน", "ui_approval_document": "ไม่มีรายการเอกสารอนุมัติ", "ui_approval_1": "ตักเตือน", "ui_approval_2": "สัญญาประกัน", "ui_approval_3": "หน่วยงาน BU", "btn_nonAgreement_1": "สัญญาไม่เปิด\nเผยข้อมูล", "ui_likecredit_t": "แลกสิทธิพิเศษ", "ui_likecredit_1": "คุณต้องการที่จะใช้คะแนนแลก", "ui_likecredit_2": "สิทธิพิเศษหรือไม่", "ui_profile": "ข้อมูลส่วนตัว", "ui_birthday": "วันเดือนปีเกิด", "ui_IDcard": "เลขบัตรประชาชน", "ui_number": "เบอร์โทรศัพท์", "ui_numberlikewallet": "เบอร์โทรศัพท์ผูกบัญชี", "ui_email": "อีเมล์", "ui_security_p": "ระบบความปลอดภัย", "ui_profile_e": "แก้ไขข้อมูลส่วนตัว", "ui_life": " อายุงาน นับจากวันบรรจุ", "ui_rewardCU": "คุณได้รับ Reward ประจำวันจากกองทุน Meta CU เรียบร้อยแล้วค่ะ", "ui_jobmenu": "คุณมีประเด็นงานใหม่ กรุณากดรับงานที่เมนู\"ประเด็นงานของฉัน\"", "ui_noti": "มีแจ้งเตือนใหม่รอให้คุณอ่าน กรุณากดอ่านที่เมนู\"กระดิ่งแจ้งเตือน\" ", "ui_news_s": "มีข่าวใหม่รอให้คุณอ่าน กรุณากดอ่านที่เมนู \"ข่าวสาร\"", "ui_fin": "มีเอกสารรอคุณอนุมัติ กรุณากดที่เมนู \"อนุมัติเอกสารด้านการเงิน\"", "ui_hr_approval": "มีเอกสารรอคุณอนุมัติ กรุณากดที่เมนู\"อนุมัติเอกสารด้านบุคคลากร\"", "ui_wi_approval": "มีเอกสารรอคุณอนุมัติ กรุณากดที่เมนู\"อนุมัติเอกสารด้านกระบวนการ\"", "ui_fullname": "ชื่อ-นามสกุล", "ui_confirm_exchange": "แลก likecredit เรียบร้อย", "ui_OKR": "ข้อมูลOKR", "ui_paste": "วาง", "ui_Kip": "กีบ", "ui_otp_build_TG": "ใส่รหัส", "ui_otp_build_TG_2": "เราได้ส่ง SMS พร้อมรหัสเปิดใช้งานไปยังเบอร์โทรศัพท์ของคุณ", "ui_otp_build_TG_3": "กรอก OTP ที่ได้รับจาก SMS", "btn_signupTG": "สมัคร Telegram เรียบร้อยแล้ว", "btn_signupTG_m": "คุณได้ทำการสมัคร Telegram เรียบร้อยแล้ว"}