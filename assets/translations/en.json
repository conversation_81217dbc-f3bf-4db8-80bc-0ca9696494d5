{"login": "login", "ui_login": "login", "fild_username": "username", "fild_password": "password", "btn_signin": "signin", "btn_signup": "signup", "ui_login_app": "confirm login", "home": "home", "ui_hello": "Hi", "ui_morning_y": "you have checked in TA", "ui_morning_n": "you are haven’t to checked in TA", "ui_bye_y": "you have checked out TA", "ui_bye_n": "you are haven’t to checked out TA", "btn_peopleProcess": "People\nProcess", "btn_4C": "4C", "btn_tools": "Work\nTools", "btn_bitrix": "My job", "btn_CuGtc": "CU / GTC", "btn_seeAll": "See All", "ui_peopleProcess": "People Process", "ui_4C": "4C", "ui_CuGtc": "CU / GTC", "ui_tools": "Work Tools", "btn_issue": "Main Issue", "btn_toolsPMS": "Work\nTools PMS", "btn_toolsAAM": "Work\nTools AAM", "btn_toolsCS": "Work\nTools CS24", "btn_ta": "TA", "btn_welfare": "Benefits Status", "btn_callcenter": "Call center", "btn_buyGTC": "GTC", "btn_scanQR": "scanQR", "btn_cew": "save CEW", "btn_idea": "PKG\nIdea", "btn_Leave": "Leave/\nApprove", "ui_statistics": "Statistics", "btn_Income": "Income", "btn_LikeWallet": "LikeWallet", "ui_LikeWallet": "LikeWallet", "btn_gtc": "GTC", "btn_cu": "CU", "btn_meta_cu": "Meta CU", "btn_Insurance_CU": "Insurance", "ui_Salary": "Salary", "ui_Incentive": "Incentive", "ui_dateUpdate": "update date", "ui_Reward": "<PERSON><PERSON>", "ui_PFS": "(PFS)", "ui_Bath": "Bath", "ui_Totally": "Totally", "ui_Locked": "Locked", "ui_Unlock": "Unlock", "ui_LIKE": "LIKE", "ui_MetaCu": "Meta CU", "ui_BV": "Value BV", "ui_sharing": "Sharing amount", "ui_TotallyGTC": "Total", "ui_TotallyGTCcoin": "GTCcoin", "ui_Unit": "Unit", "ui_Collateralized": "Collateralized with CU", "ui_TotallyCU": "Total", "ui_SavingCU": "Saving", "ui_InsuranceCU": "Insurance", "ui_DebtCU": "Loan", "ui_reward": "<PERSON><PERSON>", "ui_reward2": "From investment", "ui_Conditions": "Pending share to conditions", "ui_ContractCU": "Contract", "ui_value": "value(Bath)", "ui_bath": "Bath", "ui_likevalue": "like", "ui_cu": "CU Shares", "ui_gtc": "GTC Shares (Holding)", "ui_debt": "CU Loans", "ui_like": "Likepoint", "ui_likecredit": "LIKE CREDIT", "ui_detailFull": "see more", "ui_Savingmoney": "Saving money", "ui_position": "Position", "ui_purpose": "Purpose", "ui_borrowcu": "ordinary loan agreement", "ui_nondisclosure": "NON-DISCLOSURE AGREEMENT", "ui_passcode": "Passcode", "ui_create_passcode": "Create Passcode", "ui_confirm_passcode": "Confirm Passcode", "ui_languages": "Languages", "ui_feedback": "<PERSON><PERSON><PERSON>", "ui_theme": "Theme", "ui_guid": "MS24 Guid", "ui_supporter": "Support", "ui_talk": "Cha<PERSON>", "ui_logout": "Log out", "ui_logoutalert": "Log out", "ui_logoutCF": "Are you want to log out\nFor MS24 ?", "btn_cancel": "Cancel", "btn_logout": "Log out", "ui_update": "About to update", "ui_updateDetail": "MS24 Mobile Application\nHas been revised New functionality\nFor better use Please update the version.\n\nPress the “Update” button below to update.", "btn_update": "Update", "btn_After": "Later", "ui_titleAlert": "Coming Soon", "ui_detailAlert": "This menu is not yet\navailable now.", "ui_detailAlert2": "File you selected too large\nPlease make file smaller.", "ui_titleAlert2": "Error", "ui_detailAlert3": "Customer information not found\ntry new search", "btn_okAlert": "Done", "modify": "modify", "ui_modify": "About to improvement", "ui_modifyDetail": "MS24 Mobile Application\nClosed for revision And temporarily not serving this menu\n\nSorry for the inconvenience.", "passscode": "passcode", "ui_enter": "Enter your passcode", "ui_wrong": "Passcode wrong , try again", "ui_lostpin": "If you forget your pin, click here to log out", "changepasscode": "changepasscode", "ui_setting": "passcode setting", "ui_resetpasscode": "change passcode", "ui_putpasscodedefals": "old passcode", "ui_putpasscodenew": "new passcode", "ui_confirmpasscodenew": "confirm passcode", "ui_passwrong": "Passcode wrong , try again", "ui_alertpass": "Change Success!", "ui_alertdata": "Your passcode was a change\nsuccess already, Thank you", "btn_ok": "Okay", "ui_alerthome": "Cancel to Setting", "ui_datahome": "Are you want to cancel\nTo changing the passcode ?", "btn_continew": "Go back", "btn_okhome": "Cancel", "changlanguagessetting": "changlanguagessetting", "ui_selectlanguages": "select languages", "btn_eng": "English", "btn_th": "Thai", "ui_LanguageSetting": "Language Setting", "ui_settingCFf": "Are you want to changing\nTo", "ui_settingCFl": "version ?", "ui_selecteng": "English", "ui_selectth": "Thai", "ui_selectkm": "Cambodia", "ui_selectlao": "Laos", "btn_settingcancel": "Cancel", "ui_settingchange": "Change", "feedback": "feedback", "ui_headFeedback": "<PERSON><PERSON><PERSON>", "ui_subject": "Title", "tf_typingSj": "Typing to suggest.", "ui_detailFeedback": "Detail / Description", "tf_detailFeedback": "Typing details", "ui_imgUpload": "Upload picture", "btn_imgUpload": "Upload picture", "btn_saveFeedback": "Save", "ui_feedbacOK": "Success", "ui_feedbackDe": "Thank you so much\nfor sending your <PERSON><PERSON><PERSON>.", "ui_feedbackOK": "Ok", "contact": "contact", "ui_headcontact": "contact", "ui_subjectcontact": "Title", "tf_typingSjcontact": "Typing to contact.", "ui_detailcontact": "Detail", "tf_detailcontact": "Typing details", "ui_imgUploadcontact": "Upload picture", "btn_imgUploacontact": "Upload picture", "btn_savecontact": "Send", "ui_contactOK": "Success", "ui_contactDe": "Thank you so much\nfor sending your Contact.", "ui_contactkOK": "Ok", "ta": "ta", "ui_ta": "SIGNIN TA", "ui_date": "Date", "ui_timeNow": "Time", "btn_checkin": "Check In", "btn_checkin2": "Check In(Afternoon)", "btn_checkout": "Check out", "ui_problem": "Problems can be reported to Telegram in the service room.", "reasonforlate": "reasonforlate", "ui_why": "Why are you late?", "tf_message": "Text a message…", "btn_send": "Send", "sendproblem": "sendproblem", "ui_what": "What ’s the problem?", "ui_notWorking": "This application is not working.", "ui_notInternet": "Internet is not connected.", "reciveProblem": "reciveProblem", "ui_wait": "* We are received a message,\nPlease waiting for 5 minute to contact.", "notification": "notification", "ui_notify": "Notification", "ui_claim": "Claim LIKE", "btn_claim": "CLAIM", "ui_act": "Activities", "ui_news": "News", "pkgWelfare": "pkgWelfare", "ui_select": "Select menu", "btn_basic": "Basic welfare", "btn_flexible": "Flexible welfare", "btn_health": "Health insurance", "ui_contact": "Contact us, for more detail", "pkgwelfarebasic": "pkgwe<PERSON><PERSON>bas<PERSON>", "ui_headBasic": "Basic welfare", "ui_detail": "Basic welfare details", "ui_welfarecity": "Welfare rights according to the labor law of each country", "ui_welfareout": "Labor welfare beyond the law", "ui_welfareout1": "Welfare for wedding ceremony", "ui_welfareout2": "Maternity benefits", "ui_welfareout3": "Benefits for Ordination", "ui_welfareout4": "Funeral benefits: family staff / staff family", "ui_welfareout5": "Life and accident insurance benefits", "ui_welfareout6": "Benefits for group health insurance", "ui_welfareout7": "Benefits of group life insurance", "ui_welfareout8": "Annual health check-up assistance welfare (Only work group)", "ui_welfareout9": "Welfare Scholarships", "ui_welfareout10": "Employment benefits", "ui_welfareout11": "staff uniform benefits (excluding mechanic forms)", "ui_welfareout12": "Welfare, budget, remuneration, staff resignation", "ui_welfareout13": "Employee benefits in case of resignation", "ui_welfareout14": "Foreign work allowance /", "ui_welfareout15": "On-site work / training", "ui_welfareout16": "Accommodation welfare", "ui_helpbudget": "The organization has a budget to help.", "pkgwelfarebasicform": "pkgwelfarebasicform", "ui_headBasicform": "Basic welfare", "ui_status": "status", "dp_select": "select", "dp_member": "Regular staff", "dp_test": "Probation staff", "dp_hire": "Contract staff", "dp_Advisor": "Advisor", "ui_datework": "Please select date", "ui_remarkdatework": "Specify the date of events such as ordination, wedding, etc. according to the specified conditions And if in the case of maternity leave, insert the date of maternity leave every time", "tf_datework": "mm/dd/yyyy", "ui_selectwelfare": "Choose benefits", "ui_remarkwelfare": "In the case of leaving ordination for more than 15 days, select >>>>> ordination work (ordination over 15 days) and enter the date to return to work in the field on the date of returning to work for the ordained walk 15 days too !!", "dp_welfareselect": "select", "dp_marri": "Wedding ceremony", "dp_monk": "Ordination", "dp_monk15": "Ordination (ordination over 15 days)", "dp_baby": "Give birth", "dp_gran": "Relatives funeral (father-mother of spouse and sibling, same parent, grandfather, grandmother)", "dp_father": "Family staff funeral (father-mother, child, spouse)", "dp_advisor": "Funeral; staff of the counselor group, including the parents, spouses and children of the counselor died.", "ui_link": "Attach link cards for various ceremonies / birth certificates", "ui_remarklink": "(If you don't have a card yet, put another one)", "ui_showlink": "link ที่อัพโหลด", "ui_card": "For still no ceremony cards", "dp_cardselect": "Select", "dp_nocard": "No card", "ui_bank": "The account number must be Kasikorn Thai only !!", "ui_remarkbank": "If there is a leading 0, enter - Ex. 0-********* *", "tf_typingbank": "Typing bank", "ui_remarkbasic": "note", "tf_typingnote": "Typing note", "ui_monk": "For the topic * Ordination work (Ordained over 15 days) *", "ui_remarkmonk": "* Please enter the date of return to work as well *", "btn_savebasic": "Save", "btn_nextbasic": "Next", "btn_beforebasic": "Previous", "pkgwelfareflexible": "pkgwelfareflexible", "ui_headFlexible": "Flexible welfare", "ui_amount": "Amount withdraw", "ui_balance": "Net balance", "btn_apply": "Apply", "ui_history": "History", "ui_recive": "Received", "pkgwelfareflexiblewithdraw": "pkgwelfareflexiblewithdraw", "ui_headFlexibleWithdraw": "Flexible welfare", "ui_amountWithdrow": "Available balance", "ui_amount2": "Amount", "tf_amount": "Typing amount", "ui_grouptype": "Receipt category", "dr_select": "Select list", "ui_uploadimmage": "Upload receipt", "ui_exImage": "receipt example", "ui_linkimmage": "link image", "btn_uploadimmage": "Upload Photo", "btn_save": "Save", "btn_details": "Confirm welfare withdrawal", "btn_details2": "You withdraw benefits flexibly", "btn_details3": "amount", "btn_details4": "baht", "btn_details5": "kip", "pkgwelfareflexibleconfirm": "pkgwelfareflexibleconfirm", "ui_headFlexibleConfirm": "Flexible welfare", "pkgwelfarehealth": "pkgwelfarehealth", "ui_headhealth": "Health insurance", "ui_selectinsurance": "Select list", "btn_selectIPD": "IPD \ninsurance", "btn_selectOPD": "OPD \ninsurance", "ui_conditionIPD": "Health insurance conditions IPD...", "ui_conditionOPD": "Health insurance conditions OPD...", "ui_messageIPD_thai1": "Medical expenses not exceeding 10,000 baht / time according to the withdrawal conditions as follows: (Including room / doctor fee / medicine) \n- Pay a room 1,500 baht / day  \n- Consultation fee for a specialist doctor 2,000 baht / time \n- The actual drug cost does not exceed the total credit limit of 10,000 baht / time \n Surgery cost 5,000 baht / time. ", "ui_messageIPD_thai": "welfare conditions for IPD and OPD Conditions are in accordance with the announcement of the PAO team.", "ui_messageIPD_rplc1": "Medical expenses not exceeding 4,500,000 Kip / time according to the withdrawal conditions as follows: (Including room / doctor fee / medicine) \n- Pay a room 450,000 Kip / day \n- Consultation fee for a specialist doctor 600,000 Kip / time \n- The actual drug cost does not exceed the total credit limit of 4,500,000 Kip / time \n Surgery cost 4,500,000 Kip / time. ", "ui_messageIPD_rafco1": "Medical expenses not exceeding 500 $ / time according to the withdrawal conditions as follows: (Including room / doctor fee / medicine) \n- Pay a room 50 $ / day \n- Consultation fee for a specialist doctor 66.66 $ / time \n- The actual drug cost does not exceed the total credit limit of 500 $ / time \n Surgery cost 500 $ / time. ", "ui_messageOPD_thai1": "Outpatient treatment fee 3,600 baht per year", "ui_messageOPD_rplc1": "Outpatient treatment fee 1,080,000 Kip per year", "ui_messageOPD_rafco1": "Outpatient treatment fee 110 $ per year", "ui_messageNOTE": "*** can be used for regular staff only \n *** Treat at a clinic or hospital only. In the case of buying drugs at pharmacies, you can not get it. \n *** Receipt that do not bring for withdrawal, such as a pregnancy examination receipt, can not be reimbursed \n Details at PAO or inquire No. 152 153 154 and 158 ", "btn_statusWithdrow": "Status", "pkgwelfarehealthwithdraw": "pkgwelfarehealthwithdraw", "ui_headhealthwithdraw": "Health insurance", "ui_statusWithdrawPKG": "Transaction status insurance", "ui_Used": "Used", "ui_Balance": "Balance", "ui_IPD": "Insurance IPD", "ui_OPD": "Insurance OPD", "ui_amountWithdraw": "Amount spent", "ui_listWithdraw": "History", "ui_claimtypeIPD": "Claimed IPD", "ui_claimtypeOPD": "Claimed <PERSON>", "btn_key": "Apply", "pkgwelfarehealthfrom": "pkgwelfarehealthfrom", "ui_headhealthfrom": "Health insurance", "ui_headamount": "Reserve amount paid form PAO (Urgent)", "tf_healthamount": "Typing amount", "ui_healthremark": "* In the event that PAO is not given Ask permission to enter 0", "ui_headtype": "Claim category", "dp_selecthealth": "Select list", "dp_out": "Outpatient", "dp_in": "Inpatient", "ui_headhealthamountnet": "Total cost", "tf_amountnet": "Typing amount", "ui_amountnetremark": "* The total amount actually paid according to the bill.", "ui_headamountroom": "room’s cost", "tf_amountroom": "Typing amount", "ui_roomremark": "* mattress room fee charged as per receipt (if not available, enter 0)", "ui_headamountday": "Number of days cost", "tf_amounttday": "Typing day", "ui_dayremark": "* Number of days the bill has been charged on the receipt. (If there is no amount, enter 0)", "btn_next": "Next", "pkgwelfarehealthfrom2": "pkgwelfarehealthfrom2", "ui_headhealthfrom2": "Health insurance", "tf_remark": "* If not, please enter 0", "tf_typingamount": "Typing amount", "ui_visitingdoctor": "Doctor diagnose’s cost", "ui_amountday": "Amount day for Doctor diagnose", "tf_amountday": "Typing amount", "ui_treatmentcost": "Other medical costs", "ui_surgerycost": "Surgery cost", "ui_medication": "Home medication", "btn_before": "Previous", "btn_next2": "Next", "pkgwelfarehealthfrom3": "pkgwelfarehealthfrom3", "ui_headhealthfrom3": "Health insurance", "tf_remarknamebank": "* Such as <PERSON><PERSON><PERSON> etc.", "tf_remarknumbank": "*Ex. **********", "tf_remarktel": "*Ex. ***********", "ui_doc": "Attachment (Original’s)", "btn_image": "Upload Photo", "ui_remark": "Note", "tf_typingremark": "Typing detail", "ui_namebank": "Bank tranfer account’s name", "tf_naembank": "Typing detail", "ui_numbank": "Ka<PERSON>orn Account no.", "tf_numbank": "Account no.", "ui_tel": "Phone", "tf_tel": "Phone no.", "btn_before3": "Previous", "btn_next3": "Next", "pkgwelfarehealthfrom4": "pkgwelfarehealthfrom4", "ui_headhealthfrom4": "Health insurance", "ui_historymodify": "History of treatment / Hospital name", "tf_historymodify": "Typing detail", "ui_namedoc": "Attending physician’s name", "tf_namedoc": "Typing detail", "ui_becurse": "Admit’s caused", "tf_becurse": "Typing detail", "ui_drug": "Medicine", "tf_drug": "Typing detail", "ui_typedrug": "* Specify the type of drug received in this treatment.", "btn_savefrom": "Save", "btn_before4": "Previous", "pkgwelfarehealthconfirm": "pkgwelfarehealthconfirm", "ui_headwelfarehealthConfirm": "Health insurance", "GTCbuyORsell": "GTCbuyORsell", "ui_GTCbuyORsell": "GTC", "ui_GTCbuy": "buy", "ui_GTCsell": "sell", "ui_GTCbuycondition": "Read conditions", "ui_GTCbuycondition2": "Read conditions", "ui_GTCbuyRule": "GTC purchase conditions", "ui_GTCbuyRuleDetail": "Staff working, they are packed as regular Staff.\nbuy shares per person GTC's terms conditions.\neach purchase\nStaff purchase Please attach transfer\nTransfer likepoint on the date of notification.\n\nIf checked Not transferred,\nTeam requested to cancel the GTC\nthat have notified the purchase.", "btn_GTCbuyRuleOK": "Done", "ui_GTCsellRule": "GTC Sell conditions", "ui_GTCsellRuleDetail": "1.Can sell up to 20.0% of the GTC\nyou hold\n2.Can sell no more than 380 Units\n( About 10,000 Bath )\n3.Can sell no more than the amount\nof GTC, that is open for sale remaining.", "btn_GTCsellRuleOK": "Done", "ui_GTCunit": "Unit", "ui_GTCbath": "Bath", "btn_GTCbuy": "BUY", "ui_GTCheadBuy": "GTC", "ui_GTCbv": "Buy for BV rates.", "ui_GTCnamesale": "<PERSON><PERSON>", "ui_GTCnumsale": "Amount of GTC for sale", "ui_GTCnumbuy": "Amount of GTC purchased", "ui_GTCenterOnlyNum": "\n* Enter only numbers", "tf_GTCnumbuy": "Enter amount", "ui_GTCrecive": "Totally GTC Earn", "ui_GTCbuyMoney": "Amount to purchased", "ui_GTCcashout": "Amount to payment", "btn_GTCbuyindetail": "BUY", "ui_GTCdetail": "* Detail for buying", "ui_GTCdetailtime": "This transaction will be cancelling in 30 minute.", "ui_GTCchanal": "Reaching to <PERSON><PERSON>", "ui_GTCcashing": "Pending payment", "ui_GTCdetailcancle": "This transaction will be cancelling in", "ui_GTCdetailcancle2": "30", "ui_GTCdetailcancle3": "minute.", "ui_GTCkeybuy": "Transaction No.", "ui_GTCdetailbuy": "Detail :", "ui_GTCnumunit": "Amount of GTC purchased", "ui_GTCpriceperunit": "BV rates / Unit", "ui_GTCremark": "Note :", "ui_GTCremarkdetail": "Please, Call to <PERSON><PERSON> for check all about payment.\n", "ui_GTCremarkdetail2": "If you already transferred, Press the “Transferred” button", "btn_GTCcancel": "Cancel", "btn_GTCmove": "Transferred", "ui_GTCstatus": "Status", "ui_GTCremarkdetail3": "If you haven’t get your ordered from the seller,", "ui_GTCremarkdetail4": "You can press the “Appeal” button to contact GTC service.", "btn_GTCreport": "Appeal", "ui_GTCreport": "Appeal", "btn_GTCAppealcancle": "Cancel", "ui_GTCAppealdetail": "Detail", "tf_GTCAppealType": "Please type the details..", "btn_GTCAppealSend": "Send", "ui_GTCAppealSuccess": "Success", "ui_GTCAppealDetail": "We have received your\ninformation Already.", "btn_GTCAppealok": "Done", "ui_GTCtransuccess": "Transaction Sucsess", "ui_GTCdetailsuccess": "GTC Already, Transferred to your account.\nYou can check that on home menu.", "ui_GTCsuccess": "Done", "ui_GTCconnectsale": "Call to <PERSON><PERSON>", "ui_GTCconnectsale2": "If you want to reach the seller\nClick to", "btn_GTCconnectOK": "Done", "ui_GTCnum": "Amount of your GTC", "ui_GTCguarantee": "Owned and bearing CU", "ui_GTCfree": "Owned and non - burdened", "ui_GTCover": "Amount of GTC sell not more than", "ui_GTCforsale": "Amount of GTC Sell", "tf_GTCforsale": "Enter amount", "ui_GTCtel": "Phone", "tf_GTCtel": "Enter phone number", "ui_GTCforsaleDetail": "* Sales detail info.", "ui_GTCsale": "Sell for BV rates", "ui_GTCforsaleunit": "Amount of GTC for sale", "ui_GTCforsalebath": "Offering value", "ui_GTCdate": "Offering Date", "ui_GTCcheck": "* Please , Check all detail of your selling., Before to confirm.", "btn_GTCok": "Confirm", "ui_GTCconfirmSuccess": "Success", "ui_GTCreciveData": "We have received information\non the selling of GTC.\nof you ", "ui_GTCreciveData2": "\nCompleted", "btn_GTCsellSuccess": "Done", "ui_GTCheadTransfre": "Confirm to GTC selling", "ui_GTCtransferDetail": "Detail :", "ui_GTCtransfersale": "<PERSON><PERSON>", "ui_GTCtransfernumbuy": "Amount of GTC purchased", "ui_GTCtransferConfirm": "Confirm", "ui_GTCtransferunit": "BV rates / Unit", "ui_GTCtransfermoney": "Amount to payment", "btn_GTCtransfercancle": "Cancel", "btn_GTCtransferOK": "Confirm", "ui_GTCheadersuccess": "Confirm to GTC selling", "ui_GTCtransferdetail": "GTC Already, Transferred GTC to your buyer.\nYou can check the balance on home menu.", "btn_GTCtransferSuccess": "Done", "Leave": "Leave", "ui_ABSENCE": "ABSENCE", "btn_approve": "Approve", "btn_leave": "Create New", "ui_typeleavecreate": "Type", "dp_selectleavecreate": "Select list", "ui_dateleavecreate": "Date from", "ui_dateleavetocreate": "to", "sl_dateleave": "Select a date", "ui_timestart": "Starts on", "ui_timestop": "Ends on", "tf_detail": "Typing detail", "ui_sumdate": "Total / Days", "ui_detailleave": "Detail", "tf_typingdetailcreate": "Typing detail", "ui_personApprove": "Approver", "dp_personApprove": "Select list", "btn_createleave": "Submit", "btn_cancelcreate": "Cancel", "LeaveDetail": "LeaveDetail", "ui_LeaveDetail": "Detail", "ui_name": "Name : ", "ui_typeleave": "Type : ", "ui_dateleave": "Date from : ", "ui_dateleaveto": "Up to date : ", "ui_sumdateleave": "Sum date : ", "ui_DetailLeave": "Detail : ", "ui_datekey": "Date sent : ", "btn_approveDetail": "Approve", "btn_notApproveDetail": "Not approve", "ui_approvesuccess": "Success", "ui_approvedetail": "You have approved the form\nof you ", "ui_approvedetailsuccess": "\nCompleted", "ui_approvesuccessCan": "Not approved", "ui_approvedetailCan": "You do not approve the form\nof you ", "ui_approvedetailsuccessCan": "\nPlease notify staff", "ui_createLeaveSuccess": "Success", "ui_createdetail": "Your Leave Request has\nbeen sent.\nPlease wait for the approval", "ui_approveok": "Done", "leaveconfirm": "leaveconfirm", "ui_leaveconfirm": "Confirm approval", "ui_leaveYN": "Do you want to confirm the approval?", "ui_remarkleave": "remark :", "btn_leaveOK": "ok", "btn_leaveCancel": "edit", "createleaveconfirm": "createleaveconfirm", "ui_createleaveconfirm": "Confirm the Leave Request Creation.", "ui_createleaveYN": "Do you want to create the Leave Request?", "btn_createleaveOK": "OK", "btn_createleaveCancel": "Cancel", "mainidea": "mainidea", "ui_mainideahead": "PKG IDEA", "btn_newsidea": "Idea News", "btn_vdo": "Video", "btn_addidea": "Send Idea", "ui_by": "BY", "btn_sendidea": "Send IDEA", "addidea": "addidea", "ui_headaddidea": "Send Idea", "ui_nameidea": "Title", "tf_typingidea": "Typing name idea", "ui_detailidea": "Detail / Description", "tf_typingdetail": "Typing detail", "btn_saveidea": "Save", "ui_savesuccess": "Success", "ui_thx": "Thank you so much\nfor sending your IDEA.", "btn_okidea": "ok", "detailidea": "detailidea", "ui_headdetail": "Detail", "ui_post": "By", "ui_vote": "Vote", "btn_savevote": "Save", "ui_votesuccess": "Success", "ui_votethx": "Thank you for voting\nAbout this Idea.", "ui_voteok": "Ok", "ui_comment": "Comment", "tf_Write": "Write a comment…", "btn_hidecomments": "Hide Comments", "btn_comments": "Comments", "QRscan": "QRscan", "ui_QRscanhead": "QRscan", "ui_QRscanSuccess": "Success", "ui_QRscanSuccessDetail": "We have create activity claim\nfor you", "ui_QRscanSuccessDetail2": "\nCompleted", "ui_QRscanok": "Done", "ui_QRscanSuccessDetail3": "Login WebConnect", "cew": "cew", "ui_cewhead": "save CEW", "btn_cewPerson": "person", "ui_cewTeam": "team", "ui_cewTeamRec": "receive team", "dp_cewTeam": "select team", "ui_cewPersonRec": "recive person", "tf_cewIdPerson": "Enter ID / Enter Name", "tf_cewBU": "Enter BU", "ui_cewType": "select type CEW", "btn_cewRoadmap": "roadmap", "btn_cewAtmosphere": "atmosphere", "btn_cewGeneral": "general", "tf_cewTyping": "typing...", "tf_cewNumLike": "Enter like point", "dp_cewCategory": "select category", "ui_cewDetailAAM": "* AAM please select county", "ui_cewDetailAAM2": "FU BU board and\nRPLC select “ none ”", "dp_cewCounty": "select county", "ui_cewGiveCew": "Issuer CEW", "ui_cewDetailCew": "detailed information CEW", "ui_cewPersonRec2": "Recive CEW", "ui_cewNumLike": "Number Like Point", "ui_cewUnitLike": "Like", "ui_cewTypeCategory": "Type / category", "ui_cewDate": "date save CEW", "ui_cewConfirm": "confirm", "ui_cewSuccess": "Success", "ui_cewSuccessDetail": "We have recive save CEW\nfor you", "ui_cewSuccessDetail2": "\nCompleted", "ui_cewSuccessok": "Done", "mainBitrix": "mainBitrix", "ui_mainBitrixhead": "My job", "btn_mainBitrixRecJob": "Recive job", "btn_mainBitrixSendJob": "Sent job", "ui_mainBitrixNumJob": "You have all job issues.", "ui_mainBitrixList": "list", "ui_mainBitrixOverDeadline": "No report\nMore progress has been made for", "ui_mainBitrixOverDeadline2": "days.", "btn_mainBitrixHideComment": "Hide Comment", "btn_mainBitrixComment": "Comment", "btn_mainBitrixAddComment": "Add Comment", "btn_mainBitrixTyping": "typing..", "btn_mainBitrixUploadImg": "image", "btn_mainBitrixEdit": "Edit", "ui_mainBitrixNotSuccess": "Failed", "ui_mainBitrixNotSuccessDetail": "1. Please press the ", "ui_mainBitrixNotSuccessDetail2": " Start button first every time.\nto receive your work issue\n2. Typing a progress report.\n3. If your work is finished\nDone, please press the Finish button.", "btn_mainBitrixNotSuccessok": "Done", "ui_mainBitrixSuccess": "Success", "ui_mainBitrixSuccessDetail": "Recive submission information\nClose this issue\nSuccess", "btn_mainBitrixSuccessok": "Done", "ui_mainBitrixCloseIssueSuccess": "Success", "ui_mainBitrixCloseIssueSuccessDetail": "Recive report\nClose issue from you\n", "ui_mainBitrixCloseIssueSuccessDetail2": "\nSuccess", "btn_mainBitrixCloseIssueSuccessok": "Done", "ui_mainBitrixReturnSuccess": "Success", "ui_mainBitrixReturnSuccessDetail": "Recive Return\nIssue from you\n", "ui_mainBitrixReturnSuccessDetail2": "\nSuccess", "btn_mainBitrixReturnSuccessok": "Done", "ui_mainBitrixShowDetailHead": "more explanation", "ui_mainBitrixShowDetai": "1. Verify issue is correct. Press \n", "ui_mainBitrixShowDetai2": " Job can be closed now\n2.Check job issue is invalid\nPress ", "ui_mainBitrixShowDetai3": " ready ", "ui_mainBitrixShowDetai4": "Give reason and information\n3.Click change date of completion\nAlso every time.", "btn_mainBitrixShowDetaiContinue": "Continue", "likeCredit": "likeCredit", "ui_likeCredithead": "LIKE CREDIT", "ui_likeCreditPoint": "You likecredit", "ui_likeCreditLastUpdate": "last update", "ui_likeCreditChange": "Use points to chang special privileges", "ui_likeCreditUnitPoint": "point", "btn_likeCreditChange": "change", "ui_likeCreditConfirmChange": "Confirm change point", "ui_likeCreditConfirmChangeDetail": "You use point\n", "ui_likeCreditConfirmChangeDetail2": "add yes or no?\n", "ui_likeCreditConfirmChangeDetail3": "use points", "ui_likeCreditConfirmChangeDetail4": "points", "btn_likeCreditConfirmok": "Done", "btn_likeCreditConfirmcancel": "Cancel", "NDA": "NDA", "ui_NDAhead": "Non-disclosure Agreement", "ui_NDAtitle": "สัญญาการรักษาข้อมูลที่เป็นความลับ\n(Non-disclosure Agreement)", "ui_NDAdetail": "“ข้อมูลที่เป็นความลับ” หมายความถึง ข้อมูลใดๆ รวมทั้งข้อมูลของบุคคลภายนอกที่ฝ่าย ผู้ให้ข้อมูลได้เปิดเผยแก่ฝ่ายผู้รับข้อมูล และฝ่ายผู้ให้ข้อมูลประสงค์ให้ฝ่ายผู้รับข้อมูลเก็บรักษาข้อมูลดังกล่าวไว้เป็นความลับและ/หรือความลับทางการค้าของฝ่ายผู้ให้ข้อมูล โดยข้อมูลดังกล่าวจะเกี่ยวข้องกับผลงานวิจัยเดิม ผลงานวิจัยของโครงการ และ/หรือการดำเนินงานโครงการ แผนและ/หรือแนวทางการวิจัย และ/หรือแผนทางการตลาด ซึ่งรวมถึงแต่ไม่จำกัดเฉพาะกระบวนการ ขั้นตอนวิธี โปรแกรมคอมพิวเตอร์ (รหัสต้นฉบับ รหัสจุดหมาย โปรแกรมปฏิบัติการ และฐานข้อมูลที่ใช้เชื่อมต่อโปรแกรมคอมพิวเตอร์) แบบ ต้นแบบ ภาพวาด สูตร เทคนิค การพัฒนาผลิตภัณฑ์ ข้อมูลการทดลอง และข้อมูลอื่นใดที่เกี่ยวข้องกับผลงานวิจัยเดิม ผลงานวิจัยของโครงการ และ/หรือการดำเนินงานโครงการ ขอให้ท่านอ่านสัญญาอย่างถี่ถ้วน พร้อมลงนามยินยอมตามข้อตกลง", "btn_ReadDoc": "Document Review", "btn_sign": "Sign Doc", "btn_signP1": "Sign Doc P1", "btn_signP2": "Sign Doc P2", "more": "more", "ui_morehead": "All Menu", "ui_toolsWork": "WorkTools / Work", "ui_HWWTF": "HWWTF", "btn_ToolsBU": "Work\nTools BU", "btn_TA": "TA", "btn_Cew": "Save\nCEW", "ui_agreement": "ข้อมูลสัญญา", "btn_loanCU": "Ordinary loan\nAgreement", "btn_nonAgreement": "NON\nDisclosure\nAgreement", "ui_MVP": "Test Menu", "btn_nitroSign": "Fin Document\nApproval", "btn_CS24": "App CS24", "Nitrosign": "Sign the documents", "ui_NitroListhead": "Finance Document Approval", "ui_Nitrohead": "Sign the documents", "ui_Nitroreference": "ref number", "ui_Nitrostatus": "status", "btn_NitroSign": "Sign", "ui_NitroSelect": "Select", "ui_NitroSelectDetail": "You will use a signature\nexisting or created new?", "ui_NitroSelectExist": "existing", "ui_NitroSelectNew": "Create New", "ui_NitroAgree": "Agree", "ui_NitroAgreeDetail": "Do you agree to use\nyour existing\nelectronic signature?", "ui_NitroAgreeOK": "Agree", "ui_NitroAgreeNO": "No Agree", "CS24": "CS24", "ui_CS24head": "discovered list", "ui_CS24head2": "customer details", "btn_seeDetail": "view profile", "ui_detailBU": "detail BU", "btn_buPMS": "PMS", "btn_buAAM": "AAM", "btn_buPMG": "PMG", "btn_buPCC": "PCC", "btn_buRPLC": "RPLC", "btn_buRAFCO": "RAFCO", "alert_not_found_likewallet": " Phone number not found like wallet", "test": "test", "ui_totalHeldShares": "Total held shares", "ui_holdShareReturns": "Hold share returns", "ui_waitHeldShares": "Sharing amount", "ui_titleAlert3": "<PERSON><PERSON>", "ui_detailAlert4": "Please, Sign this document", "btn_HRSign": "HR Document\nApproval", "pending_sign": "Wait Confirmation", "document_detail": "Document detail", "report_invalid_document": "Click here! if the document is invalid", "report_invalid_document_detail": "Reject detail", "btn_report_invalid_document": "Confirm", "ui_here": "here!", "ui_regis_signature": "No signature found in the database.\nPlease register your signature", "ui_click_here_to_sign": "Click here! to sign the document", "ui_register_twoFA": "Register 2FA", "not_support_NFC": "Your device does not support NFC.\n Please press Next to sign up for MS24 OTP log in.", "not_support_NFC_verify": "Your device does not support NFC.\n Please press Next to MS24 OTP log in.", "btn_register_twoFA": "Register", "btn_register_OTP": "Register OTP", "ui_success_yubikey": "Your YubiKey has been registered.", "ui_success_yubikey_verify": "Activated", "ui_success_otp": "OTP signing up has been completed.", "ui_success_otp_sending": "Sending OTP", "ui_success_otp_send": "OTP has been sent to your Telegram account.\n", "ui_success_otp_verify_header": "OTP verification", "ui_otp_here": "Enter OTP from Telegram", "ui_success_otp_verify": "Activated", "ui_waiting": "Please wait...", "ui_duplicate_otp": "Your device does not support NFC.", "ui_duplicate_otp_detail": "You already registered OTP.", "ui_touch_yubikey": "Touch YubiKey", "ui_duplicate_yubikey": "This YubiKey has been registered.", "ui_register_twoFA_header": "Security PKG", "ui_scan_yubikey": "<PERSON><PERSON>", "ui_change_to_otp": "click here! to change to OTP", "ui_please_unlock": "Please unlock", "ui_unlock": "Unlock", "ui_to_sign_in": "to sign in MS24", "privacy_policy": "Privacy Policy", "btn_PMSSign": "WI Document\nApproval", "ui_notification_list": "No Notification List", "ui_delete_l": "Delete all News", "ui_delete_1": "Please read your news. ", "ui_delete_2": "before deleting all news!!", "ui_delete_3": "Delete All", "ui_delete_4": "Cancel", "ui_delete_5": "Have your news unread!!", "ui_approval_document": "No list of approved documents", "ui_approval_1": "Admonish", "ui_approval_2": "Insurance", "ui_approval_3": "BU agency", "btn_nonAgreement_1": "NON\nDisclosure\nAgreement", "ui_NitroListhead_1": "Approve Personnel Documents", "ui_likecredit_t": "chang special privileges", "ui_likecredit_1": "You use point", "ui_likecredit_2": "special privileges yes or no?", "ui_profile": "Profile", "ui_birthday": "Date of Birth", "ui_IDcard": "CardID", "ui_number": "PhoneNumber", "ui_numberlikewallet": "PhoneNumber Linked to Account", "ui_email": "Email", "ui_security_p": "Security", "ui_profile_e": "EditProfile", "ui_life": " service life\nfrom the packing date", "ui_rewardCU": "You have received Meta CU daily reward.", "ui_jobmenu": "New job is assigned,please press \"START\" in My job menu.", "ui_noti": "You have got notifications, please press Notifications for content.", "ui_news_s": "We have got news for you, please press News for the content.", "ui_fin": "Your approval is requested, please press Fin. Expense to proceed.", "ui_hr_approval": "Your approval is requested, please press HR Approval to proceed.", "ui_wi_approval": "Your approval is requested, please press WI Approval to proceed.", "ui_fullname": "Name", "ui_namenumbank": "Account", "ui_confirm_exchange": "Redeem like<PERSON><PERSON> successfully", "ui_OKR": "OKR information", "ui_paste": "Paste", "ui_Kip": "<PERSON><PERSON>", "ui_otp_build_TG": "Enter Code", "ui_otp_build_TG_2": "We've sent an SMS with an activation code to your phone", "ui_otp_build_TG_3": "Enter <PERSON> from SMS", "btn_signupTG": "signups Telegram", "btn_signupTG_m": "You have successfully subscribed to Telegram"}