#!/bin/sh
# This is a generated file; do not edit or check into version control.
export "FLUTTER_ROOT=/Users/<USER>/Flutterversion/flutter3.24.5"
export "FLUTTER_APPLICATION_PATH=/Users/<USER>/githubproject/v3/MS24/Mapp_MS24_Flutter3"
export "COCOAPODS_PARALLEL_CODE_SIGN=true"
export "FLUTTER_TARGET=/Users/<USER>/Documents/Mapp_MS24_Flutter3/lib/main.dart"
export "FLUTTER_BUILD_DIR=build"
export "FLUTTER_BUILD_NAME=3.0.0"
export "FLUTTER_BUILD_NUMBER=21"
export "DART_OBFUSCATION=false"
export "TRACK_WIDGET_CREATION=true"
export "TREE_SHAKE_ICONS=false"
export "PACKAGE_CONFIG=/Users/<USER>/Documents/Mapp_MS24_Flutter3/.dart_tool/package_config.json"
