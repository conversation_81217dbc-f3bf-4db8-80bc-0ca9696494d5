name: mapp_ms24
description: Rebuild MS24 Start November 2023
publish_to: 'none' # Remove this line if you wish to publish to pub.dev
#ios version: 3.0.0+17
#android version: 3.0.0+17

version: 3.0.0+21

environment:
  sdk: '>=3.1.5 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  flutter_painter : ^1.0.1
  cupertino_icons: ^1.0.2
  get: ^4.6.6
  pin_input_text_field: ^4.5.1
  awesome_notifications: ^0.7.4+1
  global_configuration: ^2.0.0-nullsafety.1
  flutter_easyloading: ^3.0.5
  get_storage: ^2.1.1
  flutter_secure_storage: ^8.1.0
  flutter_local_notifications: ^17.2.2
  flutter_svg: ^2.0.7
  sentry_flutter: ^8.9.0
#  firebase_core_desktop: ^1.0.2
  flutterfire_cli: ^0.2.7
  ags_authrest2: ^1.0.1
  cached_network_image: ^3.0.0
  cached_network_image_web: ^1.2.0
  easy_loader: ^2.0.0
  rive: ^0.11.1
  loading_animations: ^2.2.0
  flutter_webview_plugin: ^0.4.0

  permission_handler: ^10.0.0
  flutter_screenutil: ^5.9.0
  lottie: ^2.7.0
  flutter_telegram_login: ^0.0.2
  flutter_animation_progress_bar: ^2.3.1
  intl: ^0.17.0
  dots_indicator: ^2.0.0
  fluttertoast: ^8.2.4
  date_picker_plus: ^1.1.3
  open_store: ^0.5.0
  flutter_dotenv: ^5.0.2
  shared_preferences: ^2.2.2
  location: ^5.0.3
  pin_code_fields: ^8.0.1
#  nfc_manager: ^3.3.0
  camera: ^0.10.5+5
  rxdart: ^0.27.7
  draggable_fab: ^0.1.4
  http: any
  smooth_page_indicator: ^1.1.0
  animated_custom_dropdown: ^2.0.0
#  flutter_easy_search_bar: ^0.0.2
  google_fonts: any
  device_info_plus: ^8.2.2
  nfc_manager: ^3.1.1
  uni_links: ^0.5.1
  mqtt_client: ^9.8.0
  loading_indicator: ^3.1.1
  # firebase_dynamic_links: ^6.0.11
#  scan: ^1.6.0
  mobile_scanner: ^6.0.2
  loading_animation_widget: ^1.2.1
  flutter_pdfview: ^1.3.2
  pdf: ^3.10.8
  # flutter_painter: ^1.0.1
  flutter_inappwebview: ^5.4.3+7
  image_picker: ^1.0.7
  syncfusion_flutter_signaturepad: ^20.3.61
  flutter_image_compress: ^2.3.0
  phosphor_flutter: ^1.4.0
  syncfusion_flutter_pdf: ^20.3.61
#  pin_code_fields: ^8.0.1
  dio: ^5.0.3
  flutter_signature_pad: ^3.0.1
  printing: ^5.10.1
  photo_view: ^0.15.0
#  video_player: ^2.8.1
#  flutter_inappwebview: ^6.1.0+1
#  flutter_inappwebview: ^5.7.2+3
  animated_text_kit: ^4.2.1
  firebase_messaging: ^14.9.4
#  firebase_core: ^2.32.0
  awesome_dialog: ^3.1.0
  shorebird_code_push: ^1.1.3
  restart_app: ^1.3.1
  percent_indicator: ^4.2.3
  flutter_tts: ^4.2.0
  audioplayers: ^0.20.1
  audio_session: ^0.1.8
  flutter_sound: ^9.2.13
  just_audio_background: any
  path_provider: ^2.0.15
  ffmpeg_kit_flutter: ^4.5.1
  local_auth: ^2.3.0
  webview_flutter_web: ^0.2.3+4
  webview_flutter: ^4.10.0
  url_launcher: ^6.3.1
  image_gallery_saver: ^2.0.3
  # spinwheel
  flutter_fortune_wheel: ^1.3.2
  flutter_timer_countdown: ^1.0.7
  cloud_firestore: ^4.17.0
#  firebase_dynamic_links: ^5.5.7
  confetti: ^0.8.0
  firebase_core: ^2.31.1
  screenshot: ^3.0.0
  package_info_plus: ^3.1.2
  record: ^5.2.0
  flutter_js: ^0.7.2
  ffi: ^2.1.3
  microphone: ^0.1.0
  open_file: ^3.2.1
  file_picker: ^5.0.1
  permission_handler_web: ^0.0.2
  qr_flutter: ^4.1.0
  geolocator: ^9.0.2
  #ส่วนของการใช้งาน web
  flutter_nfc_kit: ^3.3.3
  universal_html: ^2.0.11
  uuid: ^3.0.7
  js: ^0.6.5

# spinwheel

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/logo/
    - assets/ADD/
    - assets/Claim/
    - assets/fonts/
    - assets/Happy/
    - assets/Menu/
    - assets/minilike/
    - assets/Timmi/
    - assets/images/yubikey/
    - assets/
    - shorebird.yaml
    - assets/images/
    - assets/svgs/
    - assets/bitrix/
    - assets/MSP/
    - assets/Theme/

  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:

     - family: SukhumvitSet-Text
       fonts:
         - asset: assets/fonts/sukhumvit-set/SukhumvitSet-Text.ttf
     - family: SukhumvitSet-Medium
       fonts:
         - asset: assets/fonts/sukhumvit-set/SukhumvitSet-Medium.ttf
     - family: SukhumvitSet-Bold
       fonts:
         - asset: assets/fonts/sukhumvit-set/SukhumvitSet-Bold.ttf
     - family: SukhumvitSet-SemiBold
       fonts:
         - asset: assets/fonts/sukhumvit-set/SukhumvitSet-SemiBold.ttf
     - family: Majesticons
       fonts:
         - asset: assets/fonts/Majesticons.ttf
     - family: Majesticons2
       fonts:
         - asset: assets/fonts/Majesticon.ttf
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
