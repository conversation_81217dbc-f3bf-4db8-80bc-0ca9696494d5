package com.prachakij.mapp_ms24_flutter3

import android.content.Context
import android.view.View
import android.webkit.WebView
import io.flutter.embedding.android.FlutterActivity
import io.flutter.plugin.platform.PlatformView
import io.flutter.plugin.platform.PlatformViewFactory
import io.flutter.plugins.flutter_plugin_android_lifecycle.FlutterPlatformViewsPlugin
import io.flutter.embedding.engine.FlutterEngine
import androidx.annotation.NonNull

class MainActivity: FlutterActivity() {
    override fun configureFlutterEngine(@NonNull flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        // ลงทะเบียน platform view factory ที่ใช้ viewType "telegramIframe"
        FlutterPlatformViewsPlugin.registerViewFactory("telegramIframe", MyCustomHtmlViewFactory())
    }
}

class MyCustomHtmlViewFactory : PlatformViewFactory(StandardMessageCodec.INSTANCE) {
    override fun create(context: Context?, viewId: Int, args: Any?): PlatformView {
        // ดึงค่า URL จาก args ที่ Flutter ส่งมา
        val params = args as? Map<String, String>
        val url = params?.get("url") ?: "https://ms24-beta.web.app/"  // ถ้าไม่มี URL ให้ใช้ค่าเริ่มต้น

        return MyCustomHtmlView(context, viewId, url)
    }
}

class MyCustomHtmlView(context: Context?, viewId: Int, url: String) : PlatformView {
    private val webView: WebView = WebView(context)

    init {
        webView.loadUrl(url)  // โหลด URL ที่ได้รับจาก Flutter
    }

    override fun getView(): View {
        return webView
    }

    override fun dispose() {
        webView.destroy()  // ทำลาย WebView เมื่อไม่ใช้
    }
}
