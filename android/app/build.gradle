
def localProperties = new Properties()

def localPropertiesFile = rootProject.file('local.properties')

if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

//add new
def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load( new FileInputStream(keystorePropertiesFile))
}
//

apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'com.google.gms.google-services'
apply from: "$flutterRoot/packages/flutter_tools/gradle/flutter.gradle"


android {
    // compileSdkVersion flutter.compileSdkVersion
    compileSdkVersion 34
    namespace 'com.prachakij.ms24beta'
    ndkVersion "25.1.8937393"
    
    configurations{
        all*.exclude module:'bcprov-jdk15on'
    }

    packagingOptions {
        pickFirst '**/libc++_shared.so'
        pickFirst '**/libfbjni.so'
    }

    kotlinOptions {
//         jvmTarget = '1_8'
        jvmTarget = JavaVersion.VERSION_11.toString()
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.prachakij.ms24beta"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
        minSdkVersion 27
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    //git checkout

    kotlinOptions {
        jvmTarget = "1.8"
    }

    

   signingConfigs {
        // debug {
        //     storeFile file('keystore/debug.keystore')
        // }
        release {
           keyAlias keystoreProperties['keyAlias']
           keyPassword keystoreProperties['keyPassword']
           storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
           storePassword keystoreProperties['storePassword']
       }
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            minifyEnabled false
            shrinkResources false
            multiDexEnabled true
            signingConfig signingConfigs.release
            
        }
    }


    flavorDimensions "env"

     productFlavors {
        dev {
        // only local testnet
            dimension "env"
            resValue "string", "app_name", "MS24(Beta Test) local"
            resValue "string", "keytool_sha1","9E:F7:CD:D2:2E:88:09:DC:C8:D4:CD:5B:AD:ED:B6:63:91:66:79:59"
            applicationId "com.prachakij.ms24beta"
       
        }

        // only deploy to store mainnet
        prod {
            dimension "env"
            resValue "string", "app_name", "MS24(Beta Test)"
            resValue "string", "keytool_sha1","9E:F7:CD:D2:2E:88:09:DC:C8:D4:CD:5B:AD:ED:B6:63:91:66:79:59"
            applicationId "com.prachakij.ms24beta"
        }

      
    }

}

flutter {
    source '../..'
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation "com.android.support:multidex:2.0.1"
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.firebase:firebase-messaging:23.1.2'
}
   

