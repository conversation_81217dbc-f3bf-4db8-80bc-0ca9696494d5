buildscript {
    ext.kotlin_version = '1.8.21'
    repositories {
        google()
        mavenCentral()
        // maven {
        //     url = uri("https://storage.googleapis.com/r8-releases/raw")
        // }
       maven { url "https://maven.google.com/"}
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.1.0'
        // START: FlutterFire Configuration
        classpath 'com.google.gms:google-services:4.3.10'
        // END: FlutterFire Configuration
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"

    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url "https://maven.google.com/"}
    }
        gradle.projectsEvaluated{
        tasks.withType(JavaCompile){
            options.compilerArgs << "-Xlint:deprecation"
        }
    }

}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}

