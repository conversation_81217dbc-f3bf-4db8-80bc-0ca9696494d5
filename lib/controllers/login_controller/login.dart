import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/controllers/internal/Profile/userController.dart';
import 'package:mapp_ms24/controllers/internal/wealthController/wealthController.dart';
import 'package:mapp_ms24/controllers/utils/connect.dart';
import 'package:mapp_ms24/controllers/utils/url.dart';
import 'package:mapp_ms24/models/login_models/LoginModels.dart';
import 'package:mapp_ms24/models/userModel/usermodel.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../../models/LocalStorageModel.dart';

class CenterController extends GetxController {
  final box = GetStorage();
  Future<dynamic> login(String username, String password) async {
    try {
      Map data = {
        "username": username,
        "password": password,
      };
      final response = await AppApi.callAPIjwt("POST", AppUrl.login, data);

      if (response["status"] == 200) {
        var userController = Get.put(ProfileController());
        try {
          userController.setModelUser(response["result"][0]);
          return response["result"][0];
        } catch (e) {
          print(e);
        }
      } else {
        const GetSnackBar(
          title: "เกิดข้อผิดพลาด",
          message: "เกิดข้อผิดพลาด",
          duration: Duration(seconds: 3),
        );
      }
      return;
    } catch (e) {
      if (kDebugMode) {
        print("error getCarReg1 =>$e");
      }
      await Sentry.captureException(e);
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
      return;
    }
  }

  Future<void> logout() async {
    try {
      await box.remove(LocalStorage.usernameLogin);
      await box.remove(LocalStorage.passwordLogin);
      await box.remove(LocalStorage.stepPin);
      await box.remove(LocalStorage.pinCode);
      await box.remove(LocalStorage.webAuthnCredentialId);
      // Navigate to login screen
      Get.offAllNamed('login');
      update();
    } catch (e) {
      await Sentry.captureException(e);
    }
  }
}
