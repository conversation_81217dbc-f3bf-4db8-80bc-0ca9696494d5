import 'package:flutter/cupertino.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:get/get.dart';
import 'dart:html' as html; // ใช้สำหรับดึงค่าภาษาบนเว็บ
import 'package:flutter/foundation.dart';
import 'package:microphone/microphone.dart'; // สำหรับ kIsWeb

class Annoucer extends GetxController {
  final FlutterTts flutterTts = FlutterTts();
  // final MicrophoneRecorder _recorder = MicrophoneRecorder();
  @override
  Future<void> onInit() async {
    super.onInit();
  }

  Future<void> setLanguageByLocale(BuildContext context) async {
    try {
      String languageCode = "en-US"; // กำหนดค่าเริ่มต้นเป็น "en-US"

      if (kIsWeb) {
        // ดึงค่าภาษาเริ่มต้นของเบราว์เซอร์
        final browserLanguage =
            html.window.navigator.language; // ตัวอย่าง: "th-TH" หรือ "en-US"

        // เช็คภาษาและตั้งค่าตามที่รองรับ
        if (browserLanguage.startsWith('th')) {
          languageCode = "th-TH"; // ภาษาไทย
        } else {
          languageCode = "en-US"; // ภาษาอังกฤษ
        }
      } else {
        // ดึง Locale ของแอปพลิเคชันบนแพลตฟอร์มอื่น
        final locale = Localizations.localeOf(context);
        if (locale.languageCode == 'th') {
          languageCode = "th-TH"; // ภาษาไทย
        } else {
          languageCode = "en-US"; // ภาษาอังกฤษ
        }
      }

      // ตั้งค่าภาษาสำหรับ TTS
      await flutterTts.setLanguage(languageCode);
    } catch (e) {
      print("Error setting language: $e");
    }
  }

  Future<void> speakAllTexts(List<dynamic> texts) async {
    for (String text in texts) {
      await speak(text);
      // รอให้ข้อความพูดเสร็จก่อนเริ่มข้อความถัดไป
      await Future.delayed(const Duration(seconds: 2));
    }
  }

  Future<void> speak(textToSpeak) async {
    await flutterTts.setLanguage("en-US");
    await flutterTts.setPitch(1.0); // ระดับเสียง (1.0 คือปกติ)
    await flutterTts.setSpeechRate(0.5); // ความเร็วในการพูด
    await flutterTts.speak(textToSpeak);
  }
}
