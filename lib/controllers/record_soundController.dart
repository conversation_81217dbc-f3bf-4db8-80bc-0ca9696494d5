import 'dart:convert';
import 'dart:io';
import 'dart:html' as html; // ใช้สำหรับ Web
import 'package:flutter/foundation.dart'; // สำหรับ kIsWeb
import 'dart:js'; // นำเข้า allowInterop
import 'package:http/http.dart' as http;
import 'package:get/get.dart';
import 'package:http_parser/http_parser.dart';
import 'package:mapp_ms24/controllers/utils/connect.dart';
import 'package:mapp_ms24/controllers/utils/url.dart';
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';
import 'package:microphone/microphone.dart';
import 'package:permission_handler/permission_handler.dart';

import '../models/userModel/usermodel.dart';
import 'googleDriveController.dart';
import 'internal/Profile/profileController.dart';
import 'internal/Tacontroller/Tacontroller.dart';

class RecordController extends GetxController {
  ProfileController profileCrt = Get.find<ProfileController>();
  Tacontroller taCrt = Get.find<Tacontroller>();
  var isRecording = false.obs;
  var filePath = ''.obs;
  var file_link = ''.obs;
  var isMicrophoneReady = false.obs;
  var isCameraReady = false.obs;
  html.MediaRecorder? _webRecorder; // To store the MediaRecorder for Web
  MicrophoneRecorder _mobileRecorder = MicrophoneRecorder();
  html.MediaStream? _mediaStream;
  var isMuted = false.obs;
  var isLoading = false.obs;
  final List<String> topics = [
    'เสียงจากลูกค้า',
    'ข้อร้องเรียน',
    'feedback จากลูกค้า',
    'การอัดเสียงปกติ',
  ];

  String? selectedTopic;

  Future<void> initializeRecorder() async {
    if (kIsWeb) {
      await _requestMediaPermissionForWeb();
    } else {
      await _requestMediaPermissionForMobile();
    }
  }

// **ขอ permission สำหรับ Web**
  Future<void> _requestMediaPermissionForWeb() async {
    try {
      await Permission.microphone.status;
      final mediaDevices = html.window.navigator.mediaDevices;
      if (mediaDevices != null) {
        // ขอ permission ทั้ง microphone และ camera
        await mediaDevices.getUserMedia({'audio': true, 'video': true});
      } else {
        print("Media devices not supported in this browser.");
      }
    } catch (e) {
      print("Error requesting media permissions on Web: $e");
    }
  }

// **ขอ permission สำหรับ Mobile**
  Future<void> _requestMediaPermissionForMobile() async {
    try {
      // ตรวจสอบ permission ของ microphone
      var microphoneStatus = await Permission.microphone.status;
      if (microphoneStatus.isDenied || microphoneStatus.isPermanentlyDenied) {
        final requestMicrophone = await Permission.microphone.request();
        if (!requestMicrophone.isGranted) {
          _handlePermissionDenied("Microphone");
          return;
        }
      }

      // ตรวจสอบ permission ของ camera
      var cameraStatus = await Permission.camera.status;
      if (cameraStatus.isDenied || cameraStatus.isPermanentlyDenied) {
        final requestCamera = await Permission.camera.request();
        if (!requestCamera.isGranted) {
          _handlePermissionDenied("Camera");
          return;
        }
      }
    } catch (e) {
      print("Error requesting media permissions on Mobile: $e");
      Get.snackbar(
        "Permission Error",
        "An error occurred while requesting microphone and camera permissions.",
      );
    }
  }

// **จัดการกรณีที่ permission ถูกปฏิเสธ**
  void _handlePermissionDenied(String permission) {
    Get.snackbar(
      "$permission Permission Denied",
      "Please enable $permission access in app settings.",
    );
  }

  void clearSelectedTopic() {
    selectedTopic = ''; // รีเซ็ตค่า
  }

  Future<void> openInUrl() async {}
  // ฟังก์ชันสำหรับเริ่มการบันทึก
  Future<void> startRecording() async {
    try {
      await startRecordingForWeb();
      if (kIsWeb) {
      } else {
        await startRecordingForMobile();
      }
    } catch (e) {
      print("Error starting recording: $e");
      Get.snackbar("เกิดข้อผิดพลาด", "ไม่สามารถเริ่มการบันทึกเสียงได้");
    }
  }

  Future<void> startRecordingForWeb() async {
    final mediaDevices = html.window.navigator.mediaDevices;

    if (mediaDevices == null || mediaDevices.getUserMedia == null) {
      Get.snackbar("Error", "Your browser does not support audio recording.");
      return;
    }

    try {
      final stream = await mediaDevices.getUserMedia({'audio': true});

      if (stream != null) {
        _mediaStream = stream;
        _webRecorder = html.MediaRecorder(stream);

        _webRecorder?.addEventListener('dataavailable', allowInterop((event) {
          if (event is html.BlobEvent) {
            final blob = event.data;
            final url = html.Url.createObjectUrlFromBlob(blob!);
          } else {
            print('Unexpected event type: ${event.runtimeType}');
          }
        }));

        _webRecorder?.start();
        isRecording.value = true;
      } else {
        print("Failed to get user media stream.");
      }
    } catch (e) {
      print("Error accessing user media: $e");
      Get.snackbar("Error", "Unable to access microphone: $e");
    }
  }

  Future<void> startRecordingForMobile() async {
    final directory = await getApplicationDocumentsDirectory();
    filePath.value =
        '${directory.path}/record_${DateTime.now().millisecondsSinceEpoch}.aac';

    await _mobileRecorder.start();
    isRecording.value = true;
  }

// ฟังก์ชันสำหรับหยุดการบันทึก
  Future<void> stopRecording(filename, customer_id, topic) async {
    try {
      if (kIsWeb) {
        await stopRecordingForWeb(
            filename: filename, customerId: customer_id, topic: topic);
      } else {
        await stopRecordingForMobile();
      }
    } catch (e) {
      print("Error stopping recording: $e");
      Get.snackbar("เกิดข้อผิดพลาด", "ไม่สามารถหยุดการบันทึกเสียงได้");
    }
  }

  Future<void> stopRecordingForWeb({
    required String customerId,
    required String topic,
    required filename,
  }) async {
    isLoading.value = true;

    if (_webRecorder != null && isRecording.value) {
      // เพิ่ม Listener สำหรับ Event 'dataavailable'
      _webRecorder?.addEventListener('dataavailable',
          allowInterop((event) async {
        if (event is html.BlobEvent) {
          final blob = event.data;

          if (blob != null) {
            try {
              // อ่าน Blob เป็น Uint8List
              final reader = html.FileReader();
              reader.readAsArrayBuffer(blob);

              reader.onLoadEnd.listen((_) async {
                try {
                  final Uint8List data = reader.result as Uint8List;

                  // แปลงเป็น Base64
                  final base64Data = base64Encode(data);

                  // ตั้งชื่อไฟล์
                  final fileName = filename;

                  // อัปโหลดไปยัง S3
                  final response =
                      await uploadToS3WithAPI(base64Data, fileName);

                  if (response != null &&
                      response['result']?['url']?['Location'] != null) {
                    final location = response['result']['url']['Location'];
                    file_link.value = location.toString();

                    // เตรียมข้อมูลสำหรับ RDS
                    final datatoRDS = {
                      "date": DateTime.now().toIso8601String(),
                      "user": profileCrt.responseMember?.id,
                      "rec_to": customerId,
                      "topic": fileName,
                      "file_name": topic,
                      "company_management":
                          profileCrt.responseMember?.company_management ??
                              "unknown_company",
                      "gps_location":
                          "${taCrt.startLocation?.latitude ?? 0.0}, ${taCrt.startLocation?.longitude ?? 0.0}",
                      "link": file_link.value,
                    };

                    Get.snackbar("สำเร็จ", "การอัปโหลดเสร็จสมบูรณ์");
                  } else {
                    throw Exception(
                        "Error: S3 upload failed or location is null");
                  }
                } catch (e) {
                  Get.snackbar("เกิดข้อผิดพลาด", e.toString());
                } finally {
                  isLoading.value = false; // ยกเลิกสถานะการโหลด
                }
              });
            } catch (e) {
              Get.snackbar("ERROR", e.toString());
              isLoading.value = false;
            }
          } else {
            isLoading.value = false;
          }
        } else {
          isLoading.value = false;
        }
      }));

      _webRecorder?.stop();
      isRecording.value = false;
    } else {
      print("Recorder is not active or already stopped for Web.");
      isLoading.value = false;
    }
  }

  void downloadFile(Uint8List fileData, String fileName) {
    final blob = html.Blob([fileData]);
    final url = html.Url.createObjectUrlFromBlob(blob);

    html.Url.revokeObjectUrl(url);
  }

  Future<void> stopRecordingForMobile() async {
    if (_mobileRecorder != null && isRecording.value) {
      await _mobileRecorder.stop();
      isRecording.value = false;

      if (filePath.value.isNotEmpty) {
        try {
          final file = File(filePath.value);
          final Uint8List bytes = await file.readAsBytes();

          final fileName =
              "record_${DateTime.now().millisecondsSinceEpoch}.mp3";

          final directory = await getApplicationDocumentsDirectory();
          final exampleFile = File("${directory.path}/$fileName");
          await exampleFile.writeAsBytes(bytes);

          Get.snackbar("สำเร็จ",
              "ไฟล์บันทึกเสียงถูกบันทึกในเครื่องแล้ว: ${exampleFile.path}");
        } catch (e) {
          Get.snackbar("เกิดข้อผิดพลาด", "ไม่สามารถอ่านหรืออัปโหลดไฟล์ได้");
        }
      } else {
        print("File path is empty, cannot upload recording.");
      }
    } else {
      print("Recorder is not started for Mobile. Cannot stop.");
    }
  }

  Future<List<Map<String, dynamic>>> GetRECHistory() async {
    try {
      final data = {
        "user": profileCrt
            .responseMember?.id, // ใช้ค่า default หาก user id เป็น null
      };

      // เรียกใช้ API
      final response =
          await AppApi.callAPIjwt("POST", AppUrl.GetRecordHisV1, data);

      if (response is Map<String, dynamic>) {
        // หาก response เป็น Map
        if (response["status"] == 200) {
          final result = response["result"];
          if (result != null && result["data"] != null) {
            return List<Map<String, dynamic>>.from(result["data"]);
          } else {
            return [];
          }
        } else {
          return [];
        }
      } else {
        return [];
      }
    } catch (e) {
      return []; // คืนค่า empty list กรณีเกิดข้อผิดพลาด
    }
  }

  // ฟังก์ชันสำหรับ Mute เสียง
  Future<void> muteForWeb() async {
    if (_mediaStream != null) {
      final audioTracks =
          _mediaStream?.getAudioTracks(); // ดึง audio tracks จาก MediaStream
      if (audioTracks != null && audioTracks.isNotEmpty) {
        final audioTrack = audioTracks.first; // ใช้ audio track แรก (ไมโครโฟน)

        // ปิดเสียง (Mute)
        audioTrack.enabled = false;
        isMuted.value = true; // อัพเดตสถานะ Mute
      } else {
        print("No audio tracks available in MediaStream.");
      }
    } else {
      print("MediaStream is not initialized.");
    }
  }

// ฟังก์ชันสำหรับ Unmute เสียง
  Future<void> unmuteForWeb() async {
    if (_mediaStream != null) {
      final audioTracks =
          _mediaStream?.getAudioTracks(); // ดึง audio tracks จาก MediaStream
      if (audioTracks != null && audioTracks.isNotEmpty) {
        final audioTrack = audioTracks.first; // ใช้ audio track แรก (ไมโครโฟน)

        // เปิดเสียง (Unmute)
        audioTrack.enabled = true;
        isMuted.value = false; // อัพเดตสถานะ Unmute
      } else {
        print("No audio tracks available in MediaStream.");
      }
    } else {
      print("MediaStream is not initialized.");
    }
  }

  Future<dynamic> uploadToS3WithAPI(String base64Data, String fileName) async {
    isLoading.value = true; // เริ่มการโหลด
    try {
      final data = {
        "name": "center_voice_recording",
        "folder": "center_voice_recording/CPDG/6403020",
        "file": fileName,
        "voice": base64Data,
        "type": "audio/mp3",
      };

      final response = await http.post(
        Uri.parse(AppUrl.uploadS3_voice),
        body: json.encode(data),
        headers: {"Content-Type": "application/json"},
      );

      if (response.statusCode == 200) {
        try {
          final resBody = json.decode(response.body);

          if (resBody is Map<String, dynamic>) {
            if (resBody["statusCode"] == 200) {
              return resBody['result']["Location"].toString();
            } else {
              return {"error": resBody["message"] ?? "Unknown error"};
            }
          }
        } catch (e) {
          return {"error": "Invalid JSON response"};
        }
      }

      return {"error": "Unexpected status code: ${response.statusCode}"};
    } catch (e) {
      return {"error": e.toString()};
    } finally {
      isLoading.value = false; // สิ้นสุดการโหลด
    }
  }

  Future<void> clearAndRequestPermissions({
    required RxBool isMicrophoneReady,
    required RxBool isCameraReady,
  }) async {
    final mediaDevices = html.window.navigator.mediaDevices;

    if (mediaDevices != null) {
      try {
        final List<Future<html.MediaStream?>> futures = [
          mediaDevices.getUserMedia({'audio': true}),
          mediaDevices.getUserMedia({'video': true}),
        ];

        final streams = await Future.wait(
          futures.whereType<Future<html.MediaStream>>(),
        );

        // หยุด media stream ที่ยังค้างอยู่
        for (final stream in streams) {
          stream.getTracks().forEach((track) => track.stop());
        }

        isMicrophoneReady.value =
            await mediaDevices.getUserMedia({'audio': true}) != null;
        isCameraReady.value =
            await mediaDevices.getUserMedia({'video': true}) != null;
      } catch (e) {
        isMicrophoneReady.value = false;
        isCameraReady.value = false;
      }
    } else {
      // สำหรับ Mobile
      try {
        // ขอ permission ไมโครโฟน
        final microphoneStatus = await Permission.microphone.request();
        isMicrophoneReady.value = microphoneStatus.isGranted;

        // ขอ permission กล้อง
        final cameraStatus = await Permission.camera.request();
        isCameraReady.value = cameraStatus.isGranted;
      } catch (e) {
        isMicrophoneReady.value = false;
        isCameraReady.value = false;
      }
    }
  }

  Future<void> checkPermissions({
    required RxBool isMicrophoneReady,
    required RxBool isCameraReady,
  }) async {
    final mediaDevices = html.window.navigator.mediaDevices;

    try {
      await clearAndRequestPermissions(
        isMicrophoneReady: isMicrophoneReady,
        isCameraReady: isCameraReady,
      );

      final micStream = await mediaDevices?.getUserMedia({'audio': true});
      isMicrophoneReady.value = micStream != null;
      micStream?.getTracks().forEach((track) => track.stop());

      // ตรวจสอบการอนุญาตกล้อง
      final cameraStream = await mediaDevices?.getUserMedia({'video': true});
      isCameraReady.value = cameraStream != null;
      cameraStream?.getTracks().forEach((track) => track.stop());
    } catch (e) {
      isMicrophoneReady.value = false;
      isCameraReady.value = false;
    }
  }

  Future<void> requestPermissions() async {
    try {
      // ขอ permission สำหรับไมโครโฟน
      final microphoneStatus = await Permission.microphone.request();
      if (microphoneStatus.isGranted) {
        isMicrophoneReady.value = true;
      } else {
        isMicrophoneReady.value = false;
      }

      // ขอ permission สำหรับกล้อง
      final cameraStatus = await Permission.camera.request();
      if (cameraStatus.isGranted) {
        isCameraReady.value = true;
      } else {
        isCameraReady.value = false;
      }
    } catch (e) {
      print("Error requesting permissions: $e");
    }
  }

  @override
  void onClose() {
    if (!kIsWeb) {
      _mobileRecorder.dispose();
    }
    super.onClose();
  }
}
