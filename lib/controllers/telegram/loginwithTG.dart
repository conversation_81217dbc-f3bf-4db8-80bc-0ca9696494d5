import 'package:flutter_telegram_login/flutter_telegram_login.dart';
import 'package:get/get.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

class LoginwithTG extends GetxController {
  final TelegramLogin telegramLogin = TelegramLogin(
      '<PHONE_NUMBER:String>', '<BOT_ID:String>', ' <BOT_DOMAIN:String>');
  Future<void> Loginwithtg() async {
    try {
      var data = await telegramLogin.getData();

      if (data) {
        print(telegramLogin.userData);
      }

      await telegramLogin.telegramLaunch();
    } catch (e) {
      await Sentry.captureException(e);
    }
  }
}
