import 'dart:convert';
import 'dart:math';

import 'package:get/get.dart';
import 'package:get/get_rx/get_rx.dart';
import 'package:mapp_ms24/controllers/utils/connect.dart';

import '../configs/firestore.dart';
import '../models/member_likepoint_model.dart';
import '../views/screens/service/service.dart';
import 'internal/Profile/profileController.dart';
import 'internal/securestorage_controller.dart';

class WebViewLikePointController extends GetxController {
  RxString phoneEncode = "".obs;
  RxString merchantID = "".obs;
  RxString urlLikePointAPI = "".obs;
  RxString urlMiniLikePoint = "".obs;
  RxString namePoint = "".obs;
  RxBool isOpen = false.obs;
  RxString firstName = "".obs;
  RxString lastName = "".obs;
  RxString balanceLikePoint = "0".obs;
  RxInt balanceLikePointAvailable = 0.obs;
  RxString balanceLikePointTHB = "0".obs;
  RxBool statusMigrate = false.obs;
  RxBool isChecked = false.obs;
  RxBool isUpgrading = false.obs;
  RxBool isSuccess = false.obs;
  RxBool useOnlyLikePoint = false.obs;
  RxBool readyForUse = false.obs;
  RxString pocketID = "".obs;
  RxString exchangeRate = "0".obs;

  // FOR MIGRATE
  RxString amount = "0".obs;
  RxString address = "".obs;
  RxString displayName = "".obs;
  RxString likewalletUID = "".obs;
  RxString memberID = "".obs;

  // FOR MIGRATE
  final SecureStorage secureStorage = SecureStorage();
  ProfileController profileCtl = Get.find<ProfileController>();
  RxMap notiPoint = {
    "title": "",
    "dec": "",
  }.obs;

  @override
  onInit() async {
    super.onInit();
    try {
      await getMerchantID();
      await getPocketBalance();
      await checkMigrate();
      getRateExchange();
    } catch (e) {
      print("Error in onInit: $e");
    }
  }

  getMerchantID() async {
    var res = await FireStore.getMerchantID();
    merchantID.value = res[1]['merchantID'];
    isOpen.value = res[1]['status'];
  }

  getRateExchange() async {
    var res = await FireStore.getMerchantID();
    exchangeRate.value = res[1]['valuePoint'];
  }

  Future<dynamic> checkMigrate() async {
    try {
      if (profileCtl.responseMember!.telNumber == null) {
        return;
      } else {
        String phoneNumber = profileCtl.responseMember!.telNumber.toString();
        String phoneWithCountryCode = addCountryCode(phoneNumber);
        profileCtl.responseMember!.telNumber = phoneWithCountryCode;
        update();
      }

      Map requestBody = {
        'phone': profileCtl.responseMember!.telNumber,
      };
      final response = await AppApi.postLikePoint(
          '${urlLikePointAPI.value}/member/info-likewallet', requestBody);

      if (response['data'].length > 0) {
        var migratable = response['data']['migratable'];
        var detail = response['data']['detail'];

        if (migratable == true && detail == 'พบข้อมูลผู้ใช้งาน') {
          Map requestCheckMigrate = {'uid': response['data']['data']['uid']};

          final responseMigrate = await AppApi.postLikePoint(
              '${urlLikePointAPI.value}/member/check-migrate',
              requestCheckMigrate);
          var migrateStatus = responseMigrate['data']['status'];

          if (migrateStatus == "already migrated") {
            useOnlyLikePoint.value = true;
          } else {
            statusMigrate.value = true;
            address.value = response['data']['data']['address'];
            displayName.value = response['data']['data']['displayName'];
            likewalletUID.value = response['data']['data']['uid'];
          }
          readyForUse.value = true;
          update();
        } else {
          useOnlyLikePoint.value = true;
          readyForUse.value = true;
          update();
        }
      }
    } catch (e) {
      print('error Get Phone Encode : $e');
    }
  }

  Future<bool> upgradeLikePoint() async {
    try {
      isUpgrading.value = true;
      update();
      Map requestBodyLikeWallet = {
        'phone': profileCtl.responseMember!.telNumber,
      };

      final responseBalanceLikeWallet = await AppApi.postLikePoint(
          '${urlLikePointAPI.value}/member/balance-likewallet',
          requestBodyLikeWallet);
      amount.value =
          responseBalanceLikeWallet['data']['totalBalance'].toString();
      Map requestBody = {
        'phone': profileCtl.responseMember!.telNumber,
        'merchantID': merchantID.value,
        'amount': amount.value,
        'address': address.value,
        'displayName': displayName.value,
        'likewalletUID': likewalletUID.value,
        'memberID': memberID.value,
      };

      final response = await AppApi.postLikePoint(
          '${urlLikePointAPI.value}/mint-point/mint-point-member-migrate',
          requestBody);
      if (response['data']['status'] == "Success") {
        isSuccess.value = true;
        useOnlyLikePoint.value = true;
        await getPocketBalance();
        update();
        return true;
      } else {
        return false;
      }
    } catch (e) {
      print('error Migrate Like Point : $e');
      return false;
    }
  }

  String addCountryCode(String phoneNumber) {
    if (phoneNumber.startsWith('0')) {
      // Remove the leading zero
      phoneNumber = phoneNumber.substring(1);
    }
    // Add the +66 country code
    return '+66' + phoneNumber;
  }

  Future<dynamic> getPocketBalance() async {
    // try {

    if (profileCtl.responseMember!.telNumber == null) {
      return;
    } else {
      String phoneNumber = profileCtl.responseMember!.telNumber.toString();
      String phoneWithCountryCode = addCountryCode(phoneNumber);
      profileCtl.responseMember!.telNumber = phoneWithCountryCode;
      update();
    }
    var infoLikePoint = await FireStore.getInfoLikePoint();
    String jsonString = jsonEncode(infoLikePoint);
    print("InfoLikePoint JSON: $jsonString");
    merchantID.value = infoLikePoint['merchantID'];
    urlLikePointAPI.value = infoLikePoint['urlAPI'];
    urlMiniLikePoint.value = infoLikePoint['url'];
    // urlMiniLikePoint.value = 'mini-likepoint.web.app';
    namePoint.value = infoLikePoint['namePoint'];
    isOpen.value = infoLikePoint['status'];
    update();

    Map requestBody = {
      'phone': profileCtl.responseMember!.telNumber,
      'merchantID': merchantID.value,
    };

    final responseWallet = await AppApi.postLikePoint(
        '${urlLikePointAPI.value}/wallet/findWalletByPhone', requestBody);

    if (responseWallet['data'][0]['memberInfo'] == null) {
      Map<String, dynamic> requestBody = {
        'phone': profileCtl.responseMember!.telNumber,
        'firstName':
            (profileCtl.responseMember!.name_th?.toString()?.trim()?.isEmpty ??
                    true)
                ? "-"
                : profileCtl.responseMember!.name_th.toString().trim(),
        'lastName': (profileCtl.responseMember!.surname_th?.isEmpty ?? true)
            ? "-"
            : profileCtl.responseMember!.surname_th,
        'merchantID': merchantID.value,
        'pkg_id_member': profileCtl.responseMember!.id,
        'pkg_member_status': true
      };

      final response = await AppApi.postLikePoint(
          '${urlLikePointAPI.value}/member/regis-partners', requestBody);

      if (response['data'].length > 0) {
        var memberInfo = ResponseMemberLikePoint.fromJson(response['data'][0]);
        memberID.value = memberInfo.id!;
        Map dataCreatePocket = {
          "memberID": memberID.value,
          "merchantID": merchantID.value,
        };

        await AppApi.postLikePoint(
          "${urlLikePointAPI.value}/pocket/create-pocket-merchant",
          dataCreatePocket,
        );
        update();
      } else {
        if (responseWallet['data'][0]['walletInfo'] == null) {
          Map dataCreatePocket = {
            "memberID": responseWallet['data'][0]['memberInfo']['id'],
            "merchantID": merchantID.value,
          };

          await AppApi.postLikePoint(
            "${urlLikePointAPI.value}/pocket/create-pocket-merchant",
            dataCreatePocket,
          );

          balanceLikePoint.value = AppService.numberFormatNon0(0);
          balanceLikePointAvailable.value =
              int.parse(infoLikePoint['valuePoint']);
          balanceLikePointTHB.value = AppService.numberFormat(
              (0) * double.parse(infoLikePoint['valuePoint'].toString()));
          memberID.value = responseWallet['data'][0]['memberInfo']['id'];
          pocketID.value =
              responseWallet['data'][0]['walletInfo']['pocket'][0]['id'];
        } else {
          final pocketBalance = responseWallet['data'][0]['walletInfo']
              ['pocket'][0]['pocketBalance'];
          balanceLikePoint.value = AppService.numberFormatNon0(pocketBalance);
          balanceLikePointAvailable.value =
              int.parse(infoLikePoint['valuePoint']);
          balanceLikePointTHB.value = AppService.numberFormat(
              int.parse(pocketBalance.toString()) *
                  double.parse(infoLikePoint['valuePoint'].toString()));
          memberID.value = responseWallet['data'][0]['memberInfo']['id'];
          pocketID.value =
              responseWallet['data'][0]['walletInfo']['pocket'][0]['id'];

          print(pocketBalance);
          update();
        }
      }
    } else {
      if (responseWallet['data'][0]['walletInfo'] == null) {
        Map dataCreatePocket = {
          "memberID": responseWallet['data'][0]['memberInfo']['id'],
          "merchantID": merchantID.value,
        };

        await AppApi.postLikePoint(
          "${urlLikePointAPI.value}/pocket/create-pocket-merchant",
          dataCreatePocket,
        );

        balanceLikePoint.value = AppService.numberFormatNon0(0);
        balanceLikePointTHB.value = AppService.numberFormat(
            (0) * double.parse(infoLikePoint['valuePoint'].toString()));
        balanceLikePointAvailable.value =
            int.parse(infoLikePoint['valuePoint']);
        memberID.value = responseWallet['data'][0]['memberInfo']['id'];
        pocketID.value =
            responseWallet['data'][0]['walletInfo']['pocket'][0]['id'];
      } else {
        final pocketBalance = responseWallet['data'][0]['walletInfo']['pocket']
            [0]['pocketBalance'];
        balanceLikePoint.value = AppService.numberFormatNon0(pocketBalance);
        balanceLikePointAvailable.value = int.parse(pocketBalance.toString());
        balanceLikePointTHB.value = AppService.numberFormat(
            int.parse(pocketBalance.toString()) *
                double.parse(infoLikePoint['valuePoint'].toString()));
        memberID.value = responseWallet['data'][0]['memberInfo']['id'];
        pocketID.value =
            responseWallet['data'][0]['walletInfo']['pocket'][0]['id'];

        update();
      }
    }
    // } catch (e) {
    //   print('error Get Pocket Balance : $e');
    // }
  }

  Future<dynamic> payPOI(String activityID) async {
    try {
      Map requestBody = {
        'activityID': activityID,
        'merchantID': merchantID.value,
        "phone": profileCtl.responseMember!.telNumber,
        "firstName": profileCtl.responseMember!.name_th,
        "lastName": profileCtl.responseMember!.surname_th,
      };

      final response = await AppApi.postLikePoint(
          '${urlLikePointAPI.value}/transactions-activity/pay-poi-in-app',
          requestBody);

      if (response["status"] == 500) {
        return false;
      } else if (response['data'].length > 0) {
        await getPocketBalance();

        notiPoint.value['title'] =
            'คุณได้รับ $namePoint จากกิจกรรม ${response['data'][0]['activityName']}';
        notiPoint.value['dec'] =
            'จำนวน ${response['data'][0]['activityAmount']} $namePoint';
        update();
        return true;
      } else {
        return false;
      }
    } catch (e) {
      print('error Pay POI : $e');
      return false;
    }
  }

  Future checkAccept(value) async {
    try {
      isChecked.value = value;
      update();
    } catch (e) {
      print('error Pay POI : $e');
    }
  }
}
