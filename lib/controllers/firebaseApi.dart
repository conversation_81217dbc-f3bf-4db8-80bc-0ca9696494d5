import 'dart:io';
import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

class FirebaseController extends GetxController {
  FirebaseController();
  static FirebaseController get to => Get.find();

  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  @override
  void onInit() {
    super.onInit();
    init();

    FirebaseMessaging.instance.onTokenRefresh.listen((newToken) {
      updateTokenToBackend(newToken);
    });
  }

  Future<void> init() async {
    await _setupNotificationChannel();
    await _requestPermission();
    await _getToken();
    _listenToForegroundMessages();
    FirebaseMessaging.onBackgroundMessage(_handlerBackground);
  }

  Future<void> _setupNotificationChannel() async {
    const AndroidInitializationSettings androidInitSettings =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    final InitializationSettings initSettings =
        InitializationSettings(android: androidInitSettings);
    await flutterLocalNotificationsPlugin.initialize(initSettings);

    const AndroidNotificationChannel channel = AndroidNotificationChannel(
      'general_channel',
      'General Notifications',
      description: 'Notifications for all messages',
      importance: Importance.high,
    );

    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(channel);
  }

  Future<void> _requestPermission() async {
    try {
      final settings = await FirebaseMessaging.instance.requestPermission(
        alert: true,
        sound: true,
        badge: true,
      );
    } catch (e) {
      print("ℹ️ Notification permission error: ${e}");
    }
  }

  Future<void> _getToken() async {
    final fcToken = await FirebaseMessaging.instance.getToken(
        vapidKey: kIsWeb
            ? "BEueDzySHtc-OT1bABSKo-3RPL5ZGVV0J-9Ou4TIYDxK4Akm6dfoCqLE9yzSrsCbPpt6DnJiAdxOwutL7jAFxwY"
            : null);

    if (fcToken != null) {
      if (!kIsWeb) {
        await FirebaseMessaging.instance.subscribeToTopic('all');
      } else {
        print("🌐 Web client – skip topic subscription.");
      }
    }
  }

  void _listenToForegroundMessages() {
    FirebaseMessaging.onMessage.listen((RemoteMessage msg) {
      _showNotification(msg.notification?.title, msg.notification?.body);
    });
  }

  Future<void> _showNotification(String? title, String? body) async {
    if (kIsWeb) {
      // ✅ Web: ใช้ showDialog แสดง popup ด้านบน
      Get.snackbar(
        title ?? '📩 แจ้งเตือน',
        body ?? 'ไม่มีข้อความ',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.amber.shade100,
        colorText: Colors.black87,
        margin: const EdgeInsets.all(12),
        borderRadius: 8,
        duration: const Duration(seconds: 4),
        icon: const Icon(Icons.notifications, color: Colors.black),
      );
    } else {
      // ✅ Mobile: ใช้ awesome_dialog หรือ flutter_local_notifications
      AwesomeDialog(
        context: Get.context!,
        dialogType: DialogType.info,
        animType: AnimType.topSlide,
        title: title ?? '📩 แจ้งเตือน',
        desc: body ?? 'ไม่มีข้อความ',
        btnOkOnPress: () {},
      ).show();
    }
  }

  Future<void> updateTokenToBackend(String token) async {
    final userId = GetStorage().read('user_id') ?? '';

    if (userId.isEmpty) return;
  }

  static Future<void> _handlerBackground(RemoteMessage message) async {
    print("🌙 Background message: ${message.notification?.title}");
  }
}
