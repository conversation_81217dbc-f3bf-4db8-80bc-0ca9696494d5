

import 'package:get/get.dart';

class esingController extends GetxController  {

//
// static IconData getShapeIcon(ShapeFactory? shapeFactory) {
// if (shapeFactory is LineFactory) return PhosphorIcons.lineSegment;
// if (shapeFactory is ArrowFactory) return PhosphorIcons.arrowUpRight;
// if (shapeFactory is DoubleArrowFactory)
// return PhosphorIcons.arrowsHorizontal;
// if (shapeFactory is RectangleFactory) return PhosphorIcons.rectangle;
// if (shapeFactory is OvalFactory) return PhosphorIcons.circle;
// return PhosphorIcons.polygon;
// }
//
// void undo() {
// controller.undo();
// }
//
// void redo() {
// controller.redo();
// }
//
// void toggleFreeStyleDraw() {
// controller.freeStyleMode = controller.freeStyleMode != FreeStyleMode.draw
// ? FreeStyleMode.draw
//     : FreeStyleMode.none;
// }
//
// void toggleFreeStyleErase() {
// controller.freeStyleMode = controller.freeStyleMode != FreeStyleMode.erase
// ? FreeStyleMode.erase
//     : FreeStyleMode.none;
// }
//
// void addText() {
// if (controller.freeStyleMode != FreeStyleMode.none)
// controller.freeStyleMode = FreeStyleMode.none;
// controller.addText();
// }
//
// void addSticker() async {
// await loadRDSSign();
// final imageLink = await showDialog<String>(
// context: context,
// builder: (context) => SelectStickerImageDialog(
// imagesLinks: imageLinks,
// ),
// );
// if (imageLink == null) return;
// controller.addImage(await NetworkImage(imageLink).image, Size(100, 100));
// }
//
// void setFreeStyleStrokeWidth(double value) {
// controller.freeStyleStrokeWidth = value;
// }
//
// void setFreeStyleColor(double hue) {
// controller.freeStyleColor = HSVColor.fromAHSV(1, hue, 1, 1).toColor();
// }
//
// void setTextFontSize(double size) {
// // Set state is just to update the current UI, the [FlutterPainter] UI updates without it
// setState(() {
// controller.textSettings = controller.textSettings.copyWith(
// textStyle: controller.textSettings.textStyle.copyWith(fontSize: size),
// );
// });
// }
//
// void setShapeFactoryPaint(Paint paint) {
// // Set state is just to update the current UI, the [FlutterPainter] UI updates without it
// setState(() {
// controller.shapePaint = paint;
// });
// }
//
// void setTextColor(double hue) {
// controller.textStyle = controller.textStyle.copyWith(
// color: HSVColor.fromAHSV(1, hue, 1, 1).toColor(),
// );
// }
//
// void selectShape(ShapeFactory? factory) {
// controller.shapeFactory = factory;
// }
//
// void renderAndDisplayImage() {
// if (backgroundImage == null) return;
//
// // showLoadingDialog();
//
// final backgroundImageSize = Size(
// backgroundImage!.width.toDouble(),
// backgroundImage!.height.toDouble(),
// );
//
// final imageFuture = controller
//     .renderImage(backgroundImageSize)
//     .then<Uint8List?>((ui.Image image) => image.pngBytes);
//
// // showDialog(
// //   context: context,
// //   builder: (context) => RenderedImageDialog(imageFuture: imageFuture),
// // );
//
// // String base64 = '';
// imageFuture.then((value) async {
// await _convertImageToPDF(value);
// await uploadtoS3(remotePath);
// if(linkPDFS3 != '') {
// await savePDFtoRDS(linkPDFS3);
// }
// });
// }
// String remotePath = "";
// Future<void> _convertImageToPDF(path) async {
// var compressImg = await FlutterImageCompress.compressWithList(path, quality: 100);
// Completer<File> completer = Completer();
// //Create the PDF document
// // PdfDocument document = PdfDocument();
// PdfDocument document = PdfDocument(inputBytes: File(widget.path.toString()).readAsBytesSync());
// document.pageSettings.margins.all = 0;
// //Add the page
// PdfPage page = document.pages[widget.currentPage];
// //Load the image.
// final PdfImage image =
// PdfBitmap(compressImg);
// //draw image to the first page
// page.graphics.drawImage(
// // image, Rect.fromLTWH(0, 0, backgroundImage!.width.toDouble(), backgroundImage!.height.toDouble()));
// image, Rect.fromLTWH(0, 0, 595, 842));
// //Save the docuemnt
// Future<List<int>> bytes = document.save();
// String dir = (await getApplicationDocumentsDirectory()).path;
// File file = File(
// "$dir/" + DateTime.now().millisecondsSinceEpoch.toString() + "_$nitroFileName");
// await file.writeAsBytes(await bytes);
// remotePath = file.path;
// completer.complete(file);
// }
//
// loadAgreeSign() async {
// SharedPreferences prefs = await SharedPreferences.getInstance();
// var id = await prefs.getString('id');
// String url =
// 'https://mhmo3nnbr5.execute-api.ap-southeast-1.amazonaws.com/latest';
// //    String url = 'https://086e808a.ngrok.io/agslearn/us-central1/ApiAppMS24';
// Map map = {"menu": "searchAgreeSign", "id": id.toString()};
//
// final response = await apiRequest(url, map);
// var jsonResponse = json.decode(response);
// if (jsonResponse["statusCode"].toString() == "200") {
// setState(() {
// if (jsonResponse["result"][0]["link_NDA_Signature"].toString() != "" &&
// jsonResponse["result"][0]["link_NDA_Signature"].toString() !=
// "null" &&
// jsonResponse["result"][0]["link_NDA_Signature"].toString() !=
// null) {
// imageLinks.add("https://mapp-app.s3.ap-southeast-1.amazonaws.com/MappMS/img_Signature_NDA/" +
// id.toString() +
// "_e_signature1.png");
// }
// });
// } else {
// print('No data!!');
// }
// }
//
// loadRDSSign() async {
// imageLinks.clear();
// SharedPreferences prefs = await SharedPreferences.getInstance();
// var id = await prefs.getString('id');
// String url =
// 'https://mhmo3nnbr5.execute-api.ap-southeast-1.amazonaws.com/latest';
// //    String url = 'https://086e808a.ngrok.io/agslearn/us-central1/ApiAppMS24';
// Map map = {"menu": "searchSignRDS", "id": id.toString()};
//
// final response = await apiRequest(url, map);
// var jsonResponse = json.decode(response);
// if (jsonResponse["statusCode"].toString() == "200") {
// setState(() {
// if (jsonResponse["result"].length > 0) {
// for(var data in jsonResponse["result"]) {
// imageLinks.add(data["url"]);
// }
// }
// });
// } else {
// print('No data!!');
// }
// }
//
// String linkPDFS3 = '';
// uploadtoS3(path) async {
// final bytes = File(path).readAsBytesSync();
// String base64Image = base64Encode(bytes);
// SharedPreferences prefs = await SharedPreferences.getInstance();
// var id = await prefs.getString('id');
// String url =
// 'https://oxphgjyvu2.execute-api.ap-southeast-1.amazonaws.com/latest/uploadPDFS3_Center';
// Map map = {
// "name": "MappMS",
// "folder": "MappMS/signPDF",
// "image": base64Image
// };
//
// final response = await apiRequest(url, map);
// // print(response);
// var jsonResponse = json.decode(response);
// if (jsonResponse["statusCode"].toString() == "200") {
// print("linkPDFS3 : ${jsonResponse["result"]["url"]["Location"]}");
// setState(() {
// linkPDFS3 = (jsonResponse["result"]["url"]["Location"].toString());
// });
// //      saveImageProfile(jsonResponse["result"]["url"]["Location"].toString());
// } else {
// print('upload fail!');
// Fluttertoast.showToast(
// msg: "upload fail!",
// toastLength: Toast.LENGTH_SHORT,
// gravity: ToastGravity.TOP,
// backgroundColor: Colors.red,
// textColor: Colors.black,
// fontSize: 16.0);
// }
// }
//
// savePDFtoRDS(linkPDF) async {
// SharedPreferences prefs = await SharedPreferences.getInstance();
// var id = await prefs.getString('id');
// String url =
// 'https://mhmo3nnbr5.execute-api.ap-southeast-1.amazonaws.com/latest';
// //    String url = 'https://086e808a.ngrok.io/agslearn/us-central1/ApiAppMS24';
// Map map = {"menu": "saveSignRDS", "id": id.toString(), "linkPDF": linkPDF.toString(), "dataPDF": widget.dataPDF, "system": "App_MS24"};
//
// final response = await apiRequest(url, map);
// var jsonResponse = json.decode(response);
// if (jsonResponse["statusCode"].toString() == "200") {
// Navigator.push(
// context,
// MaterialPageRoute(
// builder: (context) =>
// nitrosignPDFsuccess(path: remotePath),
// ),
// );
// } else {
// print('save fail!');
// Fluttertoast.showToast(
// msg: "save fail!",
// toastLength: Toast.LENGTH_SHORT,
// gravity: ToastGravity.TOP,
// backgroundColor: Colors.red,
// textColor: Colors.black,
// fontSize: 16.0);
// }
// }
//
// void removeSelectedDrawable() {
// final selectedDrawable = controller.selectedObjectDrawable;
// if (selectedDrawable != null) controller.removeDrawable(selectedDrawable);
// }
//
// void showAlertDialog(){
// alertDialog(
// context,
// "ui_titleAlert3".tr.toString(),
// "ui_detailAlert4".tr.toString(),
// "btn_okAlert".tr.toString());
// }
// void flipSelectedImageDrawable() {
//   final imageDrawable = controller.selectedObjectDrawable;
//   if (imageDrawable is! ImageDrawable) return;
//
//   controller.replaceDrawable(
//       imageDrawable, imageDrawable.copyWith(flipped: !imageDrawable.flipped));
// }
}
