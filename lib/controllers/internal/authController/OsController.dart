import 'dart:io';

import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';

class OsController extends GetxController {
  Future<void> openAppBasedOnOS(link, AndAppName, iosAppName) async {
    // ลิงก์ OneLink ที่จะทำการ Redirect ไปยัง Store หรือเปิดแอปโดยตรง
    Uri oneLinkUrl = Uri.parse(link);
    // Uri oneLinkUrl = Uri.parse('https://ms24pkg.page.link/openms24');

    // URL Scheme เฉพาะสำหรับ Android และ iOS
    // Uri androidUrl = Uri.parse('android-app://com.prachakij.ms24beta');
    Uri androidUrl = Uri.parse('android-app://$AndAppName');

    Uri iosUrl = Uri.parse('https://example.com/$iosAppName');

    // พยายามเปิด OneLink ก่อน
    if (await canLaunchUrl(oneLinkUrl)) {
      await launchUrl(oneLinkUrl, mode: LaunchMode.externalApplication);
    } else {
      // หากเปิด OneLink ไม่ได้ ให้เปิด URL Scheme ตามแพลตฟอร์ม
      Uri platformSpecificUrl = Platform.isAndroid ? androidUrl : iosUrl;

      if (await canLaunchUrl(platformSpecificUrl)) {
        await launchUrl(platformSpecificUrl,
            mode: LaunchMode.externalApplication);
      }
    }
  }
}
