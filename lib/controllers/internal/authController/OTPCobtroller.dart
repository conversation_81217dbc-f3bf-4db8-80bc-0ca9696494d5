import 'dart:convert';

import 'package:ags_authrest2/ags_authrest.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
// import 'package:nfc_manager/nfc_manager.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../views/screens/Widget/OTP.dart';
import '../../../views/screens/Widget/library.dart';

class OtpController extends GetxController {
  static String email = '';
  static String yubi_name = '';
  static String id_member = '';
  static String Grpc_Uri = "yubitg-nfh5zn25kq-as.a.run.app";
  static bool statusOTP = false;

  static checkOTP() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var id = prefs.getString('id');
    String url =
        'https://mhmo3nnbr5.execute-api.ap-southeast-1.amazonaws.com/latest';
    Map map = {"menu": "checkTWOFAOTPTG", "personID": id};
    final response = await apiRequest(url, map);
    var jsonResponse = json.decode(response);
    if (jsonResponse["statusCode"].toString() == "200" &&
        jsonResponse["result"][0]["ms_otp_tg"] != null) {
      statusOTP = true;
    } else {
      statusOTP = false;
    }
  }

  static String? otp;
  static String? mac;
  static sendTG_PKG(message) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var id = await prefs.getString('id');
    // var name = '${prefs.getString('fname')} ${prefs.getString('lname')}';
    List<dynamic> data = [
      {
        "emp": id,
        "fromdata": {"message": message}
      }
    ];

    AppHttps.postTG_PKG(AppHttps.sendTelegramPKG, data);
  }

  static getData() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    email = prefs.getString('email1')!;
    yubi_name = prefs.getString('userName')!;
    id_member = prefs.getString('id')!;
  }

  static Future getMAc() async {
    // var deviceInfo = DeviceInfoPlugin();
    // if (Platform.isIOS) {
    //   var iosDeviceInfo = await deviceInfo.iosInfo;
    //   return iosDeviceInfo.identifierForVendor; // unique ID on iOS
    // } else if (Platform.isAndroid) {
    //   var androidDeviceInfo = await deviceInfo.androidInfo;
    //   return androidDeviceInfo.androidId; // unique ID on Android
    // }
  }
  static Future Check_session() async {
    getData();
    try {
      var uniqueId = await getMAc();
      var auth = Ags_restauth();
      auth.SECERT_JWT = 'fpw!vbe.epm9nhf6YPN';
      auth.R_USER = 'yubikeyms24';

      var headers = {
        'Authorization':
            auth.genTokenEncryp(), // genTokenEncryp() or genToken()
        'Content-Type': 'application/json'
      };

      var bodyData = {
        "Appname": "MS24",
        "Email": email,
        "mac_address": uniqueId,
      };
      var body = json.encode(bodyData);
      var request = http.Request('POST',
          Uri.parse('https://agilesoftgroup.com/cf_yubitg_ms/chkSession'));
      request.body = json
          .encode(auth.encrypbody(body)); //encrypbody require genTokenEncryp()
      request.headers.addAll(headers);
      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        var dataRaw = await response.stream.bytesToString();
        var data = json.decode(dataRaw);
        if (data['status'] == 'success') {
          // _onSuccessAlertPressed(context, dataJson['message'], dataJson['status']);
          return true;
        } else {
          // Yubikey.onSuccessAlertPressed(context);
          // Yubikey.onErrorAlertPressed(context, dataJson['message']);
          return false;
        }
      } else {
        print(response.reasonPhrase);
      }
    } catch (e) {
      return false;
      // _onErrorAlertPressed(context, "server error");
    }
  }

  static Future varifyYubikey(context) async {
    String? initialLink;

    // initialLink = (await getInitialLink());
    try {
      var auth = Ags_restauth();
      auth.SECERT_JWT = 'fpw!vbe.epm9nhf6YPN';
      auth.R_USER = 'yubikeyms24';

      var headers = {
        'Authorization':
            auth.genTokenEncryp(), // genTokenEncryp() or genToken()
        'Content-Type': 'application/json'
      };

      var uniqueId = await getMAc();
      // var pathRoute = "verify";
      var bodyData = {
        "yubiId": otp?.substring(0, 12),
        "yubiAppname": "MS24",
        // "yubiEmail": email,
        "mac_address": uniqueId,
        "yubiOtp": otp,
      };
      var body = json.encode(bodyData);
      var request = http.Request(
          'POST', Uri.parse('https://agilesoftgroup.com/cf_yubitg_ms/verify'));
      request.body = json.encode(auth.encrypbody(body));
      request.headers.addAll(headers);
      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        var dataRaw = await response.stream.bytesToString();
        var data = json.decode(dataRaw);
        if (data['status'] == 'success') {
          Yubikey.onSuccessAlertPressed(
              context, "ui_scan_yubikey".tr, "ui_success_yubikey_verify".tr);
          if (initialLink == null ||
              initialLink == "" ||
              initialLink == "null") {
            Get.toNamed('home');
          } else {}
        } else {
          Yubikey.onErrorAlertPressed(context, data['message']);
        }
      } else {
        print(response.reasonPhrase);
      }
    } catch (e) {
      Yubikey.onErrorAlertPressed(context, "server error");
    }
  }

  static Future registerYubikey(context) async {
    try {
      var auth = Ags_restauth();
      auth.SECERT_JWT = 'fpw!vbe.epm9nhf6YPN';
      auth.R_USER = 'yubikeyms24';

      var headers = {
        'Authorization':
            auth.genTokenEncryp(), // genTokenEncryp() or genToken()
        'Content-Type': 'application/json'
      };

      var bodyData = {
        "yubiAppname": "MS24",
        // "yubiEmail": email,
        "yubiOtp": otp
      };
      var body = json.encode(bodyData);
      var request = http.Request('POST',
          Uri.parse('https://agilesoftgroup.com/cf_yubitg_ms/register'));
      request.body = json
          .encode(auth.encrypbody(body)); //encrypbody require genTokenEncryp()
      request.headers.addAll(headers);
      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        var dataRaw = await response.stream.bytesToString();
        var data = json.decode(dataRaw);
        if (data['status'] == 'success') {
          sendTG_PKG(
              "คุณได้ทำการลงทะเบียน YUBIKEY สำหรับ 2FA MS เรียบร้อยแล้ว");
          if (!statusOTP) {
            Yubikey.onSuccessAlertPressedWithRegisOTP(context);
          } else {
            Yubikey.onSuccessAlertPressed(
                context, "ui_register_twoFA".tr, "ui_success_yubikey".tr);
          }
        } else {
          Yubikey.onErrorAlertPressed(context, data['message']);
        }
      } else {
        print(response.reasonPhrase);
      }
    } catch (e) {
      Yubikey.onErrorAlertPressed(context, "server error");
    }
  }

  static Future<void> RegisTgotpManual(emp_id) async {
    try {
      var auth = Ags_restauth();
      auth.SECERT_JWT = 'fpw!vbe.epm9nhf6YPN';
      auth.R_USER = 'yubikeyms24';

      var headers = {
        'Authorization':
            auth.genTokenEncryp(), // genTokenEncryp() or genToken()
        'Content-Type': 'application/json'
      };

      //var pathRoute = "registerTgotp";
      var bodyData = {
        "emp_id": emp_id.toString(),
      };

      var body = json.encode(bodyData);
      var request = http.Request('POST',
          Uri.parse('https://agilesoftgroup.com/cf_yubitg_ms/registerTgotp'));
      request.body = json
          .encode(auth.encrypbody(body)); //encrypbody require genTokenEncryp()
      request.headers.addAll(headers);
      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        var dataRaw = await response.stream.bytesToString();
        var data = json.decode(dataRaw);
        if (data['status'] == 'success') {
          sendTG_PKG(
              "คุณได้ทำการลงทะเบียน OTP Telegram สำหรับ 2FA MS เรียบร้อยแล้ว");
        }
        return;
      } else {
        print(response.reasonPhrase);
      }
    } catch (e) {
      return;
    }
  }

  static Future<void> RegisTgotp(context, emp_id) async {
    try {
      var auth = Ags_restauth();
      auth.SECERT_JWT = 'fpw!vbe.epm9nhf6YPN';
      auth.R_USER = 'yubikeyms24';

      var headers = {
        'Authorization':
            auth.genTokenEncryp(), // genTokenEncryp() or genToken()
        'Content-Type': 'application/json'
      };

      var bodyData = {
        "emp_id": emp_id.toString(),
      };
      var body = json.encode(bodyData);
      var request = http.Request('POST',
          Uri.parse('https://agilesoftgroup.com/cf_yubitg_ms/registerTgotp'));
      request.body = json
          .encode(auth.encrypbody(body)); //encrypbody require genTokenEncryp()
      request.headers.addAll(headers);
      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        var dataRaw = await response.stream.bytesToString();
        var data = json.decode(dataRaw);
        // Navigator.of(context, rootNavigator: true).pop();
        if (data['status'] == 'success') {
          sendTG_PKG(
              "คุณได้ทำการลงทะเบียน OTP Telegram สำหรับ 2FA MS เรียบร้อยแล้ว");
          Yubikey.onSuccessAlertPressed(
              context, "btn_register_OTP".tr, "ui_success_otp".tr);
        } else {
          Yubikey.onErrorAlertPressed(context, data['message']);
        }
      } else {
        print(response.reasonPhrase);
      }
    } catch (e) {
      Yubikey.onErrorAlertPressed(context, "server error");
    }
  }

  static Future SendTgotp(context, emp_id) async {
    Yubikey.onSendingOTP(context);
    try {
      var auth = Ags_restauth();
      auth.SECERT_JWT = 'fpw!vbe.epm9nhf6YPN';
      auth.R_USER = 'yubikeyms24';

      var headers = {
        'Authorization':
            auth.genTokenEncryp(), // genTokenEncryp() or genToken()
        'Content-Type': 'application/json'
      };

      var bodyData = {
        "emp_id": emp_id,
      };

      var body = json.encode(bodyData);
      var request = http.Request('POST',
          Uri.parse('https://agilesoftgroup.com/cf_yubitg_ms/sendTgotp'));
      request.body = json
          .encode(auth.encrypbody(body)); //encrypbody require genTokenEncryp()
      request.headers.addAll(headers);
      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        var dataRaw = await response.stream.bytesToString();
        var data = json.decode(dataRaw);
        if (data['status'] == 'success') {
          Yubikey.onVerifyOTP(context);
        } else {
          Yubikey.onErrorAlertPressed(context, data['message']);
        }
      } else {
        print(response.reasonPhrase);
      }
    } catch (e) {
      Yubikey.onErrorAlertPressed(context, "server error");
    }
  }

  static Future<void> VerifyTgotp(context, emp_id, otp) async {
    String? initialLink;

    try {
      var auth = Ags_restauth();
      auth.SECERT_JWT = 'fpw!vbe.epm9nhf6YPN';
      auth.R_USER = 'yubikeyms24';

      var headers = {
        'Authorization': auth.genTokenEncryp(),
        'Content-Type': 'application/json'
      };

      var uniqueId = await getMAc();
      var bodyData = {
        "emp_id": emp_id,
        "otp": otp,
        "mac_address": uniqueId,
      };

      var body = json.encode(bodyData);
      var request = http.Request('POST',
          Uri.parse('https://agilesoftgroup.com/cf_yubitg_ms/verifyTgotp'));
      request.body = json.encode(auth.encrypbody(body));
      request.headers.addAll(headers);
      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        var dataRaw = await response.stream.bytesToString();
        var data = json.decode(dataRaw);
        if (data['status'] == 'success') {
          Yubikey.onSuccessAlertPressed(
              context, "ui_success_otp_verify".tr, "ui_success_otp_verify".tr);
          if (initialLink == null ||
              initialLink == "" ||
              initialLink == "null") {
          } else {}
        } else {
          Yubikey.onErrorAlertPressed(context, data['message']);
        }
      } else {
        print(response.reasonPhrase);
      }
    } catch (e) {
      Yubikey.onErrorAlertPressed(context, "server error");
    }
  }

  static Future<void> nfcListener(context, bool varify) async {
    await getData();
    await checkOTP();
  }
}
