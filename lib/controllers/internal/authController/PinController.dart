
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/src/painting/text_style.dart';
import 'package:get/get.dart';
import 'package:pin_input_text_field/pin_input_text_field.dart';

class PinController extends PinDecoration{

  var passcode = "".obs;

  PinController({
    TextStyle? textStyle,
    ObscureStyle? obscureStyle,
    String? errorText,
    TextStyle? errorTextStyle,
    String? hintText,
    TextStyle? hintTextStyle,
    ColorBuilder? bgColorBuilder,
  }) : super(
    textStyle: textStyle,
    obscureStyle: obscureStyle,
    errorText: errorText,
    errorTextStyle: errorTextStyle,
    hintText: hintText,
    hintTextStyle: hintTextStyle,
  );

  @override
  PinDecoration copyWith({
    TextStyle? textStyle,
    ObscureStyle? obscureStyle,
    String? errorText,
    TextStyle? errorTextStyle,
    String? hintText,
    TextStyle? hintTextStyle,
    ColorBuilder? bgColorBuilder,
  }) {
    return PinController(
        textStyle: textStyle ?? this.textStyle,
        obscureStyle: obscureStyle ?? this.obscureStyle,
        errorText: errorText ?? this.errorText,
        errorTextStyle: errorTextStyle ?? this.errorTextStyle,
        hintText: hintText ?? this.hintText,
        hintTextStyle: hintTextStyle ?? this.hintTextStyle,
        bgColorBuilder: bgColorBuilder);
  }
  @override
  void drawPin(
      Canvas canvas,
      Size size,
      String text,
      int pinLength,
      Cursor? cursor,
      TextDirection textDirection,
      ) {
    /// You can draw anything you want here.
    canvas.drawLine(
      Offset.zero,
      Offset(size.width, size.height),
      Paint()
        ..color = Colors.red
        ..strokeWidth = 10
        ..style = PaintingStyle.stroke
        ..isAntiAlias = true,
    );
  }
  void addDigit(String digit) {
    if (passcode.value.length < 6) {
      passcode.value += digit;
    }
  }

  void removeLastDigit() {
    if (passcode.isNotEmpty) {
      passcode.value = passcode.substring(0, passcode.value.length - 1);
    }
  }

  void clearPasscode() {
    passcode.value = "";
  }

  @override
  void notifyChange(String pin) {
    // TODO: implement notifyChange
  }

  @override
  PinEntryType get pinEntryType => PinEntryType.customized;


}

