import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/controllers/internal/Tacontroller/Tacontroller.dart';
import 'package:mapp_ms24/controllers/utils/connect.dart';
import 'package:mapp_ms24/controllers/utils/loader.dart';
import 'package:mapp_ms24/controllers/utils/url.dart';
import 'package:mapp_ms24/controllers/utils/widget.dart';
import 'package:mapp_ms24/models/userModel/HealthModel.dart';
import 'package:mapp_ms24/models/userModel/usermodel.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';

class HealthController extends GetxController {
  EexerciseModel? eexerciseModel;
  ProportionModel? proportionModel;
  ProfileController profile = Get.find<ProfileController>();
  Tacontroller ta = Get.find<Tacontroller>();
  List dataLinkImage = [];
  String _value = '0';
  List dataHistoryHelp = [];
  double numhealthIPD = 0;
  double numhealthOPD = 0;
  double amounthealt = 0;

  Future<void> getDataEexercise(inputId) async {
    try {
      Map data = {"id": inputId};

      final res = await AppApi.callAPIjwt("POST", AppUrl.Eexercise, data);

      if (res["status"] == 200) {
        eexerciseModel = EexerciseModel.fromJson(res["result"]);
      } else {
        Get.snackbar("เกิดข้อผิดพลาด", "เกิดข้อผิดพลาด");
      }

      update();
    } catch (e) {
      if (kDebugMode) {
        print("error getDataDaysWork =>$e");
      }
      Get.snackbar("เกิดข้อผิดพลาด", "เกิดข้อผิดพลาด");
    }
  }

  Future<void> getDataProportio(inputId) async {
    try {
      Map data = {"id": inputId};

      final res = await AppApi.callAPIjwt("POST", AppUrl.Proportion, data);

      if (res["status"] == 200) {
        proportionModel = ProportionModel.fromJson(res["result"]);
      } else {
        Get.snackbar("เกิดข้อผิดพลาด", "เกิดข้อผิดพลาด");
      }

      update();
    } catch (e) {
      if (kDebugMode) {
        print("error getDataDaysWork =>$e");
      }
      Get.snackbar("เกิดข้อผิดพลาด", "เกิดข้อผิดพลาด");
    }
  }

  Future<void> uploadtoS3(base64Image) async {
    try {
      Map data = {
        "name": "MappMS",
        "folder": "MappMS/img_health_welfare",
        "image": base64Image
      };

      final res = await AppApi.callAPIjwt("POST", AppUrl.uploadS3, data);
      if (res["status"] == 200) {
        dataLinkImage.add(res["result"]["url"]["Location"].toString());
      } else {
        Get.snackbar("เกิดข้อผิดพลาด",
            "รูปภาพมีขนาดใหญ่เกิน กรุณาลดความละเอียดของไฟล์ภาพ");
      }

      update();
    } catch (e) {
      if (kDebugMode) {
        print("error getDataDaysWork =>$e");
      }
      Get.snackbar("เกิดข้อผิดพลาด", "$e");
    }
  }

  Future<void> updateToBCT(running, _withdrawAmountController,
      _surgerycostController, dataLinkImagedata) async {
    try {
      Map data = {
        "menu": "sendwelfarehealthTObct",
        "running": running.toString(),
        "email": profile.responseMember!.email.toString(),
        "id": profile.responseMember!.id.toString(),
        "bu": profile.responseMember!.company_management.toString(),
        "type": _value.toString(),
        "amount": _withdrawAmountController.toString(),
        "amountroom": "0",
        "amountday": "0",
        "amountdoctor": "0",
        "amountother": "0",
        "amountsurgery": _surgerycostController.text.toString(),
        "amountmedication": "0",
        "namehospital": "-",
        "namedoctor": "-",
        "becursemodify": "-",
        "medicine": "-",
        "image": dataLinkImagedata.toString(),
        "namelike": profile.responseMember!.full_name_th.toString()
      };

      final res = await AppApi.callAPIjwt("POST", AppUrl.updateToBCT, data);

      if (res["status"].toString() == "ok") {
      } else {
        Get.snackbar(
            "เกิดข้อผิดพลาด", "update fail! กรุณาลองใหม่อีกครั้งภายหลัง");
      }

      update();
    } catch (e) {
      if (kDebugMode) {
        print("error getDataDaysWork =>$e");
      }
      Get.snackbar("เกิดข้อผิดพลาด", "$e");
    }
  }

  selectimage(context) {
    return showModalBottomSheet(
      context: context,
      builder: (BuildContext bc) {
        return Container(
          child: new Wrap(
            children: <Widget>[
              new ListTile(
                leading: new Icon(Icons.add_a_photo),
                title: new Text('Take a photo'),
                onTap: () {
                  Navigator.of(context).pop();
                  getImageCamara();
                },
              ),
              new ListTile(
                leading: new Icon(Icons.photo_library),
                title: new Text('From gallery'),
                onTap: () {
                  Navigator.of(context).pop();
                  getImageDirectory();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Future getImageCamara() async {
    var image = await ImagePicker()
        .pickImage(source: ImageSource.camera, maxHeight: 2500);

    File imageFile = new File(image!.path);
    String base64Image = base64Encode(imageFile.readAsBytesSync());
    uploadtoS3(base64Image);
  }

  Future getImageDirectory() async {
    var image = await ImagePicker()
        .pickImage(source: ImageSource.gallery, maxHeight: 2500);

    File imageFile = new File(image!.path);
    String base64Image = base64Encode(imageFile.readAsBytesSync());

    uploadtoS3(base64Image);
  }
}
