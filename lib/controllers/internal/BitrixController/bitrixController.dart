import 'dart:convert';

import 'package:get/get.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/controllers/internal/Tacontroller/Tacontroller.dart';
import 'package:mapp_ms24/controllers/utils/connect.dart';
import 'package:mapp_ms24/controllers/utils/loader.dart';
import 'package:mapp_ms24/controllers/utils/url.dart';
import 'package:mapp_ms24/models/userModel/PurposeModel.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';

class BitrixController extends GetxController {
  String token = "";
  String idBitrix = "";
  List dataGroupID = [];
  List dataBitrix = [];
  List dataBitrixMe = [];
  List dataBitrixComment = [];
  List recipientBitrix = [];
  List dataBitrixCreate = [];
  List dataLinkVideoEdit = [];
  int bitrixlModellength = 0;
  int overDateComment = 0;
  int positionEdit = -1;
  bool checkBitrix = false;
  var statusDeadLine = 0;
  int statusChangeStatus = 0;
  int alertSuccessFinish = 0;
  int statusAlertFinishError = 0;
  int alertalertSuccessFinish = 0;
  int countBitrix = 0;

  ProfileController profile = Get.find<ProfileController>();
  BitrixlModelList? bitrixlModel = BitrixlModelList();
  Tacontroller taCtr = Get.find<Tacontroller>();

  Future<void> loadingAllProgress() async {
    await loadTokenBitrix();
    print('loadTokenBitrix');
    await loadIDBitrix();
    print('loadIDBitrix');

    await loadNumBitrix();
    print('loadNumBitrix');

    await loadBitrixList();
    print('loadBitrixList');

    await loadBitrixListCreate();
    print('loadBitrixListCreate');

    await loadNameGroupID();
    print('loadNameGroupID');

  }

  allload(context, idIssue) async {
    AppLoader.loader(context);
    await loadTokenBitrix();
    await loadBitrixComment(context, idIssue);
    AppLoader.dismiss(context);
  }

  Future<void> loadTokenBitrix() async {
    Map data = {};

    final res = await AppApi.callAPIjwt("POST", AppUrl.searchTokenBitrix, data);
    if (res.length > 0) {
      token = res["result"][0]["access_token"];
    } else {
      const GetSnackBar(
        title: "load token fail !!!",
        message: "โหลด token ล้มเหลว กรุณาลองใหม่อีกครั้ง",
        duration: Duration(seconds: 3),
      );
    }
  }

  String nameGroupID(groupID) {
    var result = "";
    for (var i = 0; i < dataGroupID.length; i++) {
      if (groupID.toString() == dataGroupID[i]["ID"].toString()) {
        result = dataGroupID[i]["NAME"].toString();
        break;
      }
    }
    return result;
  }


  Future<void> loadIDBitrix() async {
    Map data = {
      "EMAIL": profile.responseMember!.email,
    };

    print(token);
    var dataBit = await AppApi.apiRequest(
      'https://msspkg.bitrix24.com/rest/user.get?auth=${token}',
      data,
    );

    if (dataBit["result"] is List && dataBit["result"].isNotEmpty) {
      idBitrix = dataBit["result"][0]["ID"].toString();
      update();
    } else {
      Get.showSnackbar(
        const GetSnackBar(
          title: "Load Token Fail!!!",
          message: "โหลด token ล้มเหลว หรือไม่พบข้อมูล",
          duration: Duration(seconds: 3),
        ),
      );
    }
  }


  Future<void> loadBitrixList() async {
    Map data = {"ID": "ASC"};
    Map data2 = {
      "RESPONSIBLE_ID": [idBitrix.toString()],
      "REAL_STATUS": [1, 2, 3]
    };

    List data3 = [data, data2];

    final res = await AppApi.apiRequest2(
        'https://msspkg.bitrix24.com/rest/task.items.getlist?auth=${token}',
        data3);
    var response = json.decode(res);
    List jsonResponse2 = response["result"];

    if (jsonResponse2.length > 0) {
      for (var i = 0; i < jsonResponse2.length; i++) {
        if (jsonResponse2[i]["TITLE"].indexOf("Fill out profile") == -1 &&
            jsonResponse2[i]["TITLE"]
                    .indexOf("Download Bitrix24 application") ==
                -1 &&
            jsonResponse2[i]["PARENT_ID"] != null) {
          bool exists = dataBitrix
              .any((element) => element["TITLE"] == jsonResponse2[i]["TITLE"]);

          if (!exists) {
            dataBitrix.add(jsonResponse2[i]);
          }
        }
      }
      dataBitrixMe = dataBitrix;

      bitrixlModel = BitrixlModelList.fromJson(dataBitrix);
      update();
    } else {
      const GetSnackBar(
        title: "load ID Bittrix fail !!!",
        message: "ไม่สามารถค้นหา ID Bittrix ของคุณได้",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<void> loadNumBitrix() async {
    Map data = {"ID": "ASC"};
    Map data2 = {
      "RESPONSIBLE_ID": [idBitrix.toString()],
      "REAL_STATUS": [1, 2, 3]
    };

    List data3 = [data, data2];

    final res = await AppApi.apiRequest2(
        'https://msspkg.bitrix24.com/rest/task.items.getlist?auth=${token}',
        data3);
    var response = json.decode(res);
    List jsonResponse2 = response["result"];

    if (jsonResponse2.length > 0) {
      for (var i = 0; i < jsonResponse2.length; i++) {
        if (jsonResponse2[i]["REAL_STATUS"].toString() == "1" ||
            jsonResponse2[i]["REAL_STATUS"].toString() == "2") {
          countBitrix = countBitrix + 1;
        }
      }
    } else {
      print('load bitrixList fail !!!');
      const GetSnackBar(
        title: "load ID Bittrix fail !!!",
        message: "ไม่สามารถค้นหา ID Bittrix ของคุณได้",
        duration: Duration(seconds: 3),
      );
    }
  }
  Future<void> loadNameGroupID() async {
    Map data = {
      "": {"": ""}
    };

    final Map<String, dynamic> dataGroup = await AppApi.apiRequest(
      'https://msspkg.bitrix24.com/rest/sonet_group.get?auth=$token',
      data,
    );

    final List nameGroupID = dataGroup["result"];

    if (nameGroupID.isNotEmpty) {
      dataGroupID = nameGroupID;
      update();
    } else {
      Get.showSnackbar(
        const GetSnackBar(
          title: "load ID fail !!!",
          message: "ไม่สามารถค้นหา NameGroupID ของคุณได้",
          duration: Duration(seconds: 3),
        ),
      );
    }
  }

  Future<void> loadBitrixListCreate() async {
    Map data = {"ID": "ASC"};
    Map data2 = {
      "CREATED_BY": [idBitrix.toString()],
      "REAL_STATUS": [4]
    };

    List data3 = [data, data2];

    final Map<String, dynamic> dataBit = await AppApi.apiRequest(
      'https://msspkg.bitrix24.com/rest/task.items.getlist?auth=$token',
      data3,
    );

    final List bitrixListCreate = dataBit["result"];

    if (bitrixListCreate.isNotEmpty) {
      dataBitrixCreate = bitrixListCreate;
      update();
    } else {
      Get.showSnackbar(
        const GetSnackBar(
          title: "load ID Bitrix fail !!!",
          message: "ไม่สามารถค้นหา ID Bitrix ของคุณได้",
          duration: Duration(seconds: 3),
        ),
      );
    }
  }

  Future<void> loadBitrixComment(context, idIssue) async {
    Map data = {"ID": "ASC"};

    List data2 = [idIssue, data];
    final res = await AppApi.apiRequest2(
        'https://msspkg.bitrix24.com/rest/task.commentitem.getlist?auth=${token}',
        data2);
    var dataBit = jsonDecode(res);

    List BitrixList = dataBit["result"];

    if (BitrixList.length > 0) {
      for (var i = 0; i < BitrixList.length; i++) {
        String postMessage = BitrixList[i]["POST_MESSAGE"];

        if (postMessage.indexOf("Participants") == -1 &&
            postMessage.indexOf("A task was created") == -1 &&
            postMessage.indexOf("Task is") == -1 &&
            postMessage.indexOf("a task") == -1 &&
            postMessage.indexOf("Author") == -1 &&
            postMessage.indexOf("review upon") == -1 &&
            postMessage.indexOf("Deadline") == -1 &&
            postMessage.indexOf("Change the deadline") == -1 &&
            postMessage.indexOf("Responsible person") == -1 &&
            postMessage.indexOf("Performance decreased") == -1 &&
            postMessage.indexOf("is required") == -1 &&
            postMessage.indexOf("Task has been") == -1 &&
            postMessage.indexOf("Task") == -1 &&
            postMessage.indexOf("task") == -1) {
          bool exists = dataBitrixComment
              .any((element) => element["POST_MESSAGE"] == postMessage);

          if (!exists) {
            dataBitrixComment.add(BitrixList[i]);
          }
        }
      }

      update();
    } else {
      const GetSnackBar(
        title: "load ID Bittrix fail !!!",
        message: "ไม่สามารถค้นหา ID Bittrix ของคุณได้",
        duration: Duration(seconds: 3),
      );
    }
  }

  int statusAlertSuccessReturn = 0;
  int statusAlertSuccessCloseIssue = 0;
  Future<void> updateBitrixStatus(
      context, statusNew, idIssue, realsStatus) async {
    AppLoader.loader(context);
    Map data = {"STATUS": statusNew};
    List data2 = [idIssue, data];
    final res = await AppApi.apiRequest2(
        'https://msspkg.bitrix24.com/rest/task.item.update?auth=${token}',
        data2);
    var jsonResponse = jsonDecode(res);
    if (jsonResponse["result"] != "") {
      realsStatus = statusNew;
      statusChangeStatus = 1;

      if (statusNew.toString() == "2") {
        statusAlertSuccessReturn = 1;
        AppLoader.dismiss(context);
      }
      if (statusNew.toString() == "5") {
        statusAlertSuccessCloseIssue = 1;
        AppLoader.dismiss(context);
      }
      if (statusNew.toString() == "4") {
        alertalertSuccessFinish = 1;
        AppLoader.dismiss(context);
      }
      AppLoader.dismiss(context);

      update();
    } else {
      const GetSnackBar(
        title: "load ID Bittrix fail !!!",
        message: "ไม่สามารถค้นหา ID Bittrix ของคุณได้",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<void> updateBitrixComment(
      context, idIssue, idComment, commentEditController) async {
    Map data = {"POST_MESSAGE": commentUpdate.toString()};

    List data2 = [idIssue, idComment, data];

    final res = await AppApi.apiRequest2(
        'https://msspkg.bitrix24.com/rest/task.commentitem.update?auth=${token}',
        data2);
    var jsonResponse = jsonDecode(res);

    List jsonResponse2 = jsonResponse["result"];
    if (jsonResponse2 != "") {
      commentEditController.text = "";
      positionEdit = -1;
      dataLinkImage = [];
      dataLinkImageEdit = [];
      loadBitrixComment(context, idIssue);
    } else {
      const GetSnackBar(
        title: "load ID Bittrix fail !!!",
        message: "ไม่สามารถค้นหา ID Bittrix ของคุณได้",
        duration: Duration(seconds: 3),
      );
    }
  }

  int statusAlertFinish = 0;

  var commentUpdate = "";

  Future<void> processCommentUpdate(
      context, idIssue, idComment, commentEditController) async {
    commentUpdate = commentEditController.text;
    commentUpdate += "\n\n";
    for (var i = 0; i < dataLinkImage.length; i++) {
      commentUpdate += "linkImg:" + dataLinkImageEdit[i].toString() + "\n";
    }
    commentUpdate += "\n\n\n\n";
    commentUpdate += "********** สำหรับทีมพัฒนาเท่านั้น **********";
    commentUpdate += "\n";
    commentUpdate += "&APP&TOSPLIT&";
    commentUpdate += dataLinkImageEdit.toString();
    commentUpdate += "|||";
    updateBitrixComment(context, idIssue, idComment, commentEditController);
  }

  List dataLinkImage = [];
  List dataLinkVideo = [];
  List dataLinkImageEdit = [];

  Future<void> uploadtoS3(base64Image) async {
    Map data = {
      "name": "MappMS",
      "folder": "MappMS/img_Bitrix",
      "image": base64Image
    };

    final res = await AppApi.callAPIjwt("POST", AppUrl.uploadS3, data);

    if (res["statusCode"].toString() == "200") {
      res(() {
        dataLinkImage.add(res["result"]["url"]["Location"].toString());
        dataLinkImageEdit.add(res["result"]["url"]["Location"].toString());
      });
    }
  }

  var commentInsert = "";

  Future<void> processCommentInsert(context, idIssue, commentController) async {
    AppLoader.loader(context);

    commentInsert = commentController;
    commentInsert += "\n\n";
    for (var i = 0; i < dataLinkImage.length; i++) {
      commentInsert += "linkImg:" + dataLinkImage[i].toString() + "\n";
    }
    for (var i = 0; i < dataLinkVideo.length; i++) {
      commentInsert += "linkVideo:" + dataLinkVideo[i].toString() + "\n";
    }
    commentInsert += "\n\n\n\n";
    commentInsert += "********** สำหรับทีมพัฒนาเท่านั้น **********";
    commentInsert += "\n";
    commentInsert += "&APP&TOSPLIT&";
    commentInsert += dataLinkImage.toString();
    commentInsert += "|||";
    commentInsert += dataLinkVideo.toString();
    insertBitrixComment(idIssue, context, commentController);
    AppLoader.dismiss(context);
  }

  Future<void> insertBitrixComment(idIssue, context, commentController) async {
    AppLoader.loader(context);

    Map data = {
      "POST_MESSAGE": commentInsert.toString(),
      "AUTHOR_ID": idBitrix
    };

    List data2 = [idIssue, data];

    final res = await AppApi.apiRequest2(
        'https://msspkg.bitrix24.com/rest/task.commentitem.add?auth=${token}',
        data2);

    var jsonResponse = jsonDecode(res);

    if (jsonResponse["result"] != "") {
      commentController = "";
      positionEdit = -1;
      dataLinkImage = [];
      dataLinkImageEdit = [];
      loadBitrixComment(context, idIssue);
      AppLoader.dismiss(context);
    } else {
      const GetSnackBar(
        title: "load ID Bittrix fail !!!",
        message: "ไม่สามารถค้นหา ID Bittrix ของคุณได้",
        duration: Duration(seconds: 3),
      );
    }
  }

  //--------------------------------- ส่วนของการหน้าผู้จ่ายงาน ---------------------------------
  Future<void> Dispatcher(
    context,
    deadline,
  ) async {
    taCtr.getTime(context);
    await checkDeadLine(deadline);
  }

  checkDeadLine(deadline) async {
    var diffDeadLine = (DateTime.parse(convertDateTime(deadline, "dd-MM-YYYY"))
        .difference(
            DateTime.parse(convertDateTime(taCtr.datetime, "dd-MM-YYYY2")))
        .inDays);
    if (diffDeadLine < 0) {
      statusDeadLine = 1;
    }
  }

  Future<void> processFinish(context, statusNew, idIssue, realsStatus) async {
    AppLoader.loader(context);
    var lastIndex = dataBitrixComment.length - 1;
    var lastdatetimtUpdate = dataBitrixComment.length == 0
        ? "1989-01-01T01:00:00+03:00"
        : dataBitrixComment[lastIndex]["POST_DATE"];
    var lastdateUpdate = convertDateTime(lastdatetimtUpdate, "dd-MM-YYYY");
    var dateNow = convertDateTime(taCtr.datetime.toString(), "dd-MM-YYYY2");

    if (realsStatus.toString() == "1" ||
        realsStatus.toString() == "2" ||
        lastdateUpdate != dateNow) {
      statusAlertFinish = 1;
    } else {
      updateBitrixStatus(context, 4, idIssue, realsStatus);
    }
    AppLoader.dismiss(context);
  }

  bool margin = false;

  processDataForUpdateComment(data, commentEditController) {
    var splitcomment = data.split("&APP&TOSPLIT&");
    if (splitcomment.length > 1) {
      var splitImgVdo = splitcomment[1].split("|||");
      var splitImgCutFont = splitImgVdo[0].substring(1, splitImgVdo[0].length);
      var splitImgCutBack =
          splitImgCutFont.substring(0, splitImgCutFont.length - 1);
      dataLinkImageEdit = splitImgCutBack.split(", ");

      var splitVdoCutFont = splitImgVdo[1].substring(1, splitImgVdo[1].length);
      var splitVdoCutBack =
          splitVdoCutFont.substring(0, splitVdoCutFont.length - 1);
      dataLinkVideoEdit = splitVdoCutBack.split(", ");
    }

    var splitcommentGetTextlinkImg = splitcomment[0].split("\n\nlinkImg");
    var splitcommentGetTextlinkVideo = splitcomment[0].split("\n\nlinkVideo");
    if (splitcommentGetTextlinkImg.length > 1) {
      commentEditController = splitcommentGetTextlinkImg[0];
    } else if (splitcommentGetTextlinkVideo.length > 1) {
      commentEditController = splitcommentGetTextlinkVideo[0];
    } else {
      commentEditController = splitcomment[0]
          .split("\n\n\n\n\n\n********** สำหรับทีมพัฒนาเท่านั้น **********")[0];
    }
  }

  processShowImg(data) {
    bool margin = false;
    var splitImgUse = [];
    var splitVdoUse = [];
    var splitcomment = data.split("&APP&TOSPLIT&");
    if (splitcomment.length > 1) {
      var splitImgVdo = splitcomment[1].split("|||");
      var splitImgCutFont = splitImgVdo[0].substring(1, splitImgVdo[0].length);
      var splitImgCutBack =
          splitImgCutFont.substring(0, splitImgCutFont.length - 1);
      splitImgUse = splitImgCutBack.split(", ");

      var splitVdoCutFont = splitImgVdo[1].substring(1, splitImgVdo[1].length);
      var splitVdoCutBack =
          splitVdoCutFont.substring(0, splitVdoCutFont.length - 1);
      splitVdoUse = splitVdoCutBack.split(", ");
    }
    if (splitImgUse.toString() == "[]" && splitVdoUse.toString() == "[]") {
      margin = false;
    } else {
      margin = true;
    }
    return margin;
  }
}
