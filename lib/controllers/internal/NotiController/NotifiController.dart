import 'dart:convert';

import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:mapp_ms24/controllers/internal/FinaceController/FinaceController.dart';
import 'package:mapp_ms24/controllers/internal/LeaveController/LeaveApprovalController.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/controllers/internal/Tacontroller/Tacontroller.dart';
import 'package:mapp_ms24/controllers/utils/connect.dart';
import 'package:mapp_ms24/controllers/utils/loader.dart';
import 'package:mapp_ms24/controllers/utils/url.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/NotiScreen/Notification.dart';

class NotiController extends GetxController {
  TextEditingController voteEditingController = TextEditingController();
  ProfileController profile = Get.find<ProfileController>();
  FinaceController finaceCtr = Get.find<FinaceController>();
  Tacontroller tactl = Get.find<Tacontroller>();
  LeaveAndAppController landAndCrt = Get.find<LeaveAndAppController>();
  RxList testNoti = [].obs;
  RxList testNews = [].obs;
  RxList testCrouse = [].obs;
  List<String> texts = [];
  RxBool isExpand = false.obs;

  List<RxBool> isExpandList = List.generate(100, (index) => false.obs);
  RxList<String> read_status = <String>[].obs;
  RxList<RxBool> new_noti = <RxBool>[].obs;

  List datanotificationNews = [];
  List datanotificationReward = [];
  List datanotificationdemo2 = [];
  List datanotificationCourse = [].obs;
  var statusactionclick3;
  bool deleteButton = false;
  var datastatus = [];

  var i = 0;

  void initnot() {
    new_noti.value = List.generate(isExpandList.length, (index) => true.obs);
    read_status.value = List<String>.filled(isExpandList.length, "");
  }

  void toggleExpandState(int index) {
    isExpandList[index].value = !isExpandList[index].value;

    //
    // if (isExpandList[index].value) {
    //   read_status[index] = "read"; // Mark as read
    // }
    if (isExpandList[index].value == true) {
      read_status[index] = "read";
      new_noti[index].value = false;
    }
  }

  void resetExpandList() {
    for (int i = 0; i < isExpandList.length; i++) {
      isExpandList[i].value = false;
    }
  }

  void clearList() {
    testNoti.clear();
  }

  void showErrorDialog(context) {
    showDialog(
      context: context,
      // context คือ BuildContext ที่ได้รับจาก widget ที่กำลัง build
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('ระบบลงเวลาสำรอง'),
          content: Container(
            height: 100.h,
            width: 200.w,
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 10.0),
                  child: Container(
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20),
                        // borderRadius: BorderRadius.all(Radius.circular(20)),
                        shape: BoxShape.rectangle,
                        border: Border.all(
                          color: Colors.black.withOpacity(0.5),
                          width: 1.0,
                        )),
                    child: TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: Text('ลงเวลาเช้า'),
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 10.0),
                  child: Container(
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(20)),
                        shape: BoxShape.rectangle,
                        border: Border.all(
                          color: Colors.black.withOpacity(0.5),
                          width: 1.0,
                        )),
                    child: TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: Text('ลงเวลาเย็น'),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void updateText(String newText) {
    voteEditingController.text = newText;
  }

  var title = "";
  Future<void> getNotificationInfo(inputID, bu) async {
    try {
      Map data = {
        "id": inputID
        // "id": '6609247'
      };
      final response1 =
          await AppApi.callAPIjwt("POST", AppUrl.loadNotification, data);

      if (response1["status"] == 200) {
        testNews.clear();
        testCrouse.clear();
        datanotificationReward.clear();
        datanotificationNews.clear();
        for (var i = 0; i < response1["result"].length; i++) {
          if (response1["result"][i]["typenotification"] == "reward") {
            if (response1["result"][i]["status_recive_like"] != "Y") {
              datanotificationReward.add(response1["result"][i]["title"]);
              String title = response1["result"][i]["title"] ??
                  response1["result"][i]["detail"];

              Map<String, dynamic> newNotification = {
                'time': response1["result"][i]["update_time"],
                'activity': title,
                'des': response1["result"][i]["detail"],
                'running_act': response1["result"][i]["running_act"]
              };

              bool exists = testNoti.any((notification) =>
                  notification['time'] == newNotification['time'] &&
                  notification['activity'] == newNotification['activity'] &&
                  notification['des'] == newNotification['des'] &&
                  notification['running_act'] ==
                      newNotification['running_act']);

              if (!exists) {
                testNoti.add(newNotification);
              } else {
                print('ข้อมูลซ้ำ: ไม่ได้เพิ่มข้อมูลเข้าไปในรายการ');
              }
            }
          } else if (response1["result"][i]["typenotification"] == "news") {
            datanotificationNews.add(response1["result"][i]);
            datanotificationdemo2
                .add(response1["result"][i]["running"].toString());
            testNews.add({
              'time': response1["result"][i]["update_time"],
              'activity': response1["result"][i]["title"],
              'des': response1["result"][i]["detail"]
            });
          }
        }
        await getpadCourse(inputID, bu);

        update();
      } else {
        const GetSnackBar(
          title: "เกิดข้อผิดพลาด",
          message: "เกิดข้อผิดพลาด",
          duration: Duration(seconds: 3),
        );
        update();
      }
    } catch (e) {
      if (kDebugMode) {
        print("error getCarReg =>$e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<void> getpadCourse(String inputID, String bu) async {
    try {
      testCrouse.clear();
      datanotificationCourse.clear();

      final url = Uri.parse(
          'https://n8n-deploy.agilesoftgroup.com/webhook/PostforCourse');
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          "id": inputID,
          "bu": bu,
        }),
      );

      final Map<String, dynamic> responseJson = jsonDecode(response.body);

      if (responseJson.containsKey("result") &&
          responseJson["result"] is List) {
        final List result = responseJson["result"];

        if (result.isEmpty) {
          Get.snackbar("แจ้งเตือน", "ไม่มีข้อมูลใน result");
          return;
        }

        for (var item in result) {
          Map<String, dynamic> course = {
            "id": item["id"] ?? "-",
            "name": item["name"] ?? "-",
            "surname": item["surname"] ?? "-",
            "nickname": item["nickname"] ?? "-",
            "type": item["type"] ?? "-",
            "role": item["role"] ?? "-",
            "team": item["team"] ?? "-",
            "company": item["company"] ?? "-",
            "group": item["group"] ?? "-",
            "bmc_smc": item["bmc_smc"] ?? "-",
            "notify_title": item["notify_title"] ?? "-",
            "message": item["notify_message"] ?? "-",
            "content_title": item["content_title"] ?? "-",
            "content_link": item["content_link"] ?? "-",
            "exam_title": item["exam_title"] ?? "-",
            "exam_link": item["exam_link"] ?? "-",
            "do_before": item["do_before"] ?? "-"
          };

          testCrouse.add(course);
          datanotificationCourse.add(course);
        }
      } else {
        Get.snackbar(
            "เกิดข้อผิดพลาด", "ไม่พบ result หรือข้อมูล response ไม่ถูกต้อง");
      }
    } catch (e) {
      Get.snackbar("เกิดข้อผิดพลาด", "ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ได้");
    }
  }

  var AddressLikepoint = "";
  void ClaimLikepoint(context, running, act_id, name_activity, fullname, mobile,
      id_ldx, amount) {
    getaddress(context);
    ClaimLike(context, running, act_id, name_activity, fullname, mobile, id_ldx,
        amount);
  }

  Future<void> getaddress(context) async {
    Map map = {"phoneNumber": profile.responseMember!.phone_like};
    final response = await postNormal(
        'https://new.likepoint.io/getAddressByphoneNumber', map);
    if (response["status"].toString() == "200") {
      AddressLikepoint = response["result"]["data"].toString();
    } else {
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "ไม่สามารถค้นหาจำนวน likewallet ได้",
        duration: Duration(seconds: 3),
      );
    }
  }

  ClaimLike(context, running, act_id, name_activity, fullname, mobile, id_ldx,
      amount) async {
    try {
      AppLoader.loader(context);

      Map data = {
        "running": running,
        "act_id": act_id.toString(),
        "actName": name_activity.toString(),
        "fullname": fullname.toString(),
        "mobile": mobile.toString(),
        "id_ldx": id_ldx.toString(),
        "address": AddressLikepoint,
        "amount": amount
      };
      final response1 =
          await AppApi.callAPIjwt("POST", AppUrl.tranferclaimlike, data);

      if (response1["result"]["res_msg"].toString() ==
          "Insert Transaction Complete") {
        updateActivityManual(context, running.toString());
        updateStatusReciveLike(context, running.toString());
        AppLoader.dismiss(context);
      } else if (response1["result"]["res_msg"].toString() ==
          "You don't have Api Key! or Address format incorect! or Wallet not found! or id 205 < 5") {
        Fluttertoast.showToast(
            msg: "ไม่มีกระเป๋ากิจกรรม หรือกิจกรรมไม่ถูกต้อง",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.TOP,
            backgroundColor: Colors.red,
            textColor: Colors.black,
            fontSize: 16.0);
        AppLoader.dismiss(context);

        update();
      } else {
        Fluttertoast.showToast(
            msg: "ไม่สำเร็จ กรุณากดใหม่อีกครั้ง",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.TOP,
            backgroundColor: Colors.red,
            textColor: Colors.black,
            fontSize: 16.0);

        update();
        AppLoader.dismiss(context);
      }
    } catch (e) {
      AppLoader.dismiss(context);

      if (kDebugMode) {
        print("error getCarReg =>$e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<void> updateActivityManual(context, running) async {
    AppLoader.loader(context);
    Map data = {
      "running": running.toString(),
      "id_ldx": profile.responseMember!.id.toString()
    };
    final response1 =
        await AppApi.callAPIjwt("POST", AppUrl.TransferActManual, data);

    if (response1["changedRows"].toString() != "0") {
      update();
      AppLoader.dismiss(context);
    } else {
      update();
      AppLoader.dismiss(context);
    }
  }

  Future<void> updateStatusReciveLike(context, running) async {
    AppLoader.loader(context);
    Map data = {
      "running_act": running.toString(),
      "update_user": profile.responseMember!.email.toString(),
      "id": profile.responseMember!.id.toString()
    };
    final response1 =
        await AppApi.callAPIjwt("POST", AppUrl.updateStatusLike, data);

    if (response1["changedRows"].toString() != "0") {
      GetSnackBar(
        title: "ทำรายการสำเร็จ",
        message: "likepoint เข้ากระเป๋าภายใน 10 นาที",
        duration: Duration(seconds: 3),
      );
      update();
      AppLoader.dismiss(context);
    } else {
      update();
      AppLoader.dismiss(context);
    }
  }

  Future<void> doreloadcheckclaim(context, running) async {
    AppLoader.loader(context);
    Map data = {
      "running": running.toString(),
    };
    final response1 = await AppApi.callAPIjwt("POST", AppUrl.checkclaim, data);

    if (response1.length > 0) {
      ClaimLikepoint(
          context,
          response1['result'][0]["running"].toString(),
          response1['result'][0]["id_activity"].toString(),
          response1['result'][0]["name_activity"].toString(),
          response1['result'][0]["name_ldx"].toString() +
              " " +
              response1['result'][0]["lname_ldx"].toString(),
          response1['result'][0]["tel_ldx"].toString(),
          response1['result'][0]["id_ldx"].toString(),
          response1['result'][0]["point"].toString());
      update();
      AppLoader.dismiss(context);
    } else {
      Fluttertoast.showToast(
          msg: "คุณกดรับไปแล้ว ในกิจกรรมนี้",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.TOP,
          backgroundColor: Colors.red,
          textColor: Colors.black,
          fontSize: 16.0);
    }
    update();
    AppLoader.dismiss(context);
  }

  Future<void> checkreadstatusall(context) async {
    Map data = {
      "id": profile.responseMember!.id.toString(),
    };
    final response1 =
        await AppApi.callAPIjwt("POST", AppUrl.loadnotificationAll, data);

    if (response1.length > 0) {
      datastatus = response1["result"];
    } else {}
    GetSnackBar(
      title: "เกิดข้อผิดพลาด",
      message: "search found",
      duration: Duration(seconds: 10),
    );
    update();
  }

  Future<void> checkreadstatus(context, idnotification, title, detail,
      typenotification, select, round) async {
    // AppLoader.loader(context);
    statusactionclick3 = round;
    update();
    Map data = {
      "id": profile.responseMember!.id.toString(),
      "idnotification": idnotification.toString()
    };
    final response1 =
        await AppApi.callAPIjwt("POST", AppUrl.notificationStatus, data);

    if (response1.length > 0) {
      print("statusactionclick3: $statusactionclick3");
    } else {
      savereadstatus(
          idnotification, title, detail, typenotification, select, round);
      // AppLoader.dismiss(context);
    }
    GetSnackBar(
      title: "เกิดข้อผิดพลาด",
      message: "search found",
      duration: Duration(seconds: 10),
    );

    update();
  }

  Future<void> savereadstatus(
      idnotification, title, detail, typenotification, select, round) async {
    Map data = {
      "email": profile.responseMember!.email.toString(),
      "id": profile.responseMember!.id.toString(),
      "idnotification": idnotification,
      "status": "read"
    };
    final response1 =
        await AppApi.callAPIjwt("POST", AppUrl.notificationStatus, data);

    if (response1["insertId"].toString() != "") {
      statusactionclick3 = "statusactionclick${round.toString()}";
    } else {}
    GetSnackBar(
      title: "เกิดข้อผิดพลาด",
      message: "save error",
      duration: Duration(seconds: 10),
    );
    update();
  }

  Future<void> savedeletestatus(context, idnotification) async {
    AppLoader.loader(context);
    Map data = {
      "email": profile.responseMember!.email.toString(),
      "id": profile.responseMember!.id.toString(),
      "idnotification": idnotification,
      "status": "delete"
    };
    final response1 =
        await AppApi.callAPIjwt("POST", AppUrl.updateNotiStatus, data);

    if (response1["insertId"].toString() != "") {
      Navigator.pushReplacement(
          context, MaterialPageRoute(builder: (context) => NotiScreen(1)));
      AppLoader.dismiss(context);
    } else {
      AppLoader.dismiss(context);
    }
    GetSnackBar(
      title: "เกิดข้อผิดพลาด",
      message: "เกิดข้อผิดพลาดในการลบข้อมูล",
      duration: Duration(seconds: 10),
    );
    AppLoader.dismiss(context);

    update();
  }

  checkread(context, idnotification) {
    var result = "false";
    for (var i = 0; i < datastatus.length; i++) {
      if (datastatus[i]["idnotification"].toString() == idnotification) {
        if (datastatus[i]["status"].toString() == "delete" ||
            datastatus[i]["status"].toString() == "read") {
          result = "true";
          break;
        }
      }
    }
    return result;
  }

  checkdelete(idnotification) {
    var result = "false";
    for (var i = 0; i < datastatus.length; i++) {
      if (datastatus[i]["idnotification"].toString() == idnotification) {
        if (datastatus[i]["status"].toString() == "delete") {
          result = "true";
          break;
        }
      }
    }
    return result;
  }
}
