import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/controllers/internal/Tacontroller/Tacontroller.dart';
import 'package:mapp_ms24/controllers/utils/connect.dart';
import 'package:mapp_ms24/controllers/utils/loader.dart';
import 'package:mapp_ms24/controllers/utils/url.dart';
import 'package:mapp_ms24/models/wealthModel/cuModel.dart';
import 'package:mapp_ms24/models/wealthModel/gctModel.dart';
import 'package:mapp_ms24/models/wealthModel/metacuModel.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';

class WealthController extends GetxController {
  ProfileController profile = Get.put(ProfileController());
  Tacontroller taCtr = Get.find<Tacontroller>();
  double debtBalance = 0.0;

  double sharesDeposited = 0.00;
  double sharesDepositedReduce = 0.00;
  double amountDeposited = 0.00;
  double amountDepositedReduce = 0.00;
  double totalShares = 0.00;
  double totalAmount = 0.00;

  CU_Model? cU_Model;
  double GTCguarantee = 0.0;
  List dataDebtContact = [];

  Future<void> getdebtBalance(context) async {
    try {
      Map<String, dynamic> data = {
        "datecheck": convertDateTime(taCtr.datetime, "DDB"),
        "cuID": profile.responseMember!.cuID,
      };

      final response =
          await AppApi.callAPIjwt("POST", AppUrl.searchDebtV2, data);

      // Decode JSON responses
      if (response["status"].toString() == "200") {
        dataDebtContact = response["result"];
        for (var i = 0; i < response["result"].length; i++) {
          if (response["result"][i]['contract_balance'].toString() != "null") {
            debtBalance = debtBalance +
                double.parse(
                    response["result"][i]['contract_balance'].toString());
          }
        }
      } else {
        debtBalance = 0.0;
      }

      update();
      return;
    } catch (e) {
      AppLoader.dismiss(context);

      if (kDebugMode) {
        print("error getCarReg => $e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<void> getGTCguarantee(context) async {
    try {
      Map<String, dynamic> data = {"personID": profile.responseMember!.id};

      final response =
          await AppApi.callAPIjwt("POST", AppUrl.GTCguarantee, data);

      if (response["status"] == 200) {
        if (response["result"]["GTCguarantee"] != null) {
          GTCguarantee =
              double.parse(response["result"]["GTCguarantee"].toString());
        } else {
          GTCguarantee = double.parse(0.00.toString());
        }
      } else {
        GTCguarantee = double.parse(0.00.toString());
      }
      update();
      return;
    } catch (e) {
      AppLoader.dismiss(context);

      if (kDebugMode) {
        print("error getCarReg => $e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  var shareBalance = "0.0";
  double savingMoney = 0.0;
  Future<void> getCUinfo(context) async {
    try {
      Map<String, dynamic> data = {"personID": profile.responseMember!.id};

      var now = DateTime.now();
      Map<String, dynamic> data2 = {
        "datecheck": now.toString(),
        "cuID": profile.responseMember!.cuID
      };

      final response1 = await AppApi.callAPIjwt("POST", AppUrl.ShareCU, data);
      final response2 = await AppApi.callAPIjwt("POST", AppUrl.Saving, data2);
      print("response1 ShareCU => $response1");
      print("response2 Saving => $response2");
      if (response1["status"] == 200 && response2["status"] == 200) {
        if (response1["result"]["shareBalance"].toString() != "null") {
          shareBalance = response1["result"]['shareBalance'].toString();
          if (shareBalance.indexOf('.') > 0) {
            shareBalance = shareBalance;
          } else {
            shareBalance = shareBalance + ".0";
          }
        }
        if (response2["result"]['saving'].toString() == "null") {
          savingMoney = 0;
        } else {
          savingMoney = double.parse(response2["result"]['saving'].toString());
        }

        Map<String, dynamic> margeJson = {
          "shareBalance": double.parse(shareBalance),
          "saving": double.parse(savingMoney.toString()),
        };
        cU_Model = CU_Model.fromJson(margeJson);
      } else {
        savingMoney = 0;
        shareBalance = "0.0";
        Map<String, dynamic> margeJson = {
          "shareBalance": double.parse(shareBalance),
          "saving": double.parse(savingMoney.toString()),
        };
        cU_Model = CU_Model.fromJson(margeJson);
      }

      update();
      return;
    } catch (e) {
      if (kDebugMode) {
        print("error getCarReg => $e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<void> Post_for_cu(context) async {
    try {
      Map<String, dynamic> data = {
        "memberID": profile.responseMember!.id.toString(),
      };

      final response = await http.post(
        Uri.parse(
            "https://n8n-deploy.agilesoftgroup.com/webhook/b465b552-9a15-4dc9-b1ca-f28990de2610"),
        headers: {"Content-Type": "application/json"},
        body: jsonEncode(data),
      );
      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);

        if (jsonData is List && jsonData.length >= 2) {
          final Map<String, dynamic> merged = {};

          for (final map in jsonData) {
            if (map is Map<String, dynamic>) {
              map.forEach((key, value) {
                final num currentValue = merged[key] ?? 0;
                final num newValue = (value is num) ? value : 0;
                merged[key] = currentValue + newValue;
              });
            }
          }

          totalShares = (merged['shares_deposited'] ?? 0).toDouble();
          totalAmount = (merged['amount_deposited'] ?? 0).toDouble();
        } else {
          print("⚠️ Response is not a valid list of objects");
        }
      } else {
        print("⚠️ Error response: ${response.statusCode}");
      }
    } catch (e) {
      print("❌ Error: $e");
    }
  }

  Future<void> searchGTCguarantee(inputPersonID) async {
    try {
      Map data = {
        "personID": profile.responseMember!.id,
      };

      final response1 = await AppApi.callAPIjwt("POST", AppUrl.GTC, data);
      final response2 = await AppApi.callAPIjwt("POST", AppUrl.BV, {});

      if (response1["status"] == 200 && response2["status"] == 200) {
        holdingTotal = response1["result"]["HoldingTotal"];
        BV = response2["result"]["BV"];
        update();
      } else {
        const GetSnackBar(
          title: "เกิดข้อผิดพลาด",
          message: "เกิดข้อผิดพลาด",
          duration: Duration(seconds: 3),
        );
      }
      update();
      return;
    } catch (e) {
      if (kDebugMode) {
        print("error getGCTinfo =>$e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  late int holdingTotal = 0;
  late String BV = "0.00";
  var GTCBalance = "0.00";
  Future<void> getGCTinfo(inputPersonID) async {
    try {
      Map data = {
        "personID": profile.responseMember!.id,
      };

      final response1 = await AppApi.callAPIjwt("POST", AppUrl.GTC, data);
      final response2 = await AppApi.callAPIjwt("POST", AppUrl.BV, {});

      if (response1["status"] == 200 && response2["status"] == 200) {
        holdingTotal = response1["result"]["HoldingTotal"];
        BV = response2["result"]["BV"];
        GTCBalance = (double.parse(BV) *
                double.parse(response1["result"]["HoldingTotal"].toString()))
            .toString();
        update();
      } else {
        const GetSnackBar(
          title: "เกิดข้อผิดพลาด",
          message: "เกิดข้อผิดพลาด",
          duration: Duration(seconds: 3),
        );
      }
      update();
      return;
    } catch (e) {
      if (kDebugMode) {
        print("error getGCTinfo =>$e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  MCU_Model? mcu_Model;
  double holdmcu = 0.0;
  double mcu = 0.0;

  Future<void> getMCUinfo(inputPersonID) async {
    try {
      Map data = {"personID": profile.responseMember!.id.toString()};
      final response1 = await AppApi.callAPIjwt("POST", AppUrl.MCU, data);
      final response2 = await AppApi.callAPIjwt("POST", AppUrl.HoldMCU, data);

      if (response1["status"] == 200 && response2["status"] == 200) {
        if (response2["result"]['mcu'].toString() == "null") {
          holdmcu = 0;
        } else {
          holdmcu = response2["result"]['mcu'];
        }
        if (response1["result"]['mcu'].toString() == "null") {
          mcu = 0;
        } else {
          mcu = response1["result"]['mcu'];
        }
        Map<String, dynamic> margeJson = {"mcu": mcu, "holdMCU": holdmcu};
        mcu_Model = MCU_Model.fromJson(margeJson);

        update();
        return;
      } else {
        const GetSnackBar(
          title: "เกิดข้อผิดพลาด",
          message: "เกิดข้อผิดพลาด",
          duration: Duration(seconds: 3),
        );
        update();
        return;
      }
    } catch (e) {
      if (kDebugMode) {
        print("error getMCUinfo1 =>$e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  RewardMCU_Model? rewardMCU_Model;
  Future<void> getRewardMCU() async {
    try {
      Map data = {"personID": profile.responseMember!.id};

      final response1 =
          await AppApi.callAPIjwt("POST", AppUrl.getRewardHoldMCU, data);

      final response2 =
          await AppApi.callAPIjwt("POST", AppUrl.getRewardMCU, data);

      if (response1["status"] == 200 && response2["status"] == 200) {
        Map<String, dynamic> margeJson = {
          "reward_holdmcu": response1["result"]["reward_mcu"].toDouble(),
          "reward_mcu": response2["result"]["reward_mcu"].toDouble()
        };
        rewardMCU_Model = RewardMCU_Model.fromJson(margeJson);
        update();
        return;
      } else if (response1["status"] == 404 && response2["status"] == 404) {
        Map<String, dynamic> margeJson = {
          "reward_holdmcu": double.parse("0.00"),
          "reward_mcu": double.parse("0.00"),
        };
        rewardMCU_Model = RewardMCU_Model.fromJson(margeJson);
        update();
        return;
      } else if (response1["status"] == 404 && response2["status"] == 200) {
        Map<String, dynamic> margeJson = {
          "reward_holdmcu": double.parse("0.00"),
          "reward_mcu": response2["result"]["reward_mcu"].toDouble(),
        };
        rewardMCU_Model = RewardMCU_Model.fromJson(margeJson);
        update();
        return;
      } else if (response1["status"] == 200 && response2["status"] == 404) {
        Map<String, dynamic> margeJson = {
          "reward_holdmcu": response1["result"]["reward_mcu"].toDouble(),
          "reward_mcu": double.parse("0.00"),
        };
        rewardMCU_Model = RewardMCU_Model.fromJson(margeJson);
        update();
        return;
      } else {
        const GetSnackBar(
          title: "เกิดข้อผิดพลาด",
          message: "เกิดข้อผิดพลาด",
          duration: Duration(seconds: 3),
        );
        update();
        return;
      }
    } catch (e) {
      if (kDebugMode) {
        print("error getMCUinfo1 =>$e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  var Guarantee = 0;
  var months = 0;
  var months1 = 0;
  var deductions = 0;
  var money_first = 0;
  var guarantee_money = 0;
  var guaranty_status = '';
  var interest = 0;
  var savingMoney5 = 0;
  var savingCU = 0;

  setGuarantee() async {
    try {
      Map data = {
        "datecheck": convertDateTime(taCtr.datetime, "DDB"),
        "personID": profile.responseMember!.id,
      };
      final response1 =
          await AppApi.callAPIjwt("POST", AppUrl.guaranteemoney, data);
      if (response1["status"] == 200) {
        if (response1.length > 0) {
          months = int.parse(response1["result"][0]["months"].toString());
          months1 = int.parse(response1["result"][0]["months1"].toString());
          deductions =
              int.parse(response1["result"][0]["deductions"].toString());
          money_first =
              int.parse(profile.responseMember!.money_first.toString());

          guarantee_money =
              int.parse(profile.responseMember!.guarantee_money.toString());

          guaranty_status = profile.responseMember!.guaranty_status!;

          if (interest == '' || interest == 'null' || interest == null) {
            interest = 0;
          }
          if (deductions == 'null' || deductions == '' || deductions == null) {
            deductions = 0;
          }

          if (savingMoney5 == 0) {
            if (guaranty_status.toString() == "null" ||
                guaranty_status == null) {
              savingCU = 0;
            }
            if (int.parse(months1.toString()) == 0 &&
                int.parse(deductions.toString()) > 0) {
              savingCU = (int.parse(months1.toString()) +
                  int.parse(interest.toString()));

              return savingCU;
            }
            if (savingMoney5 == 0) {
              if (int.parse(months.toString()) <= -1) {
                savingCU = int.parse(guarantee_money.toString());
              } else {
                savingCU = (int.parse(guarantee_money.toString()) +
                        int.parse(money_first.toString()) +
                        int.parse(interest.toString())) +
                    (((int.parse(months1.toString()) + 1) -
                            (int.parse(months.toString()) + 1)) *
                        int.parse(deductions.toString()));
              }
              if (money_first == "0" && deductions == "0" ||
                  months == "null" && months1 == 'null') {
                savingCU = 0;
              }
            }
          }
        }
      } else {
        const GetSnackBar(
          title: "เกิดข้อผิดพลาด",
          message: "เกิดข้อผิดพลาด",
          duration: Duration(seconds: 3),
        );
      }
      update();
    } catch (e) {
      if (kDebugMode) {
        print("error getMCUinfo2 =>$e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }
}
