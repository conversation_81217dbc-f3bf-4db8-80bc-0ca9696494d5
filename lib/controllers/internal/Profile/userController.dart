import 'package:get/get.dart';

class UserController extends GetxController {
  String selectedCode = "+66";
  String selectedCodeWithlike = "+66";
  List<Map<String, String>> phoneCodes = [
    {"name": "ui_selectth".tr, "code": "+66"},
    {"name": "ui_selectlao".tr, "code": "+865"},
    {"name": "ui_selectkm".tr, "code": "+855"},
    // Add more countries as needed
  ];
  var customerName = ''.obs;
  var customerDetails = ''.obs;


  void updateCustomerInfo(String name, String details) {
    customerName.value = name;
    customerDetails.value = details;
  }
}