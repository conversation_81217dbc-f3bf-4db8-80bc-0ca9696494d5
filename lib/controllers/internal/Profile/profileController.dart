import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mapp_ms24/controllers/internal/Tacontroller/Tacontroller.dart';
import 'package:mapp_ms24/controllers/internal/wealthController/wealthController.dart';
import 'package:mapp_ms24/controllers/utils/connect.dart';
import 'package:mapp_ms24/controllers/utils/url.dart';
import 'package:mapp_ms24/models/userModel/IncomeModel.dart';
import 'package:mapp_ms24/models/userModel/PurposeModel.dart';
import 'package:mapp_ms24/models/userModel/usermodel.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

class ProfileController extends GetxController {
  ResponseMember? responseMember;
  Income_Model? incomeModel;
  BirthdayModel? birthdayModel;
  Tacontroller? tacontroller;
  var likebalance = "0.0";
  var availableBalance = "0.0";
  var lockedBalance = "0.0";
  var tg_link = "".obs;

  oninit() {
    fetchTelegramLink();
  }

  Future<void> fetchTelegramLink() async {
    try {
      final response = await http.post(
        Uri.parse(
            'https://n8n-deploy.agilesoftgroup.com/webhook/mapp_MS24_get_telegram_group'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'id': responseMember?.id}),
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = jsonDecode(response.body);

        if (data['status'] == 200 &&
            data['result'] is List &&
            data['result'].isNotEmpty) {
          tg_link.value = data['result'][0]['invite_link'] ?? '';
        } else {
          print('No invite_link found or status != 200');
        }

        update();
      } else {
        print('API error: ${response.statusCode}');
      }
    } catch (e) {
      print('Fetch error: $e');
    }
  }

  var imageProfile = "";
  Future<void> loadImage() async {
    print("loadImage");
    try {
      print('Success!');
      final response = await http.get(
        Uri.parse('https://n8n-cpdg.agilesoftgroup.com/webhook/get-image-binary?id=${responseMember?.id}'),
      );
      print('Success!');

      // แปลง body เป็น JSON
      final jsonData = jsonDecode(response.body);

      // เช็ค success == true
      if (jsonData['success'] == true) {
        print('Success! ทำงานต่อที่นี่');

        // ทำงานต่อที่คุณต้องการ เช่น โหลดภาพจาก URL
        final imageUrl = 'https://linebotkeep-file.s3.ap-southeast-1.amazonaws.com/${responseMember?.id}_ms24.jpg';
        imageProfile = imageUrl;
        print(imageProfile);
        update();
      } else {
        final imageUrl = 'https://s3-ap-southeast-1.amazonaws.com/storage-pkg/icon/icon1548914956default.png';
        imageProfile = imageUrl;

        print('ไม่สำเร็จ หรือไม่มี success');
      }
    } catch (e) {
      print('Fetch error: $e');
    }
  }
  Future<ResponseMember?> setModelUser(jsonInput) async {
    try {
      responseMember = ResponseMember.fromJson(jsonInput);
    } catch (e) {
      if (kDebugMode) {
        print("error setModelUser =>$e");
      }
      await Sentry.captureException(e);
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }

    getPurpose();
    update();
  }

  Future<void> getIncome(inputPersonID) async {
    try {
      var now = DateTime.now();
      Map data = {
        "datecheck": now.toString(),
        "id": inputPersonID,
        "country": 'ไทย'
      };

      var res = await AppApi.callAPIjwt("POST", AppUrl.checkSalary, data);

      if (res["status"] == 200) {
        incomeModel = Income_Model.fromJson(res["result"]);
      } else {
        const GetSnackBar(
          title: "เกิดข้อผิดพลาด",
          message: "ไม่มีข้อมูลเงินเดือนของคุณ",
          duration: Duration(seconds: 3),
        );
      }
      update();
      return;
    } catch (e) {
      if (kDebugMode) {
        print("error getIncome =>$e");
      }
      await Sentry.captureException(e);

      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<void> getlikebalance() async {
    try {
      String url = 'https://new.likepoint.io/getBalanceByphoneNumber';
      Map map = {"phoneNumber": responseMember!.phone_like};
//    Map map = {"phoneNumber": "+66858802504"};
      final response = await postNormalWeb(url, map);
      if (response["status"].toString() == "200") {
        likebalance = response['balance'].toString();
        availableBalance = response['availableBalance'].toString();
        lockedBalance = response['lockedBalance'].toString();
      } else {
        const GetSnackBar(
          title: "เกิดข้อผิดพลาด",
          message: "ไม่สามารถค้นหาจำนวน likewallet ได้",
          duration: Duration(seconds: 3),
        );
      }
    } catch (e) {
      await Sentry.captureException(e);
    }
  }

  var pfs;
  Future<void> getPFS(inputPersonID) async {
    try {
      Map data = {"id": inputPersonID};

      final response1 = await AppApi.callAPIjwt("POST", AppUrl.checkPFS, data);
      if (response1["status"] == 200) {
        // incomeModel = Income_Model.fromJson(response1["result"]);
        pfs = response1["result"]["bonusTotal"];
      } else {
        pfs = "0";
      }
      update();
      return;
    } catch (e) {
      if (kDebugMode) {
        print("error getPFS =>$e");
      }
      await Sentry.captureException(e);
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  var likecredit;
  Future<void> getLikeCredit() async {
    try {
      String url = 'https://apicredit.prachakij.com/userBalance';
      Map data = {"address": responseMember!.phone_like};

      final response1 = await postNormalWeb2(url, data);

      if (response1["res_status"] == 200) {
        /// there have only 1 parameter
        likecredit = response1["balance"];
      } else {
        likecredit = 0.00;
      }

      update();
      return;
    } catch (e) {
      if (kDebugMode) {
        print("error getLikeCredit =>$e");
      }
      await Sentry.captureException(e);
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<void> getPurpose() async {
    try {
      Map data = {
        "job_position_id": responseMember!.job_position_id,
      };

      var response = await AppApi.callAPIjwt("POST", AppUrl.purpose, data);

      if (response["status"] == 200) {
        Map data = {
          "job_position_id": response["result"]["division_id"],
        };

        final response1 =
            await AppApi.callAPIjwt("POST", AppUrl.purposeTeam, data);

        if (response1["status"] == 200) {
          responseMember!.purpose_p = response1["result"];
        } else {
          const GetSnackBar(
            title: "เกิดข้อผิดพลาด",
            message: "เกิดข้อผิดพลาด",
            duration: Duration(seconds: 3),
          );
        }
      } else {
        const GetSnackBar(
          title: "เกิดข้อผิดพลาด",
          message: "เกิดข้อผิดพลาด",
          duration: Duration(seconds: 3),
        );
      }
      update();
      return;
    } catch (e) {
      if (kDebugMode) {
        print("error getPurpose =>$e");
      }
      await Sentry.captureException(e);
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  List campasArray = [];
  var months;
  var years;
  String? campas_person = "";
  String? campas_person2 = "";
  String? campas_person3 = "";
  String? campas_person4 = "";
  String? campas_person5 = "";
  String? campas_person6 = "";
  String? campas_person7 = "";
  String? campas_person8 = "";
  String? campas_person9 = "";
  String? campas_person10 = "";
  String? okr_person3 = "";
  String previousCampasPerson = ""; // เก็บข้อมูลก่อนหน้า

  Future<void> getOKR() async {
    DateTime c = DateTime.now(); // Current time
    months = DateFormat("M").format(c);
    years = DateFormat("yyyy").format(c);

    try {
      Map data = {
        "id": responseMember!.id,
        "month_plan": months.toString(),
        "year_3pass": years.toString()
      };

      var response =
          await AppApi.callAPIjwt("POST", AppUrl.OKRinformation, data);

      if (response["status"] == 200) {
        campasArray.clear();
        campas_person = "";

        List<String> newCampasData = [];

        if (response['result'][0]['campas_person'] != "null") {
          newCampasData.add(response['result'][0]['campas_person'].toString());
        }
        if (response['result'][0]['campas_person2'] != "null" &&
            response['result'][0]['campas_person2'] != "") {
          newCampasData.add(response['result'][0]['campas_person2'].toString());
        }
        if (response['result'][0]['okr_person3'] != "null" &&
            response['result'][0]['okr_person3'] != "") {
          newCampasData.add(response['result'][0]['okr_person3'].toString());
        }
        if (response['result'][0]['campas_person3'] != "null" &&
            response['result'][0]['campas_person3'] != "") {
          newCampasData.add(response['result'][0]['campas_person3'].toString());
        }
        if (response['result'][0]['campas_person4'] != "null" &&
            response['result'][0]['campas_person4'] != "") {
          newCampasData.add(response['result'][0]['campas_person4'].toString());
        }
        campasArray.addAll(newCampasData.toSet());

        if (campasArray.isNotEmpty) {
          for (var i = 0; i < campasArray.length; i++) {
            if (i == 0) {
              campas_person = "${i + 1}.${campasArray[i]}";
            } else {
              campas_person = "$campas_person\n${i + 1}.${campasArray[i]}";
            }
          }
          update();
        } else {
          campas_person = 'ไม่มีข้อมูล OKR';
        }
      } else {
        Get.snackbar(
          "เกิดข้อผิดพลาด",
          "ไม่มีข้อมูล OKR",
          duration: Duration(seconds: 3),
        );
      }

      update();
    } catch (e) {
      if (kDebugMode) {
        print("error getPurpose =>$e");
      }
      await Sentry.captureException(e);
      Get.snackbar(
        "ไม่มีข้อมูลOKR",
        "กรุณาเพิ่มข้อมูลOKR",
        duration: Duration(seconds: 3),
      );
    }
  }

  var date_benefitsresilient = "";
  var start_benefitsresilient = "0";
  var imgExBill = "";
  String? dateMonth = "";
  int dateMonths = 0;
  var OPD_Total = 0;
  var IPD_Total = 0;
  Future<void> configApp() async {
    try {
      Map data = {};
      final res = await AppApi.callAPIjwt("POST", AppUrl.ConfigApp, data);
      if (res["status"] == 200) {
        imgExBill = res["result"][0]['imgExBill'].toString();
        date_benefitsresilient =
            res["result"][0]['date_benefitsresilient'].toString();
        if (responseMember!.welfare_country == 'ไทย') {
          OPD_Total = int.parse(res["result"][0]['setnumOPD']);
          IPD_Total = int.parse(res["result"][0]['setnumIPD']);
        } else if (responseMember!.welfare_country == 'RPLCG' &&
            responseMember!.welfare_country == 'RPLCgr') {
          OPD_Total = int.parse(res["result"][0]['setnumOPD_RPLC']);
          IPD_Total = int.parse(res["result"][0]['setnumIPD_RPLC']);
        } else if (responseMember!.welfare_country == 'RAFCOG' &&
            responseMember!.welfare_country == 'RAFCOgr') {
          OPD_Total = int.parse(res["result"][0]['setnumOPD_RAFCO']);
          IPD_Total = int.parse(res["result"][0]['setnumIPD_RAFCO']);
        }
        update();
      } else {
        if (kDebugMode) {
          print("error configApp =>${res["message"]}");
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print("error configApp =>$e");
      }
      await Sentry.captureException(e);
    }
  }

  var bmi = "0";
  var bmiStatus = "_";
  Future<void> getBIMinfo() async {
    try {
      Map data = {
        "id": responseMember!.id,
      };

      final res = await AppApi.callAPIjwt("POST", AppUrl.healthBMI, data);
      if (res["status"] == 200) {
        bmi = res["result"]["BMI"];
        bmiStatus = res["result"]["obtained"];
      } else {
        const GetSnackBar(
          title: "เกิดข้อผิดพลาด",
          message: "เกิดข้อผิดพลาด",
          duration: Duration(seconds: 3),
        );
      }
      update();
    } catch (e) {
      if (kDebugMode) {
        print("error getBIMinfo =>$e");
      }
      await Sentry.captureException(e);
    }
  }

  String HBD = "";

  Future<void> getHBD(id) async {
    try {
      Map data = {
        "id": id,
      };

      final res = await AppApi.callAPIjwt("POST", AppUrl.BirthDay, data);

      if (res["status"] == 200) {
        birthdayModel = BirthdayModel.fromJson(res["result"]);
      } else {
        const GetSnackBar(
          title: "เกิดข้อผิดพลาด",
          message: "เกิดข้อผิดพลาด",
          duration: Duration(seconds: 3),
        );
      }
      update();
    } catch (e) {
      if (kDebugMode) {
        print("error getBIMinfo =>$e");
      }
      await Sentry.captureException(e);
    }
  }

  String? servicelife = "";

  Future<void> WorkExperience() async {
    try {
      if (responseMember!.employment_type == "สมาชิกประจำ") {
        //อายุงาน นับจากวันบรรจุ
        DateTime pastDate =
            DateTime.parse(responseMember!.date_added.toString());
        DateTime now = DateTime.now(); //เวลาปัจจุบัน

        Duration difference = now.difference(pastDate);
        double years = difference.inDays / 365.24;
        double months = (difference.inDays ~/ 30.44) % 12;
        double days = difference.inDays -
            (years.toInt() * 365.24) -
            (months.toInt() * 30.44);

        if (years.toInt() > 0) {
          servicelife = " ${years.toInt()} ปี";
        }
        if (months.toInt() > 0) {
          servicelife = servicelife.toString() + " ${months.toInt()} เดือน";
        }
        if (days.toInt() > 0) {
          servicelife = servicelife.toString() + " ${days.toInt()} วัน";
        }
      } else {
        servicelife = "ยังไม่ได้บรรจุ";
      }
    } catch (e) {
      await Sentry.captureException(e);
    }
  }
}
