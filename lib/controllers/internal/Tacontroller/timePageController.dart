import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/controllers/utils/connect.dart';
import 'package:mapp_ms24/controllers/utils/url.dart';
import 'package:mapp_ms24/models/userModel/daysWorkModel.dart';

class TimeController extends GetxController {
  TaScoreModel? taScoreModel;
  LostDayModelList? lostDayModel = LostDayModelList();
  ProfileController profile = Get.put(ProfileController());

  Future<void> getDataTaScore() async {
    try {
      Map data = {
        "id": profile.responseMember!.id,
      };

      final res = await AppApi.callAPIjwt("POST", AppUrl.Calculate, data);

      if (res["status"] == 200) {
        taScoreModel = TaScoreModel.fromJson(res["result"]);
      } else {
        Get.snackbar("เกิดข้อผิดพลาด", "เกิดข้อผิดพลาด");
      }

      update();
    } catch (e) {
      if (kDebugMode) {
        print("error getDataDaysWork =>$e");
      }
      Get.snackbar("เกิดข้อผิดพลาด", "เกิดข้อผิดพลาด");
    }
  }

  Future<void> getDataLostDay(inputId, country) async {
    try {
      Map data = {"id": inputId, "country": country};

      final res = await AppApi.callAPIjwt("POST", AppUrl.LostDay, data);
      if (res["status"] == 200) {
        lostDayModel = LostDayModelList.fromJson(res["result"]);
      } else {
        Get.snackbar("เกิดข้อผิดพลาด", "เกิดข้อผิดพลาด");
      }

      update();
    } catch (e) {
      if (kDebugMode) {
        print("error getDataDaysWork =>$e");
      }
      Get.snackbar("เกิดข้อผิดพลาด", "เกิดข้อผิดพลาด");
    }
  }
}
