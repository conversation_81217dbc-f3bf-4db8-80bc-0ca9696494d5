import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:location/location.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/controllers/utils/connect.dart';
import 'package:mapp_ms24/controllers/utils/loader.dart';
import 'package:mapp_ms24/controllers/utils/url.dart';
import 'package:mapp_ms24/models/questionModer.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/TA/TaScreen.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../views/screens/Widget/library.dart';

class Tacontroller extends GetxController {
  Question_Model? QuestionModel;

  RxString? id = "".obs;
  RxString? username = "".obs;
  RxString? company_management = "".obs;
  RxString? division_name = "".obs;
  RxString? purpose = "".obs;
  RxString? phone_like = "".obs;
  RxString? email = "".obs;
  RxString? full_name_th = "".obs;
  RxString? country = "".obs;
  var datetime = "00-00-0000T00:00:00";
  var datetime2 = DateTime.now().toString();
  var numdate = "0";
  LocationData? _startLocation;
  LocationData? startLocation;
  RxBool isUpdating = false.obs;
  RxBool taStatus = false.obs;
  ProfileController profile = Get.find<ProfileController>();
  int statusButtonMorning = 0;
  int statusButtonAfternoon = 0;
  int statusButtonGoodbye = 0;
  var num;
  bool check = true;
  var checkin = 0;
  TextEditingController? CauseLatenessController = TextEditingController();
  Future<void> updateTA(value) async {
    taStatus.value = value;
  }

  Future getsession() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();

    id!.value = profile.responseMember!.id!;
    username!.value = profile.responseMember!.username!;
    company_management!.value = profile.responseMember!.company_management!;
    division_name!.value = profile.responseMember!.company_management!;
    purpose!.value = profile.responseMember!.purpose!;
    phone_like!.value = profile.responseMember!.phone_like!;
    email!.value = profile.responseMember!.email!;
    full_name_th!.value = profile.responseMember!.full_name_th!;
    country!.value = profile.responseMember!.country!;

    id = id;
    username = username;
    company_management = company_management;
    division_name = division_name;
    purpose = purpose;
    phone_like = phone_like;
    email = email;
    full_name_th = full_name_th;
    country = country;
  }

  void insertTimeProcess(context) async {
    String os = kIsWeb ? "web" : Platform.operatingSystem;
    var ip = "**********";
    Map data = {
      "personID": profile.responseMember!.id,
      "timeDate": convertDateTime(datetime, "DTDB").toString(),
      "timeIN": convertDateTime(datetime, "TH").toString() +
          ":" +
          convertDateTime(datetime, "TM").toString(),
      "timeOut": "00:00",
      "timeINstat": "1",
      "device": os,
      "ipIN": ip,
      "Inby": "1",
      "gps": _startLocation!.latitude.toString() +
          "," +
          _startLocation!.longitude.toString(),
      "edit_user": profile.responseMember!.id,
      "details": "NULL",
    };

    final res = await AppApi.callAPIjwt("POST", AppUrl.insertTimeprocess, data);

    if (res["result"]["lastInsertId"].toString() != "") {
      statusButtonMorning = 1;
      if (!context.mounted) return;
      showAlertDialog(context, 'assets/ADD/TA_D.png', 'assets/ADD/TA_B.png',
          "คุณได้ลงเวลามาทำงาน เรียบร้อยแล้ว \nขอให้มีความสุขกับวันทำงานนะคะ");
      await sendTG_PKG(
          "ลงเวลาเรียบร้อย  📍Location : https://www.google.co.th/maps/place/" +
              _startLocation!.latitude.toString() +
              "," +
              _startLocation!.longitude.toString());
    } else {
      const GetSnackBar(
        title: "ลงเวลาไม่สำเร็จ",
        message: "ลงเวลาไม่สำเร็จ กรุณาลองใหม่อีกครั้ง",
        duration: Duration(seconds: 3),
      );
    }
  }

  void insertTimeProcessLate(context, CauseLateness) async {
    getTime(context);
    String os = kIsWeb ? "web" : Platform.operatingSystem;
    var ip = "**********";

    Map data = {
      "personID": profile.responseMember!.id,
      "timeDate": convertDateTime(datetime, "DTDB").toString(),
      "timeIN": convertDateTime(datetime, "TH").toString() +
          ":" +
          convertDateTime(datetime, "TM").toString(),
      "timeOut": "00:00",
      "timeINstat": "2",
      "device": os,
      "ipIN": ip,
      "Inby": "1",
      "gps": _startLocation!.latitude.toString() +
          "," +
          _startLocation!.longitude.toString(),
      "edit_user": profile.responseMember!.id,
      "details": CauseLateness,
    };

    final res = await AppApi.callAPIjwt("POST", AppUrl.insertTimeprocess, data);

    if (res["result"]["lastInsertId"].toString() != "") {
      statusButtonMorning = 1;
      showAlertDialog(context, 'assets/ADD/TA_D.png', 'assets/ADD/TA_B.png',
          "คุณได้ลงเวลาและบันทึกเหตุผล เรียบร้อยแล้ว \nขอให้มีความสุขกับวันทำงานนะคะ");
      await sendlinenotify(
          "ลงเวลาและบันทึกเหตุผล เรียบร้อย 📍Location : https://www.google.co.th/maps/place/" +
              _startLocation!.latitude.toString() +
              "," +
              _startLocation!.longitude.toString() +
              " | เหตุผลที่มาสาย : " +
              CauseLateness.toString());
      await sendTG_PKG(
          "ลงเวลาและบันทึกเหตุผล เรียบร้อย 📍Location : https://www.google.co.th/maps/place/" +
              _startLocation!.latitude.toString() +
              "," +
              _startLocation!.longitude.toString() +
              " | เหตุผลที่มาสาย : " +
              CauseLateness.toString());
    } else {
      const GetSnackBar(
        title: "ลงเวลาไม่สำเร็จ",
        message: "ลงเวลาไม่สำเร็จ กรุณาลองใหม่อีกครั้ง",
        duration: Duration(seconds: 3),
      );
    }
  }

  void updateTimeProcess(
      context, runID, timeOutStat, Outby, selectalert) async {
    String os = kIsWeb ? "web" : Platform.operatingSystem;
    var ip = "**********";
    Map data = {
      "runID": runID.toString(),
      "timeOut": convertDateTime(datetime, "TH").toString() +
          ":" +
          convertDateTime(datetime, "TM").toString(),
      "timeOutStat": timeOutStat.toString(),
      "ipOUT": ip.toString(),
      "device": os.toString(),
      "Outby": Outby.toString(),
      "gps": _startLocation!.latitude.toString() +
          "," +
          _startLocation!.longitude.toString(),
      "edit_user": profile.responseMember!.id.toString(),
    };
    final res = await AppApi.callAPIjwt("POST", AppUrl.checkOut, data);

    if (res["insertId"].toString() != "") {
      if (selectalert.toString() == "1") {
        showAlertDialog(
            context,
            'assets/ADD/Time_stamp_D.png',
            'assets/ADD/Time_stamp_B.png',
            "ท่านออกก่อนเวลา เวลาที่ออก " +
                convertDateTime(datetime, "TH").toString() +
                ":" +
                convertDateTime(datetime, "TM").toString() +
                ":" +
                convertDateTime(datetime, "TS").toString());
        await sendTG_PKG("ท่านออกก่อนเวลา เวลาที่ออก " +
            convertDateTime(datetime, "TH").toString() +
            ":" +
            convertDateTime(datetime, "TM").toString() +
            ":" +
            convertDateTime(datetime, "TS").toString() +
            "  📍Location : https://www.google.co.th/maps/place/" +
            _startLocation!.latitude.toString() +
            "," +
            _startLocation!.longitude.toString());
      } else {
        showAlertDialog(context, 'assets/ADD/TA_D.png', 'assets/ADD/TA_B.png',
            "คุณได้ลงเวลาเลิกงาน เรียบร้อยแล้ว  \n อย่าลืม! ปิดเครื่องใช้ไฟฟ้า และ \nปิดประตูก่อนกลับบ้าน ให้เรียบร้อย\n ขอให้เดินทางกลับโดยสวัสดิภาพ");

        await sendlinenotify(
            "ลงเวลาออกเรียบร้อย โปรดเดินทางกลับบ้านโดยสวัสดิภาพ \n อย่าลืม !! ปิดเครื่องใช้ไฟฟ้า ปิดประตู ก่อนกลับให้เรียบร้อยด้วยนะจ๊ะ  📍Location : https://www.google.co.th/maps/place/" +
                _startLocation!.latitude.toString() +
                "," +
                _startLocation!.longitude.toString());
        await sendTG_PKG(
            "ลงเวลาออกเรียบร้อย โปรดเดินทางกลับบ้านโดยสวัสดิภาพ \n อย่าลืม !! ปิดเครื่องใช้ไฟฟ้า ปิดประตู ก่อนกลับให้เรียบร้อยด้วยนะจ๊ะ  📍Location : https://www.google.co.th/maps/place/" +
                _startLocation!.latitude.toString() +
                "," +
                _startLocation!.longitude.toString());
      }
    } else {
      const GetSnackBar(
        title: "ลงเวลาไม่สำเร็จ",
        message: "ลงเวลาไม่สำเร็จ กรุณาลองใหม่อีกครั้ง",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future getCheckin(context) async {
    if (profile.responseMember!.id != "" &&
        profile.responseMember!.id != null &&
        profile.responseMember!.id != "null") {
      Map data = {
        "personID": profile.responseMember!.id,
        "timeDate": convertDateTime(datetime, "DDB").toString(),
      };
      final response1 =
          await AppApi.callAPIjwt("POST", AppUrl.searchCheckIn, data);

      if (response1["result"].length > 0) {
        statusButtonMorning = 1;
      } else {
        statusButtonMorning = 0;
        print("getCheckin2");
      }
    } else {
      const GetSnackBar(
        title: "รหัสสมาชิกหายไปจาก app MS",
        message: "รหัสสมาชิกหายไปจาก app MS กรุณา logout และ login อีกครั้ง",
        duration: Duration(seconds: 3),
      );
    }
  }

  getPerson(context) async {
    AppLoader.loader(context);
    if (profile.responseMember!.id != "" &&
        profile.responseMember!.id != null &&
        profile.responseMember!.id != "null") {
      Map data = {"personID": profile.responseMember!.id};
      final response1 =
          await AppApi.callAPIjwt("POST", AppUrl.searchPerson, data);
      if (response1["status"] == 200) {
        getTimeCard(context, response1["result"][0]["timeInType"]);
        AppLoader.dismiss(context);
      } else {
        AppLoader.dismiss(context);
        const GetSnackBar(
          title: "ชื่อผู้ใช้ไม่ถูกต้อง",
          message: "รหัสสมาชิกหายไปจาก app MS กรุณา logout และ login อีกครั้ง ",
          duration: Duration(seconds: 3),
        );
      }
    } else {
      const GetSnackBar(
        title: "รหัสสมาชิกหายไปจาก app MS",
        message: "รหัสสมาชิกหายไปจาก app MS กรุณา logout และ login อีกครั้ง",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future getTimeCard(context, int timeInType) async {
    if (profile.responseMember!.id != "" &&
        profile.responseMember!.id != null &&
        profile.responseMember!.id != "null") {
      Map data = {
        "timeInType": timeInType.toString(),
        "dayID": numdate.toString()
      };

      final res = await AppApi.callAPIjwt("POST", AppUrl.searchTimeCard, data);

      if (res.length > 0) {
        if (int.parse(convertDateTime(datetime, "TH")) < 12) {
          morning(context, res["result"][0]["timeIN"].toString(),
              res["result"][0]["work"].toString());
          AppLoader.dismiss(context);
        } else {
          goodbye(context, res["result"][0]["timeOUT"].toString());
          AppLoader.dismiss(context);
        }
        AppLoader.dismiss(context);
      } else {
        const GetSnackBar(
          title: "ชื่อผู้ใช้ไม่ถูกต้อง",
          message: "ชื่อผู้ใช้ไม่ถูกต้อง กรุณาลองใหม่อีกครั้ง",
          duration: Duration(seconds: 3),
        );
      }
    } else {
      const GetSnackBar(
        title: "เลขวันของสัปดาห์ว่าง",
        message:
            "เลขวันของสัปดาห์ว่าง กรุณากลับไปหน้า home แล้ว เข้ามาอีกครั้ง",
        duration: Duration(seconds: 3),
      );
    }
  }

  var checkinta = 0;
  Future morning(context, timeIN, work) async {
    getTime(context);
    if (profile.responseMember!.id != "" &&
        profile.responseMember!.id != null &&
        profile.responseMember!.id != "null") {
      Map data = {
        "personID": profile.responseMember!.id,
        "timeDate": convertDateTime(datetime, "DDB").toString()
      };
      final res = await AppApi.callAPIjwt("POST", AppUrl.searchCheckIn, data);
      if (res["result"].length > 0) {
        ErrorshowAlertDialog(context, 'ลงเวลาเช้าแล้ว', "ท่านลงเวลาไปแล้ว");
        sendlinenotify("ท่านลงเวลาไปแล้ว");
        await sendTG_PKG("ท่านลงเวลาไปแล้ว");
      } else {
        checkinta = 0;

        if (work.toString() == "Y") {
          if ((int.parse(convertDateTime(datetime, "TH")) <
                  int.parse(convertTimein(timeIN, "H"))) ||
              (int.parse(convertDateTime(datetime, "TH")) <=
                      int.parse(convertTimein(timeIN, "H"))) &&
                  (int.parse(convertDateTime(datetime, "TM")) <= 15) ||
              (int.parse(convertDateTime(datetime, "TH")) <=
                      int.parse(convertTimein(timeIN, "H"))) &&
                  (int.parse(convertDateTime(datetime, "TM")) <=
                      int.parse(convertTimein(timeIN, "M")))) {
            insertTimeProcess(context);
          } else if (((int.parse(convertDateTime(datetime, "TH")) ==
                      int.parse(convertTimein(timeIN, "H"))) &&
                  (int.parse(convertDateTime(datetime, "TM")) >
                      int.parse(convertTimein(timeIN, "M")))) ||
              ((int.parse(convertDateTime(datetime, "TH")) ==
                      int.parse(convertTimein(timeIN, "H")) + 1 &&
                  (int.parse(convertDateTime(datetime, "TM")) <= 30)))) {
            buildShow(context, CauseLatenessController!, insertTimeProcessLate);
          } else {
            ErrorshowAlertDialog(
                context, 'ไม่สามารถลงเวลาได้', "ท่านต้องทำใบลาตอนเช้า");
            // info(context, "ท่านต้องทำใบลาตอนเช้า");
            sendlinenotify("ท่านต้องทำใบลาตอนเช้า");
            await sendTG_PKG("ท่านต้องทำใบลาตอนเช้า");
          }
        }
      }
    } else {
      const GetSnackBar(
        title: "รหัสสมาชิกหายไปจาก app MS",
        message: "รหัสสมาชิกหายไปจาก app MS กรุณา logout และ login อีกครั้ง",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future goodbye(context, timeOUT) async {
    await getQuestion(context);
    if (profile.responseMember!.id != "" &&
        profile.responseMember!.id != null &&
        profile.responseMember!.id != "null") {
      Map data = {
        "personID": profile.responseMember!.id,
        "timeDate": convertDateTime(datetime, "DDB").toString()
      };

      final res = await AppApi.callAPIjwt("POST", AppUrl.searchCheckIn, data);
      if (res["result"].isNotEmpty) {
        String runID = res["result"][0]["runID"].toString();
        if ((int.parse(convertDateTime(datetime, "TH")) <
                int.parse(convertTimein(timeOUT, "H"))) ||
            ((int.parse(convertDateTime(datetime, "TH")) ==
                    int.parse(convertTimein(timeOUT, "H"))) &&
                (int.parse(convertDateTime(datetime, "TM")) <
                    int.parse(convertTimein(timeOUT, "M"))))) {
          buildShowQuery(
              context,
              CauseLatenessController!,
              () => updateTimeProcess(
                  context, res["result"][0]["runID"].toString(), 31, 2, 1),
              QuestionModel!.question,
              QuestionModel!.selection1,
              QuestionModel!.selection2,
              QuestionModel!.selection3,
              QuestionModel!.answer,
              runID,
              31,
              2,
              1);
        } else if (((int.parse(convertDateTime(datetime, "TH")) ==
                    int.parse(convertTimein(timeOUT, "H"))) &&
                (int.parse(convertDateTime(datetime, "TM")) >=
                    int.parse(convertTimein(timeOUT, "M")))) ||
            int.parse(convertDateTime(datetime, "TH")) >
                int.parse(convertTimein(timeOUT, "H"))) {
          buildShowQuery(
              context,
              CauseLatenessController!,
              () => updateTimeProcess(
                  context, res["result"][0]["runID"].toString(), 30, 1, 2),
              QuestionModel!.question,
              QuestionModel!.selection1,
              QuestionModel!.selection2,
              QuestionModel!.selection3,
              QuestionModel!.answer,
              runID,
              30,
              1,
              2);
        }
      } else {
        ErrorshowAlertDialog(
            context, 'ยังไม่ลงเวลา', "ท่านไม่ได้ลงเวลาตอนเช้า ให้ทำใบลา");
        sendlinenotify("ท่านไม่ได้ลงเวลาตอนเช้า ให้ทำใบลา");
        await sendTG_PKG("ท่านไม่ได้ลงเวลาตอนเช้า ให้ทำใบลา");
      }
    } else {
      const GetSnackBar(
        title: "รหัสสมาชิกหายไปจาก app MS",
        message: "รหัสสมาชิกหายไปจาก app MS กรุณา logout และ login อีกครั้ง",
        duration: Duration(seconds: 3),
      );
    }
  }

  var latemonth;
  Future getTime(context) async {
    try {
      Map body = {};
      final resGetTime = await AppApi.callAPIjwt('POST', AppUrl.getTime, body);
      if (resGetTime["status"] == 200) {
        datetime = resGetTime["result"]["date"];
        numdate = resGetTime["result"]["numdate"];
        latemonth = convertDateTime(datetime, "DD");
      } else {
        print("not get time");
      }
    } catch (e) {
      print(e);
    }
  }

  convertTimein(time, select) {
    var timesplit = time.split(":");
    if (select.toString() == "H") {
      return timesplit[0];
    } else {
      return timesplit[1];
    }
  }

  Future sendlinenotify(String message) async {
    if (kIsWeb) {
      return;
    }

    Map<String, String> data = {
      "token": profile.responseMember!.tokenLine.toString(),
      "message": message
    };

    try {
      final response =
          await AppApi.callAPIjwt("POST", AppUrl.sentlinenotify, data);
      var jsonResponse = json.decode(response);

      if (jsonResponse.isNotEmpty) {
        Fluttertoast.showToast(
            msg: "Line Notify sent successfully!",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.TOP,
            backgroundColor: Colors.green,
            textColor: Colors.black,
            fontSize: 16.0);
      } else {
        print('Failed to send Line Notify');
      }
    } catch (e) {
      print("Error while sending Line Notify: $e");
    }
  }

  Future sendTG_PKG(message) async {
    List<dynamic> data = [
      {
        "emp": profile.responseMember!.id,
        "fromdata": {"message": message}
      }
    ];

    AppHttps.postTG_PKG(AppHttps.sendTelegramPKG, data);
  }

  Future getlocation() async {
    Location location = new Location();

    bool _serviceEnabled;
    PermissionStatus _permissionGranted;
    LocationData _locationData;

    _serviceEnabled = await location.serviceEnabled();
    if (!_serviceEnabled) {
      _serviceEnabled = await location.requestService();
      if (!_serviceEnabled) {
        return;
      }
    }

    _permissionGranted = await location.hasPermission();
    if (_permissionGranted == PermissionStatus.denied) {
      _permissionGranted = await location.requestPermission();
      if (_permissionGranted != PermissionStatus.granted) {
        if (Platform.isIOS) {
        } else {
          return;
        }
      }
    }

    _locationData = await location.getLocation();

    _startLocation = _locationData;
    startLocation = _locationData;
  }

  Future getQuestion(context) async {
    try {
      Map body = {};
      final resGetQuestion =
          await AppApi.callAPIjwt('POST', AppUrl.question, body);
      if (resGetQuestion["status"] == 200) {
        QuestionModel = Question_Model.fromJson(resGetQuestion["result"]);
      } else {
        print("not getQuestion");
      }
    } catch (e) {
      print(e);
    }
  }
}
