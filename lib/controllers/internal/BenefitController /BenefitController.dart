import 'package:path/path.dart' as path;
import 'dart:convert';
import 'dart:io';
import 'dart:ui';
import 'package:camera/camera.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'dart:html' as html;
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/controllers/utils/connect.dart';
import 'package:mapp_ms24/controllers/utils/loader.dart';
import 'package:mapp_ms24/controllers/utils/url.dart';
import 'package:mapp_ms24/models/benefitsModel.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';
import 'package:mapp_ms24/controllers/internal/Tacontroller/Tacontroller.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/Benefit/ExBenForm.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/Benefit/pkgwelfarebasicconfirm.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/Benefit/pkgwelfareflexibleconfirm.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/Benefit/pkgwelfarehealthconfirm.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class BenefitController extends GetxController {
  WelfareModel? welfareModel;
  ProfileController profile = Get.find<ProfileController>();
  Tacontroller taCtr = Get.find<Tacontroller>();
  ExBenifitForm exBenifitForm = ExBenifitForm();
  RxInt Check = 0.obs;
  RxBool PD = false.obs;
  var isChecked = false.obs;

  RxString selectStatus = ''.obs;
  RxString SelectedCards = ''.obs;
  RxString selectedType = ''.obs;
  RxString selectedTime = ''.obs;
  RxString selectedDate = ''.obs;
  RxString selectedPatient = ''.obs;
  RxString InputSum = ''.obs;
  RxString InputDes = ''.obs;
  RxString InputRecove = ''.obs;
  RxString InputRocveDay = ''.obs;
  RxString InputSumDoc = ''.obs;
  RxString InputSumETC = ''.obs;
  RxString InputSumSurg = ''.obs;
  RxString InputMedicin = ''.obs;
  RxString InputHospital = ''.obs;
  RxString InputNameDoc = ''.obs;
  RxString InputCause = ''.obs;
  RxString InputMeddetails = ''.obs;
  RxString InputAccountNumber = ''.obs;
  RxString InputNote = ''.obs;
  final RxString imagePath = ''.obs; // Observes the image path
  final RxString imgName = ''.obs; // Observes the image name
  var currentIndex = 0.obs; // Observable variable

  final RxString base64Image = ''.obs; // Observes the base64-encoded image data

  TextEditingController NoteController = TextEditingController();
  TextEditingController AccountNumberController = TextEditingController();
  TextEditingController TotalExpensesController = TextEditingController();
  TextEditingController OperateController = TextEditingController();

  List dataHistoryHelp = [];
  double numhealthIPD = 0;
  double numhealthOPD = 0;
  double amounthealt = 0;
  var benefitsbalance = "0";
  var alreadyWithdrawn = "0";
  var start_benefitsresilient = "0";
  var databenefits = [];
  String? dateMonth = "";
  int dateMonths = 0;
  var responseWelfare;
  bool country = false;
  String? monetary = "";
  List dataLinkImage = [];
  DateTime dt = DateTime.now();
  bool check = false;
  int FlexibleWelfareStatus = 0;
  RxInt HealthWelfareStatus = 0.obs;
  List<RxBool> isExpandList = List.generate(100, (index) => false.obs);
  var accountNumber = ''.obs;
  File? _imageFile; // ตัวแปรสำหรับเก็บรูปภาพ
  File? _imageFile_name; // ตัวแปรสำหรับเก็บรูปภาพ
  final RxString withdrawalAmountText = '0'.obs;

  XFile? capturedImage;
  late CameraController cameraController;
  late List<CameraDescription> cameras;
  var isCameraInitialized = false.obs;
  var capturedImagePath = ''.obs;
  var capturedImageBase64 = ''.obs;
  var ImageBase64 = ''.obs;
  html.MediaStream? webCameraStream;
  final RxList<int> fileBytes = <int>[].obs;

  @override
  void onClose() {
    AccountNumberController.dispose();
    if (kIsWeb) {
      webCameraStream?.getTracks().forEach((track) => track.stop());
    } else {
      cameraController.dispose();
    }
    super.onClose();
  }

  void clearData() {
    withdrawalAmountText.value = '';
  }

  String calculateTotal() {
    int receiveDay = int.tryParse(InputRocveDay.value) ?? 0;
    int sumAmount = int.tryParse(InputSum.value) ?? 0;
    int total = receiveDay + sumAmount;

    return total.toString(); // คืนค่าเป็น String เพื่อแสดงผลใน UI
  }

  void updateInputSum(String value) {
    InputSum.value = value;
  }

  void updateInputDes(String value) {
    InputDes.value = value;
  }

  void updateInputRecove(String value) {
    InputRecove.value = value;
  }

  void updateInputRocveDay(String value) {
    InputRocveDay.value = value;
  }

  void updateInputSumDoc(String value) {
    InputSumDoc.value = value;
  }

  void updateAccountNumber(String value) {
    accountNumber(value);
  }

  void updateInputSumETC(String value) {
    InputSumETC.value = value;
  }

  void updateInputSumSurg(String value) {
    InputSumSurg.value = value;
  }

  void updateInputMedicin(String value) {
    InputMedicin.value = value;
  }

  void updateInputHospital(String value) {
    InputHospital.value = value;
  }

  void updateInputNameDoc(String value) {
    InputNameDoc.value = value;
  }

  void updateInputCause(String value) {
    InputCause.value = value;
  }

  void updateInputMeddetails(String value) {
    InputMeddetails.value = value;
  }

  void updateInputAccount(String value) {
    InputMeddetails.value = value;
  }

  RxList<RxString> testStatus = [
    'dp_marri'.tr.obs,
    'dp_monk'.tr.obs,
    'dp_monk15'.tr.obs,
    'dp_baby'.tr.obs,
    'dp_gran'.tr.obs,
    'dp_father'.tr.obs,
    'dp_advisor'.tr.obs
  ].obs;
  RxList<RxString> datas = ['dp_out'.tr.obs, 'dp_in'.tr.obs].obs;

  RxList<RxString> listcards = [''.obs, 'ยังไม่มีการ์ด'.obs].obs;
  RxList<RxString> listPatientType = ['ผู้ป่วยนอก'.obs, 'ผู้ป่วยใน'.obs].obs;
  RxList<RxString> testStatusMember = [
    'dp_select'.tr.obs,
    'dp_member'.tr.obs,
    'dp_test'.tr.obs,
    'dp_hire'.tr.obs,
    'dp_Advisor'.tr.obs
  ].obs;
  RxString reciveBasic = ''.obs;

  final Map<String, String> typeToAmount = {
    'งานมงคลสมรส': '2,000',
    'งานบวช': '1,000',
    'คลอดบุตร': '1,000',
    'งานบวช (บวชเกิน15วัน)': '1,000',
  };

  void CheckPD() {
    PD.value = !PD.value;
  }

  void updateselectStatus(String value) {
    selectStatus.value = value;
  }

  void updateselectedType(String value) {
    selectedType.value = value;
    if (typeToAmount.containsKey(selectedType.value)) {
      reciveBasic.value = typeToAmount[selectedType.value]!;
    } else if (selectedType.value ==
            'งานศพ สมาชิกกลุ่มที่ปรึกษารวมถึง บิดา - มารดา คู่สมรส และ บุตร ของที่ปรึกษา เสียชีวิต' &&
        profile.responseMember?.employment_type == 'ที่ปรึกษา') {
      reciveBasic.value = '11,000';
    } else if (selectedType.value ==
            'งานศพ เครือญาติสมาชิก (บิดา - มารดาของคู่สมรส และ พี่น้องร่วมบิดา-มารดาเดียวกัน ปู่ ย่า ตา ยาย )' &&
        (profile.responseMember?.employment_type == 'สมาชิกประจำ' ||
            profile.responseMember?.employment_type == 'ทดลองงาน' ||
            profile.responseMember?.employment_type == 'สมาชิกสัญญาจ้าง')) {
      reciveBasic.value = '2,000';
    } else if (selectedType.value ==
            'งานศพ ครอบครัวสมาชิก (บิดา - มารดา บุตร คู่สมรส )' &&
        (profile.responseMember?.employment_type == 'สมาชิกประจำ' ||
            profile.responseMember?.employment_type == 'ทดลองงาน')) {
      reciveBasic.value = '6,000';
    } else if (selectedType.value ==
            'งานศพ ครอบครัวสมาชิก (บิดา - มารดา บุตร คู่สมรส )' &&
        profile.responseMember?.employment_type == 'สมาชิกสัญญาจ้าง') {
      reciveBasic.value = '2,000';
    } else {
      reciveBasic.value = '0'; // ถ้าไม่มีตรงกับ type ใด ให้เป็น 0
    }
  }

  void updateSelectedCards(String value) {
    SelectedCards.value = value;
  }

  void updateSelectedPatientType(String value) {
    selectedPatient.value = value;
  }

  void updateselectedTime(String value) {
    try {
      // Parse the date string with the correct format
      DateTime parsedDate = DateFormat('dd/MM/yy').parse(value);
      // Format the parsed date back to a string if needed
      String formattedDate = DateFormat('dd/MM/yy').format(parsedDate);
      selectedTime.value = formattedDate;
    } catch (e) {
      print('Error parsing date: $e');
    }
  }

  void Updatereturnworkdate(String value) {
    try {
      // Parse the date string with the correct format
      DateTime parsedDate = DateFormat('dd/MM/yy').parse(value);
      // Format the parsed date back to a string if needed
      String formattedDate = DateFormat('dd/MM/yy').format(parsedDate);
      selectedDate.value = formattedDate;
    } catch (e) {
      print('Error parsing date: $e');
    }
  }

  void ClearData() {
    Check = 0.obs;
    selectStatus = ''.obs;
    selectedType = ''.obs;
    SelectedCards = ''.obs;
    selectedTime = ''.obs;
    selectedDate = ''.obs;
    selectedPatient = ''.obs;
    InputSum = ''.obs;
    InputDes = ''.obs;
    InputRecove = ''.obs;
    InputRocveDay = ''.obs;
    InputSumDoc = ''.obs;
    InputSumETC = ''.obs;
    InputSumSurg = ''.obs;
    InputMedicin = ''.obs;
    InputHospital = ''.obs;
    InputNameDoc = ''.obs;
    InputCause = ''.obs;
    InputMeddetails = ''.obs;
  }

  Future<void> information() async {
    if (profile.responseMember!.welfare_country == "ไทย") {
      country = true;
      monetary = "btn_details4".tr.toString();
    } else {
      country = false;
      if (profile.responseMember!.welfare_country == "RPLCG" &&
          profile.responseMember!.welfare_country == "RPLCgr") {
        monetary = "btn_details5".tr.toString();
      } else {
        monetary = "USD";
      }
    }
    update();
  }

  Future<void> loadhealtaBlance() async {
    try {
      Map data = {
        "datecheck": convertDateTime(taCtr.datetime, "DDB"),
        "id": profile.responseMember!.id
      };
      final res = await AppApi.callAPIjwt("POST", AppUrl.searchhealth, data);
      if (res["status"] == 200) {
        dataHistoryHelp = res["result"];
        for (var i = 0; i < res["result"].length; i++) {
          amounthealt = amounthealt + dataHistoryHelp[i]["reimbursable"];
          if (res["result"][i]["type"].toString() == "ผู้ป่วยนอก") {
            numhealthOPD = numhealthOPD +
                double.parse(res["result"][i]["reimbursable"].toString());
          } else {
            numhealthIPD = numhealthIPD +
                double.parse(res["result"][i]["reimbursable"].toString());
          }
        }
      } else {
        Get.snackbar("เกิดข้อผิดพลาด", "หายอดเงินไม่ได้");
      }

      update();
    } catch (e) {
      if (kDebugMode) {
        print("error getDataDaysWork =>$e");
      }
      Get.snackbar("เกิดข้อผิดพลาด", "เกิดข้อผิดพลาด");
    }
  }

  Future getImageCamara() async {
    var image = await ImagePicker()
        .pickImage(source: ImageSource.camera, maxHeight: 2500);

    File imageFile = new File(image!.path);
    String base64Image = base64Encode(imageFile.readAsBytesSync());
    // String fileName = image.path.split("/").last;
    _imageFile_name = File(image.name); // เก็บรูปภาพที่ถ่ายในตัวแปร

    imagePath.value = image.path.toString();
    imgName.value = path.basename(_imageFile_name!.path);
    uploadtoS3(base64Image);
  }

  var country1 = "";
  Future<void> getImageDirectory() async {
    try {
      // Open file picker for images
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        // Access file bytes and file name
        final file = result.files.first;
        fileBytes.value = file.bytes!;
        imgName.value = file.name;
        base64Image.value = base64Encode(file.bytes!);
      } else {
        Get.snackbar(
          'Info',
          'No image selected',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.yellow.withOpacity(0.7),
          colorText: Colors.black,
        );
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to pick image: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withOpacity(0.7),
        colorText: Colors.white,
      );
    }
  }

  Future<void> uploadtoS3(base64Image) async {
    try {
      Map data = {
        "name": "MappMS",
        "folder": "MappMS/img_Flexible_welfare",
        "image": base64Image
      };

      final res = await AppApi.callAPIjwt("POST", AppUrl.uploadS3, data);
      if (res["statusCode"].toString() == "200") {
        dataLinkImage.add(res["result"]["url"]["Location"].toString());
      } else {
        Get.snackbar("เกิดข้อผิดพลาด",
            "รูปภาพมีขนาดใหญ่เกิน กรุณาลดความละเอียดของไฟล์ภาพ");
      }

      update();
    } catch (e) {
      if (kDebugMode) {
        print("error welfare =>$e");
      }
      Get.snackbar("เกิดข้อผิดพลาด", "เกิดข้อผิดพลาด");
    }
  }

  //***********************สวัสดิการหยืดหยุ่น********************************
  double totalAlreadyWithdrawn = 0.0;
  Future<void> doreloadbenefits(context) async {
    try {
      Map data = {
        "id": profile.responseMember!.id,
        "country": profile.responseMember!.welfare_country
      };
      final res = await AppApi.callAPIjwt("POST", AppUrl.withdrawal, data);
      if (res["status"] == 200) {
        if (res["result"].length > 0) {
          databenefits = res["result"];
          for (var i = 0; i < res["result"].length; i++) {
            benefitsbalance =
                (double.parse(benefitsbalance) - res["result"][i]["summary"])
                    .toString();
            totalAlreadyWithdrawn += int.parse(alreadyWithdrawn);

            alreadyWithdrawn = res["result"][i]["summary"].toString();
          }
        }
      } else {
        benefitsbalance = double.parse(benefitsbalance).toString();
      }

      update();
    } catch (e) {
      if (kDebugMode) {
        print("error doreloadbenefits =>$e");
      }
      Get.snackbar("เกิดข้อผิดพลาด", "เกิดข้อผิดพลาด");
    }
  }

  var numIPD = "0";
  var numOPD = "0";

  seachConfigApp() async {
    Map data = {};

    final res = await AppApi.callAPIjwt("POST", AppUrl.ConfigApp, data);
    if (res.length > 0) {
      if (profile.responseMember!.welfare_country == "ไทย") {
        numIPD = res["result"][0]["setnumIPD"].toString();
        numOPD = res["result"][0]["setnumOPD"].toString();
      } else if (profile.responseMember!.welfare_country == "RPLCgr") {
        numIPD = res["result"][0]["setnumOPD_RPLC"].toString();
        numOPD = res["result"][0]["setnumIPD_RPLC"].toString();
      } else if (profile.responseMember!.welfare_country == "RPLCen") {
        numIPD = res["result"][0]["setnumOPD_RAFCO"].toString();
        numOPD = res["result"][0]["setnumIPD_RAFCO"].toString();
      }
      // numOPD = jsonResponse[0]["setnumOPD"].toString();
    } else {
      print('No data!');
    }
  }

  void resetExpandList() {
    for (int i = 0; i < isExpandList.length; i++) {
      isExpandList[i].value = false;
    }
  }

  Future<void> welfare(inputId) async {
    dateMonth = DateFormat("MM").format(dt);
    dateMonths = int.parse(dateMonth!);
    try {
      Map data = {
        "id": profile.responseMember!.id,
      };

      final res = await AppApi.callAPIjwt("POST", AppUrl.welfare, data);
      final res2 = res["result"][0];
      if (res["status"] == 200) {
        welfareModel = WelfareModel.fromJson(res2);
        if (welfareModel!.welfare_country == "ไทย") {
          if (welfareModel!.welfare_daythai != 'null') {
            start_benefitsresilient = welfareModel!.total_welfare.toString();
            benefitsbalance = welfareModel!.welfare_daythai.toString();
          } else {
            start_benefitsresilient = "0";
            benefitsbalance = "0";
          }
        } else if (welfareModel!.welfare_country == "RPLCG") {
          if (welfareModel!.welfare_daythai != 'null') {
            start_benefitsresilient = welfareModel!.total_welfare.toString();
            benefitsbalance = welfareModel!.welfare_dayrplc.toString();
          } else {
            start_benefitsresilient = "0";
            benefitsbalance = "0";
          }
        } else {
          if (dateMonths <= 06) {
            if (welfareModel!.welfare_firsthalf != 'null') {
              start_benefitsresilient =
                  welfareModel!.welfare_firsthalf.toString();
              benefitsbalance = welfareModel!.welfare_firsthalf.toString();
            } else {
              start_benefitsresilient = "0";
              benefitsbalance = "0";
            }
          } else {
            if (welfareModel!.welfare_firsthalf != "null") {
              start_benefitsresilient = welfareModel!.total_welfare.toString();
              benefitsbalance = welfareModel!.total_welfare.toString();
            } else {
              start_benefitsresilient = "0";
              benefitsbalance = "0";
            }
          }
        }
      } else {
        Get.snackbar("เกิดข้อผิดพลาด", "เกิดข้อผิดพลาด");
      }

      update();
    } catch (e) {
      if (kDebugMode) {}
      Get.snackbar("เกิดข้อผิดพลาด", "เกิดข้อผิดพลาด");
    }
  }

  Future<void> RecordWithdrawal(context, amount, dataLinkImagedata) async {
    try {
      AppLoader.loader(context);

      final Map<String, dynamic> data = {
        "id": profile.responseMember!.id.toString(),
        "email": profile.responseMember!.email.toString(),
        "bu": profile.responseMember!.company_management.toString(),
        "fullname": profile.responseMember!.full_name_th.toString(),
        "amount": amount,
        "image": dataLinkImagedata
      };

      final response = await http.post(
        Uri.parse(AppUrl.WelfareRecords),
        headers: {
          "Content-Type": "application/json; charset=utf-8",
          "Accept": "application/json"
        },
        body: jsonEncode(data),
      );

      final Map<String, dynamic> res = json.decode(response.body);

      if (res.containsKey("error") && res["error"] is Map) {
        final Map<String, dynamic>? errorData = res["error"];

        if (errorData != null && errorData.containsKey("lastInsertId")) {
          final int? lastInsertId =
              int.tryParse(errorData["lastInsertId"].toString());

          if (lastInsertId != null && lastInsertId > 0) {
            String message = """
          📣 *มีสมาชิกเบิกสวัสดิการยืดหยุ่น*
          📌 วันที่ : ${convertDateTime2(DateTime.now().toString(), "dd/MM/yyyy")}
          🌈 คุณ : ${profile.responseMember!.full_name_th}
          💡 BU : ${profile.responseMember!.company_management}
          💰 จำนวนที่เบิก : $amount
          ทีม PAO จะดำเนินการโอนเงินให้คุณภายใน 2 วันทำการ
          ***RAFCO  :: นำส่งเอกสารตัวจริงที่ทีมบัญชี เพื่อทำการเบิก ***
          🚨🚨🚨🚨🚨🚨🚨
          """;

            sendlinenotify(
                profile.responseMember!.tokenLine, message, '-1001585052138');

            updateToBCTresilient(
                lastInsertId.toString(), amount, dataLinkImagedata);

            AppLoader.dismiss(context);

            Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) =>
                      pkgWelfareFlexibleConfirm(withdraw: amount)),
            );
          } else {
            throw Exception('❌ Update failed! (lastInsertId = $lastInsertId)');
          }
        } else {
          throw Exception("❌ Response format is incorrect: $res");
        }
      } else {
        throw Exception("❌ Response format is incorrect: $res");
      }
    } catch (e) {
      _showToastOrSnackbar("เกิดข้อผิดพลาด", Colors.red);
      AppLoader.dismiss(context);
      showAlertDialog_wel(context, "ท่านไม่มีสวัสดิการยืดหยุ่นแล้ว");
    }
  }

  updateToBCTresilient(context, amount, dataLinkImagedata) async {
    try {
      AppLoader.loader(context);
      var dataLinkImagedata = "";
      if (dataLinkImage.toString() != "") {
        dataLinkImagedata = dataLinkImage
            .toString()
            .substring(1, dataLinkImage.toString().length - 1);
      }

      String url = 'https://us-central1-agslearn.cloudfunctions.net/ApiAppMS24';
      Map map = {
        "menu": "sendbenefitsresilientTObct",
        "id": profile.responseMember!.id.toString(),
        "email": profile.responseMember!.email.toString(),
        "bu": profile.responseMember!.company_management.toString(),
        "amount": amount,
        "image": dataLinkImagedata.toString(),
        "remark": "-",
        "namelike": profile.responseMember!.full_name_th.toString()
      };

      final response = await apiRequest(url, map);
      var jsonResponse = json.decode(response);
      if (jsonResponse["status"].toString() == "ok") {
        AppLoader.dismiss(context);
      } else {
        Fluttertoast.showToast(
            msg: "update fail!",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.TOP,
            backgroundColor: Colors.red,
            textColor: Colors.black,
            fontSize: 16.0);
      }
    } catch (e) {
      if (kDebugMode) {
        print("error updateToBCT =>$e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  //***********************สวัสดิการหยืดหยุ่น********************************

  //***********************สวัสดิการสุขภาพ********************************

  Future<void> WithdrawHealthBenefits(BuildContext context) async {
    AppLoader.loader(context);
    try {
      var dataLinkImagedataHealth =
          dataLinkImage.toString().replaceAll(RegExp(r'^\[|\]$'), '');

      final Map<String, dynamic> data = {
        "menu": "insertbenefitshealth",
        "id": profile.responseMember!.id.toString(),
        "email": profile.responseMember!.email.toString(),
        "bu": profile.responseMember!.company_management.toString(),
        "type": selectedPatient.value.toString(),
        "amountsurgery": InputRocveDay.value.toString().isEmpty
            ? '0'
            : InputRocveDay.value.toString(),
        "fullname": profile.responseMember!.full_name_th.toString(),
        "amount":
            InputSum.value.toString().isEmpty ? '0' : InputSum.value.toString(),
        "image": dataLinkImagedataHealth.isEmpty ? '-' : dataLinkImagedataHealth
      };

      final response = await http.post(
        Uri.parse(AppUrl.WelfareRecords),
        body: jsonEncode(data),
        headers: {
          "Content-Type": "application/json; charset=utf-8",
          "Accept": "application/json"
        },
      );

      updateToBCThealth(context);

      bool isUpdated = await updateToBCThealth(context);
      if (isUpdated) {
        String message = """
  📣 *มีสมาชิกเบิกประกันสุขภาพ*
  🏥 ประเภทการเคลม : ${selectedPatient.value}
  📌 วันที่ : ${convertDateTime2(DateTime.now().toString(), "dd/MM/yyyy")}
  🌈 คุณ : ${profile.responseMember!.full_name_th}
  💡 BU : ${profile.responseMember!.company_management}
  📍 เข้าตรวจสอบข้อมูลที่ link
  BCT PAO : ประกันสุขภาพ V.2.0 https://goo.gl/22QbT9
  🚑 🚑 🚑 🚑 🚑 🚑 🚑
  """;

        sendlinenotify(
            profile.responseMember!.tokenLine, message, '-**********');

        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => pkgwelfarehealthConfirm()),
        );

        AppLoader.dismiss(context);
        showAlertDialog_wel(context, "คีย์เบิกสวัสดิการสุขภาพเรียบร้อย");
      } else {
        AppLoader.dismiss(context);
        showAlertDialog_wel(context, "ท่านไม่สามารถเบิกสวัสดิการสุขภาพ");
        throw Exception('❌ Update failed!');
      }
      AppLoader.dismiss(context);
    } catch (e) {
      print("❌ Error: $e");

      Get.snackbar("เกิดข้อผิดพลาด", "update fail!");
      AppLoader.dismiss(context);
      showAlertDialog_wel(context, "ไม่สามารถคีย์เข้า BCT ได้");
    }
  }

  Future<bool> updateToBCThealth(BuildContext context) async {
    try {
      AppLoader.loader(context);

      var dataLinkImagedata = "";
      if (dataLinkImage.toString().isNotEmpty) {
        dataLinkImagedata =
            dataLinkImage.toString().replaceAll(RegExp(r'^\[|\]$'), '');
      }

      String url = 'https://us-central1-agslearn.cloudfunctions.net/ApiAppMS24';
      Map<String, dynamic> map = {
        "menu": "sendwelfarehealthTObct",
        "email": profile.responseMember!.email.toString(),
        "id": profile.responseMember!.id.toString(),
        "bu": profile.responseMember!.company_management.toString(),
        "type": selectedPatient.value.toString(),
        "amount": InputSum.value.toString(),
        "amountroom": "0",
        "amountday": "0",
        "amountdoctor": "0",
        "amountother": "0",
        "amountsurgery": InputRocveDay.value.toString(),
        "amountmedication": "0",
        "namehospital": "-",
        "namedoctor": "-",
        "becursemodify": "-",
        "medicine": "-",
        "image": dataLinkImagedata,
        "namelike": profile.responseMember!.full_name_th.toString()
      };

      final response = await http.post(
        Uri.parse(url),
        headers: {"Content-Type": "application/json"},
        body: jsonEncode(map),
      );

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);

        if (jsonResponse["status"].toString() == "ok") {
          AppLoader.dismiss(context);
          return true;
        }
      }

      AppLoader.dismiss(context);
      return false;
    } catch (e) {
      AppLoader.dismiss(context);
      return false;
    }
  }

  void _showToastOrSnackbar(String message, Color color) {
    if (kIsWeb) {
      Get.snackbar("แจ้งเตือน", message,
          backgroundColor: color, colorText: Colors.white);
    } else {
      Fluttertoast.showToast(
        msg: message,
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.TOP,
        backgroundColor: color,
        textColor: Colors.white,
        fontSize: 16.0,
      );
    }
  }
  //***********************สวัสดิการสุขภาพ********************************

  //***********************สวัสดิการพื้นฐาน********************************
  updateToBCTbasic(context, datework, selectwelfare, bankController,
      noteController, _datemonkController, _value) async {
    try {
      AppLoader.loader(context);

      var dataLinkImagedata = "";
      if (dataLinkImage.toString().isNotEmpty) {
        dataLinkImagedata = dataLinkImage
            .toString()
            .substring(1, dataLinkImage.toString().length - 1);
      }

      final eventDate = datework is RxString ? datework.value : datework;
      final chooseWelfare =
          selectwelfare is RxString ? selectwelfare.value : selectwelfare;
      final note =
          noteController is RxString ? noteController.value : noteController;
      final ordainedLeave = _datemonkController is RxString
          ? _datemonkController.value
          : _datemonkController;
      final noCard = _value is RxString ? _value.value : _value;

      Map map = {
        "menu": "sendwelfarebasicTObct",
        "email": profile.responseMember!.email.toString(),
        "id": profile.responseMember!.id.toString(),
        "bu": profile.responseMember!.company_management.toString(),
        "status_member": profile.responseMember!.employment_type.toString(),
        "namelike": profile.responseMember!.full_name_th.toString(),
        "event_Date": eventDate,
        "choose_welfare": chooseWelfare,
        "account_number": bankController,
        "note": note,
        "ordained_leave": ordainedLeave,
        "no_card": noCard,
        "image": dataLinkImagedata,
        "phoneLike": profile.responseMember!.phone_like.toString(),
      };

      updateToBCThealth(context);
      // ✅ เช็คว่า API ส่ง `error` กลับมาหรือไม่

      bool isUpdated = await updateToBCThealth(context);

      if (isUpdated) {
        var message = "\n📣 *มีสมาชิกเบิกสวัสดิการพื้นฐาน*";
        message += "\n สวัสดิการ : " + (chooseWelfare.toString());
        message += "\n📌วันที่ : " +
            convertDateTime2(DateTime.now().toString(), "dd/MM/YYYY");
        message +=
            "\n🌈 คุณ : " + profile.responseMember!.full_name_th.toString();
        message += "\n💡 BU : " +
            profile.responseMember!.company_management.toString();
        message += "\n📍 เข้าตรวจสอบข้อมูลที่ link";
        message +=
            "\nเบิกสวัสดิการต่างๆพื้นฐานของสมาชิก PKG https://bit.ly/2RFfvOI";
        message += "\nหลังจากที่คีย์ข้อมูล";
        message += "\nเบิกสวัสดิการสวัสดิการพื้นฐาน";
        message += "\nหากไม่มีการแจ้งกลับให้แก้ไขข้อมูลจากทีม PAO";
        message += "\nจะมีการดำเนินการโอนเงินให้";
        message += "\n👉🏽ภายใน 2 วันทำการ👈🏽";
        message += "\n***RAFCO  :: นำส่งเอกสารตัวจริงที่ทีมบัญชี ด้วยค่ะ ***";
        message += "\n🚨🚨🚨🚨🚨🚨🚨";

        sendlinenotify(
            profile.responseMember!.tokenLine, message, '-1001585052138');

        AppLoader.dismiss(context);
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => pkgwelfarebasicconfirm()),
        );
      } else {
        Fluttertoast.showToast(
          msg: "update fail!",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.TOP,
          backgroundColor: Colors.red,
          textColor: Colors.black,
          fontSize: 16.0,
        );
        AppLoader.dismiss(context);
      }
    } catch (e) {
      if (kDebugMode) {
        print("error updateToBCT => $e");
      }
      GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด: $e",
        duration: const Duration(seconds: 3),
      );
      AppLoader.dismiss(context);
      showAlertDialog_wel(context, 'ท่านไม่สามารถเบิกสวัสดิการพื้นฐานได้');
    }
  }

  sendlinenotify(tokenLine, message, tokenbu) async {
    List<dynamic> data = [
      {
        "emp": profile.responseMember!.id,
        "fromdata": {"message": message}
      }
    ];
    List<dynamic> dataBU = [
      {
        "emp": tokenbu,
        "fromdata": {"message": message}
      }
    ];
    await AppHttps.postTG_PKG(AppHttps.sendTelegramPKG, data);
    await AppHttps.postTG_PGH(AppHttps.sendTelegramPKG, dataBU);

    String url =
        'https://mhmo3nnbr5.execute-api.ap-southeast-1.amazonaws.com/latest';
    Map map = {
      "menu": "sentlinenotify",
      "token": tokenLine.toString(),
      "message": message.toString()
    };

    final response = await apiRequest(url, map);
    var jsonResponse = json.decode(response);
    if (jsonResponse.length > 0) {
      Fluttertoast.showToast(
          msg: "ส่ง line notify เรียบร้อย",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.TOP,
          backgroundColor: Colors.green,
          textColor: Colors.black,
          fontSize: 16.0);
    } else {
      print('not send line notify');
    }
  }

  Future<void> checkAndRequestCameraPermission() async {
    PermissionStatus status = await Permission.camera.request();
    if (status.isGranted) {
    } else {
      print("Camera permission denied");
    }
  }

  // เช็คและขอ permission สำหรับ Storage
  Future<void> checkAndRequestStoragePermission() async {
    PermissionStatus status = await Permission.storage.request();
    if (status.isGranted) {
    } else {
      print("Storage permission denied");
    }
  }

  Future<void> getImageCamera() async {
    await checkAndRequestCameraPermission();

    final ImagePicker _picker = ImagePicker();
    final XFile? image = await _picker.pickImage(source: ImageSource.camera);
    if (image != null) {
      imagePath.value = image.path; // อัปเดต imagePath
      int lastSlashIndex = image.path.lastIndexOf('/');
      imgName.value = imagePath.substring(lastSlashIndex + 1);

      imagePath.refresh();
    } else {
      print("ไม่สามารถเลือกภาพจาก Gallery ได้");
    }
  }

  void cleardata() {
    imagePath.value = "";
    imgName.value = "";
    imagePath.refresh();
    selectedPatient.value = "";
    InputRocveDay.value = '0';
    InputSum.value = '0';
    reciveBasic.value = '0';
  }

  Future<void> openCamera() async {
    try {
      final html.MediaStream stream = await html.window.navigator.mediaDevices!
          .getUserMedia({'video': true});

      final html.VideoElement videoElement = html.VideoElement()
        ..srcObject = stream
        ..autoplay = true;

      html.document.body!.append(videoElement);
    } catch (e) {
      print("Error accessing camera: $e");
    }
  }

  Future<void> initializeCamera() async {
    if (kIsWeb) {
      await _initializeWebCamera();
    } else {
      await _initializeMobileCamera();
    }
  }

  Future<void> _initializeWebCamera() async {
    try {
      // ใช้ HTML5 MediaStream API สำหรับ Web
      webCameraStream = await html.window.navigator.mediaDevices!
          .getUserMedia({'video': true});

      // สร้าง `<video>` element
      final html.VideoElement videoElement = html.VideoElement()
        ..srcObject = webCameraStream
        ..autoplay = true;

      // แสดง `<video>` element บนหน้าเว็บ
      html.document.body!.append(videoElement);

      isCameraInitialized.value = true;
    } catch (e) {
      print("Error initializing web camera: $e");
    }
  }

  Future<void> _initializeMobileCamera() async {
    try {
      final cameras = await availableCameras();
      if (cameras.isNotEmpty) {
        cameraController = CameraController(
          cameras[0],
          ResolutionPreset.high,
        );
        await cameraController.initialize();
        isCameraInitialized.value = true;
      } else {
        throw Exception("No cameras available");
      }
    } catch (e) {
      print("Error initializing mobile camera: $e");
    }
  }

  Future<void> captureImage() async {
    if (kIsWeb) {
      await _captureImageWeb();
    } else {
      await _captureImageMobile();
    }
  }

  Future<void> _captureImageWeb() async {
    try {
      final html.CanvasElement canvas = html.CanvasElement(
        width: 640,
        height: 480,
      );

      final html.VideoElement videoElement = html.document
          .getElementsByTagName('video')
          .first as html.VideoElement;

      final context = canvas.context2D;
      context.drawImage(videoElement, 0, 0);

      capturedImageBase64.value = canvas.toDataUrl().split(',').last;
    } catch (e) {
      print("Error capturing image on Web: $e");
    }
  }

  Future<void> _captureImageMobile() async {
    try {
      if (cameraController.value.isInitialized) {
        final XFile image = await cameraController.takePicture();
        final bytes = await File(image.path).readAsBytes();
        capturedImageBase64.value = base64Encode(bytes);
      } else {
        throw Exception("Camera is not initialized on Mobile");
      }
    } catch (e) {
      print("Error capturing image on Mobile: $e");
    }
  }

  Future<void> uploadDirectory() async {
    try {
      final ImagePicker picker = ImagePicker();
      XFile? image = await picker.pickImage(source: ImageSource.camera);

      if (image != null) {
        _imageFile = File(image.path); // เก็บรูปภาพที่ถ่ายในตัวแปร
        _imageFile_name = File(image.name); // เก็บรูปภาพที่ถ่ายในตัวแปร

        imagePath.value = image.path.toString();
        imgName.value = path.basename(_imageFile_name!.path);
      } else {
        print('User canceled the camera');
      }
    } catch (e) {
      print('Error capturing image: $e');
    }
  }

  Future<TextEditingValue> formatEditUpdateAsync(
      TextEditingValue oldValue, TextEditingValue newValue) async {
    String cleaned = newValue.text.replaceAll(RegExp(r'[^0-9]'), '');
    String formatted = '';
    for (int i = 0; i < cleaned.length; i++) {
      if (i == 3 || i == 4 || i == 10) {
        formatted += '-';
      }
      formatted += cleaned[i];
    }

    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }
}

class BankAccountFormatter {
  String formatAccountNumber(String input) {
    String cleaned = input.replaceAll(RegExp(r'[^0-9]'), '');
    String formatted = '';
    for (int i = 0; i < cleaned.length; i++) {
      if (i == 3 || i == 4 || i == 10) {
        formatted += '-';
      }
      formatted += cleaned[i];
    }
    return formatted;
  }
}
