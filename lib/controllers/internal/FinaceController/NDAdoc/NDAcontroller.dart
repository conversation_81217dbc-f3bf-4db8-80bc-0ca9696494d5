import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/controllers/internal/Tacontroller/Tacontroller.dart';
import 'package:mapp_ms24/controllers/utils/connect.dart';
import 'package:mapp_ms24/controllers/utils/url.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';

class NDAcontroller extends GetxController {
  ProfileController profile = new ProfileController();
  Tacontroller ta = new Tacontroller();
  var linkNDA = "";

  Future<void> createPdfNDA(
      id, thprefix, full_name_th, full_name_en, date_in) async {
    String spreadsheets_range = "";
    if (profile.responseMember!.company_management.toString() == "RAFCOG") {
      if (profile.responseMember!.type_NDA == "RAFCO_RPTN") {
        spreadsheets_range =
            "P_สัญญาไม่เปิดเผยข้อมูลที่เป็นความลับ_RAFCO_RPTN!A1:G";
      } else if (profile.responseMember!.type_NDA == "RAFCO_AIIL") {
        spreadsheets_range =
            "P_สัญญาไม่เปิดเผยข้อมูลที่เป็นความลับ_RAFCO_AIIL!A1:G";
      }
    } else if (profile.responseMember!.company_management.toString() ==
        "RPLCG") {
      if (profile.responseMember!.type_NDA == "RPLC_RUAM") {
        spreadsheets_range =
            "P_สัญญาไม่เปิดเผยข้อมูลที่เป็นความลับ_RPLC_Ruam!A1:G";
      } else if (profile.responseMember!.type_NDA == "RPLC_RAFCO") {
        spreadsheets_range =
            "P_สัญญาไม่เปิดเผยข้อมูลที่เป็นความลับ_RPLC_RAFCO!A1:G";
      }
    } else {
      spreadsheets_range = "P_สัญญาไม่เปิดเผยข้อมูลที่เป็นความลับ!A1:G";
    }
    try {
      Map data = {
        "url":
            "http://webapp.prachakij.com:8080/BCT/Print_document_by_form_Spreadsheet_NDA.jsp?spreadsheets_key=1oy1_NhCkvRbdtbC-vSH7B7_M7FRvU327yl5qB_1bIic&spreadsheets_range=" +
                spreadsheets_range.toString() +
                "&member_name_th=" +
                thprefix.toString() +
                "" +
                profile.responseMember!.full_name_th.toString() +
                "&member_name_en=" +
                profile.responseMember!.full_name_en.toString().toUpperCase() +
                "&date_in_work=" +
                date_in.split("T")[0].toString() +
                "&member_id=" +
                id.toString(),
        "name": profile.responseMember!.id.toString() +
            "_" +
            profile.responseMember!.full_name_th.toString(),
        'bucket': 'url2img-pdf',
        'path': 'pdf/MS24/PDF_NDA',
        'format': 'A4',
        'pageRanges': 'all',
        'rout': 'urltopdf'
      };

      final response =
          await AppApi.callAPIjwt("POST", AppUrl.OldsendTelegramPKG, data);
      if (response.toString() != "") {
        // updateLinkPdfNDA(response.toString());
      } else {
        const GetSnackBar(
          title: "เกิดข้อผิดพลาด",
          message: "ไม่สามารถแปลงเอกสารเป็น PDF ได้!",
          duration: Duration(seconds: 3),
        );
      }
      update();
      return;
    } catch (e) {
      if (kDebugMode) {
        print("error getCarReg =>$e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<void> updateLinkPdfNDA(dataLinkNDA) async {
    try {
      Map data = {
        "id": profile.responseMember!.id.toString(),
        "email": profile.responseMember!.email.toString(),
        "linkNDA": dataLinkNDA.toString(),
        "dateUpdate": convertDateTime(ta.datetime, "dd-MM-YYYY2")
      };
      final response =
          await AppApi.callAPIjwt("POST", AppUrl.updateLinkPdfNDA, data);
      if (response["statusCode"].toString() == "200") {
        linkNDA = dataLinkNDA.toString();
        createFileOfPdfUrl().then((f) {
          remotePDFpath = f.path;
        });
      } else {
        const GetSnackBar(
          title: "เกิดข้อผิดพลาด",
          message: "ไม่สามารถแปลงเอกสารเป็น PDF ได้!",
          duration: Duration(seconds: 3),
        );
      }
      update();
      return;
    } catch (e) {
      if (kDebugMode) {
        print("error updateLinkPdfNDA =>$e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  String remotePDFpath = "".obs();
  Future<File> createFileOfPdfUrl() async {
    Completer<File> completer = Completer();
    try {
      final url = linkNDA.toString();
      final filename = url.substring(url.lastIndexOf("/") + 1);
      var request = await HttpClient().getUrl(Uri.parse(url));
      var response = await request.close();
      var bytes = await consolidateHttpClientResponseBytes(response);
      var value = GetStorage().read('key');

// สำหรับเขียนค่า
      GetStorage().write('key', value);
      // var dir = await getApplicationDocumentsDirectory();
      File file = File("${value.path}/$filename");

      await file.writeAsBytes(bytes, flush: true);
      completer.complete(file);
    } catch (e) {
      throw Exception('Error parsing asset file!');
    }

    return completer.future;
  }
}
