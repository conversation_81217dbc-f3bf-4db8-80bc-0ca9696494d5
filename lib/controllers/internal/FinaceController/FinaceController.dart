import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:ui' as ui;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:mapp_ms24/controllers/internal/BenefitController%20/BenefitController.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/controllers/internal/Tacontroller/Tacontroller.dart';
import 'package:mapp_ms24/controllers/utils/connect.dart';
import 'package:mapp_ms24/controllers/utils/url.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';
import 'package:path_provider/path_provider.dart';

class FinaceController extends GetxController {
  Tacontroller taCrt = Get.find<Tacontroller>();
  ProfileController profile = Get.find<ProfileController>();
  BenefitController benefit = Get.find<BenefitController>();
  var dataNitrosignUser = [].obs;

  String spreadsheets_range = "";
  int statusHaveSign = 0;
  int statusAgreeSign = 0;
  var linkSign = "";
  int statusAlertAgreeSign = 0;
  int countSignList = 0;
  final currentpage = 0;
  int countFINSignList = 0;
  int countHRSignList = 0;
  int countPMSSignList = 0;
  RxInt currentIndex = 0.obs;
  List<RxBool> isExpandList = List.generate(100, (index) => false.obs);
  var isLoading = false.obs;
  Future<void> updatePage(int newIndex) async {
    currentIndex.value = newIndex; // กำหนดค่าใหม่ให้กับ currentIndex
    currentIndex.refresh();
    if (currentIndex.value == 0) {
      await allLoad("null");
    } else if (currentIndex.value == 1) {
      await allLoad("HR");
    } else if (currentIndex.value == 2) {
      await allLoad("PMS");
    }
    update();
  }

  void toggleExpandState(int index) {
    isExpandList[index].value = !isExpandList[index].value;
    isExpandList[index].refresh();
  }

  void resetExpandList() {
    for (int i = 0; i < isExpandList.length; i++) {
      isExpandList[i].value = false;
      isExpandList[i].refresh();
    }
  }

  RxList<Map<String, RxString>> testFinance = [
    {'status': 'done'.obs, 'name': 'testFinance'.obs, 'type': 'Saraly'.obs},
    {'status': 'pending'.obs, 'name': 'testFinance'.obs, 'type': 'Saraly'.obs},
  ].obs;
  RxList<Map<String, RxString>> testHR = [
    {'status': 'done'.obs, 'name': 'testHR'.obs, 'type': 'Saraly'.obs},
    {'status': 'pending'.obs, 'name': 'testHR'.obs, 'type': 'Saraly'.obs},
  ].obs;
  RxList<Map<String, RxString>> testPMS = [
    {'status': 'done'.obs, 'name': 'testPMS'.obs, 'type': 'Saraly'.obs},
    {'status': 'pending'.obs, 'name': 'testPMS'.obs, 'type': 'Saraly'.obs},
  ].obs;

  void initState() {
    initializePage();
  }

  void initializePage() async {
    await allLoad("null");
  }

  allLoad(type) async {
    await loadNitrosign(type);
  }

  Future<void> loadNitrosign(String type) async {
    dataNitrosignUser.clear();
    isLoading.value = true;
    try {
      Map<String, dynamic> data = {"id": profile.responseMember!.id};

      final response =
          await AppApi.callAPIjwt("POST", AppUrl.searchNitrosign, data);

      if (response["status"].toString() == "200") {
        var resultList = List<Map<String, dynamic>>.from(response["result"]);

        dataNitrosignUser.addAll(resultList);

        dataNitrosignUser.assignAll(dataNitrosignUser.where((x) {
          if (type == "null") {
            return x["doc_type"] == null;
          } else {
            return x["doc_type"] == type;
          }
        }).toList());

        if (dataNitrosignUser.isNotEmpty) {
          print('Filtered dataNitrosignUser: $dataNitrosignUser');
          print('dataNitrosignUser.length: ${dataNitrosignUser.length}');
        } else {
          print('dataNitrosignUser.length: ${dataNitrosignUser.length}');
          print('No matching doc_type found or doc_type is null');
          Get.snackbar("No data", "ไม่พบข้อมูลที่ตรงกัน");
        }
      } else {
        Get.snackbar("Error", "เกิดข้อผิดพลาด");
      }
    } catch (e) {
      if (kDebugMode) {
        print("error loadNitrosign =>$e");
      }
      Get.snackbar("Error", "เกิดข้อผิดพลาด");
    } finally {
      isLoading.value = false;
    }
  }

  String linkPDFS3 = '';
  Future<void> loadNumbersign() async {
    try {
      Map data = {"id": profile.responseMember!.id};
      final response =
          await AppApi.callAPIjwt("POST", AppUrl.searchNitrosign, data);

      if (response["status"].toString() == "200") {
        countSignList = response["result"].length;
        countFINSignList = response["result"]
            .where((x) => x["doc_type"] == null)
            .toList()
            .length;
        countHRSignList = response["result"]
            .where((x) => x["doc_type"] == 'HR')
            .toList()
            .length;
        countPMSSignList = response["result"]
            .where((x) => x["doc_type"] == 'PMS')
            .toList()
            .length;
      }
      update();
      return;
    } catch (e) {
      if (kDebugMode) {
        print("error getCarReg =>$e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

//********************************** load PDF **************************************

  uploadtoS3(bytes) async {
    String base64Image = base64Encode(bytes);
    String url =
        'https://mhmo3nnbr5.execute-api.ap-southeast-1.amazonaws.com/latest/img_Signature_NDA';
    Map map = {
      "name": "MappMS",
      "id": profile.responseMember!.id.toString(),
      "typeSig": "_pdf_presign",
      "folder": "MappMS/img_pdf_presign",
      "image": base64Image
    };

    final response = await apiRequest(url, map);
    var jsonResponse = json.decode(response);
    if (jsonResponse["statusCode"].toString() == "200") {
      linkPDFS3 = (jsonResponse["result"]["url"]["Location"].toString());
//      saveImageProfile(jsonResponse["result"]["url"]["Location"].toString());
    } else {
      Fluttertoast.showToast(
          msg: "upload fail!",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.TOP,
          backgroundColor: Colors.red,
          textColor: Colors.black,
          fontSize: 16.0);
    }
  }

  Future<void> loadAgreeSign() async {
    try {
      Map data = {"id": profile.responseMember!.id.toString()};

      final res = await AppApi.callAPIjwt("POST", AppUrl.sendCEW, data);

      if (res["status"] == 200) {
        if (res["result"][0]["link_NDA_Signature"].toString() != "" &&
            res["result"][0]["link_NDA_Signature"].toString() != "null") {
          statusHaveSign = 1;
          linkSign =
              "https://mapp-app.s3.ap-southeast-1.amazonaws.com/MappMS/img_Signature_NDA/" +
                  profile.responseMember!.id.toString() +
                  "_e_signature1.png";
        }
        if (res["result"][0]["statusAgreeSign"].toString() == "Y") {
          statusAgreeSign = 1;
        }
      } else {
        print('No data!!');
      }
    } catch (e) {
      print(e);
    }
  }

  Future<void> updateAgreeSign() async {
    try {
      Map data = {"id": profile.responseMember!.id.toString()};

      final res = await AppApi.callAPIjwt("POST", AppUrl.sendCEW, data);

      if (res["status"] == 200) {
        statusAlertAgreeSign = 0;
      } else {
        print('No data!!');
      }
    } catch (e) {
      print(e);
    }
  }

  reportDocument(dataPDF, detailController, email) async {
    String url =
        "http://devdev.prachakij.com/paper/SIGN/ajax_save_reject_api.php?file_id=${dataPDF["refer_id"]}&remark=${detailController}&sign_Email=$email&sign_personID=${profile.responseMember!.id}";
    final response = await getRequestPHP(url);
    if (response == 'Y') {
      Get.toNamed('home');
      // Navigator.pushReplacement(context,
      //     MaterialPageRoute(builder: (context) => home(0)));
      // hideLoadingDialog();
    } else {
      Fluttertoast.showToast(
          msg: "report fail!",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.TOP,
          backgroundColor: Colors.red,
          textColor: Colors.black,
          fontSize: 16.0);
    }
  }

//*********************************** NDA ********************************

  var bucket = "";
  var typeSig = "";

  uploadtoS3_Signature_NDA(base64Image, statusCreateSignature) async {
    if (statusCreateSignature.toString() == "1") {
      bucket = "MappMS/img_Signature_NDA_P1";
      typeSig = "_e_signatureP1";
    } else if (statusCreateSignature.toString() == "2") {
      bucket = "MappMS/img_Signature_NDA_P2";
      typeSig = "_e_signatureP2";
    } else {
      bucket = "MappMS/img_Signature_NDA";
      typeSig = "_e_signature1";
    }

    Map data = {
      "name": "MappMS",
      "id": profile.responseMember!.id.toString(),
      "typeSig": typeSig.toString(),
      "folder": bucket.toString(),
      "image": base64Image
    };

    final jsonResponse = await AppApi.apiRequest(AppUrl.SignatureNDA, data);
    var res = json.decode(jsonResponse);

    if (res["statusCode"].toString() == "200") {
      if (statusCreateSignature.toString() == "0") {
        createPdfNDA(
            profile.responseMember!.id,
            profile.responseMember!.thprefix,
            profile.responseMember!.full_name_th,
            profile.responseMember!.full_name_en,
            profile.responseMember!.datein);
      } else {
        Get.back();
      }
    } else {
      Fluttertoast.showToast(
          msg: "upload fail!",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.TOP,
          backgroundColor: Colors.red,
          textColor: Colors.black,
          fontSize: 16.0);
    }
  }

  var linkNDA = "";
  createPdfNDA(id, thprefix, full_name_th, full_name_en, date_in) async {
    String spreadsheets_range = "";
    spreadsheets_range = "P_สัญญาไม่เปิดเผยข้อมูลที่เป็นความลับ!A1:G";

    // if (profile.responseMember!.company_management.toString() == "RAFCOG") {
    //   spreadsheets_range = "P_สัญญาไม่เปิดเผยข้อมูลที่เป็นความลับ_RAFCO!A1:G";
    // } else if (profile.responseMember!.company_management.toString() ==
    //     "RPLCG") {
    //   spreadsheets_range = "P_สัญญาไม่เปิดเผยข้อมูลที่เป็นความลับ_RPLC!A1:G";
    // } else {
    //   spreadsheets_range = "P_สัญญาไม่เปิดเผยข้อมูลที่เป็นความลับ!A1:G";
    // }

    // print("http://webapp.prachakij.com:8080/BCT/Print_document_by_form_Spreadsheet_NDA.jsp?spreadsheets_key=1oy1_NhCkvRbdtbC-vSH7B7_M7FRvU327yl5qB_1bIic&spreadsheets_range=" +
    //     spreadsheets_range.toString() +
    //     "&member_name_th=" +
    //     thprefix.toString() +
    //     "" +
    //     full_name_th.toString() +
    //     "&member_name_en=" +
    //     full_name_en.toString().toUpperCase() +
    //     "&date_in_work=" +
    //     date_in.split("T")[0].toString() +
    //     "&member_id=" +
    //     id.toString());
    Map map = {
      "url":
      // "http://webapp.prachakij.com:8080/BCT/Print_document_by_form_Spreadsheet_NDA.jsp?spreadsheets_key=1oy1_NhCkvRbdtbC-vSH7B7_M7FRvU327yl5qB_1bIic&spreadsheets_range=P_สัญญาไม่เปิดเผยข้อมูลที่เป็นความลับ!A1:G&member_name_th=นายชานนท์ สุทธะนะ&member_name_en=CHANON SUTTANA&date_in_work=2022-01-21&member_id=6501006",
      "http://webapp.prachakij.com:8080/BCT/Print_document_by_form_Spreadsheet_NDA.jsp?spreadsheets_key=1oy1_NhCkvRbdtbC-vSH7B7_M7FRvU327yl5qB_1bIic&spreadsheets_range=" +
          spreadsheets_range.toString() +
          "&member_name_th=" +
          thprefix.toString() +
          "" +
          full_name_th.toString() +
          "&member_name_en=" +
          full_name_en.toString().toUpperCase() +
          "&date_in_work=" +
          date_in.split("T")[0].toString() +
          "&member_id=" +
          id.toString(),
      "name": id.toString() + "_" + full_name_th.toString(),
      'bucket': 'url2img-pdf',
      'path': 'pdf/MS24/PDF_NDA_Signature',
      'format': 'A4',
      'pageRanges': 'all',
      'rout': 'urltopdf'
    };
    final response = await apiPost(Uri.parse(AppHttps.OldsendTelegramPKG), map);

    // var jsonResponse = json.decode(response);
    if (response.toString() != "") {
      print("testttt : $map");
      linkNDA = response.toString();
      print(response.toString());

      updateLinkPdfNDASuccess(response.toString());
    } else {
      print('ไม่สามารถแปลงเอกสารเป็น PDF ได้!');
    }
  }

  updateLinkPdfNDASuccess(dataLinkNDA) async {
    Map data = {
      "id": profile.responseMember!.id.toString(),
      "email": profile.responseMember!.email.toString(),
      "linkNDA": dataLinkNDA.toString(),
      "dateUpdate": convertDateTime(taCrt.datetime, "dd-MM-YYYY2")
    };

    final res = await AppApi.callAPIjwt("POST", AppUrl.updateLinkPdfNDA, data);

    if (res["statusCode"].toString() == "200") {
      linkNDA = dataLinkNDA.toString();
      createFileOfPdfUrl().then((f) {
        remotePDFpath = f.path;
      });
    } else {
      Fluttertoast.showToast(
          msg: res["msg"].toString(),
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.TOP,
          backgroundColor: Colors.red,
          textColor: Colors.black,
          fontSize: 16.0);
    }
  }

  String pdfName = "";
  String remotePDFpath = "";

  Future<File> createFileOfPdfUrl() async {
    Completer<File> completer = Completer();
    try {
      final url = linkNDA.toString();
      final filename = url.substring(url.lastIndexOf("/") + 1);
      pdfName = filename;
      var request = await HttpClient().getUrl(Uri.parse(url));
      var response = await request.close();
      var bytes = await consolidateHttpClientResponseBytes(response);
      var dir = await getApplicationDocumentsDirectory();
      File file = File("${dir.path}/$filename");

      await file.writeAsBytes(bytes, flush: true);
      completer.complete(file);
    } catch (e) {
      throw Exception('Error parsing asset file!');
    }

    return completer.future;
  }

  Future<void> loadLinkNDA(statusADM) async {
    var company = "";
    var typeNDA = "";

    Map data = {
      "id": profile.responseMember!.id.toString(),
    };

    final response =
        await AppApi.callAPIjwt("POST", AppUrl.searchNDAPPP7, data);
    if (response["status"].toString() == "200") {
      company = profile.responseMember!.company_management.toString();
      typeNDA = profile.responseMember!.type_NDA.toString();
      if (response["result"][0]["statusBoardCommittee"].toString() == "Y") {
        statusADM = 1;
      }
      if (response["result"][0]["link_NDA"].toString() == "null" ||
          response["result"][0]["link_NDA"].toString() == "") {
        createPdfNDA(
            profile.responseMember!.id,
            profile.responseMember!.thprefix,
            profile.responseMember!.full_name_th,
            profile.responseMember!.full_name_en,
            profile.responseMember!.datein);
      } else {
        if (response["result"][0]["link_NDA_Signature"].toString() == "null" ||
            response["result"][0]["link_NDA_Signature"].toString() == "") {
          linkNDA = response["result"][0]["link_NDA"].toString();
        } else {
          linkNDA = response["result"][0]["link_NDA_Signature"].toString();
        }
        createFileOfPdfUrl().then((f) {
          remotePDFpath = f.path;
        });
      }
    } else {
      Fluttertoast.showToast(
          msg: response["msg"].toString(),
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.TOP,
          backgroundColor: Colors.red,
          textColor: Colors.black,
          fontSize: 16.0);
    }
  }
}
