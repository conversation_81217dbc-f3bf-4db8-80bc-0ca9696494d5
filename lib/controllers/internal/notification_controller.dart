import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';

// import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';

import '../../models/userModel/usermodel.dart';
import '../utils/connect.dart';
import '../utils/url.dart';
import 'Profile/profileController.dart';
import 'securestorage_controller.dart';
import 'package:http/http.dart' as http;

class NotifyController extends GetxController {
  RxString title = "".obs;
  RxString body = "".obs;
  var messageTitle = "";
  var messageBody = "";
  List dataLinkImage = [];
  final TextEditingController subjectController = TextEditingController();
  final TextEditingController detailController = TextEditingController();
  String uploadedImageUrl = '';
  File? selectedImage;
  ResponseMember? responseMember;
  List<String> uploadedImageUrls = [];

  ProfileController profile = Get.put(ProfileController());
  @override

  // void onInit() {
  //   SecureStorage secureStorage = Get.put(SecureStorage());
  //   // NotificationService notificationService = NotificationService();
  //   // notificationService.initializePlatformNotifications();
  //   // clearNotificationWhenOpenApp();
  //   //
  //   // FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);
  //   // FirebaseMessaging firebaseMessaging = FirebaseMessaging.instance;
  //   // ignore: unused_local_variable
  //   // var fcmToken = firebaseMessaging.getToken().then(
  //   //         (value) => secureStorage.writeSecureData("fcmToken", value.toString()));
  //
  //   FirebaseMessaging.onMessage.listen((RemoteMessage event) {
  //     mapRemoteMessage(event, sendNotification);
  //   });
  //
  //   FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage event) async {
  //     mapRemoteMessage(event, sendNotification);
  //   });
  //
  //   super.onInit();
  // }
  Future<void> sendFeedbackToN8N() async {
    const url = 'https://n8n-deploy.agilesoftgroup.com/webhook/Mapp_ms24_send_suggest';

    final subject = subjectController.text.trim();
    final detail = detailController.text.trim();

    String imageUrl = '';

    try {


      final payload = {
        "subject": subject,
        "detail": detail,
        "ีuser_data": '${profile.responseMember?.id} ${profile.responseMember?.full_name_th} ',
        "image_url": uploadedImageUrls.isEmpty ? "-" : uploadedImageUrls,
        "form":"MS24Beta web"
      };

      final res = await http.post(
        Uri.parse(url),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(payload),
      );

      // ✅ ปิด loading dialog
      Get.back();

      if (res.statusCode == 200) {
        Get.snackbar('✅ ส่งสำเร็จ', 'ข้อมูลถูกส่งไปยัง Telegram แล้ว');
      } else {
        Get.snackbar('❌ ส่งไม่สำเร็จ', 'รหัส ${res.statusCode}: ${res.body}');
      }
    } catch (e) {
      Get.back(); // ✅ ปิด loading dialog กรณี error
      Get.snackbar('❌ เกิดข้อผิดพลาด', e.toString());
    }
  }

  Future<String> uploadtoS3(String base64Image) async {
    try {
      final url = Uri.parse(AppUrl.uploadS3);
      final body = {
        "name": "MappMS",
        "folder": "MappMS/img_Feedback/",
        "image": base64Image
      };

      final response = await http.post(
        url,
        headers: {"Content-Type": "application/json"},
        body: jsonEncode(body),
      );

      final res = jsonDecode(response.body);
      if (res["statusCode"] == 200) {
        final uploadedUrl = res["result"]["url"]["Location"].toString();
        return uploadedUrl;
      } else {
        Get.snackbar("อัปโหลดไม่สำเร็จ", "status != 200");
      }
    } catch (e) {
      print("❌ Upload Error: $e");
      Get.snackbar("เกิดข้อผิดพลาด", e.toString());
    }

    return '';
  }


//title and body of notification
  void setMessage(RxString recieveTitle, RxString recieveBody) {
    title = recieveTitle;
    body = recieveBody;

    update();
  }
}

// void mapRemoteMessage(
//     RemoteMessage event,
//     FutureOr<void> Function(String, String) sendMethod,
//     ) async {
//   var mess = event.data;
//
//   if (mess["out"].toString() == "false") {
//     if (mess['description'] != "") {
//       var tokenname = mess['description'].toString().split("|")[0];
//       var name = mess['description'].toString().split("|")[1];
//       var refcode = mess['description'].toString().split("|")[2];
//       sendMethod(mess["type"] == "1" ? "Receive" : "Announcement",
//           "You got ${mess['amount']} $tokenname $name $refcode");
//     } else {
//       // sendMethod(mess["type"] == "1" ? "Receive" : "Announcement",
//       //     "You got ${mess['amount']} ${await getCurrencySymbolByTokenAddress(mess['token_address'])} from ${mess['from_address']}");
//     }
//   }
// }

void sendNotification(String title, String body) {
  AwesomeNotifications().initialize('', [
    NotificationChannel(
        channelGroupKey: 'basic_tests',
        channelKey: 'basic_channel',
        channelName: 'Basic notifications',
        channelDescription: 'Notification channel for basic tests',
        // defaultColor: Color(0xFF9D50DD),

        // ledColor: Colors.white,
        // defaultColor: const Colors,
        importance: NotificationImportance.High),
  ]);
  AwesomeNotifications().isNotificationAllowed().then((isAllowed) {
    if (!isAllowed) {
      AwesomeNotifications().requestPermissionToSendNotifications();
    }
  });

  AwesomeNotifications().createNotification(
      content: NotificationContent(
          id: 1, channelKey: "basic_channel", title: title, body: body));
}

Future<void> sendLocalFlutterNotification(String title, String body) async {
  FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
  FlutterLocalNotificationsPlugin();

  final id = DateTime.now().millisecondsSinceEpoch ~/ 1000;

  AndroidNotificationDetails android = const AndroidNotificationDetails(
    'channel id',
    'channel NAME',
    channelDescription: 'CHANNEL DESCRIPTION',
    importance: Importance.max,
    priority: Priority.high,
    ticker: 'ticker',
    icon: '@mipmap/ic_launcher',
    channelShowBadge: false,
  );

  NotificationDetails platform = NotificationDetails(
      android: android, iOS: DarwinNotificationDetails(badgeNumber: 0));

  await flutterLocalNotificationsPlugin.show(
    id,
    title,
    body,
    platform,
  );
}

// Future<void> firebaseMessagingBackgroundHandler(RemoteMessage event) async {
//   mapRemoteMessage(event, sendLocalFlutterNotification);
//   // var mess = event.data;
//   //
//   // FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
//   //     FlutterLocalNotificationsPlugin();
//   //
//   // final id = DateTime.now().millisecondsSinceEpoch ~/ 1000;
//   //
//   // var android = const AndroidNotificationDetails('channel id', 'channel NAME',
//   //     channelDescription: 'CHANNEL DESCRIPTION',
//   //     importance: Importance.max,
//   //     priority: Priority.high,
//   //     ticker: 'ticker',
//   //     icon: '@mipmap/ic_launcher');
//   //
//   // var platform = NotificationDetails(android: android);
//   //
//   // if (mess['description'] == "") {
//   //   await flutterLocalNotificationsPlugin.show(
//   //       id,
//   //       mess["type"] == "1" ? "Receive" : "Announcement",
//   //       "You got ${mess['amount']} from anyone",
//   //       platform,
//   //       payload: mess["txID"]);
//   // } else {
//   //   var tokenname = mess['description'].toString().split("|")[0];
//   //   var name = mess['description'].toString().split("|")[1];
//   //   var refcode = mess['description'].toString().split("|")[2];
//   //   await flutterLocalNotificationsPlugin.show(
//   //       id,
//   //       mess["type"] == "1" ? "Receive" : "Announcement",
//   //       "You got ${mess['amount']} $tokenname from $name($refcode)",
//   //       platform,
//   //       payload: mess["description"]);
//   // }
// }

void clearNotificationWhenOpenApp() {
  FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
  FlutterLocalNotificationsPlugin();
  flutterLocalNotificationsPlugin.cancelAll();
}
