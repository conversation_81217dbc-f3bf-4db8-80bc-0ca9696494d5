import 'package:get/get.dart';

class WorkIssuseController extends GetxController{

  RxBool expand = false.obs;
  RxList<Map<String, RxString>> testWork = [
    {'ID': '6008124'.obs, 'name': 'กฤตนัน วัฒนศฤงฆาร'.obs,'type':'Sicked'.obs,'date':'8/11/2023'.obs,'note':'test'.obs},
    {'ID': '6008124'.obs, 'name': 'กฤตนัน วัฒนศฤงฆาร'.obs,'type':'Sicked'.obs,'date':'8/11/2023'.obs,'note':'test'.obs},

  ].obs;
  RxList<Map<String, RxString>> testWorkGive = [
    {'ID': '6008124'.obs, 'name': 'กฤตนัน วัฒนศฤงฆาร'.obs,'type':'Sicked'.obs,'date':'8/11/2023'.obs,'note':'test'.obs},
    {'ID': '6008124'.obs, 'name': 'กฤตนัน วัฒนศฤงฆาร'.obs,'type':'Sicked'.obs,'date':'8/11/2023'.obs,'note':'test'.obs},
    {'ID': '6008124'.obs, 'name': 'กฤตนัน วัฒนศฤงฆาร'.obs,'type':'Sicked'.obs,'date':'8/11/2023'.obs,'note':'test'.obs},
    {'ID': '6008124'.obs, 'name': 'กฤตนัน วัฒนศฤงฆาร'.obs,'type':'Sicked'.obs,'date':'8/11/2023'.obs,'note':'test'.obs},
  ].obs;
  List<RxBool> isExpandList = List.generate(100, (index) => false.obs);

  // Method to toggle expand state for a specific index
  void toggleExpandState(int index) {
    isExpandList[index].value = !isExpandList[index].value;
  }
  void resetExpandList() {
    for (int i = 0; i < isExpandList.length; i++) {
      isExpandList[i].value = false;
    }
  }
  List dataGroupID = [];

  nameGroupID(groupID) {
    var result = "";
    for (var i = 0; i < dataGroupID.length; i++) {
      if (groupID.toString() == dataGroupID[i]["ID"].toString()) {
        result = dataGroupID[i]["NAME"].toString();
        break;
      }
    }
    return result;
  }

}