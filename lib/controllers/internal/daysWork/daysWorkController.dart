import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:mapp_ms24/controllers/utils/connect.dart';
import 'package:mapp_ms24/controllers/utils/url.dart';
import 'package:mapp_ms24/models/userModel/daysOfMonthWorkModel.dart';
import 'package:mapp_ms24/models/userModel/daysWorkModel.dart';

class DaysWorkController extends GetxController {
  DaysWorkModel? daysWorkModel;
  DaysOfmonthWorkModel? daysOfmonthWorkModel;

  Future<void> getDataDaysWork(inputId) async {
    try {
      Map data = {"id": inputId};

      final res = await AppApi.callAPIjwt("POST", AppUrl.checkWorkdays, data);

      if (res["status"] == 200) {
        daysWorkModel = DaysWorkModel.fromJson(res["result"]);
      } else {
        Get.snackbar("เกิดข้อผิดพลาด", "เกิดข้อผิดพลาด");
      }

      update();
    } catch (e) {
      if (kDebugMode) {
        print("error getDataDaysWork =>$e");
      }
      Get.snackbar("เกิดข้อผิดพลาด", "เกิดข้อผิดพลาด");
    }
  }

  Future<void> getDataDaysWorkOfMonth(inputId) async {
    try {
      Map data = {"id": inputId};

      final res = await AppApi.callAPIjwt("POST", AppUrl.checkDate, data);

      if (res["status"] == 200) {
        daysOfmonthWorkModel = DaysOfmonthWorkModel.fromJson(res["result"]);
      } else {
        Get.snackbar("เกิดข้อผิดพลาด", "เกิดข้อผิดพลาด");
      }

      update();
    } catch (e) {
      if (kDebugMode) {
        print("error getDataDaysWork =>$e");
      }
      Get.snackbar("เกิดข้อผิดพลาด", "เกิดข้อผิดพลาด");
    }
  }
}
