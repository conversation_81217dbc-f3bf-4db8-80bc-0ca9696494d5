import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:ui';
import 'package:camera/camera.dart';
import 'package:get/get_navigation/src/extension_navigation.dart';
import 'package:get/get_navigation/src/extension_navigation.dart';
import 'dart:html' as html;

import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/controllers/utils/connect.dart';
import 'package:mapp_ms24/controllers/utils/loader.dart';
import 'package:mapp_ms24/controllers/utils/url.dart';
import 'package:mapp_ms24/models/cewModel/CewModel.dart';
import 'package:mapp_ms24/models/userModel/usermodel.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';
import 'package:mapp_ms24/views/screens/home/<USER>';
import 'package:mapp_ms24/views/screens/home/<USER>/Cew/cew.dart';
import 'package:http/http.dart' as http;
class CewController extends GetxController {
  RxBool isPersonSelected = true.obs;
  RxBool isTeamSelected = false.obs;
  RxString inputLike = ''.obs;
  RxString inputname = ''.obs;
  RxString formatinputLike = ''.obs;
  RxString inputCEW = ''.obs;
  RxString selectedTeam = ''.obs;
  RxString selectedCEWType = ''.obs;
  RxString selectedBranch = ''.obs;
  RxString selectedCategory = ''.obs;
  DateTime thisday = DateTime.now();
  bool selectCew = false;
  bool expandCew = true;
  DateTime checkDay = DateTime.now();
  bool checking = false;
var dataLinkImage_upload =[];
  var formatDate = '';
  var idRec = "";
  var lnameRec = "";
  var fullnameRec = "";
  var nicknameRec = "";
  var tokenLineRec = "";
  var buRec = "";
  var ImgName  = "";
  var dataName = "";
  var typeRoleGive = "";
  var nameGive = "";
  var surnameGive = "";
  var idGive = "";
  var phonelikeGive = "";
  var dataInfoGiveCew = "";
  List dataInfoRecCewAll = [];
  List<String> dataInfoRecCew = [];
  int statusshowAutoComplet = 0;
  var statusAlertSuccess = 0;
  var imagePath = ''.obs;
  TextEditingController idPersonRecController = TextEditingController();

  TextEditingController inputDes = TextEditingController();
  TextEditingController like = TextEditingController();

  SearchTeamCewModelList? teamCewModel = SearchTeamCewModelList();
  ProfileController profileCtr = Get.find<ProfileController>();

  ResponseMember? responseMember;
  // เช็คและขอ permission สำหรับ Camera


  Future<void> ClearData() async {

    isPersonSelected = true.obs;
    isTeamSelected = false.obs;
    inputLike = ''.obs;
    inputname = ''.obs;
    formatinputLike = ''.obs;
    inputCEW = ''.obs;
    selectedTeam = ''.obs;
    selectedCEWType = ''.obs;
    selectedBranch = ''.obs;
    selectedCategory = ''.obs;

    idRec = "";
    buRec = "";

    imagePath.value = "";

    ImgName = "";
    // Clear user-related data (profileCtr)
    profileCtr.responseMember!.full_name_th = "";
    profileCtr.responseMember!.role = "";

    // Log to ensure data is cleared
    print('Data cleared');
  }
  RxList<XFile> selectedImages = <XFile>[].obs;
  RxList<String> uploadedUrls = <String>[].obs;


  Future<void> uploadToS3(String base64Image, BuildContext context) async {
    AppLoader.loader(context);

    String url =
        'https://une5sgp9aj.execute-api.ap-southeast-1.amazonaws.com/dev/api/v1/uploadS3_Center';

    Map map = {
      "name": "MappMS",
      "folder": "MappMS/img_cew",
      "image": base64Image
    };

    try {
      final response = await http.post(
        Uri.parse(url),
        headers: {"Content-Type": "application/json"},
        body: jsonEncode(map),
      );
      final jsonResponse = json.decode(response.body);

      if (jsonResponse["statusCode"].toString() == "200") {
        final uploadedUrl = jsonResponse["result"]["url"]["Location"].toString();
        uploadedUrls.add(uploadedUrl);
        print("📸 Upload success: $uploadedUrl");
      } else {
        Fluttertoast.showToast(
          msg: "รูปภาพใหญ่เกิน กรุณาลดความละเอียด",
          backgroundColor: Colors.red,
          textColor: Colors.white,
        );
      }
    } catch (e) {
      print("❌ Upload failed: $e");
    } finally {
      AppLoader.dismiss(context); // ปิด Loader
    }
  }


  Future<void> getImageDirectory(BuildContext context) async {
    await checkAndRequestStoragePermission();

    final ImagePicker _picker = ImagePicker();
    final List<XFile>? images = await _picker.pickMultiImage(); // ✅ หลายรูป

    if (images != null && images.isNotEmpty) {
      selectedImages.assignAll(images);
      uploadedUrls.clear();

      for (var image in selectedImages) {
        final bytes = await image.readAsBytes();
        final base64Image = base64Encode(bytes);
        await uploadToS3(base64Image, context);
      }
    } else {
      print("ไม่ได้เลือกรูป");
    }
  }

  Future<void> getImageCamera(BuildContext context) async {
    final ImagePicker _picker = ImagePicker();

    if (!kIsWeb) {
      // ✅ ขอ permission เฉพาะบน Mobile
      final status = await Permission.camera.status;
      if (!status.isGranted) {
        final result = await Permission.camera.request();
        if (!result.isGranted) {
          Get.snackbar(
            "การเข้าถึงกล้องถูกปฏิเสธ",
            "กรุณาเปิดสิทธิ์การใช้งานกล้องในตั้งค่า",
            snackPosition: SnackPosition.TOP,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
          return;
        }
      }
    }

    try {
      final XFile? image = await _picker.pickImage(source: ImageSource.camera);

      if (image != null) {
        selectedImages.add(image);
        final bytes = await image.readAsBytes();
        final base64Image = base64Encode(bytes);
        await uploadToS3(base64Image, context);
      } else {
        if (kIsWeb) {
          // ✅ Web: ผู้ใช้กด "ไม่อนุญาต" หรือปิดกล้อง
          Fluttertoast.showToast(
            msg: "กรุณาอนุญาตให้เข้าถึงกล้องผ่านเบราว์เซอร์",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.TOP,
            backgroundColor: Colors.orange,
            textColor: Colors.black,
            fontSize: 16.0,
          );
        } else {
          print("ไม่ได้ถ่ายรูป");
        }
      }
    } catch (e) {
      if (kIsWeb) {
        Fluttertoast.showToast(
          msg: "เบราว์เซอร์ไม่สามารถเข้าถึงกล้องได้ กรุณาตรวจสอบสิทธิ์",
          toastLength: Toast.LENGTH_LONG,
          gravity: ToastGravity.TOP,
          backgroundColor: Colors.red,
          textColor: Colors.white,
          fontSize: 16.0,
        );
      }
      print("❌ Error accessing camera: $e");
    }
  }

  void cleardataIMG(){
    imagePath.value = "";
    ImgName = "";
    imagePath.refresh();
  }




  Future<void> checkAndRequestCameraPermission() async {
    PermissionStatus status = await Permission.camera.request();
    if (status.isGranted) {
      // permission granted
    } else {
      print("Camera permission denied");
    }
  }

  // เช็คและขอ permission สำหรับ Storage
  Future<void> checkAndRequestStoragePermission() async {
    PermissionStatus status = await Permission.storage.request();
    if (status.isGranted) {
      // permission granted
    } else {
      print("Storage permission denied");
    }
  }

  Future<bool> deviceHasCamera() async {
    if (kIsWeb) {
      try {
        final hasCamera = await _checkCameraOnWeb();
        return hasCamera;
      } catch (e) {
        return false;
      }
    } else {
      try {
        final cameras = await availableCameras();
        return cameras.isNotEmpty;
      } catch (e) {
        return false;
      }
    }
  }


// ✅ Web: ใช้ JS interop
  Future<bool> _checkCameraOnWeb() async {
    try {
      final stream = await html.window.navigator.mediaDevices?.getUserMedia({
        'video': true,
      });
      stream?.getTracks().forEach((track) => track.stop());
      return true;
    } catch (e) {
      print('Web Camera Check Error: $e');
      return false;
    }
  }

  Future<void> getTeamCew() async {
    try {
      final response = await http.get(
        Uri.parse("https://n8n-ags.agilesoftgroup.com/webhook/Get_TeamCew"),
      );

      final res = jsonDecode(response.body);

// ดึง object เดียวจาก list
      final Map<String, dynamic> outerResult = res[0];
      final int status = outerResult["status"];
      final List<dynamic> resultList = outerResult["result"];

      if (status == 200) {
        datasTeamRec.clear();
        for (var item in resultList) {
          datasTeamRec.add(
            KeyValueModelTeamRec(
              key: item["name_th"] ?? '',
              value: item["id"].toString(), // ✅ ป้องกัน type error
            ),
          );
        }
        update();
      } else {
        Get.snackbar("เกิดข้อผิดพลาด", "ไม่สามารถโหลดข้อมูลทีม CEW ได้");
      }


      update();
    } catch (e) {
      print("error getDataDaysWork => $e");
      Get.snackbar("เกิดข้อผิดพลาด", "โหลดข้อมูลไม่สำเร็จ");
    }
  }




  uploadtoS3(base64Image,context) async {
    AppLoader.loader(context);

    // String url =
    //     'https://mhmo3nnbr5.execute-api.ap-southeast-1.amazonaws.com/latest/img_upload';
    String url =
        'https://une5sgp9aj.execute-api.ap-southeast-1.amazonaws.com/dev/api/v1/uploadS3_Center';
//    String url = 'https://a0df7e51.ngrok.io/agslearn/us-central1/ApiAppMS24';
    Map map = {
      "name": "MappMS",
      "folder": "MappMS/img_cew",
      "image": base64Image
    };

    final response = await apiRequest(url, map);
    var jsonResponse = json.decode(response);
    print("jsonResponse: $jsonResponse");
    if (jsonResponse["statusCode"].toString() == "200") {
      print("linkImmageS3 : ${jsonResponse["result"]["url"]["Location"]}");

        dataLinkImage_upload.add(jsonResponse["result"]["url"]["Location"].toString());

      AppLoader.loader(context);
//      saveImageProfile(jsonResponse["result"]["url"]["Location"].toString());
    } else {
      print('upload fail!');
      Fluttertoast.showToast(
          msg: "รูปภาพมีขนาดใหญ่เกิน กรุณาลดความละเอียดของไฟล์ภาพ",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.TOP,
          backgroundColor: Colors.red,
          textColor: Colors.black,
          fontSize: 16.0);
      AppLoader.loader(context);
    }
  }





  Future<void> getInfoRecCew(id) async {
    print("getInfoRecCew");
    Map data = {"id": id};

    final response =
        await AppApi.callAPIjwt("POST", AppUrl.loaddetailmember, data);
    print("response: $response");
    if (response["result"].length > 0) {
      idRec = response["result"][0]["id"];
      lnameRec = response["result"][0]["name_th"];
      fullnameRec = response["result"][0]["name_th"] +
          " " +
          response["result"][0]["surname_th"];
      nicknameRec = response["result"][0]["nickname"];
      buRec = response["result"][0]["company_management"];
      print("idRec: $idRec");
      print("lnameRec: $lnameRec");
      print("fullnameRec: $fullnameRec");
      print("nicknameRec: $nicknameRec");
      print("buRec: $buRec");
    } else {
      Get.snackbar("เกิดข้อผิดพลาด", "id $id ไม่มีใน ข้อมูลสมาชิก");
    }
    update();
  }

  Future<void> loadCategory() async {
    print("loadCategory");
    Map data = {"nameMenu": "category"};

    final response =
        await AppApi.callAPIjwt("POST", AppUrl.searchDropdown, data);
    print(response);
    print("1234");

    if (response["status"] == 200) {
      datasCategory.clear(); // ✅ เคลียร์ก่อน
      datasCategory.add(KeyValueModelCategory(
        key: "dp_cewCategory".tr.toString(),
        value: "0",
      )); // ✅ เพิ่มตัวแรกเป็น default

      for (var i = 0; i < response["result"].length.toInt(); i++) {
        final value = response["result"][i]["categoryInsertCew"].toString();
        if (!datasCategory.any((e) => e.value == value)) {
          datasCategory.add(KeyValueModelCategory(
            key: value,
            value: value,
          ));
        }
      }

      update();
    }

    else {
      Get.snackbar("เกิดข้อผิดพลาด", "ไม่สามารถค้นหาหมวดหมู่ได้");
    }


    update();
  }

  Future<void> loadCounty() async {
    print("loadCategory");
    Map data = {"nameMenu": "county"};

    final response =
        await AppApi.callAPIjwt("POST", AppUrl.searchDropdown, data);
    print(response);
    print("1234");

    if (response["status"] == 200) {
      datasCounty.clear(); // ✅ ล้างก่อน

      // ✅ เพิ่มค่าเริ่มต้นกันไว้ก่อน
      datasCounty.add(KeyValueModelCounty(
        key: "dp_cewCounty".tr.toString(),
        value: "0",
      ));

      for (var i = 0; i < response["result"].length.toInt(); i++) {
        final val = response["result"][i]["countyInsertCew"].toString();
        if (!datasCounty.any((e) => e.value == val)) {
          datasCounty.add(KeyValueModelCounty(
            key: val,
            value: val,
          ));
        }
      }
    } else {
      Get.snackbar("เกิดข้อผิดพลาด", "ไม่สามารถค้นหาเขตได้");
    }

    update();
  }

  Future<void> loadTypescew() async {
    print("loadTypescew");
    Map data = {};

    final response = await AppApi.callAPIjwt("POST", AppUrl.Typescew, data);
    print(response);
    print("122111");

    if (response["status"] == 200) {
      datasTypescew.clear();
      datasTypescew.add(
        KeyValueModelTypecew(key: "dp_cewTypecew".tr.toString(), value: "0"),
      );
      datasTypescew.addAll(
        response["result"]
            .map<KeyValueModelTypecew>((e) => KeyValueModelTypecew(
          key: e.toString(),
          value: e.toString(),
        ))
            .toList(),
      );
    }
 else {
      Get.snackbar("เกิดข้อผิดพลาด", "ไม่สามารถค้นหาเขตได้");
    }

    update();
  }

  Future<void> getInfoRecCewAll() async {
    print("getInfoRecCewAll");
    Map data = {};

    final response =
        await AppApi.callAPIjwt("POST", AppUrl.loaddetailmemberall, data);
    print(response);
    print("1234");

    if (response["status"] == 200) {
      dataInfoRecCewAll = response["result"];
    } else {
      Get.snackbar("เกิดข้อผิดพลาด", "ไม่สามารถค้นหาเขตได้");
    }

    update();
  }

  List<dynamic> showNames = [];
  List<dynamic> showNamesCollector = []; // it never changes

  Future<void> searchFuncRecCew(keyword) async {
    if (keyword == "") {
      for (int i = 0; i < dataInfoRecCewAll.length; i++) {
        showNames.add(dataInfoRecCewAll[i]["id"].toString() +
            " " +
            dataInfoRecCewAll[i]["name_th"].toString() +
            " " +
            dataInfoRecCewAll[i]["surname_th"].toString());
        showNamesCollector = showNames;
      }
    } else {
      print("object");
      print(keyword);

      showNames = showNamesCollector
          .where((keyword) => showNamesCollector.contains(keyword))
          .toList();
      print(showNames);
    }
    update();
  }

  void updateCewData(keyword) {
    var parts = keyword.split(' ');
    if (parts.length >= 3) {
      idRec = parts[0];
      fullnameRec = parts[1];
      nicknameRec = parts[2];
    } else {
      // Handle the case where keyword doesn't have enough parts
      idRec = '';
      fullnameRec = '';
      nicknameRec = '';
    }
  }

  RxList<RxMap<String, String>> sampleData = [
    {"id": "0", "name": "josh", "lastname": "poppa"}.obs,
    {"id": "1", "name": "john", "lastname": "poppa"}.obs,
    {"id": "2", "name": "jack", "lastname": "brown"}.obs,
  ].obs;

  void updateAndFormatDay(String value) {
    String formattedDate = DateFormat('dd-MM-yyyy').format(checkDay);
    formatDate = formattedDate;
  }

  final TextEditingController searchController = TextEditingController();

  void toggleSelection(bool isPerson) {
    if (isPerson) {
      if (!isPersonSelected.value) {
        isPersonSelected.value = !isPersonSelected.value;
        isTeamSelected.value = false;
        selectedTeam = ''.obs;
        inputCEW = ''.obs;
        String value = selectedTeam.value;
        updateSelectedTeam(value);
        updateinputCEW(value);
        resetData();
        updateexpand();

        searchController.clear();
      }
    } else {
      if (!isTeamSelected.value) {
        isPersonSelected.value = false;
        isTeamSelected.value = !isTeamSelected.value;
        selectedTeam = ''.obs;
        inputCEW = ''.obs;
        searchController.clear();
        inputLike = ''.obs;
        searchController.clear();
        selectedTeam = ''.obs;
        selectedCEWType = ''.obs;
        selectedBranch = ''.obs;
        selectedCategory = ''.obs;
        inputCEW = ''.obs;
        updateexpand();
      }
    }
  }

  void updateexpand() {
    if (searchController.text.isEmpty) {
      expandCew = true;
    } else {
      expandCew = false;
    }
  }

  void resetData() {
    isPersonSelected = true.obs;
    isTeamSelected = false.obs;
    expandCew = true;
    inputLike = ''.obs;
    searchController.clear();
    selectedTeam = ''.obs;
    selectedCEWType = ''.obs;
    selectedBranch = ''.obs;
    selectedCategory = ''.obs;
    inputCEW = ''.obs;
    updateexpand();
  }
  Future<void> saveCew(
      BuildContext context,
      String statusshow,
      String statusTypeCew,
      String idRec,
      String buRec,
      String fullnameRec,
      String typeRec,
      String _detailController,
      String _valueCategory,
      String _valueCounty,
      String _numLikeController,
      ) async {
    try {
      AppLoader.loader(context);

      // ✅ ดึงลิงก์ทั้งหมดจาก cewCtr
      final List<String> uploadedImages = uploadedUrls.toList();

      Map<String, dynamic> data = {
        "PersonORTeam": statusshow,
        "typeCew": _valueCategory,
        "id": idRec,
        "bu": buRec,
        "detail": _detailController,
        "CewCategory": statusTypeCew,
        "Cewcounty": _valueCounty,
        "numlike": _numLikeController,
        "nameCreateCew": fullnameRec,
        "TyperoleCreateCew": typeRec,
        "img": uploadedImages, // ✅ ใส่ลิงก์รูปเป็น array
        "form": "${profileCtr.responseMember?.name_th} ${profileCtr.responseMember?.surname_th}",
        "emp_cew": "${profileCtr.responseMember?.id}"
      };

      print("📦 Data for webhook: $data");

      final response = await http.post(
        isPersonSelected == true
            ? Uri.parse("https://n8n-ags.agilesoftgroup.com/webhook/6c2226ce-0f18-4ebe-8e33-b1f7e7ea8867")
            : Uri.parse("https://n8n-ags.agilesoftgroup.com/webhook/Cew_team_n8n"),
        headers: {"Content-Type": "application/json"},
        body: jsonEncode(data),
      );

      print("🔁 Response: ${response.statusCode}");
      print("🧾 Body: ${response.body}");

      AppLoader.dismiss(context);

      if (response.statusCode == 200) {
        Get.offAllNamed('home');
      } else {
        // handle error
      }
    } catch (e) {
      AppLoader.dismiss(context);
      print("❌ Error: $e");
    }
  }


  var dataLinkImage;

  Future<void> savepersonCEW(fullnameRec, detailController) async {
    print("loadTypescew");
    Map data = {
      "id": idGive.toString(),
      "nameGive": nameGive.toString(),
      "surnameGive": surnameGive.toString(),
      "phone_like": phonelikeGive.toString(),
    };

    final response = await AppApi.callAPIjwt("POST", AppUrl.sendCEW, data);
    print(response);
    print("122111");

    if (response["status"] == 200) {
      sendtelegram_usersender(fullnameRec, detailController);
    } else {
      Get.snackbar("เกิดข้อผิดพลาด", response["msg"].toString());
    }
    update();
  }

  sendtelegram_usersender(fullnameGive, detailController) async {
    var message = "\n แจ้ง :: " +
        profileCtr.responseMember!.full_name_th.toString() +
        " ";
    message += "\n คุณได้รับ :: 100 Likepoint " + " ";
    message += "\n จากกิจกรรม :: ผู้จับถูก (CEW เพื่อนๆ)" + " ";
    message += "\n จากการ CEW :: " + fullnameGive.toString() + " ";
    message += "\n\n เรื่อง :: " + detailController.toString()+ " ";
    message += "\n\n อย่าลืมกดเคลม ใน APP MS นะคะ";
    message += "\n\n สงสัยเพิ่มเติม";
    message += "\n ติดต่ออรรถ 4C";

    List<dynamic> data = [
      {
        "emp": idRec.toString(),
        "fromdata": {"message": message}
      }
    ];
    AppHttps.postTG_PKG(AppHttps.sendTelegramPKG, data);
  }

  sendtelegram_user(fullnameRec, detailController, fullnameGive) async {
    dataLinkImage = "http://devdev.prachakij.com/PPP7/uploads/emp/" +
        idRec.toString() +
        ".jpg";

    var message = "\n CEW : " + fullnameGive.toString() + " ";
    message += "\n\n\n รายละเอียด CEW : " + detailController.toString() + " ";
    message += "\n\n\n สมาชิกที่ออก CEW : " + profileCtr.responseMember!.full_name_th.toString()+ " ";
    print("123457");

    print(dataLinkImage.toString());
    List<dynamic> data = [
      {
        "emp": idRec.toString(),
        "fromdata": {"message": message}
      }
    ];
    data.add({
      "emp":idRec.toString(),
      "fromdata": {"img": dataLinkImage}
    });

    // if(dataLinkImages.isEmpty){
    //   print("1");
    // }else{
    //   data.add(
    //       {
    //         "emp": idRec.toString(),
    //         "fromdata": {
    //           "img": dataLinkImages[0]
    //         }
    //       });
    //   print(data);
    // }
    AppHttps.postTG_PKG(AppHttps.sendTelegramPKG, data);
  }

  sendtelegram(fullnameRec, detailController, fullnameGive) async {
    dataLinkImage = "http://devdev.prachakij.com/PPP7/uploads/emp/" +
        idRec.toString() +
        ".jpg";

    var message = "\n กลุ่มCEW : " + fullnameGive.toString() + " ";
    message += "\n\n\n รายละเอียด CEW : " + detailController.toString() + " ";
    message += "\n\n\n สมาชิกที่ออก CEW : " + fullnameGive.toString() + " ";

    List token = [
      "-4274191816"
      // // 4C PKG
      // "-688894001",
      // "-1001742657226",
      // // 4C PGH&CPDgr
      // "-694291531",
      // // 4C PMS
      // "-761740727",
      // // 4C AAM
      // "-611173753"
    ];
    for (var i in token) {
      List<dynamic> data = [
        {
          "emp": i,
          "fromdata": {"message": message}
        }
      ];
      data.add({
        "emp": i,
        "fromdata": {"img": dataLinkImage}
      });
      // if(dataLinkImages.isEmpty){
      //   print("1");
      //   print("token: "+ i);
      //   print(data);
      // }else{
      //   data.add(
      //       {
      //         "emp": i,
      //         "fromdata": {
      //           "img": dataLinkImages[0]
      //         }
      //       });
      //   print(data);
      //   print("token: "+ i);
      // }
      AppHttps.postTG_PGH(AppHttps.sendTelegramPKG, data);
    }
  }

  String formatNumberWithCommas(String value) {
    // Remove existing commas
    String cleanedValue = value.replaceAll(',', '');

    // Convert to a number
    num numericValue = num.tryParse(cleanedValue) ?? 0;

    // Format the number with commas every three digits
    String formattedValue = numericValue.toStringAsFixed(0);

    // Add commas every three digits
    final parts = List<String>.from(formattedValue.split(''));
    for (int i = parts.length - 3; i > 0; i -= 3) {
      parts.insert(i, ',');
    }

    return parts.join();
  }

  void updateInputLike(String value) {
    String formattedValue = formatNumberWithCommas(value);
    inputLike.value = formattedValue;
  }

  void updateSelectedTeam(String value) {
    selectedTeam.value = value;
  }

  void updateCew(String? value, String text) {
    inputCEW.value = '';
    inputLike.value = '';
    if (value == 'บรรยากาศ') {
      inputCEW.value = '100';
      inputLike.value = '100';
    } else if (value == 'ทั่วไป') {
      inputCEW.value = '1000';
      inputLike.value = '1000';
    } else if (value == 'แผนงาน') {
      text = '';
      inputCEW.value = text;
      inputLike.value = text;
    }
    update();
  }

  void updateKeyword(String value, keyword) {
    keyword = value;
    print('updateKeyword ' + value);
    print('updateKeyword ' + keyword);
  }

  void updateinputCEW(String value) {
    inputCEW.value = value;
  }

  void updateSelectedCEWType(String value) {
    selectedCEWType.value = value;
  }

  void updateSelectedBranch(String value) {
    selectedBranch.value = value;
  }

  void updateSelectedCategory(String value) {
    selectedCategory.value = value;
  }

  void updatesearchController(String value) {
    searchController.text = value.toString();
    updateinputCEW(value);
    selectCew != selectCew;
  }

  List<KeyValueModelTeamRec> datasTeamRec = [
    KeyValueModelTeamRec(key: "dp_cewTeam".tr.toString(), value: "0"),
  ];
  List<KeyValueModelCategory> datasCategory = [
    KeyValueModelCategory(key: "dp_cewCategory".tr.toString(), value: "0"),
  ];
  List<KeyValueModelCounty> datasCounty = [
    KeyValueModelCounty(key: "dp_cewCounty".tr.toString(), value: "0"),
  ];
  List<KeyValueModelTypecew> datasTypescew = [
    KeyValueModelTypecew(key: "dp_cewTypecew".tr.toString(), value: "0"),
  ];
}
