import 'dart:convert';

import 'package:ags_authrest2/ags_authrest.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:http/http.dart' as http;
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/controllers/internal/Tacontroller/Tacontroller.dart';
import 'package:mapp_ms24/controllers/utils/connect.dart';
import 'package:mapp_ms24/controllers/utils/loader.dart';
import 'package:mapp_ms24/controllers/utils/url.dart';
import 'package:mapp_ms24/models/userModel/usermodel.dart';
import 'package:mapp_ms24/views/screens/Widget/Widget.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';
import 'package:mapp_ms24/views/screens/home/<USER>';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:mqtt_client/mqtt_server_client.dart';
import 'package:geolocator/geolocator.dart';
import 'package:mobile_scanner/mobile_scanner.dart' as mobile_scanner;

class QrScanController extends GetxController {
  ResponseMember? responseMember;
  Tacontroller tacontroller = Get.find<Tacontroller>();
  ProfileController profileCon = Get.find<ProfileController>();
  List tokenNotification = [];
  var statusAlertSuccess = 0;
  var hasScanned = false.obs;
  var scannedBody = ''.obs;
  var isScanning = false.obs;
  var scanMode = "work".obs;
  final scannerController = mobile_scanner.MobileScannerController();

  Future<bool> checkData(String? data) async {
    if (data == null) return false;
    try {
      final decoded = jsonDecode(data);
      return decoded is Map && decoded['encrypData']?.isNotEmpty == true;
    } catch (_) {
      return false;
    }
  }


  Future authen(BuildContext context, data, id) async {
    var Jsondecodere = jsonDecode(data!);
    var auth = Ags_restauth();
    auth.SECERT_JWT = 'Eca)J.L@T9_vbgZ5MDpXUC';
    auth.R_USER = 'yubikeypkg';
    final deBody =
        jsonDecode(jsonDecode(auth.decrypBodyStr(Jsondecodere['encrypData'])));

    if (deBody['authn_id'] == id.toString()) {
      verify(deBody);
      listeningEvent(deBody);
    } else {
      Fluttertoast.showToast(
          msg: 'ไม่สามารถเข้าสู่ระบบได้',
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.TOP,
          backgroundColor: Colors.red,
          textColor: Colors.black,
          fontSize: 16.0);
      Get.toNamed('home');
    }
  }

  Future verify(jdeBody) async {
    var auth = Ags_restauth();
    auth.SECERT_JWT = 'Eca)J.L@T9_vbgZ5MDpXUC';
    auth.R_USER = 'yubikeypkg';
    var headers = {
      'Authorization': auth.genTokenEncryp(),
      'Content-Type': 'application/json'
    };
    var body = json.encode(jdeBody);

    var request = http.Request(
        'POST', Uri.parse('https://agilesoftgroup.com/cf_yubitg/verifyTgotp'));
    request.body = json.encode(auth.encrypbody(body));
    request.headers.addAll(headers);
    http.StreamedResponse response = await request.send();

    if (response.statusCode == 200) {
      var dataRaw = await response.stream.bytesToString();
      var data = json.decode(dataRaw);
    } else {
      print(response.reasonPhrase);
    }
  }

  Future<MqttServerClient> listeningEvent(topic) async {
    String hosting = "wss://iac55c5c.ala.us-east-1.emqxsl.com/mqtt";
    String Uname = "webconnect";
    String Pass = "gtm5gvh_hzw5PNH4qpj";

    MqttServerClient client = MqttServerClient(hosting, 'flutter_client');
    client.keepAlivePeriod = 60;
    client.logging(on: true);
    client.onConnected = onConnected;
    client.onDisconnected = onDisconnected;
    client.onSubscribed = onSubscribed;
    client.useWebSocket = true;
    client.port = 8084;

    final connMessage = MqttConnectMessage()
        .authenticateAs(Uname, Pass)
        .withWillTopic('willtopic')
        .withWillMessage('Will message')
        .startClean()
        .withWillQos(MqttQos.atLeastOnce);
    client.connectionMessage = connMessage;
    try {
      await client.connect();
    } catch (e) {
      client.disconnect();
    }
    String topicMq = 'mqttpkg/' + topic['mqttsub'];
    client.subscribe(topicMq, MqttQos.atMostOnce);
    client.updates!.listen((List<MqttReceivedMessage<MqttMessage?>>? c) {
      final recMess = c![0].payload as MqttPublishMessage;
      final pt =
          MqttPublishPayload.bytesToStringAsString(recMess.payload.message);
      var auth = Ags_restauth();
      auth.SECERT_JWT = 'Eca)J.L@T9_vbgZ5MDpXUC';
      auth.R_USER = 'yubikeypkg';

      var authDecode = jsonDecode(jsonDecode(auth.decrypBodyStr(pt)));

      if (authDecode['status'] == 'success') {
        client.disconnect();
        Get.toNamed('home');
      }
    });
    return client;
  }

  Future<void> onConnected() async {}

  Future<void> onDisconnected() async {}

  Future<void> onSubscribed(String topic) async {}

  Future authenNew(BuildContext context, data, id, email) async {
    var auth = Ags_restauth();
    auth.SECERT_JWT = 'Eca)J.L@T9_vbgZ5MDpXUC';
    auth.R_USER = 'yubikeypkg';
    Map<String, dynamic> Jsondecodere = jsonDecode(data!);
    var deBody = auth.decrypbody(Jsondecodere);

    listeningEventNEW(deBody['UinqId'], id, email);
  }

  Future<MqttServerClient> listeningEventNEW(String topic, id, email) async {
    String hosting = "wss://iac55c5c.ala.us-east-1.emqxsl.com/mqtt";
    String Uname = "webconnect";
    String Pass = "gtm5gvh_hzw5PNH4qpj";

    MqttServerClient client = MqttServerClient(hosting, 'flutter_client');
    client.keepAlivePeriod = 60;
    client.logging(on: true);
    client.useWebSocket = true;
    client.port = 8084;

    var auth = Ags_restauth();
    auth.SECERT_JWT = 'Eca)J.L@T9_vbgZ5MDpXUC';
    auth.R_USER = 'yubikeypkg';

    final connMessage = MqttConnectMessage()
        .authenticateAs(Uname, Pass)
        .withWillTopic('willtopic')
        .withWillMessage('Will message')
        .startClean()
        .withWillQos(MqttQos.atLeastOnce);
    client.connectionMessage = connMessage;
    try {
      await client.connect();
    } catch (e) {
      client.disconnect();
    }
    String topicMq = 'mqttpkg/' + topic;
    client.subscribe(topicMq, MqttQos.atMostOnce);
    client.updates!.listen((List<MqttReceivedMessage<MqttMessage?>>? c) {
      final recMess = c![0].payload as MqttPublishMessage;
      final pt =
          MqttPublishPayload.bytesToStringAsString(recMess.payload.message);

      client.disconnect();
      Get.snackbar(
        "ui_QRscanSuccess".tr.toString(),
        "ui_QRscanSuccessDetail3".tr.toString() +
            " " +
            "ui_QRscanSuccessDetail2".tr.toString(),
      );
      Get.toNamed('home');
    });
    Map<String, dynamic> body = <String, dynamic>{
      "userId": id.toString(),
      "email": email.toString(),
    };
    var encRes = auth.encrypbody(jsonEncode(body));
    client.published!.listen((MqttPublishMessage message) {});
    final builder = MqttClientPayloadBuilder();
    builder.addString(jsonEncode(encRes));
    client.publishMessage(topicMq, MqttQos.exactlyOnce, builder.payload!);

    Future<void> onConnected() async {}

    Future<void> onDisconnected() async {}

    return client;
  }

  Future<void> getTokenNotification(context) async {
    try {
      Map data = {"id": profileCon.responseMember!.id.toString()};

      final res = await AppApi.callAPIjwt("POST", AppUrl.tokenNotifyById, data);

      if (res["status"] == 200) {
        for (var i = 0; i < res["result"]["tokenNotify"].length; i++) {
          tokenNotification = res["result"]["tokenNotify"];
        }
      } else {
        Get.snackbar("เกิดข้อผิดพลาด", "เกิดข้อผิดพลาด");
      }

      update();
    } catch (e) {
      if (kDebugMode) {
        print("error getDataDaysWork =>$e");
      }
      Get.snackbar("เกิดข้อผิดพลาด", "เกิดข้อผิดพลาด");
    }
  }

  Future<void> processQR(
      context, detail, id, email, phone_like, fname, lname, company) async {
    AppLoader.loader(context);
    Map body = {
      "datecheck":
          convertDateTime(tacontroller.datetime, "dd-MM-YYYY2").toString(),
      "timecheck": convertDateTime(tacontroller.datetime, "THM").toString(),
      "id": id.toString(),
      "email1": email.toString(),
      "phone_like": phone_like..toString(),
      "fname": fname.toString(),
      "lname": lname.toString(),
      "groupBU": company.toString(),
      "detailQR": detail.toString(),
      "tokens": tokenNotification.toString()
    };
    final resQR = await AppApi.callAPIjwt('POST', AppUrl.processQrcode, body);

    if (resQR["status"].toString() == "200") {
      Get.snackbar(
          "ui_QRscanSuccess".tr.toString(),
          "ui_QRscanSuccessDetail".tr.toString() +
              " " +
              profileCon.responseMember!.full_name_th.toString() +
              "ui_QRscanSuccessDetail2".tr.toString());
      AppLoader.dismiss(context);
      Get.toNamed('home');

      update();
    } else {
      Get.snackbar("เกิดข้อผิดพลาด", resQR["msg"]);
      Navigator.pushReplacement(
          context, MaterialPageRoute(builder: (context) => HomeScreen()));
    }
  }

  Future<void> verifyEmpolyee(case_id, topicId) async {
    try {
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      final qrData = {
        "case_id": case_id,
        "my_lat": position.latitude,
        "my_lng": position.longitude,
        "emp_code": "6609247",
        "message_thread_id": topicId,
      };

      final getQrResponse = await http.post(
        Uri.parse(
            'https://n8n-deploy.agilesoftgroup.com/webhook/generate-confirm-qr'),
        headers: {"Content-Type": "application/json"},
        body: jsonEncode(qrData),
      );

      if (getQrResponse.statusCode == 200) {
        final responseData = jsonDecode(getQrResponse.body);
        final String? qrUrl = responseData["qr_url"];

        if (qrUrl != null && qrUrl.isNotEmpty) {
          Get.dialog(
            AlertDialog(
              title: Text("🔍 QR Code สำหรับ Case ID: $case_id"),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Image.network(qrUrl,
                      width: 250, height: 250, fit: BoxFit.cover),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Get.back(),
                  child: const Text("ปิด"),
                ),
              ],
            ),
            barrierDismissible: true,
          );
        } else {
          print("❌ ไม่พบ QR Code ใน Response");
        }
      } else {
        print(
            "❌ API Request Failed: ${getQrResponse.statusCode} - ${getQrResponse.body}");
      }
    } catch (e) {
      print("🚨 เกิดข้อผิดพลาด: $e");
    }
  }

  void handleBarcodeScan(capture) async {
    if (hasScanned.value) return;

    final List barcodes = capture.barcodes;

    if (barcodes.isNotEmpty) {
      final String rawData = barcodes.first.rawValue ?? '';

      if (rawData.isEmpty) {
        return;
      }

      final String qrUrl =
          "https://api.qrserver.com/v1/create-qr-code/?size=600x600&data=${Uri.encodeComponent(rawData)}";

      Map<String, dynamic>? jsonData;
      try {
        jsonData = jsonDecode(rawData);
      } catch (e) {
        jsonData = null; // ไม่ใช่ JSON
      }

      scannedBody.value = rawData;
      hasScanned.value = true;
      isScanning.value = false;
      scannerController.stop();

      if (jsonData != null) {
        jsonData["qr_url"] = qrUrl;
        jsonData.forEach((key, value) {});
        var phone =
            removeCountryCode(profileCon.responseMember!.phone_like.toString());

        await confirmScan(
          caseId: jsonData["case_id"].toString() ?? "",
          selectedService: jsonData["selectedService"].toString() ?? "",
          messageThreadId: jsonData["message_thread_id"].toString() ?? "",
          phone: phone.toString(),
          empCode: jsonData["emp_code"].toString() ?? "",
          qrUrl: jsonData["qr_url"].toString() ?? qrUrl,
          chat_id: '',
          latitude: jsonData["latitude"].toString() ?? '',
          longitude: jsonData["longitude"].toString() ?? '',
        );
      } else {
        print("📄 Raw Data: $rawData");
        print("🔗 QR Code URL: $qrUrl");
      }
    }
  }

  String removeCountryCode(phone) {
    if (phone.startsWith('+66')) {
      return '0' + phone.substring(3);
    }
    return phone;
  }

  Future<void> confirmScan({
    required String caseId,
    required String selectedService,
    required String messageThreadId,
    required String phone,
    required String empCode,
    required String qrUrl,
    required String chat_id,
    required latitude,
    required longitude,
  }) async {
    final String url =
        "https://n8n-deploy.agilesoftgroup.com/webhook/confirm-scan";
    Position position = await Geolocator.getCurrentPosition(
      desiredAccuracy: LocationAccuracy.high,
    );

    final Map<String, dynamic> requestBody = {
      "case_id": caseId,
      "selectedService": selectedService,
      "message_thread_id": messageThreadId,
      "phone": phone,
      "emp_code": empCode,
      "qr_url": qrUrl,
      "latitude": latitude,
      "longitude": longitude,
      "my_lat": position.latitude,
      "my_long": position.longitude,
      "chat_id": chat_id,
    };

    try {
      final response = await http.post(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json",
        },
        body: jsonEncode(requestBody),
      );

      sendLiveLocation(chat_id, messageThreadId);

      if (response.statusCode == 200) {
        calculate_distand(messageThreadId, latitude, longitude,
            position.latitude, position.longitude);
      } else {
        print(
            "❌ Confirm Scan Failed: ${response.statusCode} - ${response.body}");
      }
    } catch (e) {
      print("🚨 Error: $e");
    }
  }

  Future<void> calculate_distand(
      messageThreadId, emp_lat, emp_long, mylat, mylong) async {
    final String url =
        "https://n8n-deploy.agilesoftgroup.com/webhook/calculate-distan";

    Position? position;
    try {
      position = await determinePosition();
    } catch (e) {
      position = null;
    }

    final Map<String, dynamic> requestBody = {
      "message_thread_id": messageThreadId,
      "my_lat": position?.latitude ?? 0.0,
      "my_lng": position?.longitude ?? 0.0,
      "latitude": emp_lat,
      "longitude": emp_long,
    };

    try {
      final response = await http.post(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json",
        },
        body: jsonEncode(requestBody),
      );

      if (response.statusCode == 200) {
        print("✅ Confirm Scan Success: ${response.body}");
      } else {
        print(
            "❌ Confirm Scan Failed: ${response.statusCode} - ${response.body}");
      }
    } catch (e) {
      print("🚨 Error: $e");
    }
  }

  Future<void> verifyCustomer(mobile_scanner.BarcodeCapture capture) async {
    if (hasScanned.value) return;

    hasScanned.value = true;

    Position? position;
    if (capture.barcodes.isNotEmpty) {
      final String rawData = capture.barcodes.first.rawValue ?? '';

      if (rawData.isEmpty) {
        hasScanned.value = false;
        return;
      }

      Map<String, dynamic>? jsonData;
      try {
        jsonData = jsonDecode(rawData);
      } catch (e) {
        hasScanned.value = false;
        return;
      }

      if (jsonData != null) {
        String caseId = jsonData["case_id"] ?? "";
        String messageThreadId = jsonData["message_thread_id"] ?? "";
        String empCode = jsonData["emp_code"] ?? "";
        double empLat = jsonData["my_lat"] ?? 0.0;
        double empLng = jsonData["my_lng"] ?? 0.0;

        try {
          position = await Geolocator.getCurrentPosition(
            desiredAccuracy: LocationAccuracy.high,
          );

          var data = {
            "caseId": caseId,
            "message_thread_id": messageThreadId,
            "empCode": empCode,
            "emp_lat": empLat,
            "emp_lng": empLng,
            "latitude": position.latitude,
            "longitude": position.longitude,
          };

          final response = await http.post(
            Uri.parse(
                'https://n8n-deploy.agilesoftgroup.com/webhook/confirm-work'),
            headers: {
              "Content-Type": "application/json",
            },
            body: jsonEncode(data),
          );

          if (response.statusCode == 200) {
            sendLiveLocation('-1002346141851', messageThreadId);

            Get.snackbar("สำเร็จ", "ยืนยันงานเรียบร้อย!",
                snackPosition: SnackPosition.BOTTOM);
          } else {
            Get.snackbar("ผิดพลาด", "ไม่สามารถยืนยันงานได้!",
                snackPosition: SnackPosition.BOTTOM);
          }
        } catch (e) {
          Get.snackbar("ผิดพลาด", "เกิดข้อผิดพลาดระหว่างส่งข้อมูล!",
              snackPosition: SnackPosition.BOTTOM);
        }
      }
    }

    Future.delayed(Duration(seconds: 3), () {
      hasScanned.value = false;
    });
  }

  Future<Position> determinePosition() async {
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      throw Exception('❌ Location services are disabled.');
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        throw Exception('❌ Location permissions are denied.');
      }
    }

    if (permission == LocationPermission.deniedForever) {
      throw Exception('❌ Location permissions are permanently denied.');
    }

    return await Geolocator.getCurrentPosition(
      desiredAccuracy: LocationAccuracy.high,
    );
  }

  Future<void> sendLiveLocation(chatId, messageThreadId) async {
    final String TELEGRAM_BOT_TOKEN =
        "**********************************************";
    final String TELEGRAM_CHAT_ID = "-1002346141851"; //
    final String TELEGRAM_USER_ID = "5977488991"; //
    try {
      Position position = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high);

      double latitude = position.latitude;
      double longitude = position.longitude;

      final response = await http.post(
        Uri.parse(
            "https://n8n-deploy.agilesoftgroup.com/webhook/live-location"),
        // ✅ URL ของ Webhook
        headers: {"Content-Type": "application/json"},
        body: jsonEncode({
          "user_id": TELEGRAM_USER_ID,
          "chat_id": chatId,
          "message_thread_id": messageThreadId,
          "latitude": latitude,
          "longitude": longitude
        }),
      );
    } catch (e) {
      print("❌ Error sending location to n8n: $e");
    }
  }
}
