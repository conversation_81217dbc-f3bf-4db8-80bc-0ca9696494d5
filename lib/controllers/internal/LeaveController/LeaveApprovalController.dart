import 'dart:convert';
import 'dart:ui';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/controllers/utils/color.dart';
import 'package:mapp_ms24/controllers/utils/connect.dart';
import 'package:mapp_ms24/controllers/utils/loader.dart';
import 'package:mapp_ms24/controllers/utils/url.dart';
import 'package:mapp_ms24/models/leaveModel/absenceListModel.dart';
import 'package:mapp_ms24/views/screens/Widget/Widget.dart';
import 'package:mapp_ms24/views/screens/home/<USER>';
import 'package:mapp_ms24/views/screens/home/<USER>/Leave/LeaveScreen.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../views/screens/Widget/library.dart';

class LeaveAndAppController extends GetxController {
  RxString selectedAccepter = ''.obs;
  RxString inputReson = ''.obs;
  RxDouble dayDifference = 0.0.obs;
  RxInt Check = 0.obs;
  RxBool cheang = false.obs;
  bool expand = false;
  bool visible = false;
  int statusDateStart = 0;
  int statusDateStop = 0;
  int statusAlertSuccess = 0;
  var selectedLeaveType = ''.obs;
  var selectedStartTime;
  var selectedEndTime;
  var selectedTimetypemorning = ''.obs;
  var selectedTimetypeevening = ''.obs;
  var approveID = ''.obs;
  TextEditingController sumdateController = TextEditingController();
  int positionID = 0;

  List testLeavetype = [];
  List dataApprove = [];

  AbsenceModelList? testLeaveForm = AbsenceModelList();
  ProfileController profile = Get.put(ProfileController());
  LeaveScreen leaveScreen = Get.put(LeaveScreen());
  List<KeyValueModel> datas = [
    KeyValueModel(key: "dp_selectleavecreate".tr.toString(), value: "0"),
  ];

  List<KeyValueModelApprove> datasApprove = [
    KeyValueModelApprove(key: "dp_personApprove".tr.toString(), value: "0"),
  ];
  List Timetypemorning = ["8.30", "8.30", "13.00"];
  List Timetypeevening = ["12.00", "15.30", "16.00", "17.00"];

  List testAccepter = [];
  List<RxBool> isExpandList = List.generate(100, (index) => false.obs);
  resetData() {
    selectedLeaveType = ''.obs;
    selectedAccepter = ''.obs;
    selectedTimetypemorning = ''.obs;
    selectedTimetypeevening = ''.obs;
    inputReson = ''.obs;
    selectedStartTime = ''.obs;
    selectedEndTime = ''.obs;
    dayDifference = 0.0.obs;
  }

  void clearForm() {
    selectedLeaveType.value = '';
    selectedStartTime = null;
    selectedEndTime = null;
    selectedTimetypemorning.value = '';
    selectedTimetypeevening.value = '';
    approveID.value = '';
    sumdateController.clear();
  }
  // Method to toggle expand state for a specific index
  void toggleExpandState(int index) {
    isExpandList[index].value = !isExpandList[index].value;
  }

  Future<void> resetExpandList() async {
    for (int i = 0; i < isExpandList.length; i++) {
      isExpandList[i].value = false;
    }
  }

  Future<void> deleteItemAtIndex(int index) async {
    if (index >= 0 && index < testLeaveForm!.data!.length) {
      // Remove the item at the specified index
      testLeaveForm!.data!.removeAt(index);

      // Update the RxList to reflect the changes in the UI
      testLeaveForm.obs.refresh();
    }
  }

  void updateselectedEndTime(String value) {
    try {
      // Parse the date string with the correct format
      DateTime parsedDate = DateFormat('dd/MM/yyyy').parse(value);
      // Format the parsed date back to a string if needed
      String formattedDate = DateFormat('dd/MM/yyyy').format(parsedDate);
      selectedEndTime.value = formattedDate;
      statusDateStop = 1;
    } catch (e) {
      print('Error parsing date: $e');
    }
  }

  void updateCheck(value) {
    Check.value = value;
  }

  void chektime(String value) {
    if (Check == 0) {
      updateselectedStartTime(value);
    }
    if (Check == 1) {
      updateselectedEndTime(value);
    }
  }

  void updateselectedStartTime(String value) {
    try {
      // Parse the date string with the correct format
      DateTime parsedDate = DateFormat('dd/MM/yyyy').parse(value);
      // Format the parsed date back to a string if needed
      String formattedDate = DateFormat('dd/MM/yyyy').format(parsedDate);
      selectedStartTime.value = formattedDate;
      statusDateStart = 1;
    } catch (e) {
      print('Error parsing date: $e');
    }
  }

  void updatetestTimetypemorning(String value) {
    selectedTimetypemorning.value = value;
  }

  void updatetestTimetypeevening(String value) {
    selectedTimetypeevening.value = value;
    selectedTimetypeevening.value = value;
  }

  void updateselectedLeaveType(String value) {
    selectedLeaveType.value = value;
  }

  void updateselectedAccepter(String value) {
    selectedAccepter.value = value;
  }

  getdataLeave(_value, context) {
    getRstID(_value);
    getApproveName();
    loadcheckday();
    getTypeLeave();
  }

  Future<void> getApproveName() async {
    try {
      Map data = {"personID": profile.responseMember!.id};
      final res = await AppApi.callAPIjwt("POST", AppUrl.approveName, data);

      if (res["result"].length > 0) {
        for (int i = 0; i < res["result"].length; i++) {
          datasApprove.add(
            KeyValueModelApprove(
                key: "${res["result"][i]["FullName"].toString()}",
                value: "${res["result"][i]["FullName"].toString()}"),
          );
        }
      }
      update();
    } catch (e) {
      print(e);
    }
  }

  var ApproveID = "";

  Future<void> getIDApprove(ApproveName, context) async {
    try {
      AppLoader.loader(context);
      Map data = {"personID": profile.responseMember!.id};
      final res = await AppApi.callAPIjwt("POST", AppUrl.approveName, data);

      if (res["status"] == 200) {
        // int targetReasonID = 7;

        // ค้นหาดัชนีของรายการที่มี reasonID ตรงกับ targetReasonID
        var index = res["result"]
            .indexWhere((reason) => reason["FullName"] == ApproveName);

        // ดึงค่า description ของรายการที่พบ
        ApproveID = res["result"][index]["approveID"];
        AppLoader.dismiss(context);
      }
      update();
    } catch (e) {
      print(e);
    }
  }

  var rstID = "";

  Future<void> getRstID(_value) async {
    try {
      Map data = {"reasonID": _value.toString()};
      final res = await AppApi.callAPIjwt("POST", AppUrl.RstID, data);

      if (res["status"] == 200) {
        rstID = res["result"][0]["rstID"].toString();
      }
      update();
    } catch (e) {
      print(e);
    }
  }

  Future<void> loadcheckday() async {
    try {
      Map data = {};
      final res = await AppApi.callAPIjwt(
          "POST",
          "http://devdev.prachakij.com:8080/absenceNew/ApiAppMS24/createLeave/checkLeaveCheckdayApp.jsp?door=e6392b80c7d05dfbd61d4fb5c9c27d8238aa80e51c3fc41ad56c3b9521af56a9&personID=${profile.responseMember!.id}",
          data);
      if (res.length > 0) {
        checkday = res[0]["checkday"].toString();
      }
      update();
    } catch (e) {
      print(e);
    }
  }

  List<Map<String, dynamic>> checkLeaveApp = [];

  loadcheckLeaveApp(reasonID, context) async {
    var difference = 0;
    if (selectedStartTime == "" || selectedEndTime == "") {
    } else {
      var datetimestartSplit = selectedStartTime.split(" ")[0];
      var datestartSplit = datetimestartSplit.split("/");
      var datetimestopSplit = selectedEndTime.split(" ")[0];
      var datestopSplit = datetimestopSplit.split("/");
      final datestart = DateTime(int.parse(datestartSplit[2]),
          int.parse(datestartSplit[1]), int.parse(datestartSplit[0]));
      final datestop = DateTime(int.parse(datestopSplit[2]),
          int.parse(datestopSplit[1]), int.parse(datestopSplit[0]));
      difference = datestop.difference(datestart).inDays;
    }
    var datetimestartSplit = selectedStartTime.split(" ")[0];
    var datestartSplit = datetimestartSplit.split("/");
    var datetimestopSplit = selectedEndTime.split(" ")[0];
    var datestopSplit = datetimestopSplit.split("/");
    DateTime datestart = DateTime(
        int.parse(datestartSplit[2]), // ปี
        int.parse(datestartSplit[1]), // เดือน
        int.parse(datestartSplit[0]) // วัน
        );
    DateTime datestop = DateTime(
        int.parse(datestopSplit[2]), // ปี
        int.parse(datestopSplit[1]), // เดือน
        int.parse(datestopSplit[0]) // วัน
        );
    var LeaveStartDate =
        '${datestartSplit[0]}-${datestartSplit[1]}-${datestartSplit[2]}';
    var LeaveEndDate =
        '${datestopSplit[0]}-${datestopSplit[1]}-${datestopSplit[2]}';
    difference = datestop.difference(datestart).inDays;
    if (profile.responseMember!.id == "" ||
        profile.responseMember!.id == null ||
        profile.responseMember!.id == "null") {
      AppLoader.dismiss(context);
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "รหัสสมาชิกหายไปจาก app MS กรุณา logout และ login อีกครั้ง",
        duration: Duration(seconds: 3),
      );
    } else if (reasonID.toString() == "0") {
      AppLoader.dismiss(context);
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "กรุณาเลือกประเภทใบลา",
        duration: Duration(seconds: 3),
      );
    } else if (rstID.toString() == "" ||
        rstID.toString() == null ||
        rstID.toString() == "null") {
      AppLoader.dismiss(context);

      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "กรุณาเลือกประเภทใบลา อีกครั้งเพื่อค้นหา rstID",
        duration: Duration(seconds: 3),
      );
    } else if (statusDateStart == 0) {
      AppLoader.dismiss(context);

      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "กรุณาเลือกวันที่เริ่มต้น",
        duration: Duration(seconds: 3),
      );
    } else if (statusDateStop == 0) {
      AppLoader.dismiss(context);

      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "กรุณาเลือกวันที่สิ้นสุด",
        duration: Duration(seconds: 3),
      );
    } else if (difference < 0) {
      AppLoader.dismiss(context);
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message:
            "กรุณาเลือกวันที่ให้ถูกต้อง วันที่เริ่มต้องไม่มากกว่าวันที่สิ้นสุด",
        duration: Duration(seconds: 3),
      );
    } else {
      try {
        final Map<String, dynamic> data = {
          "memid": profile.responseMember!.id.toString(),
          "reasonID": reasonID.toString(),
          "rstID": rstID.toString(),
          "LeaveStartDate": LeaveStartDate.toString(),
          "LeaveEndDate": LeaveEndDate.toString(),
        };

        final url =
            Uri.parse('https://n8n-deploy.agilesoftgroup.com/webhook/sumdate');
        final Map<String, String> headers = {
          'Content-Type': 'application/json',
          'x-api-key': 'FIF12OExFj6uoE83hskYI2t03o8WSVF53H3vu9ky',
        };

        final response = await http.post(
          url,
          headers: headers,
          body: jsonEncode(data), // แปลงข้อมูลเป็น JSON
        );

        if (response != null) {
          final Map<String, dynamic> responseData = jsonDecode(response.body);

          checkLeaveApp = [responseData];
          loadcheckLeaveRule(context, reasonID);
          AppLoader.dismiss(context);
        } else {
          AppLoader.dismiss(context);

          Fluttertoast.showToast(
              msg: "ไม่สามารถดึง checkLeaveApp ได้",
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.TOP,
              backgroundColor: Colors.red,
              textColor: Colors.black,
              fontSize: 16.0);
          AppLoader.dismiss(context);
        }
      } catch (e) {
        print("Error occurred: $e");
      }
    }
  }

  var description = "";

  Future<void> getTypeLeave() async {
    try {
      Map data = {};

      final res = await AppApi.callAPIjwt("POST", AppUrl.typeLeave, data);

      if (res["status"] == 200) {
        for (int i = 0; i < res["result"].length; i++) {
          datas.add(
            KeyValueModel(
                key: "${res["result"][i]["description"].toString()}",
                value: "${res["result"][i]["reasonID"].toString()}"),
          );
        }
      }
      datas.sort((a, b) => a.key.compareTo(b.key));
      update();
    } catch (e) {
      print(e);
    }
  }

  var description1 = "";

  Future<String> apiPostRequest(dynamic url, Map<String, String> data) async {
    // ตรวจสอบและแปลง URL ให้เป็น Uri หากยังเป็น String อยู่
    final Uri parsedUrl = url is String ? Uri.parse(url) : url;

    if (kIsWeb) {
      final response = await http.post(
        parsedUrl,
        headers: {"Content-Type": "application/x-www-form-urlencoded"},
        body: data,
      );

      if (response.statusCode == 200) {
        return response.body;
      } else {
        throw Exception('Failed to load data from Web: ${response.statusCode}');
      }
    } else {
      final response = await http.post(
        parsedUrl,
        headers: {"Content-Type": "application/x-www-form-urlencoded"},
        body: data,
      );

      if (response.statusCode == 200) {
        return response.body;
      } else {
        throw Exception(
            'Failed to load data from Mobile: ${response.statusCode}');
      }
    }
  }

  Future<void> getreasonIDLeave(targetReasonID, context) async {
    try {
      AppLoader.loader(context);
      Map data = {};
      final res = await AppApi.callAPIjwt("POST", AppUrl.typeLeave, data);

      if (res["status"] == 200) {
        var index = res["result"].indexWhere(
            (reason) => reason["reasonID"] == int.parse(targetReasonID));

        if (index != -1) {
          description1 = res["result"][index]["description"];
          AppLoader.dismiss(context);
        } else {
          AppLoader.dismiss(context);
          const GetSnackBar(
            title: "เกิดข้อผิดพลาด",
            message: "กรุณาเลือกประเภทใบลาใหม่อีกครั้ง",
          );
        }
      }
      update();
    } catch (e) {
      print(e);
    }
  }

  Future<void> loadCheckAgeWork(context, _value) async {
    try {
      Map data = {};

      final res = await AppApi.apiRequest(
          "http://devdev.prachakij.com:8080/absenceNew/ApiAppMS24/createLeave/personConfirmApp.jsp?door=e6392b80c7d05dfbd61d4fb5c9c27d8238aa80e51c3fc41ad56c3b9521af56a9&personID=${profile.responseMember!.id} &reasonID=${_value.toString()}",
          data);
      List jsonResponse = json.decode(res);

      if (jsonResponse.length > 0) {
        if (jsonResponse[0]["status"].toString() == "true") {
          update();
        } else {
          alerterror(
              context,
              "ไม่สามารถทำใบลาพักร้อนได้ เนื่องจากอายุงานยังไม่ครบ 1 ปี หรือยังไม่บรรจุ จำนวนวันที่ทำงานคือ " +
                  jsonResponse[0]["numWork"].toString() +
                  " วัน");
          update();
        }
      } else {
        Fluttertoast.showToast(
            msg: "ไม่สามารถดึง CheckAgeWork ได้",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.TOP,
            backgroundColor: Colors.red,
            textColor: Colors.black,
            fontSize: 16.0);
      }
    } catch (e) {
      print(e);
    }
  }

  var checkday = "0";

  Future<void> loadcheckLeaveRule(context, _value) async {
    try {
      Map data = {"checkday": checkday.toString(), "reason": _value.toString()};
      final res = await AppApi.callAPIjwt("POST", AppUrl.typeLeave, data);

      if (res.length > 0) {
        if (res["status"].toString() != "") {
          loadcheckLeaveRuleExecute(context, _value);
        } else {
          loadcheckLeaveRuleExecute(context, _value);
        }
      }

      update();
    } catch (e) {
      print(e);
    }
  }

  var howday = "";

  Future<void> loadcheckLeaveRuleExecute(context, _value) async {
    var datetimestartSplit = selectedStartTime.split(" ")[0];
    var datestartSplit1 = datetimestartSplit.split("/");
    var datetimestopSplit = selectedEndTime.split(" ")[0];
    var datestopSplit1 = datetimestopSplit.split("/");

    var LeaveStartDate =
        '${datestartSplit1[0]}-${datestartSplit1[1]}-${datestartSplit1[2]}';
    var LeaveEndDate =
        '${datestopSplit1[0]}-${datestopSplit1[1]}-${datestopSplit1[2]}';
    // AppLoader.loader(context);
    try {
      Map data = {
        "code": checkLeaveApp[0]["code"].toString(),
        "numday": checkLeaveApp[0]["numday"].toString(),
        "msg": checkLeaveApp[0]["msg"].toString(),
        "startDate": LeaveStartDate.toString(),
        "endDate": LeaveEndDate.toString()
      };
      final res =
          await AppApi.callAPIjwt("POST", AppUrl.checkLeaveRuleExecute, data);
      List jsonResponse = json.decode(res);

      if (jsonResponse.length > 0) {
        if (jsonResponse[0]["status"].toString() == "confirm") {
          howday = jsonResponse[0]["howday"].toString();
          sumdateController.text = jsonResponse[0]["howday"].toString();
          update();
        } else if (jsonResponse[0]["status"].toString() == "msg") {
          showSingleButtonDialog(context, jsonResponse[0]["msg"].toString());
          update();
        } else {
          howday = jsonResponse[0]["howday"].toString();
          sumdateController.text = jsonResponse[0]["howday"].toString();
          update();
        }
      } else {
        Fluttertoast.showToast(
            msg: "ไม่สามารถดึง checkLeaveRuleExecute ได้",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.TOP,
            backgroundColor: Colors.red,
            textColor: Colors.black,
            fontSize: 16.0);
      }
      // AppLoader.dismiss(context);

      update();
    } catch (e) {
      print(e);
      // AppLoader.dismiss(context);
    }
  }

  Future<void> loadApprove() async {
    try {
      Map data = {"approveID": profile.responseMember!.id.toString()};

      final res = await AppApi.callAPIjwt("POST", AppUrl.absenceList, data);

      if (res["status"] == 200) {
        testLeaveForm = AbsenceModelList.fromJson(res["result"]);
        dataApprove = res["result"];
      } else {
        dataApprove = [];
      }
      update();
    } catch (e) {
      print(e);
    }
  }

  TextEditingController _timeStartController = TextEditingController();
  TextEditingController _timeStopController = TextEditingController();
  Future<void> createLeave(
      context,
      personID,
      reasonID,
      typeID,
      phoneNumber,
      detail,
      startDate,
      endDate,
      howday,
      deputize,
      approveID,
      selectedTimetypemorning,
      selectedTimetypeevening,
      Submit,
      _LeaveDetailsController) async {
    AppLoader.loader(context);
    // loadTokenApprove(context,approveID);
    createAbsence(
        context,
        personID,
        reasonID,
        typeID,
        phoneNumber,
        detail,
        startDate,
        endDate,
        howday,
        deputize,
        approveID,
        selectedTimetypemorning,
        selectedTimetypeevening,
        Submit,
        _LeaveDetailsController);
    AppLoader.dismiss(context);
  }

  void showTopSnackBar2(BuildContext context, String message, Color color) {
    final snackBar = SnackBar(
      content: Text(message),
      backgroundColor: color,
      behavior: SnackBarBehavior.floating,
      margin: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top + 16.0,
        left: 16.0,
        right: 16.0,
      ),
      duration: Duration(seconds: 2),
    );
    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }

  Future<void> createAbsence(
      context,
      personID,
      reasonID,
      typeID,
      phoneNumber,
      detail,
      startDate,
      endDate,
      howday,
      deputize,
      approveID,
      selectedTimetypemorning,
      selectedTimetypeevening,
      Submit,
      _LeaveDetailsController) async {
    DateTime date;
    DateFormat format = DateFormat("dd/MM/yyyy");
    var formattedstart = format.parse(startDate);
    var formattedstop = format.parse(endDate);

    var datestartDB = formattedstart.toString().split(" ")[0] + " 08:15:00";
    var datestopDB = formattedstop.toString().split(" ")[0] + " 05:00:00";
    try {
      AppLoader.loader(context);

      final Map<String, dynamic> data = {
        "personID": personID.toString(),
        "reasonID": reasonID.toString(),
        "typeID": typeID.toString(),
        "phoneNumber": phoneNumber.toString(),
        "detail": detail.toString(),
        "startDate": datestartDB.toString(),
        "endDate": datestopDB.toString(),
        "howday": howday.toString(),
        "deputize": "",
        "approveID": approveID.toString(),
        "startTime": _timeStartController.text,
        "endTime": _timeStopController.text,
        "Submit": Submit.toString(),
      };
      print("data");

      print(data);
      final url = Uri.parse(
          'https://n8n-deploy.agilesoftgroup.com/webhook/e603900f-4f73-49b0-9627-48e18536ed2a');
      final Map<String, String> headers = {
        'Content-Type': 'application/json',
        'x-api-key': 'FIF12OExFj6uoE83hskYI2t03o8WSVF53H3vu9ky',
      };

      final response = await http.post(
        url,
        headers: headers,
        body: jsonEncode(data),
      );

      final Map<String, dynamic> responseData = jsonDecode(response.body);

      if (responseData["status"].toString() == "200") {
        Get.back();
        _LeaveDetailsController.clear();
        Get.showSnackbar(
          GetSnackBar(
            message: 'สร้างใบลาสำเร็จ',
            icon: Icon(Icons.check_circle, color: AppColors.SeaGreen),
            backgroundColor: AppColors.SeaGreen.withOpacity(0.5),
            duration: Duration(seconds: 3),
            borderRadius: 12,
            borderColor: AppColors.SeaGreen,
            snackPosition: SnackPosition.TOP,
          ),
        );
        AppLoader.dismiss(context);
      } else if (responseData["status"].toString() == "400") {
        showSingleButtonDialog(context, responseData["msg"].toString());

        AppLoader.dismiss(context);
      } else {
        Fluttertoast.showToast(
            msg: "สร้างใบลาไม่สำเร็จ กรุณาลองใหม่อีกครั้ง",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.TOP,
            backgroundColor: Colors.red,
            textColor: Colors.black,
            fontSize: 16.0);
        AppLoader.dismiss(context);
      }
      AppLoader.dismiss(context);

      update();
    } catch (e) {
      print(e);
    }
  }

  List tokenNoti = [];
  loadTokenApprove(context, approveID) async {
    if (approveID.toString() != "" &&
        approveID.toString() != null &&
        approveID.toString() != "null") {
      Map data = {"id": approveID};

      final res = await AppApi.callAPIjwt("POST", AppUrl.tokenNotifyById, data);

      if (res["status"] == 200) {
        for (var i = 0; i < res.length.toInt(); i++) {
          tokenNoti.add(res["result"]["tokenNotify"].toString());
        }
      } else if (res["status"] == 400) {
        alerterror(context, res["result"]["msg"].toString());
      } else {
        Fluttertoast.showToast(
            msg: "ไม่สามารถค้นหา token ผู้อนุมัติได้",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.TOP,
            backgroundColor: Colors.red,
            textColor: Colors.black,
            fontSize: 16.0);
      }
      update();
    } else {
      Fluttertoast.showToast(
          msg: "ไม่มีรหัส ผู้อนุมัติ",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.TOP,
          backgroundColor: Colors.red,
          textColor: Colors.black,
          fontSize: 16.0);
    }
  }

  Widget alertSuccess(context) {
    return ClipRect(
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 1.97, sigmaY: 1.97),
        child: Container(
          alignment: Alignment.topCenter,
          decoration: BoxDecoration(
            color: const Color(0x00bebebe),
          ),
          child: Container(
            width: mediaQuery(context, "w", 520),
            height: mediaQuery(context, "h", 550),
            margin: EdgeInsets.only(top: mediaQuery(context, "h", 405)),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(mediaQuery(context, "w", 80)),
              color: const Color(0xfffcf6e4),
            ),
            child: Stack(
              children: [
                Container(
                  alignment: Alignment.topCenter,
                  margin: EdgeInsets.only(top: mediaQuery(context, "h", 82)),
                  child: SvgPicture.string(
                    '<svg viewBox="163.0 0.0 50.0 50.0" ><path transform="translate(160.43, -1.93)" d="M 27.57070350646973 6.61553430557251 C 22.14501762390137 6.61553430557251 17.04413795471191 8.728425025939941 13.20761585235596 12.56494903564453 C 9.371090888977051 16.40147399902344 7.25820255279541 21.50235176086426 7.25820255279541 26.92803764343262 C 7.25820255279541 32.35371780395508 9.371092796325684 37.4546012878418 13.20761680603027 41.2911262512207 C 17.04413986206055 45.12765121459961 22.14501953125 47.24053955078125 27.57070350646973 47.24053955078125 C 32.99638748168945 47.24053955078125 38.09726333618164 45.12765121459961 41.93379211425781 41.2911262512207 C 45.77031326293945 37.4546012878418 47.88320541381836 32.35372161865234 47.88320541381836 26.92803764343262 C 47.88320541381836 21.50235176086426 45.77031326293945 16.40147399902344 41.933837890625 12.56494903564453 C 38.09736251831055 8.728425025939941 32.99638748168945 6.61553430557251 27.57070350646973 6.61553430557251 Z M 27.57070350646973 1.928033590316772 C 41.37783050537109 1.928033590316772 52.57070159912109 13.12090492248535 52.57070159912109 26.92803764343262 C 52.57070159912109 40.73516845703125 41.37783050537109 51.92803955078125 27.57070350646973 51.92803955078125 C 13.76357555389404 51.92803955078125 2.570701122283936 40.73516845703125 2.570701122283936 26.92803955078125 C 2.570701122283936 13.12090969085693 13.76357173919678 1.928035974502563 27.57070350646973 1.928035974502563 Z M 24.44570159912109 36.30303955078125 L 30.69570350646973 36.30303955078125 L 30.69570350646973 42.55304336547852 L 24.44570159912109 42.55304336547852 L 24.44570159912109 36.30303955078125 Z M 24.44570159912109 11.30303478240967 L 30.69570350646973 11.30303478240967 L 30.69570350646973 30.05303573608398 L 24.44570159912109 30.05303573608398 L 24.44570159912109 11.30303478240967 Z" fill="#934d0f" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                    allowDrawingOutsideViewBox: true,
                    height: mediaQuery(context, "h", 50),
                  ),
                ),
                Container(
                  alignment: Alignment.topCenter,
                  margin: EdgeInsets.only(top: mediaQuery(context, "h", 155)),
                  child: Text(
                    "ui_createLeaveSuccess".tr.toString(),
                    style: TextStyle(
                      fontFamily: 'SukhumvitSet-Bold',
                      fontSize: mediaQuery(context, "h", 33),
                      color: const Color(0xff302c49),
                      letterSpacing: mediaQuery(context, "h", 1.32),
                      fontWeight: FontWeight.w700,
                      height: 0.9393939393939394,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Container(
                  alignment: Alignment.topCenter,
                  margin: EdgeInsets.only(top: mediaQuery(context, "h", 210)),
                  child: Text(
                    "ui_createdetail".tr.toString(),
                    style: TextStyle(
                      fontFamily: 'SukhumvitSet-Medium',
                      fontSize: mediaQuery(context, "h", 28),
                      color: const Color(0xff302c49),
                      letterSpacing: mediaQuery(context, "h", 1.12),
                      fontWeight: FontWeight.w500,
                      height: 1.5357142857142858,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Container(
                  alignment: Alignment.topCenter,
                  margin: EdgeInsets.only(top: mediaQuery(context, "h", 379)),
                  child: GestureDetector(
                    onTap: () {
                      Navigator.of(context).pop();
                      Navigator.pushReplacement(
                          context,
                          MaterialPageRoute(
                              builder: (context) => LeaveScreen()));
                    },
                    child: Container(
                      alignment: Alignment.center,
                      width: mediaQuery(context, "w", 250),
                      height: mediaQuery(context, "h", 90),
                      decoration: BoxDecoration(
                        borderRadius:
                            BorderRadius.circular(mediaQuery(context, "h", 30)),
                        color: const Color(0xff7f420c),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0x1a000000),
                            offset: Offset(0, 1),
                            blurRadius: 2,
                          ),
                        ],
                      ),
                      child: Text(
                        "ui_approveok".tr.toString(),
                        style: TextStyle(
                          fontFamily: 'SukhumvitSet-Bold',
                          fontSize: mediaQuery(context, "h", 30),
                          color: const Color(0xfffcf6e4),
                          letterSpacing: mediaQuery(context, "h", 1.2),
                          fontWeight: FontWeight.w700,
                          // height: 1.0333333333333334,
                          shadows: [
                            Shadow(
                              color: const Color(0x26000000),
                              offset: Offset(0, 1),
                              blurRadius: 1,
                            )
                          ],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  sendNotificationApprove(context, token, detail) async {
    String url =
        'https://mhmo3nnbr5.execute-api.ap-southeast-1.amazonaws.com/latest';
    Map map = {
      "menu": "sendNotify",
      "title": "คุณมีใบลาที่รออนุมัติ",
      "body":
          "จาก คุณ ${profile.responseMember!.full_name_th} รายละเอียดการลา ${detail.toString()}",
      "token": tokenNoti,
      "activity": "approveLeave",
      "point": "0"
    };
    await apiRequest(url, map);
  }

  //_________________________________________________________________________

  sendNotiUserApprove(token, Submit, detail, _remarkController) async {
    var message = "";
    if (Submit.toString() == "อนุมัติ") {
      message =
          "ใบลาของคุณ ${profile.responseMember!.full_name_th} >>> รายละเอียดการลา : ${detail.toString()}";
    } else {
      message =
          "ใบลาของคุณ ${profile.responseMember!.full_name_th} >>> เหตุผลไม่อนุมัติ : ${_remarkController.toString()} >>> รายละเอียดการลา : ${detail.toString()}";
    }

    String url =
        'https://mhmo3nnbr5.execute-api.ap-southeast-1.amazonaws.com/latest';
    Map map = {
      "menu": "sendNotify",
      "title": "ใบลาของคุณได้รับการ : " + Submit,
      "body": message.toString(),
      "token": token,
      "activity": "approveLeave",
      "point": "0"
    };
    await apiRequest(url, map);
  }

  var approveComment = "";

  Future<void> approveLeave(
      context,
      absenceID,
      typeID,
      description,
      detail,
      cancalComment,
      date,
      startDate,
      endDate,
      sumDate,
      personID,
      uthFirstName,
      uthLastName,
      reasonID,
      user_add,
      approveID,
      Submit) async {
    int positionID;
    String approveComment;

    try {
      AppLoader.loader(context);

      if (Submit == "อนุมัติ") {
        positionID = 2;
        approveComment = "";
      } else {
        positionID = 3;
        approveComment = "";
      }

      final Map<String, dynamic> data = {
        "absenceID": absenceID.toString(),
        "positionID": positionID.toString(),
        "reasonID": reasonID.toString(),
        "user_add": user_add.toString(),
        "approveComment": approveComment,
        "approveID": approveID.toString(),
        "detail": detail.toString(),
        "startdate": startDate.toString(),
        "enddate": endDate.toString(),
        "Submit": "ตกลง",
        "typeID": typeID.toString(),
      };

      final url = Uri.parse(
          'https://n8n-deploy.agilesoftgroup.com/webhook/75cf7718-773e-43a1-ae87-3441d45ec6c8');
      final Map<String, String> headers = {
        'Content-Type': 'application/json',
        'x-api-key': 'FIF12OExFj6uoE83hskYI2t03o8WSVF53H3vu9ky',
      };

      final response =
          await http.post(url, headers: headers, body: jsonEncode(data));
      print("Response status: ${response}");
      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        print("Response status: ${response.statusCode}");
        if (responseData["status"].toString() == "200") {
          Fluttertoast.showToast(
            msg: "อนุมัติใบลาสำเร็จ",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.BOTTOM,
            backgroundColor: Colors.teal.withOpacity(0.1),
            textColor: Colors.teal,
            fontSize: 16.0,
          );
          sendNotiUserApprove(context, Submit, tokenNoti, detail.toString());
        } else {
          Fluttertoast.showToast(
            msg: "ไม่สามารถอนุมัติใบลาได้",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.BOTTOM,
            backgroundColor: Colors.orange,
            textColor: Colors.black,
            fontSize: 16.0,
          );
        }
      } else {
        Fluttertoast.showToast(
          msg: "เกิดข้อผิดพลาดในการอนุมัติใบลา",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          backgroundColor: Colors.red,
          textColor: Colors.white,
          fontSize: 16.0,
        );
      }

      AppLoader.dismiss(context);
      update();
    } catch (e) {
      print("Error occurred: $e");
      Fluttertoast.showToast(
        msg: "เกิดข้อผิดพลาดในการอนุมัติใบลา",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
        fontSize: 16.0,
      );
      AppLoader.dismiss(context);
    }
  }

  Future<Widget> alertSuccessCreate(context) async {
    if (statusAlertSuccess == 1) {
      return ClipRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 1.97, sigmaY: 1.97),
          child: Container(
            alignment: Alignment.topCenter,
            decoration: BoxDecoration(
              color: const Color(0x00bebebe),
            ),
            child: Container(
              width: mediaQuery(context, "w", 520),
              height: mediaQuery(context, "h", 550),
              margin: EdgeInsets.only(top: mediaQuery(context, "h", 405)),
              decoration: BoxDecoration(
                borderRadius:
                    BorderRadius.circular(mediaQuery(context, "w", 80)),
                color: const Color(0xfffcf6e4),
              ),
              child: Stack(
                children: [
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 82)),
                    child: SvgPicture.string(
                      '<svg viewBox="163.0 0.0 50.0 50.0" ><path transform="translate(160.43, -1.93)" d="M 27.57070350646973 6.61553430557251 C 22.14501762390137 6.61553430557251 17.04413795471191 8.728425025939941 13.20761585235596 12.56494903564453 C 9.371090888977051 16.40147399902344 7.25820255279541 21.50235176086426 7.25820255279541 26.92803764343262 C 7.25820255279541 32.35371780395508 9.371092796325684 37.4546012878418 13.20761680603027 41.2911262512207 C 17.04413986206055 45.12765121459961 22.14501953125 47.24053955078125 27.57070350646973 47.24053955078125 C 32.99638748168945 47.24053955078125 38.09726333618164 45.12765121459961 41.93379211425781 41.2911262512207 C 45.77031326293945 37.4546012878418 47.88320541381836 32.35372161865234 47.88320541381836 26.92803764343262 C 47.88320541381836 21.50235176086426 45.77031326293945 16.40147399902344 41.933837890625 12.56494903564453 C 38.09736251831055 8.728425025939941 32.99638748168945 6.61553430557251 27.57070350646973 6.61553430557251 Z M 27.57070350646973 1.928033590316772 C 41.37783050537109 1.928033590316772 52.57070159912109 13.12090492248535 52.57070159912109 26.92803764343262 C 52.57070159912109 40.73516845703125 41.37783050537109 51.92803955078125 27.57070350646973 51.92803955078125 C 13.76357555389404 51.92803955078125 2.570701122283936 40.73516845703125 2.570701122283936 26.92803955078125 C 2.570701122283936 13.12090969085693 13.76357173919678 1.928035974502563 27.57070350646973 1.928035974502563 Z M 24.44570159912109 36.30303955078125 L 30.69570350646973 36.30303955078125 L 30.69570350646973 42.55304336547852 L 24.44570159912109 42.55304336547852 L 24.44570159912109 36.30303955078125 Z M 24.44570159912109 11.30303478240967 L 30.69570350646973 11.30303478240967 L 30.69570350646973 30.05303573608398 L 24.44570159912109 30.05303573608398 L 24.44570159912109 11.30303478240967 Z" fill="#934d0f" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                      allowDrawingOutsideViewBox: true,
                      height: mediaQuery(context, "h", 50),
                    ),
                  ),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 155)),
                    child: Text(
                      "ui_createLeaveSuccess".tr.toString(),
                      style: TextStyle(
                        fontFamily: 'SukhumvitSet-Bold',
                        fontSize: mediaQuery(context, "h", 33),
                        color: const Color(0xff302c49),
                        letterSpacing: mediaQuery(context, "h", 1.32),
                        fontWeight: FontWeight.w700,
                        height: 0.9393939393939394,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 210)),
                    child: Text(
                      "ui_createdetail".tr.toString(),
                      style: TextStyle(
                        fontFamily: 'SukhumvitSet-Medium',
                        fontSize: mediaQuery(context, "h", 28),
                        color: const Color(0xff302c49),
                        letterSpacing: mediaQuery(context, "h", 1.12),
                        fontWeight: FontWeight.w500,
                        height: 1.5357142857142858,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 379)),
                    child: GestureDetector(
                      onTap: () {
                        Navigator.of(context).pop();
                        Navigator.pushReplacement(
                            context,
                            MaterialPageRoute(
                                builder: (context) => HomeScreen()));
                      },
                      child: Container(
                        alignment: Alignment.center,
                        width: mediaQuery(context, "w", 250),
                        height: mediaQuery(context, "h", 90),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(
                              mediaQuery(context, "h", 30)),
                          color: const Color(0xff7f420c),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0x1a000000),
                              offset: Offset(0, 1),
                              blurRadius: 2,
                            ),
                          ],
                        ),
                        child: Text(
                          "ui_approveok".tr.toString(),
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Bold',
                            fontSize: mediaQuery(context, "h", 30),
                            color: const Color(0xfffcf6e4),
                            letterSpacing: mediaQuery(context, "h", 1.2),
                            fontWeight: FontWeight.w700,
                            // height: 1.0333333333333334,
                            shadows: [
                              Shadow(
                                color: const Color(0x26000000),
                                offset: Offset(0, 1),
                                blurRadius: 1,
                              )
                            ],
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    } else {
      return Container();
    }
  }

  void showSingleButtonDialog(context, msg) {
    Get.defaultDialog(
      title: "เกิดข้อผิดพลาด",
      titleStyle: TextStyle(fontSize: 16.h, fontFamily: 'SukhumvitSet-Text'),
      middleText: msg,
      middleTextStyle:
          TextStyle(fontSize: 14.h, fontFamily: 'SukhumvitSet-Text'),
      textConfirm: "OK",
      buttonColor: Color(0xff7f420c),
      onConfirm: () {
        AppLoader.loader(context);
        howday = "0";
        sumdateController.text = "";
        selectedStartTime = ''.obs;
        selectedEndTime = ''.obs;
        inputReson = ''.obs;
        Get.back();
        AppLoader.dismiss(context);
      },
      confirmTextColor: Colors.white,
    );
  }
}
