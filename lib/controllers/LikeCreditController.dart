import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../views/screens/Widget/library.dart';
import 'internal/BenefitController /BenefitController.dart';
import 'internal/Profile/profileController.dart';
import 'internal/Tacontroller/Tacontroller.dart';

class LikeCreditController extends GetxController {
  ProfileController profileCtr = Get.find<ProfileController>();
  BenefitController benCrt = Get.find<BenefitController>();
  Tacontroller taCrt = Get.find<Tacontroller>();
  ProfileController profile = Get.find<ProfileController>();

  RxBool checking = true.obs;
  RxList dataPrivilege = <dynamic>[].obs;
  RxDouble likecredit = 0.0.obs;
  RxString date = "".obs;
  RxString summer_no = "".obs;
  RxString rtdNum = "".obs;
  RxString dateYear = "".obs;
  RxString remainin = "".obs;
  RxString Vacation = "".obs;
  RxString date_d = "".obs;
  RxString date_m = "".obs;
  RxDouble calmm = 0.0.obs;
  RxString sum = "".obs;
  RxString resignationStatus = "".obs;
  RxString Press = "".obs;
  RxString LateDay = "".obs;
  RxString Exchange = "".obs;
  RxString workStatus = "".obs;
  RxBool sumVacation = true.obs;
  RxInt instate = 0.obs;
  RxString ChangeLikeCredit = "".obs;
  RxInt statusAlertChangeSuccess = 0.obs;
  RxString namePrivilege = "".obs;
  RxString detaiPrivilege = "".obs;
  RxString pointChange = "".obs;
  RxString idActivityLikeCredit = "".obs;

  @override
  void onInit() {
    initializePage();
    update();
    super.onInit();
  }

  @override
  void onClose() {
    // Call the method to update values here
    initializePage();
    update();
    super.onClose();
  }

  // Page Initialization
  void initializePage() async {
    await benCrt.welfare(profile.responseMember!.id);
    await benCrt.doreloadbenefits(Get.context!);
    await taCrt.getTime(Get.context!);

    await getlikecredit();
    await loadPrivilege();
    await checkSumVacation();
    await checkPressLikeCredit();
    await checkLate();
    await checkLateExchange();
    checking.value = false;

    update();
    refresh();
  }

  // Load privilege data
  Future<void> loadPrivilege() async {
    try {
      String url =
          'https://mhmo3nnbr5.execute-api.ap-southeast-1.amazonaws.com/latest';
      Map map = {"menu": "searchPrivilegeLikecredit", "namePage": "likeCredit"};

      final response = await apiRequest(url, map);
      var jsonResponse = json.decode(response);

      if (jsonResponse["statusCode"].toString() == "200") {
        dataPrivilege.value = jsonResponse["result"];
      } else {
        Fluttertoast.showToast(
            msg: "ไม่สามารถค้นหาสิทธิ์ที่เปิดให้แลกได้",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.TOP,
            backgroundColor: Colors.red,
            textColor: Colors.black,
            fontSize: 16.0);
      }
      update();
    } catch (e) {
      await Sentry.captureException(e);
    }
  }

  // Get like credit
  Future<void> getlikecredit() async {
    String url = 'https://apicredit.prachakij.com/userBalance';
    Map map = {"address": profile.responseMember!.phone_like};
    try {
      final response = await apiRequest(url, map);
      var jsonResponse = json.decode(response);

      if (jsonResponse["res_status"].toString() == "200") {
        likecredit.value = double.parse(jsonResponse['balance'].toString());
      } else {
        Fluttertoast.showToast(
            msg: "ไม่สามารถค้นหาจำนวน likecredit ได้",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.TOP,
            backgroundColor: Colors.red,
            textColor: Colors.black,
            fontSize: 16.0);
      }
      update();
    } catch (e) {
      await Sentry.captureException(e);
    }
  }

  // Save changes to like credit
  Future<void> saveChangLikeCredit(String idActivityLikeCredit) async {
    String url =
        'https://mhmo3nnbr5.execute-api.ap-southeast-1.amazonaws.com/latest';
    Map map = {
      "menu": "insertChangeLikecredit",
      "idActivityLikeCredit": idActivityLikeCredit.toString(),
      "id": profile.responseMember!.id.toString(),
    };
    try {
      final response = await apiRequest(url, map);
      var jsonResponse = json.decode(response);

      if (jsonResponse["statusCode"].toString() == "200") {
        Get.back(); // Close dialog or navigate back
      } else {
        Fluttertoast.showToast(
            msg: jsonResponse["msg"].toString(),
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.TOP,
            backgroundColor: Colors.red,
            textColor: Colors.black,
            fontSize: 16.0);
      }
      update();
    } catch (e) {
      await Sentry.captureException(e);
    }
  }

  Future<void> checkSumVacation() async {
    try {
      if (profile.responseMember!.id == "" ||
          profile.responseMember!.id == null ||
          profile.responseMember!.id == "null") {
        // Display error
        Get.snackbar("Error",
            "รหัสสมาชิกหายไปจาก app MS กรุณา logout และ login อีกครั้ง",
            snackPosition: SnackPosition.TOP,
            backgroundColor: Colors.red,
            colorText: Colors.white);
      } else {
        String url =
            "http://devdev.prachakij.com:8080/absenceNew/ApiAppMS24/createLeave/checkLeaveApp.jsp?door=e6392b80c7d05dfbd61d4fb5c9c27d8238aa80e51c3fc41ad56c3b9521af56a9&personID=" +
                profile.responseMember!.id.toString() +
                "&reasonID=7" +
                "&rstID=9" +
                "&startDate=" +
                convertDateTime(taCrt.datetime, "dd-MM-YYYY").toString() +
                "&endDate=" +
                convertDateTime(taCrt.datetime, "dd-MM-YYYY").toString();

        Map map = {};
        final response = await apiRequest(url, map);
        List jsonResponse = json.decode(response);

        if (jsonResponse[0]['msg'] == 'วันลาพักร้อนหมดแล้ว') {
          sumVacation.value = true;
        } else {
          sumVacation.value = false;
        }

        resignationStatus.value = profile.responseMember!.status ?? "";

        if (resignationStatus.value == "A") {
          workStatus.value = "รออนุมัติลาออก";
        } else if (resignationStatus.value == "W") {
          workStatus.value = "อนุมัติลาออกแล้วแต่ทำงานอยู่";
        } else if (resignationStatus.value == "O") {
          workStatus.value = "ไม่มาทำงานแล้ว แต่รอคืนเงิน";
        }
      }
      update();
    } catch (e) {
      await Sentry.captureException(e);
    }
  }

  Future<void> checkPressLikeCredit() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var id = await prefs.getString('id');

    try {
      if (profile.responseMember!.id != "" &&
          profile.responseMember!.id != null &&
          profile.responseMember!.id != "null") {
        String url =
            'https://mhmo3nnbr5.execute-api.ap-southeast-1.amazonaws.com/latest';
        Map map = {
          "menu": "CheckPressLikecredit",
          "id": profile.responseMember!.id.toString(),
          "create_time": convertDateTime(taCrt.datetime, "DDB").toString()
        };

        final response = await apiRequest(url, map);
        var jsonResponse = json.decode(response);

        Press.value = jsonResponse.toString() == "[]" ? "0" : "1";
      } else {
        Fluttertoast.showToast(
            msg: "รหัสสมาชิกหายไปจาก app MS กรุณา logout และ login อีกครั้ง",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.TOP,
            backgroundColor: Colors.red,
            textColor: Colors.black,
            fontSize: 16.0);
      }
      update();
    } catch (e) {
      await Sentry.captureException(e);
    }
  }

  Future<void> checkLate() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var id = await prefs.getString('id');

    try {
      if (profileCtr.responseMember!.id != "" &&
          profileCtr.responseMember!.id != null &&
          profileCtr.responseMember!.id != "null") {
        String url =
            'https://mhmo3nnbr5.execute-api.ap-southeast-1.amazonaws.com/latest';
        Map map = {
          "menu": "checkLate",
          "id": profile.responseMember!.id.toString(),
          "month": convertDateTime(taCrt.datetime, "MM").toString(),
          "year": convertDateTime(taCrt.datetime, "YYYY").toString()
        };

        final response = await apiRequest(url, map);
        var jsonResponse = json.decode(response);

        LateDay.value = jsonResponse.toString() == "[]"
            ? "0"
            : jsonResponse.length.toString();
      } else {
        Fluttertoast.showToast(
            msg: "รหัสสมาชิกหายไปจาก app MS กรุณา logout และ login อีกครั้ง",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.TOP,
            backgroundColor: Colors.red,
            textColor: Colors.black,
            fontSize: 16.0);
      }
      update();
    } catch (e) {
      await Sentry.captureException(e);
    }
  }

  Future<void> checkLateExchange() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var id = await prefs.getString('id');

    try {
      if (profile.responseMember!.id != "" &&
          profile.responseMember!.id != null &&
          profile.responseMember!.id != "null") {
        String url =
            'https://mhmo3nnbr5.execute-api.ap-southeast-1.amazonaws.com/latest';
        Map map = {
          "menu": "CheckLateExchange",
          "id": profile.responseMember!.id.toString(),
          "month": convertDateTime(taCrt.datetime, "MM").toString(),
          "year": convertDateTime(taCrt.datetime, "YYYY").toString()
        };

        final response = await apiRequest(url, map);
        var jsonResponse = json.decode(response);

        Exchange.value = jsonResponse.toString() == "[]"
            ? "0"
            : jsonResponse.length.toString();
      } else {
        Fluttertoast.showToast(
            msg: "รหัสสมาชิกหายไปจาก app MS กรุณา logout และ login อีกครั้ง",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.TOP,
            backgroundColor: Colors.red,
            textColor: Colors.black,
            fontSize: 16.0);
      }
      update();
    } catch (e) {
      await Sentry.captureException(e);
    }
  }
}
