import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get/get.dart';

import 'package:mapp_ms24/controllers/internal/securestorage_controller.dart';
import 'package:mapp_ms24/models/mspModel/msp_point.dart';
import 'package:mapp_ms24/models/mspModel/msp_tier.dart';

import 'package:shared_preferences/shared_preferences.dart';
import '../internal/Profile/profileController.dart';
import '../utils/connect.dart';
import '/views/screens/service/service.dart';
import 'dart:convert';
import '/configs/firestore.dart';

class MspService extends GetxController {
  String? idToken;
  String userId = '';
  RxBool isLoadingPoints = true.obs;

  late PocketMember pocketMember = PocketMember();
  late TeamId teamId = TeamId();
  late PocketTeam teamBalance = PocketTeam();

  RxInt totalPoints = 0.obs;
  RxString recentTier = ''.obs;

  late ProfileController profileController;

  RxBool isOwnerWalletTeam = false.obs;

  @override
  Future<void> onInit() async {
    profileController = await Get.find<ProfileController>();
    userId = profileController.responseMember!.id.toString();
    getWallet();
    getTeamMSP();
    super.onInit();
  }

  Future<bool> claimSpin(amount) async {
    Map dataActivity = {
      "activityID": '8b52d5cd-2056-4ffd-a25c-0c7b90623272',
      "pkg_id_member": userId,
      "merchantID": '78ce7814-b9e9-4187-a139-2faf08cfdc6e',
      "amount": amount,
    };

    final resPOI = await AppApi.postLikePoint(
      'https://api2.likepoint.io/transactions-activity/pay-spin-in-app',
      dataActivity,
    );
    if (resPOI['statusCode'] == 200) {
      return true;
      // Get.toNamed("/home");
      // Get.back();
    } else {
      // Get.back();
      if (resPOI['data'][0] == 'promo is end') {
        return false;
        // AppService.
      } else {
        return false;
        // AppService.toastError(context, "ไม่พบกิจกรรม");
      }
    }
  }

  Future<String> getMemberInfo() async {
    try {
      try {
        var response = await AppApi.getLikepoint(
          "https://api2.likepoint.io/member/check-member-pkg/$userId",
        );

        if (response) {
          return response["data"][0]["memberInfo"]["id"];
        } else {
          return "0"; // Return a default value if response is not valid
        }
      } catch (error) {
        return "Error"; // Return a default error value
      }
    } catch (e) {
      return "Error"; // Return a default error value
    }
  }

  getWallet() async {
    try {
      String merchantID = "78ce7814-b9e9-4187-a139-2faf08cfdc6e";

      var walletRequestData = {
        "pkg_id_member": profileController.responseMember!.id,
        "merchantID": merchantID,
      };
      var response = await AppApi.postLikePoint(
        "https://api2.likepoint.io/wallet/getWalletByPKGIDAndMerchantID",
        walletRequestData,
      );

      if (response["total"] != 0) {
        pocketMember = PocketMember.fromJson(response['data'].first);
      } else {
        var regisPartner = {
          "phone": profileController.responseMember!.phone_like,
          "pkg_id_member": profileController.responseMember!.id,
          "firstName": profileController.responseMember!.full_name_th,
          "lastName": profileController.responseMember!.surname_th,
          "merchantID": merchantID,
          "pkg_member_status": true
        };
        var resRegisPartner = await AppApi.postLikePoint(
          "https://api2.likepoint.io/member/regis-partners",
          regisPartner,
        );
        if (resRegisPartner != null) {
          var response2 = await AppApi.postLikePoint(
            "https://api2.likepoint.io/wallet/getWalletByPKGIDAndMerchantID",
            walletRequestData,
          );

          if (response2["total"] != 0) {
            pocketMember = PocketMember.fromJson(response2['data'].first);
            update();
          }
        } else {
          print('Partner registration failed.');
        }
      }
    } catch (error) {
      debugPrint('Error in getWallet: $error');
    }
  }

  Future<dynamic> getTeamMSP() async {
    try {
      Map data = {
        "member_id": profileController.responseMember!.id,
      };

      final response = await AppApi.postHttps(
          "https://n8n-cpdg.agilesoftgroup.com/webhook/ms24/getTeamMSP", data);

      if (response["status"] == 200) {
        teamId = TeamId.fromJson(response);
        update();

        await getPocketBalanceTeam();
      } else {
        return null;
      }
    } catch (error) {
      return null;
    }
  }

  Future<dynamic> getPocketBalanceTeam() async {
    try {
      Map requestBody = {
        'phone': teamId.teamId,
        'merchantID': "78ce7814-b9e9-4187-a139-2faf08cfdc6e",
      };

      final responseWallet = await AppApi.postLikePoint(
          'https://api2.likepoint.io/wallet/findWalletByPhone', requestBody);

      if (responseWallet['data'][0]['memberInfo'] != null) {
        final pocketBalance = responseWallet['data'][0]['walletInfo']['pocket']
            [0]['pocketBalance'];
        teamBalance.pocketBalance = AppService.numberFormatNon0(pocketBalance);
        update();
      } else {
        return null;
      }
    } catch (error) {
      return null;
    }
  }
}
