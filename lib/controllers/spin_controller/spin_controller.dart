import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:confetti/confetti.dart';
import 'package:flutter/material.dart';
import 'package:flutter_fortune_wheel/flutter_fortune_wheel.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:mapp_ms24/Models/language.dart';
import 'dart:math' as math;

import 'package:mapp_ms24/configs/color_system.dart';
import 'package:mapp_ms24/controllers/spin_controller/point_service.dart';
// import 'package:mapp_ms24/models/spinModel/spin_fortunes.dart';

// import 'package:nativtest/controllers/app_config_service.dart';
// import 'package:nativtest/controllers/loyalty_controller/loyalty_service.dart';
// import 'package:nativtest/controllers/setting_controller/profile_controller.dart';
// import 'package:nativtest/models/lotto_reward.dart';
// import 'package:nativtest/screens/loyalty_screen/lotto_reward_screen.dart';
// import 'package:nativtest/widgets/toast_widget.dart';
import 'package:path_provider/path_provider.dart';
import 'package:screenshot/screenshot.dart';

import '../../models/spinModel/spin_fortunes.dart';
import '../../models/spinModel/spin_rewards.dart';
import '../../models/userModel/usermodel.dart';
import '../../views/screens/spinwheel/spin_reward_screen.dart';
import '../internal/Profile/profileController.dart';
// import 'package:screenshot/screenshot.dart';
// import 'package:share_plus/share_plus.dart';

class SpinController extends GetxController {
  late StreamController<int> dailyLottoController;

  ProfileController profileController = Get.put(ProfileController());
  RxList<FortuneItem> items = <FortuneItem>[].obs;
  late ConfettiController confettiController;
  // late ResponseMember responseMember;
  // late LoyaltyService loyaltyService;
  late ScreenshotController screenshotController;
  // late ProfileController profileController;
  RxBool isUserCanSpin = false.obs;
  // AppConfigService appConfig = Get.find<AppConfigService>();
  String firebaseConfigCollection = 'Lotto';
  List<SpinReward> rewards = [];
  late SpinReward? spinReward;

  MspService mspService = Get.put(MspService());

  List<SpinFortune> fortunes = [];
  late SpinFortune spinFortune;

  RxBool isSpining = false.obs;
  Timer? timer;
  int spinIndex = -2;
  int spinTimeTillErr = 0;

  DateTime? startSpinDateTime;
  DateTime? endSpinDateTime;

  @override
  Future<void> onInit() async {
    spinReward = SpinReward(unit: "Point", amount: 0, ratio: 0);

    spinTimeTillErr = 0;
    // profileController = Get.find();
    dailyLottoController = StreamController<int>();
    // firebaseConfigCollection = appConfig.configFirebaseLottoCollection;
    await setFortuneItems();
    await checkUserCanSpin(profileController.responseMember!.id);
    // isUserCanSpin.value = profileController.isUserCanSpinLotto.value;
    // await checkIfAlreadySpin();
    // loyaltyService = Get.find();

    screenshotController = ScreenshotController();
    confettiController =
        ConfettiController(duration: const Duration(milliseconds: 1000));
    spinIndex = await getTheRewardFromSpin();
    super.onInit();
  }

  @override
  void dispose() {
    dailyLottoController.close();
    timer?.cancel();
    super.dispose();
  }

  Future<void> setFortuneItems() async {
    items.value = [];
    DocumentSnapshot documentSnapshot = await FirebaseFirestore.instance
        .collection("Lotto")
        .doc('prizeList')
        .get()
        .catchError((error) {});

    if (documentSnapshot.exists) {
      Map<String, dynamic>? data =
          documentSnapshot.data() as Map<String, dynamic>?;
      rewards = lottoRewardFromJson(data!['priz_List_Json']);
    }

    List<Color> colorBg = [
      const Color(0xFF39C1A8),
      const Color(0xFF4AACF3),
      const Color(0xFFFEBA52),
      const Color(0xFFF3564C),
      lightPrimaryColor,
      const Color(0xFFA3C6FF),
    ];

    for (var i = 0; i < rewards.length; i++) {
      items.add(lottoItem(
          imagePath: rewards[i].amount == 1000
              ? 'assets/Timmi/Timmi Worring.png'
              : "assets/Timmi/TimmiLovelyMini.png",
          isSvg: rewards[i].unit == 'Points',
          text: rewards[i].unit == 'None'
              ? 'No'.tr
              : rewards[i].amount.toString(),
          isSpecial: rewards[i].ratio == 1,
          backgroundColor: i <= colorBg.length - 1
              ? colorBg[i]
              : colorBg[i - colorBg.length],
          prizeAmount: 1000,
          prizeUnit: rewards[i].unit));
    }
    update();
  }

  Future<Map<String, dynamic>?> getFortuneRewards(String? id) async {
    try {
      // Fetch document snapshot
      DocumentSnapshot documentSnapshot =
          await FirebaseFirestore.instance.collection("Fortune").doc(id).get();

      if (documentSnapshot.exists) {
        final data = documentSnapshot.data() as Map<String, dynamic>?;

        if (data != null) {
          return {
            "prizeUnit": data["person_fortune"]['prizeUnit'] ?? '',
            "prizeAmount": data["person_fortune"]['prizeAmount'] ?? 0
          };
        }
      }
      return null;
    } catch (error) {
      return null;
    }
  }

  Future<void> checkUserCanSpin(String? id) async {
    try {
      DocumentSnapshot documentSnapshot =
          await FirebaseFirestore.instance.collection("Fortune").doc(id).get();

      if (documentSnapshot.exists) {
        final data = documentSnapshot.data() as Map<String, dynamic>?;

        if (data != null) {
          isUserCanSpin.value = data["playing"] == false ? true : false;
          update;
        }
      }
    } catch (error) {
      debugPrint("Failed to fetch Fortune rewards: $error");
    }
  }

  Future<void> updateUserCanSpin(String? id) async {
    try {
      await FirebaseFirestore.instance
          .collection("Fortune")
          .doc(id)
          .update({"playing": true});
      DocumentSnapshot documentSnapshot =
          await FirebaseFirestore.instance.collection("Fortune").doc(id).get();

      if (documentSnapshot.exists) {
        final data = documentSnapshot.data() as Map<String, dynamic>?;

        if (data != null) {
          isUserCanSpin.value = data["playing"] == false ? true : false;
        }
      }
    } catch (error) {
      debugPrint("Failed to update or fetch Fortune rewards: $error");
    }
  }

  FortuneItem lottoItem(
      {required String imagePath,
      required bool isSvg,
      required String text,
      required String prizeUnit,
      required int prizeAmount,
      required bool isSpecial,
      required Color backgroundColor}) {
    return FortuneItem(
        style: FortuneItemStyle(
            color: backgroundColor, borderColor: whiteColor, borderWidth: 2),
        child: Transform.rotate(
          angle: math.pi / 2,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                width: Get.width * 0.12,
                height: Get.width * 0.12,
                decoration: const ShapeDecoration(
                  color: whiteColor,
                  shape: OvalBorder(),
                ),
                child: isSvg
                    ? Image.asset(imagePath)
                    : Container(
                        decoration: ShapeDecoration(
                            shape: const OvalBorder(),
                            image:
                                DecorationImage(image: AssetImage(imagePath))),
                      ),
              ),
              Padding(
                padding: EdgeInsets.only(
                    top: Get.height * 0.01, bottom: Get.height * 0.023),
                child: Text(
                  text,
                  style: const TextStyle(
                    color: whiteColor,
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              isSpecial
                  ? SvgPicture.asset(
                      'assets/svgs/icon-crown.svg',
                      width: 14,
                    )
                  : Opacity(
                      opacity: 0.4,
                      child: Container(
                        width: 6,
                        height: 6,
                        decoration: const ShapeDecoration(
                          color: whiteColor,
                          shape: OvalBorder(),
                        ),
                      ),
                    ),
              const SizedBox(height: 36)
            ],
          ),
        ));
  }

  Future<int> getTheRewardFromSpin() async {
    isSpining.value = true;
    update();
    int spinRewardIndex = -1;

    final tempSpinReward =
        await getFortuneRewards(profileController.responseMember!.id);

    if (tempSpinReward != null) {
      spinReward = rewards.firstWhere(((reward) =>
          reward.unit == tempSpinReward['prizeUnit'] &&
          reward.amount == tempSpinReward['prizeAmount']));
      spinRewardIndex = rewards.indexOf(spinReward!);
    }
    isSpining.value = false;
    update();
    return spinRewardIndex;
  }

  Future<void> spinTheWheel() async {
    spinTimeTillErr = 0;
    isUserCanSpin.value = false;
    update();

    await checkUserCanSpin(profileController.responseMember!.id);

    timer = Timer.periodic(const Duration(milliseconds: 100), (time) async {
      if (isSpining.value) {
        dailyLottoController.add(0);
      } else {
        if (spinIndex < 0 && spinTimeTillErr <= 2) {
          spinTimeTillErr += 1;
          dailyLottoController.add(0);
          spinIndex = await getTheRewardFromSpin();
        } else {
          dailyLottoController
              .add(spinTimeTillErr == 3 && spinIndex < 0 ? 0 : spinIndex);
          timer?.cancel();
          spinTimeTillErr = 0;
        }
      }
    });
  }

  Future<void> checkReward() async {
    if (spinIndex >= 0) {
      if (spinReward?.unit == "Points") {}
      await checkSpinDateTime();
      updateUserCanSpin(profileController.responseMember!.id);
      update();
      confettiController.play();
      final tempSpinReward =
          await getFortuneRewards(profileController.responseMember!.id);

      await mspService.claimSpin(tempSpinReward!["prizeAmount"]);
      Get.to(
        () => const SpinRewardScreen(),
        transition: Transition.downToUp,
        curve: Curves.ease,
        duration: const Duration(milliseconds: 300),
      );
    } else {
      isUserCanSpin.value = true;
      checkUserCanSpin(profileController.responseMember!.id);
      update();
    }
  }

  Future<bool> checkSpinDateTime() async {
    try {
      final documentSnapshot = await FirebaseFirestore.instance
          .collection("Lotto")
          .doc("prizeList")
          .get();

      if (documentSnapshot.exists) {
        final data = documentSnapshot.data() as Map<String, dynamic>?;

        if (data != null) {
          Timestamp? startTimestamp = data["start_time"] as Timestamp?;
          Timestamp? endTimestamp = data["end_time"] as Timestamp?;

          if (startTimestamp != null && endTimestamp != null) {
            DateTime startSpinDateTime = startTimestamp.toDate();
            DateTime endSpinDateTime = endTimestamp.toDate();

            DateTime now = DateTime.now();
            return now.isAfter(startSpinDateTime) &&
                now.isBefore(endSpinDateTime);
          }
        }
      }
      return false;
    } catch (e) {
      return false;
    }
  }
}
