// ignore_for_file: unnecessary_brace_in_string_interps

import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/get_state_manager/get_state_manager.dart';

class StoragePin {
  StoragePin(this.key, this.value);

  final String key;
  final String value;
}

class SecureStorage extends GetxController {
  AndroidOptions _getAndroidOptions() => const AndroidOptions(
        encryptedSharedPreferences: true,
      );
  final _storage = const FlutterSecureStorage();

  Future<void> writeSecureData(String key, String value) async {
    await _storage.write(
        key: key, value: value, aOptions: _getAndroidOptions());
    update();
  }

  Future<String?> readSecureData(String key) async {
    var readData =
        await _storage.read(key: key, aOptions: _getAndroidOptions());
    update();
    return readData;
  }

  Future<void> deleteSecureData(String key) async {
    var deleteData =
        await _storage.delete(key: key, aOptions: _getAndroidOptions());
    return deleteData;
  }

  Future<void> deleteAllSecureData() async {
    var deleteDataAll =
        await _storage.deleteAll(aOptions: _getAndroidOptions());
    return deleteDataAll;
  }

  Future<bool> containsKeyInSecureData(String key) async {
    var containsKey =
        await _storage.containsKey(key: key, aOptions: _getAndroidOptions());
    return containsKey;
  }
}
