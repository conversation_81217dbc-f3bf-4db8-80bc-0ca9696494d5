import "package:get/get.dart";

class CustomTranslations extends Translations {
  @override
  Map<String, Map<String, String>> get keys => {
        // English
        "en": {
          //language screen
          "Language": "Language",
          //onboarding screen
          "onboarding title 1": "MPC Driven Security",
          "onboarding body 1": "sdsdsddsd",
          "Home": "Home",
          "login": "login",
          "ui_login": "login",
          "fild_username": "Username",
          "fild_password": "Password",
          "btn_signin": "signin",
          "btn_signup": "signup",
          "ui_login_app": "confirm login",

          "home": "home",
          "ui_hello": "Hi",
          "ui_morning_y": "you have checked in TA",
          "ui_morning_n": "you are haven’t to checked in TA",
          "ui_bye_y": "you have checked out TA",
          "ui_bye_n": "you are haven’t to checked out TA",
          "btn_peopleProcess": "People\nProcess",
          "btn_4C": "4C",
          "btn_tools": "Work\nTools",
          "btn_bitrix": "My job",
          "btn_CuGtc": "CU / GTC",
          "btn_seeAll": "See All",
          "ui_peopleProcess": "People Process",
          "ui_4C": "4C",
          "ui_CuGtc": "CU / GTC",
          "ui_tools": "Work Tools",
          "btn_issue": "Main Issue",
          "btn_toolsPMS": "Work\nTools PMS",
          "btn_toolsAAM": "Work\nTools AAM",
          "btn_toolsCS": "Work\nTools CS24",
          "btn_ta": "TA",
          "btn_welfare": "Benefits",
          "btn_callcenter": "Call center",
          "btn_buyGTC": "GTC",
          "btn_scanQR": "scanQR",
          "btn_cew": "Save CEW",
          "btn_idea": "PKG\nIdea",
          "btn_Leave": " Leave /\nApproval",
          "ui_statistics": "Statistics",
          "btn_Income": "INCOME",
          "btn_LikeWallet": "LIKEWALLET",
          "btn_MSP":"MSP",
          "btn_wheel":"Spin Wheel",

          "ui_LikeWallet": "LIKEWALLET",
          "ui_yeartpye": "(CE.)",
          "btn_gtc": "GTC",
          "btn_cu": "CU",
          "btn_meta_cu": "Meta CU",
          "btn_Insurance_CU": "INSURANCE",
          "ui_Salary": "SALARY",
          "ui_Incentive": "INCENTIVE",
          "ui_dateUpdate": "UPDATE",
          "ui_Reward": "REWARD",
          "ui_PFS": "PFS",
          "ui_Bath": "Bath",
          "ui_Totally": "Totally",
          "ui_Locked": "LOCKED ",
          "ui_Unlock": "Unlock",
          "ui_LIKE": "LIKE",
          "ui_BV": "VALUE BV",
          "ui_sharing": "Sharing amount",
          "ui_TotallyGTC": "Total",
          "ui_TotallyGTCcoin": "GTCcoin",
          "ui_Unit": "UNIT",
          "ui_Collateralized": "COLLATERALIZED",
          "ui_TotallyCU": "TOTAL",
          "ui_SavingCU": "SAVING",
          "ui_InsuranceCU": "LOCKED",
          "ui_DebtCU": "LOAN",
          "ui_reward": "REWARD",
          "ui_reward2": "From investment",
          "ui_Conditions": "*Pending share to conditions",
          "ui_ContractCU": "Contract",
          "ui_value": "value(Bath)",
          "ui_bath": "Bath",
          "ui_likevalue": "like",
          "ui_cu": "CU",
          "ui_MetaCu": "META CU",
          "ui_gtc": "GTC",
          "ui_debt": "CU Loans",
          "ui_like": "Likepoint",
          "ui_likecredit": "LIKE CREDIT",
          "ui_detailFull": "see more",
          "ui_Savingmoney": "Saving money",
          "ui_position": "Position",
          "ui_purpose": "Purpose",
          "ui_borrowcu": "ordinary loan agreement",
          "ui_nondisclosure": "Non-Dis.\nAgreement",
          "ui_passcode": "Passcode",
          "ui_create_passcode": "Create Passcode",
          "ui_confirm_passcode": "Confirm Passcode",
          "ui_languages": "Languages",
          "ui_feedback": "Feedback",
          "ui_guid": "MS24 Guid",
          "ui_supporter": "Support",
          "ui_talk": "CHAT",
          "ui_logout": "Log out",
          "ui_logoutalert": "Log out",
          "ui_logoutCF": "Are you want to log out\nFor MS24 ?",
          "btn_cancel": "Cancel",
          "btn_logout": "Log out",
          "ui_update": "About to update",
          "ui_updateDetail":
              "MS24 Mobile Application\nHas been revised New functionality\nFor better use Please update the version.\n\nPress the “Update” button below to update.",
          "btn_update": "Update",
          "btn_After": "Later",
          "ui_titleAlert": "Coming Soon",
          "ui_detailAlert": "This menu is not yet\navailable now.",
          "ui_detailAlert2":
              "File you selected too large\nPlease make file smaller.",
          "ui_titleAlert2": "Error",
          "ui_detailAlert3": "Customer information not found\ntry new search",
          "btn_okAlert": "Done",
          "ui_theme": "Theme",
          "ui_theme_0": "Device default",
          "ui_theme_1": "Dark mode",
          "ui_theme_2": "Light mode",
          "modify": "modify",
          "ui_modify": "About to improvement",
          "ui_modifyDetail":
              "MS24 Mobile Application\nClosed for revision And temporarily not serving this menu\n\nSorry for the inconvenience.",

          "passscode": "passcode",
          "ui_enter": "Enter your passcode",
          "ui_wrong": "Passcode wrong , try again",
          "ui_lostpin": "If you forget your pin, click here to log out",
          "ui_lostpin1": "If you forget your password",
          "ui_lostpin2": "Click here",
          "changepasscode": "changepasscode",
          "ui_setting": "passcode setting",
          "ui_resetpasscode": "change passcode",
          "ui_putpasscodedefals": "old passcode",
          "ui_putpasscodenew": "new passcode",
          "ui_confirmpasscodenew": "confirm passcode",
          "ui_passwrong": "Passcode wrong , try again",
          "ui_alertpass": "Change Success!",
          "ui_alertdata":
              "Your passcode was a change\nsuccess already, Thank you",
          "btn_ok": "Okay",
          "ui_alerthome": "Cancel to Setting",
          "ui_datahome": "Are you want to cancel\nTo changing the passcode ?",
          "btn_continew": "Go back",
          "btn_okhome": "Cancel",

          "changlanguagessetting": "changlanguagessetting",
          "ui_selectlanguages": "Select Languages",
          "btn_eng": "English",
          "btn_th": "Thai",
          "ui_LanguageSetting": "Language Setting",
          "ui_settingCFf": "Are you want to changing\nTo",
          "ui_settingCFl": "version ?",
          "ui_selecteng": "English",
          "ui_selectth": "Thai",
          "ui_selectkm": "Cambodia",
          "ui_selectlao": "Laos",
          "btn_settingcancel": "Cancel",
          "ui_settingchange": "Change",

          "feedback": "feedback",
          "ui_headFeedback": "Feedback",
          "ui_subject": "Title",
          "tf_typingSj": "Typing to suggest.",
          "ui_detailFeedback": "Detail / Description",
          "tf_detailFeedback": "Typing details",
          "ui_imgUpload": "Upload picture",
          "btn_imgUpload": "Upload picture",
          "btn_saveFeedback": "Save",
          "ui_feedbacOK": "Success",
          "ui_feedbackDe": "Thank you so much\nfor sending your Feedback.",
          "ui_feedbackOK": "Ok",
          // "ui_reCu": "RETURN REWARD",

          "contact": "contact",
          "ui_headcontact": "contact",
          "ui_subjectcontact": "Title",
          "tf_typingSjcontact": "Typing to contact.",
          "ui_detailcontact": "Detail",
          "tf_detailcontact": "Typing details",
          "ui_imgUploadcontact": "Upload picture",
          "btn_imgUploacontact": "Upload picture",
          "btn_savecontact": "Send",
          "ui_contactOK": "Success",
          "ui_contactDe": "Thank you so much\nfor sending your Contact.",
          "ui_contactkOK": "Ok",
          "ui_"

          "ta": "ta",
          "ui_ta": "SIGNIN TA",
          "ui_date": "Date",
          "ui_timeNow": "Time",
          "btn_checkin": "Check In",
          "btn_checkin2": "Check In(Afternoon)",
          "btn_checkout": "Check out",
          "ui_problem":
              "Problems can be reported to Telegram in the service room.",
"ui_error":"Error",
          "reasonforlate": "reasonforlate",
          "ui_why": "Why are you late?",
          "tf_message": "Text a message…",
          "btn_send": "Send",

          "sendproblem": "sendproblem",
          "ui_what": "What ’s the problem?",
          "ui_notWorking": "This application is not working.",
          "ui_notInternet": "Internet is not connected.",

          "reciveProblem": "reciveProblem",
          "ui_wait":
              "* We are received a message,\nPlease waiting for 5 minute to contact.",

          "notification": "notification",
          "ui_notify": "NOTIFICAITON",
          "ui_claim": "Claim LIKE",
          "btn_claim": "CLAIM",
          "btn_claim_new": "CLAIM NOW!",
          "ui_act": "Activities",
          "ui_news": "News",
          "ui_course": "Course",
          "pkgWelfare": "PKG Welfare",
          "ui_select": "Select menu",
          "btn_basic": "Basic welfare",
          "btn_flexible": "Flexible welfare",
          "btn_health": "Health insurance",
          "ui_basic": "Basic",
          "ui_flexible": "Flexible",
          "ui_health": "Health",
          "ui_contact": "Contact us, for more detail",

          "pkgwelfarebasic": "pkgwelfarebasic",
          "ui_headBasic": "Basic welfare",
          "ui_detail": "Basic welfare details",
          "ui_welfarecity":
              "Welfare rights according to the labor law of each country",
          "ui_welfareout": "Labor welfare beyond the law",
          "ui_welfareout1": "Welfare for wedding ceremony",
          "ui_welfareout2": "Maternity benefits",
          "ui_welfareout3": "Benefits for Ordination",
          "ui_welfareout4": "Funeral benefits: family staff / staff family",
          "ui_welfareout5": "Life and accident insurance benefits",
          "ui_welfareout6": "Benefits for group health insurance",
          "ui_welfareout7": "Benefits of group life insurance",
          "ui_welfareout8":
              "Annual health check-up assistance welfare (Only work group)",
          "ui_welfareout9": "Welfare Scholarships",
          "ui_welfareout10": "Employment benefits",
          "ui_welfareout11":
              "staff uniform benefits (excluding mechanic forms)",
          "ui_welfareout12": "Welfare, budget, remuneration, staff resignation",
          "ui_welfareout13": "Employee benefits in case of resignation",
          "ui_welfareout14": "Foreign work allowance /",
          "ui_welfareout15": "On-site work / training",
          "ui_welfareout16": "Accommodation welfare",
          "ui_helpbudget": "The organization has a budget to help.",
"ui_Lp_unit":"LP",
          "pkgwelfarebasicform": "pkgwelfarebasicform",
          "ui_headBasicform": "Basic welfare",
          "ui_status": "status",
          "dp_select": "select",
          "dp_member": "Regular staff",
          "dp_test": "Probation staff",
          "dp_hire": "Contract staff",
          "dp_Advisor": "Advisor",
          "ui_datework": "Please select date",
          "ui_remarkdatework":
              "Specify the date of events such as ordination, wedding, etc. according to the specified conditions And if in the case of maternity leave, insert the date of maternity leave every time",
          "tf_datework": "mm/dd/yyyy",
          "ui_selectwelfare": "Choose benefits",
          "ui_remarkwelfare":
              "In the case of leaving ordination for more than 15 days, select >>>>> ordination work (ordination over 15 days) and enter the date to return to work in the field on the date of returning to work for the ordained walk 15 days too !!",
          "dp_welfareselect": "select welfare items",
          "dp_marri": "Wedding ceremony",
          "dp_monk": "Ordination",
          "dp_monk15": "Ordination (ordination over 15 days)",
          "dp_baby": "Give birth",
          "dp_gran":
              "Relatives funeral (father-mother of spouse and sibling, same parent, grandfather, grandmother)",
          "dp_father": "Family staff funeral (father-mother, child, spouse)",
          "dp_advisor":
              "Funeral; staff of the counselor group, including the parents, spouses and children of the counselor died.",
          "ui_link":
              "Attach link cards for various ceremonies / birth certificates",
          "ui_remarklink": "(If you don't have a card yet, put another one)",
          "ui_showlink": "link ที่อัพโหลด",
          "ui_card": "For still no ceremony cards",
          "dp_cardselect": "Select",
          "dp_nocard": "No card",
          "ui_bank": "Account number for receiving money (Kasikorn Thai only)",
          "ui_remarkbank": "*If there is a leading 0, enter - Ex. 0-********* *",
          "tf_typingbank": "Typing bank",
          "ui_remarkbasic": "note",
          "tf_typingnote": "Typing note",
          "ui_monk":
              "For the topic * Ordination work (Ordained over 15 days) *",
          "ui_remarkmonk":
              "* Please enter the date of return to work as well *",
          "btn_savebasic": "Save",
          "btn_nextbasic": "NEXT",
          "btn_beforebasic": "Previous",

          "pkgwelfareflexible": "pkgwelfareflexible",
          "ui_headFlexible": "Flexible welfare",
          "ui_amount": "FlexibleWelfareLimit",
          "ui_amount1": "Amount withdrawn",
          "ui_balance": "Net balance",
          "btn_apply": "Apply",
          "ui_history": "History",
          "ui_recive": "Received",
          "btn_headhealth":"You withdraw Health insurance",
          "ui_withdraw_money": "Withdraw money",


          "pkgwelfareflexiblewithdraw": "pkgwelfareflexiblewithdraw",
          "ui_headFlexibleWithdraw": "Flexible welfare",
          "ui_amountWithdrow": "Available balance",
          "ui_amount2": "Amount",
          "tf_amount": "Typing amount",
          "ui_grouptype": "Receipt category",
          "dr_select": "Select list",
          "ui_uploadimmage": "Upload receipt",
          "ui_exImage": "receipt example",
          "ui_linkimmage": "link image",
          "btn_uploadimmage": "Upload Photo",
          "btn_save": "Save",
          "btn_details":"Confirm welfare withdrawal",
          "btn_details2":"You withdraw benefits flexibly",
          "btn_details3":"amount",
          "btn_details4":"baht",
          "btn_details5":"kip",
          "btn_details6":"yes or no?",

          "pkgwelfareflexibleconfirm": "pkgwelfareflexibleconfirm",
          "ui_headFlexibleConfirm": "Flexible welfare",

          "pkgwelfarehealth": "pkgwelfarehealth",
          "ui_headhealth": "Health insurance",
          "ui_selectinsurance": "Select list",
          "btn_selectIPD": "IPD insurance",
          "btn_selectOPD": "OPD insurance",
          "ui_conditionIPD": "Health insurance conditions IPD...",
          "ui_conditionOPD": "Health insurance conditions OPD...",
          "ui_messageIPD_thai1":
              "Medical expenses not exceeding 10,000 baht / time according to the withdrawal conditions as follows: (Including room / doctor fee / medicine) \n- Pay a room 1,500 baht / day  \n- Consultation fee for a specialist doctor 2,000 baht / time \n- The actual drug cost does not exceed the total credit limit of 10,000 baht / time \n Surgery cost 5,000 baht / time. ",
          "ui_messageIPD_thai":
              "welfare conditions for IPD and OPD \nConditions are in accordance with the announcement \nof the PAO team.",
          "ui_messageIPD_rplc1":
              "Medical expenses not exceeding 4,500,000 Kip / time according to the withdrawal conditions as follows: (Including room / doctor fee / medicine) \n- Pay a room 450,000 Kip / day \n- Consultation fee for a specialist doctor 600,000 Kip / time \n- The actual drug cost does not exceed the total credit limit of 4,500,000 Kip / time \n Surgery cost 4,500,000 Kip / time. ",
          "ui_messageIPD_rafco1":
              "Medical expenses not exceeding 500  / time according to the withdrawal conditions as follows: (Including room / doctor fee / medicine) \n- Pay a room 50  / day \n- Consultation fee for a specialist doctor 66.66  / time \n- The actual drug cost does not exceed the total credit limit of 500  / time \n Surgery cost 500  / time. ",
          "ui_messageOPD_thai1": "Outpatient treatment fee 3,600 baht per year",
          "ui_messageOPD_rplc1":
              "Outpatient treatment fee 1,080,000 Kip per year",
          "ui_messageOPD_rafco1": "Outpatient treatment fee 110  per year",
          "ui_messageNOTE":
              "*** can be used for regular staff only \n *** Treat at a clinic or hospital only. In the case of buying drugs at pharmacies, you can not get it. \n *** Receipt that do not bring for withdrawal, such as a pregnancy examination receipt, can not be reimbursed \n Details at PAO or inquire No. 152 153 154 and 158 ",
          "btn_statusWithdrow": "Status",

          "pkgwelfarehealthwithdraw": "pkgwelfarehealthwithdraw",
          "ui_headhealthwithdraw": "Health insurance",
          "ui_statusWithdrawPKG": "Transaction status insurance",
          "ui_Used": "Used",
          "ui_Balance": "BALANCE",
          "ui_IPD": "Insurance IPD",
          "ui_OPD": "Insurance OPD",
          "ui_amountWithdraw": "Amount spent",
          "ui_listWithdraw": "History",
          "ui_claimtypeIPD": "Claimed IPD",
          "ui_claimtypeOPD": "Claimed OPD",
          "btn_key": "Apply",

          "pkgwelfarehealthfrom": "pkgwelfarehealthfrom",
          "ui_headhealthfrom": "Health insurance",
          "ui_headamount": "Reserve amount paid form PAO (Urgent)",
          "tf_healthamount": "Typing amount",
          "ui_healthremark":
              "* In the event that PAO is not given Ask permission to enter 0",
          "ui_headtype": "Claim category",
          "dp_selecthealth": "Select list",
          "dp_out": "Outpatient",
          "dp_in": "Inpatient",
          "ui_headhealthamountnet": "Total cost",
          "tf_amountnet": "Typing amount",
          "ui_amountnetremark":
              "* The total amount actually paid according to the bill.",
          "ui_headamountroom": "room’s cost",
          "tf_amountroom": "Typing amount",
          "ui_roomremark":
              "* mattress room fee charged as per receipt (if not available, enter 0)",
          "ui_headamountday": "Number of days cost",
          "tf_amounttday": "Typing day",
          "ui_dayremark":
              "* Number of days the bill has been charged on the receipt. (If there is no amount, enter 0)",
          "btn_next": "Next",

          "pkgwelfarehealthfrom2": "pkgwelfarehealthfrom2",
          "ui_headhealthfrom2": "Health insurance",
          "tf_remark": "* If not, please enter 0",
          "tf_typingamount": "Typing amount",
          "ui_visitingdoctor": "Doctor diagnose’s cost",
          "ui_amountday": "Amount day for Doctor diagnose",
          "tf_amountday": "Typing amount",
          "ui_treatmentcost": "Other medical costs",
          "ui_surgerycost": "Surgery cost",
          "ui_medication": "Home medication",
          "btn_before": "Previous",
          "btn_next2": "Next",

          "pkgwelfarehealthfrom3": "pkgwelfarehealthfrom3",
          "ui_headhealthfrom3": "Health insurance",
          "tf_remarknamebank": "* Such as Siriwan Songsri etc.",
          "tf_remarknumbank": "*Ex. **********",
          "tf_remarktel": "*Ex. ***********",
          "ui_doc": "Attachment (Original’s)",
          "btn_image": "Upload Photo",
          "ui_remark": "Note",
          "tf_typingremark": "Typing detail",
          "ui_namebank": "Bank tranfer account’s name",
          "tf_naembank": "Typing detail",
          "ui_numbank": "Kasikorn Account no.",
          "tf_numbank": "Account no.",
          "ui_tel": "Phone",
          "tf_tel": "Phone no.",
          "btn_before3": "Previous",
          "btn_next3": "Next",

          "pkgwelfarehealthfrom4": "pkgwelfarehealthfrom4",
          "ui_headhealthfrom4": "Health insurance",
          "ui_historymodify": "History of treatment / Hospital name",
          "tf_historymodify": "Typing detail",
          "ui_namedoc": "Attending physician’s name",
          "tf_namedoc": "Typing detail",
          "ui_becurse": "Admit’s caused",
          "tf_becurse": "Typing detail",
          "ui_drug": "Medicine",
          "tf_drug": "Typing detail",
          "ui_typedrug": "Type of drug",
          "ui_typedrug":
              "* Specify the type of drug received in this treatment.",
          "btn_savefrom": "Save",
          "btn_before4": "Previous",

          "pkgwelfarehealthconfirm": "pkgwelfarehealthconfirm",
          "ui_headwelfarehealthConfirm": "Health insurance",

          "GTCbuyORsell": "GTCbuyORsell",
          "ui_GTCbuyORsell": "GTC",
          "ui_GTCbuy": "buy",
          "ui_GTCsell": "sell",
          "ui_GTCbuycondition": "Read conditions",
          "ui_GTCbuycondition2": "Read conditions",
          "ui_GTCbuyRule": "GTC purchase conditions",
          "ui_GTCbuyRuleDetail":
              "Staff working, they are packed as regular Staff.\nbuy shares per person GTC's terms conditions.\neach purchase\nStaff purchase Please attach transfer\nTransfer likepoint on the date of notification.\n\nIf checked Not transferred,\nTeam requested to cancel the GTC\nthat have notified the purchase.",
          "btn_GTCbuyRuleOK": "Done",
          "ui_GTCsellRule": "GTC Sell conditions",
          "ui_GTCsellRuleDetail":
              "1.Can sell up to 20.0% of the GTC\nyou hold\n2.Can sell no more than 380 Units\n( About 10,000 Bath )\n3.Can sell no more than the amount\nof GTC, that is open for sale remaining.",
          "btn_GTCsellRuleOK": "Done",
          "ui_GTCunit": "Unit",
          "ui_GTCbath": "Bath",
          "btn_GTCbuy": "BUY",

          "ui_GTCheadBuy": "GTC",
          "ui_GTCbv": "Buy for BV rates.",
          "ui_GTCnamesale": "Seller",
          "ui_GTCnumsale": "Amount of GTC for sale",
          "ui_GTCnumbuy": "Amount of GTC purchased",
          "ui_GTCenterOnlyNum": "\n* Enter only numbers",
          "tf_GTCnumbuy": "Enter amount",
          "ui_GTCrecive": "Totally GTC Earn",
          "ui_GTCbuyMoney": "Amount to purchased",
          "ui_GTCcashout": "Amount to payment",
          "btn_GTCbuyindetail": "BUY",
          "ui_GTCdetail": "* Detail for buying",
          "ui_GTCdetailtime":
              "This transaction will be cancelling in 30 minute.",
          "ui_GTCchanal": "Reaching to Seller",
          "ui_GTCcashing": "Pending payment",
          "ui_GTCdetailcancle": "This transaction will be cancelling in",
          "ui_GTCdetailcancle2": "30",
          "ui_GTCdetailcancle3": "minute.",
          "ui_GTCkeybuy": "Transaction No.",
          "ui_GTCdetailbuy": "Detail :",
          "ui_GTCnumunit": "Amount of GTC purchased",
          "ui_GTCpriceperunit": "BV rates / Unit",
          "ui_GTCremark": "Note :",
          "ui_GTCremarkdetail":
              "Please, Call to Seller for check all about payment.\n",
          "ui_GTCremarkdetail2":
              "If you already transferred, Press the “Transferred” button",
          "btn_GTCcancel": "Cancel",
          "btn_GTCmove": "Transferred",
          "ui_GTCstatus": "Status",
          "ui_GTCremarkdetail3":
              "If you haven’t get your ordered from the seller,",
          "ui_GTCremarkdetail4":
              "You can press the “Appeal” button to contact GTC service.",
          "btn_GTCreport": "Appeal",
          "ui_GTCreport": "Appeal",
          "btn_GTCAppealcancle": "Cancel",
          "ui_GTCAppealdetail": "Detail",
          "tf_GTCAppealType": "Please type the details..",
          "btn_GTCAppealSend": "Send",
          "ui_GTCAppealSuccess": "Success",
          "ui_GTCAppealDetail": "We have received your\ninformation Already.",
          "btn_GTCAppealok": "Done",
          "ui_GTCtransuccess": "Transaction Sucsess",
          "ui_GTCdetailsuccess":
              "GTC Already, Transferred to your account.\nYou can check that on home menu.",
          "ui_GTCsuccess": "Done",
          "ui_GTCconnectsale": "Call to Seller",
          "ui_GTCconnectsale2": "If you want to reach the seller\nClick to",
          "btn_GTCconnectOK": "Done",

          "ui_GTCnum": "Amount of your GTC",
          "ui_GTCguarantee": "Owned and bearing CU",
          "ui_GTCfree": "Owned and non - burdened",
          "ui_GTCover": "Amount of GTC sell not more than",
          "ui_GTCforsale": "Amount of GTC Sell",
          "tf_GTCforsale": "Enter amount",
          "ui_GTCtel": "Phone",
          "tf_GTCtel": "Enter phone number",
          "ui_GTCforsaleDetail": "* Sales detail info.",
          "ui_GTCsale": "Sell for BV rates",
          "ui_GTCforsaleunit": "Amount of GTC for sale",
          "ui_GTCforsalebath": "Offering value",
          "ui_GTCdate": "Offering Date",
          "ui_GTCcheck":
              "* Please , Check all detail of your selling., Before to confirm.",
          "btn_GTCok": "Confirm",

          "ui_GTCconfirmSuccess": "Success",
          "ui_GTCreciveData":
              "We have received information\non the selling of GTC.\nof you ",
          "ui_GTCreciveData2": "\nCompleted",
          "btn_GTCsellSuccess": "Done",

          "ui_GTCheadTransfre": "Confirm to GTC selling",
          "ui_GTCtransferDetail": "Detail :",
          "ui_GTCtransfersale": "Seller",
          "ui_GTCtransfernumbuy": "Amount of GTC purchased",
          "ui_GTCtransferConfirm": "Confirm",
          "ui_GTCtransferunit": "BV rates / Unit",
          "ui_GTCtransfermoney": "Amount to payment",
          "btn_GTCtransfercancle": "Cancel",
          "btn_GTCtransferOK": "Confirm",
          "ui_GTCheadersuccess": "Confirm to GTC selling",
          "ui_GTCtransferdetail":
              "GTC Already, Transferred GTC to your buyer.\nYou can check the balance on home menu.",
          "btn_GTCtransferSuccess": "Done",

          "Leave": "Leave",
          "ui_ABSENCE": "ABSENCE",
          "btn_approve": "Approve",
          "btn_leave": "CREATE",
          "ui_typeleavecreate": "Type : ",
          "tf_levae_note": "Note : ",
          "ui_levae_type": "Leave type",
          "ui_levae_time": "Time",
          "ui_levae_sumday": "Total amount /days",
          "ui_levae_detail": "Leaved form detail",
          "dp_selectleavecreate": "Select leave type",
          "ui_dateleavecreate": "Date from",
          "ui_dateleavetocreate": "to",
          "sl_dateleave": "Select a date",
          "ui_timestart": "Starts on",
          "ui_timestop": "Ends on",
          "tf_detail": "Typing detail",
          "ui_sumdate": "Total / Days",
          "ui_detailleave": "Detail",
          "tf_typingdetailcreate": "Typing detail",
          "ui_personApprove": "Select accepter",
          "ui_detail_personApprove": "Accepter",
          "dp_personApprove": "Select list",
          "btn_createleave": "Submit",
          "btn_comfirm": "COMFIRM",
          "btn_cancelcreate": "Cancel",

          "LeaveDetail": "LeaveDetail",
          "ui_LeaveDetail": "Detail",
          "ui_name": "Name : ",
          "ui_typeleave": "Type : ",
          "ui_dateleave": "Date from:",
          "ui_dateleaveto": "Up to date:",
          "ui_sumdateleave": "Sum date : ",
          "ui_date_lev": "DATE : ",
          "ui_create_lev": "CREATE",
          "ui_DetailLeave": "Detail : ",
          "ui_brith_skip":"SKIP",
          "ui_datekey": "Date sent : ",
          "btn_approveDetail": "APPROVAL",
          "btn_notApproveDetail": "Not approve",
          "ui_approvesuccess": "Success",
          "ui_approvedetail": "You have approved the form\nof you ",
          "ui_approvedetailsuccess": "\nCompleted",
          "ui_approvesuccessCan": "Not approved",
          "ui_approvedetailCan": "You do not approve the form\nof you ",
          "ui_approvedetailsuccessCan": "\nPlease notify staff",
          "ui_createLeaveSuccess": "Success",
          "ui_createdetail":
              "Your Leave Request has\nbeen sent.\nPlease wait for the approval",
          "ui_approveok": "Done",

          "leaveconfirm": "leaveconfirm",
          "ui_leaveconfirm": "Confirm approval",
          "ui_leaveYN": "Do you want to confirm the approval?",
          "ui_remarkleave": "remark :",
          "btn_leaveOK": "ok",
          "btn_leaveCancel": "edit",

          "createleaveconfirm": "createleaveconfirm",
          "ui_createleaveconfirm": "Confirm the Leave Request Creation.",
          "ui_createleaveYN": "Do you want to create the Leave Request?",
          "btn_createleaveOK": "OK",
          "btn_createleaveCancel": "Cancel",
          "btn_dev": "Close",
          "btn_dev_title": "Dev Working",
          "mainidea": "mainidea",
          "ui_mainideahead": "PKG IDEA",
          "btn_newsidea": "Idea News",
          "btn_vdo": "Video",
          "btn_addidea": "Send Idea",
          "ui_by": "BY",
          "btn_sendidea": "Send IDEA",

          "addidea": "addidea",
          "ui_headaddidea": "Send Idea",
          "ui_nameidea": "Title",
          "tf_typingidea": "Typing name idea",
          "ui_detailidea": "Detail / Description",
          "tf_typingdetail": "Typing detail",
          "btn_saveidea": "Save",
          "ui_savesuccess": "Success",
          "ui_thx": "Thank you so much\nfor sending your IDEA.",
          "btn_okidea": "ok",

          "detailidea": "detailidea",
          "ui_headdetail": "Detail",
          "ui_post": "By",
          "ui_vote": "Vote",
          "btn_savevote": "Save",
          "ui_votesuccess": "Success",
          "ui_votethx": "Thank you for voting\nAbout this Idea.",
          "ui_voteok": "Ok",
          "ui_comment": "Comment",
          "tf_Write": "Write a comment…",
          "btn_hidecomments": "Hide Comments",
          "btn_comments": "Comments",

          "QRscan": "QRscan",
          "ui_QRscanhead": "QRscan",
          "ui_QRscanSuccess": "Success",
          "ui_QRscanSuccessDetail": "We have create activity claim\nfor you",
          "ui_QRscanSuccessDetail2": "\nCompleted",
          "ui_QRscanok": "Done",
          "ui_QRscanSuccessDetail3": "Login WebConnect",

          "cew": "cew",
          "ui_cewhead": "SAVE CEW",
          "ui_cewinput": "Typing text..",
          "btn_cewPerson": "Person",
          "ui_cewTeam": "Team",
          "ui_cewTeamRec": "Receive team",
          "dp_cewTeam": "Select team",
          "dp_cewtype": "Select type",
          "ui_cewPersonRec": "Recive person",
          "tf_cewIdPerson": "Enter ID / Enter Name",
          "tf_cewBU": "Enter BU",
          "ui_cewType": "Select Type ",
          "btn_cewRoadmap": "roadmap",
          "btn_cewAtmosphere": "atmosphere",
          "btn_cewGeneral": "general",
          "tf_cewTyping": "typing text..",
          "tf_cewNumLike": "Enter Likepoint",
          "dp_cewCategory": "Select Category",
          "ui_cewDetailAAM": "* AAM please select branch",
          "ui_cewDetailAAM2": "FU BU board and RPLC\nselect “ none ”",
          "dp_cewCounty": "select county",
          "dp_cewTypecew": "select type CEW",
          "ui_cewGiveCew": "Issuer CEW",
          "ui_cewDetailCew": "Detailed info. CEW",
          "ui_cewPersonRec2": "Recive cew",
          "ui_cewNumLike": "Amount Likepoint",
          "ui_cewUnitLike": "Like",
          "ui_cewTypeCategory": "Type / category",
          "ui_cewDate": "Date stamped",
          "ui_cewConfirm": "confirm",
          "ui_cewSuccess": "Success",
          "ui_cewSuccessDetail": "We have recive save CEW\nfor you",
          "ui_cewSuccessDetail2": "\nCompleted",
          "ui_cewSuccessok": "Done",

          "mainBitrix": "mainBitrix",
          "ui_mainBitrixhead": "My job",
          "btn_mainBitrixRecJob": "Recive job",
          "btn_mainBitrixSendJob": "Sent job",
          "ui_mainBitrixNumJob": "You have all job issues.",
          "ui_mainBitrixList": "list",
          "ui_mainBitrixOverDeadline":
              "No report\nMore progress has been made for",
          "ui_mainBitrixOverDeadline2": "days.",
          "btn_mainBitrixHideComment": "Hide Comment",
          "btn_mainBitrixComment": "Comment",
          "btn_mainBitrixAddComment": "Add Comment",
          "btn_mainBitrixTyping": "Typing text..",
          "btn_mainBitrixUploadImg": "image",
          "btn_mainBitrixEdit": "Edit",
          "ui_mainBitrixNotSuccess": "Failed",
          "ui_mainBitrixNotSuccessDetail": "1. Please press the ",
          "ui_mainBitrixNotSuccessDetail2":
              " Start button first every time.\nto receive your work issue\n2. Typing a progress report.\n3. If your work is finished\nDone, please press the Finish button.",
          "btn_mainBitrixNotSuccessok": "Done",
          "ui_mainBitrixSuccess": "Success",
          "ui_mainBitrixSuccessDetail":
              "Recive submission information\nClose this issue\nSuccess",
          "btn_mainBitrixSuccessok": "Done",
          "ui_mainBitrixCloseIssueSuccess": "Success",
          "ui_mainBitrixCloseIssueSuccessDetail":
              "Recive report\nClose issue from you\n",
          "ui_mainBitrixCloseIssueSuccessDetail2": "\nSuccess",
          "btn_mainBitrixCloseIssueSuccessok": "Done",
          "ui_mainBitrixReturnSuccess": "Success",
          "ui_mainBitrixReturnSuccessDetail": "Recive Return\nIssue from you\n",
          "ui_mainBitrixReturnSuccessDetail2": "\nSuccess",
          "btn_mainBitrixReturnSuccessok": "Done",
          "ui_mainBitrixShowDetailHead": "more explanation",
          "ui_mainBitrixShowDetai": "1. Verify issue is correct. Press \n",
          "ui_mainBitrixShowDetai2":
              " Job can be closed now\n2.Check job issue is invalid\nPress ",
          "ui_mainBitrixShowDetai3": " ready ",
          "ui_mainBitrixShowDetai4":
              "Give reason and information\n3.Click change date of completion\nAlso every time.",
          "btn_mainBitrixShowDetaiContinue": "Continue",
          "ui_Bd_click":"CLICK THE LUCKY BUTTON",
          "ui_Bd_pround":"MAKE A WISH AND",
          "ui_Bd_below":"BELOW",
          "likeCredit": "likeCredit",
          "ui_likeCredithead": "LIKE CREDIT",
          "ui_likeCreditPoint": "You likecredit",
          "ui_likeCreditLastUpdate": "last update",
          "ui_likeCreditChange": "Use points to chang special privileges",
          "ui_likeCreditUnitPoint": "point",
          "btn_likeCreditChange": "change",
          "ui_likeCreditConfirmChange": "Confirm change point",
          "ui_likeCreditConfirmChangeDetail": "You use point\n",
          "ui_likeCreditConfirmChangeDetail2": "add yes or no?\n",
          "ui_likeCreditConfirmChangeDetail3": "use points",
          "ui_likeCreditConfirmChangeDetail4": "points",
          "btn_likeCreditConfirmok": "Done",
          "btn_likeCreditConfirmcancel": "Cancel",

          "NDA": "NDA",
          "ui_NDAhead": "Non-disclosure Agreement",
          "ui_NDAtitle":
              "สัญญาการรักษาข้อมูลที่เป็นความลับ\n(Non-disclosure Agreement)",
          "ui_NDAdetail":
              "“ข้อมูลที่เป็นความลับ” หมายความถึง ข้อมูลใดๆ รวมทั้งข้อมูลของบุคคลภายนอกที่ฝ่าย ผู้ให้ข้อมูลได้เปิดเผยแก่ฝ่ายผู้รับข้อมูล และฝ่ายผู้ให้ข้อมูลประสงค์ให้ฝ่ายผู้รับข้อมูลเก็บรักษาข้อมูลดังกล่าวไว้เป็นความลับและ/หรือความลับทางการค้าของฝ่ายผู้ให้ข้อมูล โดยข้อมูลดังกล่าวจะเกี่ยวข้องกับผลงานวิจัยเดิม ผลงานวิจัยของโครงการ และ/หรือการดำเนินงานโครงการ แผนและ/หรือแนวทางการวิจัย และ/หรือแผนทางการตลาด ซึ่งรวมถึงแต่ไม่จำกัดเฉพาะกระบวนการ ขั้นตอนวิธี โปรแกรมคอมพิวเตอร์ (รหัสต้นฉบับ รหัสจุดหมาย โปรแกรมปฏิบัติการ และฐานข้อมูลที่ใช้เชื่อมต่อโปรแกรมคอมพิวเตอร์) แบบ ต้นแบบ ภาพวาด สูตร เทคนิค การพัฒนาผลิตภัณฑ์ ข้อมูลการทดลอง และข้อมูลอื่นใดที่เกี่ยวข้องกับผลงานวิจัยเดิม ผลงานวิจัยของโครงการ และ/หรือการดำเนินงานโครงการ ขอให้ท่านอ่านสัญญาอย่างถี่ถ้วน พร้อมลงนามยินยอมตามข้อตกลง",
          "btn_ReadDoc": "Document Review",
          "btn_sign": "SIGN DOCUMENT",
          "btn_signP1": "Sign Doc P1",
          "btn_signP2": "Sign Doc P2",
          "ui_sicked": "Sicked",
          "ui_oversicked": "Sicked Over",
          "ui_Leaveofabsence": "Leave of absence",
          "ui_Leaveofabsenceover": "Over Leave of absence",
          "ui_vacation": "Vacation",
          "ui_meeting": "Meeting",
          "ui_missing_morning": "Missing TA Morning",
          "ui_missing_afternoon": "Missing TA Afternoon",
          "more": "more",
          "ui_morehead": "All Menu",
          "ui_toolsWork": "WorkTools / Work",
          "ui_HWWTF": "HWWTF",
          "btn_ToolsBU": "Work\nTools BU",
          "btn_TA": "TA",
          "btn_Cew": "Save\nCEW",
          "ui_agreement": "ข้อมูลสัญญา",
          "btn_loanCU": "Ordinary loan\nAgreement",
          "btn_nonAgreement": "NON\nDisclosure\nAgreement",
          "ui_MVP": "Test Menu",
          "btn_nitroSign": " Fin Doc.\nApproval",
          "btn_CS24": "App CS24",
          "ui_issues_selcete":"Title : ",
          "ui_issues_date":"Date : ",
          "ui_issues_note":"Note : ",

          "Nitrosign": "Sign the documents",
          "ui_NitroListhead": "Finance Document Approval",
          "ui_Nitrohead": "Sign the documents",
          "ui_Nitroreference": "ref number",
          "ui_Nitrostatus": "status",
          "btn_NitroSign": "Sign",
          "ui_NitroSelect": "Select",
          "ui_NitroSelectDetail":
              "You will use a signature\nexisting or created new?",
          "ui_NitroSelectExist": "existing",
          "ui_NitroSelectNew": "Create New",
          "ui_NitroAgree": "Agree",
          "ui_NitroAgreeDetail":
              "Do you agree to use\nyour existing\nelectronic signature?",
          "ui_NitroAgreeOK": "Agree",
          "ui_NitroAgreeNO": "No Agree",

          "CS24": "CS24",
          "ui_CS24head": "discovered list",
          "ui_CS24head2": "customer details",
          "btn_seeDetail": "view profile",
          "ui_detailBU": "detail BU",
          "btn_buPMS": "PMS",
          "btn_buAAM": "AAM",
          "btn_buPMG": "PMG",
          "btn_buPCC": "PCC",
          "btn_buRPLC": "RPLC",
          "btn_buRAFCO": "RAFCO",

          "alert_not_found_likewallet": " Phone number not found like wallet",

          "test": "test",

          "ui_totalHeldShares": "Total held shares",
          "ui_holdShareReturns": "Hold share returns",
          "ui_waitHeldShares": "Sharing amount",
          "ui_titleAlert3": "Alert",
          "ui_TryAgain": "Try again",
          "ui_detailAlert4": "Please, Sign this document",
          "btn_HRSign": "HR Document\nApproval",
          "pending_sign": "Wait Confirmation",
          "document_detail": "DERAIL DOCUMENT",
          "report_invalid_document": "Click here! if the document is invalid",
          "report_invalid_document_detail": "Reject detail",
          "btn_report_invalid_document": "Confirm",
          "ui_here": "here!",
          "ui_regis_signature":
              "No signature found in the database.\nPlease register your signature",
          "ui_click_here_to_sign": "Click here! to sign the document",

          "ui_register_twoFA": "Register 2FA",
          "not_support_NFC":
              "Your device does not support NFC.\n Please press Next to sign up for MS24 OTP log in.",
          "not_support_NFC_verify":
              "Your device does not support NFC.\n Please press Next to MS24 OTP log in.",
          "btn_register_twoFA": "Register",
          "btn_register_OTP": "Register OTP",
          "ui_success_yubikey": "Your YubiKey has been registered.",
          "ui_success_yubikey_verify": "Activated",
          "ui_success_otp": "OTP signing up has been completed.",
          "ui_success_otp_sending": "Sending OTP",
          "ui_success_otp_send":
              "OTP has been sent to your Telegram account.\n",
          "ui_success_otp_error":"problem with the OTP sending system.",
          "ui_success_otp_error1":"Sorry, there is currently a problem with the OTP sending system. Please try again later.",
          "ui_success_otp_verify_header": "OTP verification",
          "ui_otp_here": "Enter OTP from Telegram",
          "ui_success_otp_verify": "Successfully logged into the OTP system.",
          "ui_success_otp_title": "Confirm successful OTP",
          "ui_waiting": "Please wait...",
          "ui_scanning": "Scanning...",
          "ui_duplicate_otp": "Your device does not support NFC.",
          "ui_duplicate_otp_detail": "You already registered OTP.",
          "ui_touch_yubikey": "Touch YubiKey",
          "ui_duplicate_yubikey": "This YubiKey has been registered.",
          "ui_register_twoFA_header": "Security PKG",
          "ui_scan_yubikey": "Scan YubiKey",
          "ui_change_to_otp": "click here! to change to OTP",
          "ui_please_unlock": "Please unlock",
          "ui_unlock": "Unlock",
          "ui_to_sign_in": "to sign in MS24",

          "privacy_policy": "Privacy Policy",
          "btn_PMSSign": "WI Document\nApproval",

          "ui_notification_list": "No Notification List",
          "ui_delete_l": "Delete all News",
          "ui_delete_1": "Please read your news. ",
          "ui_delete_2": "before deleting all news!!",
          "ui_delete_3": "DELETE All",
          "ui_delete_4": "Cancel",
          "ui_delete_5": "Have your news unread!!",

          "ui_approval_document": "No list of approved documents",
          "ui_approval_1": "Admonish",
          "ui_approval_2": "Insurance",
          "ui_approval_3": "BU agency",
          "btn_nonAgreement_1": "NON\nDisclosure\nAgreement",
          "ui_NitroListhead_1": "Approve Personnel Documents",

          "ui_likecredit_t": "chang special privileges",
          "ui_likecredit_1": "You use point",
          "ui_likecredit_2": "special privileges yes or no?",

          "ui_profile": "Profile",
          "ui_birthday": "Date of Birth",
          "ui_IDcard": "CardID",
          "ui_number": "PhoneNumber",
          "ui_numberlikewallet": "PhoneNumber Linked to Account",
          "ui_email": "Email",
          "ui_security_p": "Security",
          "ui_profile_e": "EditProfile",
          "ui_life": " service life\nfrom the packing date",
          "ui_register_Esign":"Register E-Sign",

          "ui_rewardCU": "You have received Meta CU daily reward.",
          "ui_jobmenu":
              "New job is assigned,please press \"START\" in My job menu.",
          "ui_noti":
              "You have got notifications, please press Notifications for content.",
          "ui_news_s":
              "We have got news for you, please press News for the content.",
          "ui_fin":
              "Your approval is requested, please press Fin. Expense to proceed.",
          "ui_hr_approval":
              "Your approval is requested, please press HR Approval to proceed.",
          "ui_wi_approval":
              "Your approval is requested, please press WI Approval to proceed.",

          "ui_fullname": "Name",
          "ui_namenumbank": "Account",

          "ui_confirm_exchange": "Redeem likecredit successfully",

          "ui_OKR": "OKR information",
          "ui_paste": "Paste",
          "ui_Kip": "Kip",

          "ui_otp_build_TG": "Enter Code",
          "ui_otp_build_TG_2":
              "We've sent an SMS with an activation code to your phone",
          "ui_otp_build_TG_3": "Enter OTP from SMS",
          "btn_signupTG": "signups Telegram",
          "btn_signupTG_m": "You have successfully subscribed to Telegram",
          "ui_Availble": "AVAILABLE",
          "ui_userConditionsCu": "HELD SHARES / UNIT",
          "ui_reCu": "RETURN REWARD",
          "ui_ConditionsCu": "SHARE AMOUNT",
          "ui_deny": "DENY",
          "ui_withcu": "WITH CU",
          "ui_wealth": "WEALTH",
          "ui_health": "HEALTH",
          "ui_time": "TIME",
          "timmi": "Timmi :",
          "ui_bodyinfo": "BODY INFO.",
          "ui_bodyinfo_cm": "CM",
          "ui_bodyinfo_kg": "KG",
          "ui_disease_risk": "DISEASE RISK",
          "ui_within_criteria": "WITHIN CRITERIA",
          "ui_health_normal": "NORMAL",
          "ui_goodhealth_normal": "HEALTHY",
          "ui_health_bmi": "BMI AVERAGE",
          "ui_health_activity": "POI ACTIVITIES",
          "ui_health_total": "TOTAL TIME",

          "ui_ta_status": "TA STATUS",
          "ui_ta_late": "LATE",
          "ui_ta_sick": "SICKED",
          "ui_ta_levae": "ABSENCED",
          "ui_ta_uncheck": "TA UNCHECK",
          "ui_ta_totaltime": "TOTAL TIME",
          "ui_ta_missing": "TA MISSING",
          "ui_ta_total_average": "TOTLE TIME",
          "ui_ta_stampedchart": "STAMPED",
          "ui_ta_levaechart": "ABSENCED",
          "ui_ta_levaechart_general": "GENERAL",
          "ui_ta_levaechart_over": "OVER LINE",
          "ui_ta_levaechart_pfs": "PFS REDUCT",
          "ui_ta_alltotal": "TOTAL TIME",
          "ui_days": "DAYS",
          "ui_box_moring": "Morning : 8:15 - 12:00",
          "ui_box_afternoon": "AFTERNOON : 13:00 - 17:00.",
          "ui_box_afternoon2": "AFTERNOON : 13:00 - 15:30.",
          "ui_box_afternoon3": "AFTERNOON : 13:00 - 16:00.",
          "ui_box_allday": "ALL DAY :  8:15 - 17:00",
          "ui_box_allmem": "ALLMEM.",
          "ui_finace_doc": "FINANCE DOCUMENT APPROVAL",
          "ui_finace_general": "GENERAL",
          "ui_finace_HR": "HR",
          "ui_finace_PMS": "PMS",
          "ui_work_issues": "RECIVE JOB",
          "ui_work_give": "WORK GIVE",
          "ui_workissues": "ISSUSE",
          "dp_select_time": "Select times",
          "ui_sign_name": "DOCUMENT NAME :",
          "ui_sign_cap1": "Your signature will be recorded on the document",
          "ui_sign_cap2": "automatically",
          "ui_sign_cap3": "When you press the button below. ",
          "ui_sign_cap4": "",
          "btn_sign_1": "COMFiRM SIGNATURE",
          "ui_comfirm": "COMFIRM",
          "btn_sign_2": "DONE",
          "dp_cewBranch": "Select Branch",
          "btn_nonAgreement_head": "NON-Disclosure Agreement",

          "ui_doorscan": "OPEN DOOR",
          "ui_doorscan_barrir": "BARRIR GATE",
          "ui_doorscan_meeting": "MEETING DOOR",
          "ui_doorscan_howto": "HOW TO USE,",
          "ui_doorscan_tap": " TAP BUTTON ",
          "ui_doorscan_toopen": "TO OPEN",
          "ui_dateleave_day": "Sum Day",
          "ui_status_allday": "ALL DAY",
          "ui_status_allmem": "ALL MEM",
          "ui_status_moring": "MORNING",
          "ui_status_afternoon": "AFTERNOON",

          "ui_chart_sicked": "SICKED",
          "ui_chart_sicked_general": "GENERAL",
          "ui_chart_sicked_totaltime": "TOTAL TIME",
          "ui_chart_sicked_accident": "ACCIDENT",
          "ui_day":"Day",

          "ui_chart_vacation": "VACATION",
          "ui_chart_vacation_totaltime": "TOTAL TIME",
          "ui_chart_vacation_dayoff": "DAY OFF",
          "ui_input": "Enter Text",
          "ui_box_claim": "CLAIM",
          "tf_levae_Write": "Typing the reason...",
          "ui_cilck": "CLICK.",
          "ui_timminoti": " You leave slip has not been approved",
          "ui_timminoti_documents": "You documents have not been signed",
          "ui_timminoti_claimed": "You items have not been claimed.",
          "ui_timminoti2": "list ",
          "ui_timminoti3": "",
          "ui_noti_emtpy": "No have notifications",

          "ui_auto_claim": "Auto claim",
"ui_cew_selcet":"Type of cew",
          "ui_news_emtpy": "No have news",
          "ui_leave_emtpy": "No list of leave form approved.",
          "ui_doc_emtpy": "No list of approved document",
          "ui_doc_emtpy_hr": "No list of approved document form HR",
          "ui_doc_emtpy_pms": "No list of approved document form PMS",

          "ui_login_TG": "Or with Telegram",
          "ui_timmi_give": "GIVE ME POINT",
          "ui_timmi_send": "SEND ME A POINT FOR MS24",
          "ui_timmi_used": "WHEN YOU USED THIS APPLICATION.",
          "ui_timmi_tell": "Tell me anythings..",
          "ui_timmi_SKIP": "SKIP",
          "ui_timmi_done": "Done",
          "ui_chart_max": "MAX",
          "ui_tamissing_emtpy": "WELL DONE NO MISSING TA.",
  "ui_login_popup": "Login Successful",
  "ui_login_error": "Invalid credentials",
          "ui_save_health" : "SAVE\nGOT POINT",
          "ui_join_health":"  JOIN",
          "ui_join_health2":"  GROUP",
          "ui_Leave":"LEAVE / APPROVAL",
          "ui_finace":"FINANCE",
          "ui_emp":"Employee",
          "ui_work":"Work",

},
        //************************************************************************************ */
        // Burmese: Subtag for Burmese language is MY (Myanmar)
        "my": {
          //language
          "Language": "ဘာသာစကား",
          //onboarding screen
          "onboarding title 1": "ငွေပေးချေမှုပြုလုပ်ရန်",
          "onboarding body 1": "sdsdsddsd",
          "Home": "ဘာ"
        },
        //************************************************************************************ */
        //Thai
        "th": {
          "login": "login",
          "ui_login": "เข้าสู่ระบบ",
          "ui_login_popup": "เข้าสู่ระบบสำเร็จ",
          "ui_login_error": "เข้าสู่ระบบไม่สำเร็จ",
          "ui_login_TG": "เข้าใช้งานผ่าน Telegram",
          "fild_username": "ชื่อบัญชี",
          "fild_password": "รหัสผ่าน",
          "btn_signin": "เข้าสู่ระบบ",
          "btn_signup": "สมัครเข้าใช้งาน",
          "ui_login_app": "ยืนยันการเข้าสู่ระบบ",
          "home": "home",
          "ui_hello": "สวัสดี",
          "ui_morning_y": "คุณลงเวลาเข้างานเรียบร้อย",
          "ui_morning_n": "คุณยังไม่ได้ลงเวลาเข้างาน",
          "ui_bye_y": "คุณลงเวลาออกเรียบร้อย",
          "ui_bye_n": "คุณยังไม่ได้ลงเวลาออกงาน",
          "btn_peopleProcess": "People\nProcess",
          "btn_4C": "4C",
          "btn_tools": "เครื่องมือ\nทำงาน",
          "btn_bitrix": "ประเด็นงาน\n   ของฉัน",
          "btn_CuGtc": "CU / GTC",
          "btn_seeAll": "ดูทั้งหมด",
          "ui_peopleProcess": "People Process",
          "ui_4C": "4C",
          "ui_CuGtc": "CU / GTC",
          "ui_tools": "เครื่องมือทำงาน",
          "btn_issue": "ประเด็นหลัก",
          "btn_toolsPMS": "เครื่องมือ\nทำงาน PMS",
          "btn_toolsAAM": "เครื่องมือ\nทำงาน AAM",
          "btn_toolsCS": "เครื่องมือ\nทำงาน CS24",
          "btn_ta": "ลงเวลา\nทำงาน",
          "btn_dev": "ปิดใช้งาน",
          "btn_dev_title": "อยู่ระหว่างการพัฒนา",

          "btn_welfare": "สวัสดิการ",
          "btn_callcenter": "ติดต่อสอบถาม",
          "btn_buyGTC": "หุ้น GTC",
          "btn_scanQR": "สแกน QR",
          "btn_cew": "บันทึก\n Cew",
          "btn_idea": "PKG\nไอเดีย",
          "btn_Leave": "    ใบลา /\n อนุมัติใบลา",
          "ui_Leave": "ใบลา / อนุมัติใบลา",
          "ui_statistics": "สถานะความมั่งคั่ง",
          "btn_Income": "รายได้",
          "btn_LikeWallet": "ไลค์วอลเลท",
          "btn_MSP":"MSP",
          "btn_wheel":"วงล้อนำโชค!!",
          "ui_yeartpye": "(คศ.)",
          "btn_gtc": "หุ้น GTC",
          "btn_cu": "หุ้น CU",
          "btn_meta_cu": "หุ้น Meta CU",
          "btn_Insurance_CU": "เงินค้ำประกัน",
          "ui_Salary": "เงินเดือน",
          "ui_Incentive": "INCENTIVE",
          "ui_dateUpdate": "อัพเดทวันที่",
          "ui_Reward": "เงินรางวัล",
          "ui_PFS": "(PFS)",
          "ui_Bath": "บาท",
          "ui_Totally": "ทั้งหมด",
          "ui_Locked": "ล็อคอยู่",
          "ui_Unlock": "ใช้ได้",
          "ui_LIKE": "ไลค์",
          "ui_BV": "มูลค่าหุ้น BV ",
          "ui_sharing": "จำนวนหุ้น",
          "ui_TotallyGTC": "มูลค่าหุ้น (บาท)",
          "ui_TotallyGTCcoin": "GTCcoin",
          "ui_Unit": "หุ้น",
          "ui_Collateralized": "ติดค้ำประกัน",
          "ui_TotallyCU": "มูลค่าหุ้น",
          "ui_SavingCU": "เงินออม",
          "ui_InsuranceCU": "เงินประกันการทำงาน",
          "ui_DebtCU": "หนี้สิน",
          "ui_reward": "ผลตอบแทน",
          "ui_reCu": "ผลตอบแทนหุ้นถือครอง",
          "ui_reward2": "จากการลงทุน",
          "ui_Conditions": "*รอถือครองกรรมสิทธิ์หุ้นตามเงื่อนไข",
          "ui_ConditionsCu": "รอถือครอง / หุ้น",
          "ui_userConditionsCu": "ถือครอง / หุ้น",
          "ui_status_allday": "ทั้งวัน",
          "ui_status_allmem": "ใจถึงใจ",
          "ui_status_moring": "เช้า",
          "ui_status_afternoon": "บ่าย",
          "ui_ContractCU": "สัญญาที่",
          "ui_value": "มูลค่า(บาท)",
          "ui_bath": "บาท",
          "ui_likevalue": "ไลค์",
          "ui_cu": "CU",
          "ui_withcu": "กับ CU",
          "ui_MetaCu": "Meta CU",
          "ui_Availble": "ใช้ได้",
          "ui_gtc": "GTC",
          "ui_debt": "หนี้สิน",
          "ui_Bd_click":"กดปุ่มด้านล่าง",
          "ui_Bd_pround":"ขอให้โชคดีสุขีวันเกิด",
          "ui_Bd_below":"เพื่อรับรางวัล",

          "ui_like": "ไลค์พอยท์",
          "ui_likecredit": "ไลค์ เครดิต",
          "ui_detailFull": "ดูข้อมูลเพิ่มเติม",
          "ui_Savingmoney": "เงินออม",
          "ui_position": "ตำแหน่ง",
          "ui_purpose": "จุดมุ่งหมายทีม",
          "ui_borrowcu": "สัญญาเงินกู้สามัญ",
          "ui_nondisclosure": "สัญญาไม่เปิด\nเผยข้อมูล",
          "ui_passcode": "รหัสความปลอดภัย",
          "ui_create_passcode": "ตั้งรหัสความปลอดภัย",
          "ui_confirm_passcode": "ยืนยันรหัสความปลอดภัย",
          "ui_languages": "ตั้งค่าภาษา",
          "ui_feedback": "ข้อเสนอแนะ",
          "ui_theme": "ธีม",
          "ui_guid": "คู่มือ MS24",
          "ui_talk": "พูดคุย",
          "ui_supporter": "ผู้สนับสนุน",
          "ui_logout": "ออกจากระบบ",
          "ui_logoutalert": "ออกจากระบบ",
          "ui_logoutCF": "คุณต้องการออกจากระบบ\nMS24 ใช่หรือไม่?",
          "btn_cancel": "ยกเลิก",
          "btn_logout": "ออกจากระบบ",
          "ui_update": "แจ้งอัพเดทเวอร์ชั่น",
          "ui_updateDetail":
              "MS24 Mobile Application\nได้ทำการปรับปรุงแก้ไข ฟังก์ชั่นการใช้งานใหม่\nเพื่อการใช้งานที่ดีขึ้น กรุณาทำการอัพเดทเวอร์ชั่น\n\nกดปุ่ม “อัพเดท” ด้านล่าง เพื่อทำการอัพเดท",
          "btn_update": "อัพเดท",
          "btn_After": "ภายหลัง",
          "ui_titleAlert": "แจ้งเตือน",
          "ui_TryAgain": "ลองใหม่",
          "ui_detailAlert": "เมนูนี้กำลังอยู่ในระหว่าง\nการพัฒนา",
          "ui_detailAlert2":
              "ไฟล์ที่ท่านเลือกมีขนาดใหญ่เกินไป\nกรุณาปรับไฟล์ให้เล็กลง",
          "ui_titleAlert2": "ผิดพลาด",
          "ui_detailAlert3": "ไม่พบข้อมูลลูกค้า\nลองค้นหาใหม่",
          "btn_okAlert": "ตกลง",
          "modify": "modify",
          "ui_modify": "แจ้งปิดปรังปรุง",
          "ui_modifyDetail":
              "MS24 Mobile Application\nปิดปรับปรุงแก้ไขและงดให้บริการเมนูนี้ชัวคราว\n\nขออภัยในความไม่สะดวกค่ะ",
          "passscode": "passcode",
          "ui_enter": "ใส่รหัสความปลอดภัย",
          "ui_wrong": "รหัสของคุณไม่ถูกต้อง ลองอีกครั้ง",
          "ui_lostpin": "หากลืมพิน กดที่นี่ เพื่อออกจากระบบบ",
          "ui_lostpin1": "หากลืมรหัส",
          "ui_lostpin2": "กดที่นี่",


          "changepasscode": "changepasscode",
          "ui_setting": "ตั้งค่ารหัสความปลอดภัย",
          "ui_resetpasscode": "เปลี่ยนรหัสใหม่",
          "ui_putpasscodedefals": "ใส่รหัสเดิม",
          "ui_putpasscodenew": "ใส่รหัสใหม่",
          "ui_confirmpasscodenew": "ยันยืนรหัสใหม่อีกครั้ง",
          "ui_passwrong": "รหัสของคุณไม่ถูกต้อง",
          "ui_alertpass": "เปลี่ยนรหัสเรียบร้อย",
          "ui_alertdata":
              "คุณได้เปลี่ยนรหัส\nความปลอดภัยใหม่แล้วเรียบร้อย\nสามารถเข้าใช้งานได้เลยค่ะ",
          "btn_ok": "ตกลง",
          "ui_alerthome": "ยกเลิกตั้งค่ารหัส",
          "ui_datahome":
              "คุณต้องการจะยกเลิก\nการตั้งค่ารหัสความปลอดภัยใหม่\nใช่หรือไม่?",
          "btn_continew": "ดำเนินการต่อ",
          "btn_okhome": "ตกลง",
          "changlanguagessetting": "changlanguagessetting",
          "ui_selectlanguages": "เลือกภาษา",
          "btn_eng": "อังกฤษ",
          "btn_th": "ไทย",
          "ui_LanguageSetting": "ตั้งค่าภาษา",
          "ui_settingCFf": "คุณต้องการยืนยัน\nตั้งค่าเป็นภาษา",
          "ui_settingCFl": "ใช่หรือไม่",
          "ui_selecteng": "อังกฤษ",
          "ui_selectth": "ไทย",
          "ui_selectkm": "กัมพูชา",
          "ui_selectlao": "ลาว",
          "btn_settingcancel": "ยกเลิก",
          "ui_settingchange": "เปลี่ยนภาษา",
          "feedback": "feedback",
          "ui_headFeedback": "ข้อเสนอแนะ",
          "ui_subject": "หัวข้อ / เรื่อง",
          "tf_typingSj": "พิมพ์หัวข้อที่จะเสนอแนะ",
          "ui_detailFeedback": "รายละเอียด / คำอธิบาย",
          "tf_detailFeedback": "พิมพ์รายละเอียด",
          "ui_imgUpload": "อัพโหลดรูปภาพ",
          "btn_imgUpload": "อัพโหลดรูปภาพ",
          "btn_saveFeedback": "บันทึกข้อมูล",
          "ui_feedbacOK": "บันทึกเรียบร้อย",
          "ui_feedbackDe":
              "ทางเราได้รับข้อเสนอแนะ\nของคุณแล้วเรียบร้อย\nขอบคุณมากค่ะ",
          "ui_feedbackOK": "ตกลง",
          "contact": "contact",
          "ui_headcontact": "ติดต่อสอบถาม",
          "ui_subjectcontact": "เรื่องติดต่อ",
          "tf_typingSjcontact": "พิมพ์เรื่องที่ติดต่อ",
          "ui_detailcontact": "รายละเอียด",
          "tf_detailcontact": "พิมพ์รายละเอียด",
          "ui_imgUploadcontact": "อัพโหลดรูปภาพ",
          "btn_imgUploacontact": "อัพโหลดรูปภาพ",
          "btn_savecontact": "ส่งข้อมูล",
          "ui_contactOK": "ส่งข้อมูล เรียบร้อย",
          "ui_contactDe":
              "ทางเราได้รับการติดต่อ\nของคุณแล้วเรียบร้อย\nขอบคุณมากค่ะ",
          "ui_contactkOK": "ตกลง",
          "ta": "ta",
          "ui_ta": "ลงเวลาทำงาน",
          "ui_date": "วันที่",
          "ui_timeNow": "ขณะนี้เวลา",
          "btn_checkin": "มาทำงาน",
          "btn_checkin2": "มาทำงาน(บ่าย)",
          "btn_checkout": "ลงเวลาออก",
          "ui_problem": "สามารถแจ้งปัญหาได้ที่Telegram ห้องรับใช้",
          "reasonforlate": "reasonforlate",
          "ui_why": "เหตุผลที่สาย",
          "tf_message": "พิมพ์รายละเอียด...",
          "btn_send": "ส่งข้อมูล",
          "sendproblem": "sendproblem",
          "ui_what": "แจ้งปัญหาการลงเวลา",
          "ui_notWorking": "ระบบแอพมีปัญหา",
          "ui_notInternet": "สัญญาณอินเทอร์เน็ตใช้การไม่ได้",
          "reciveProblem": "reciveProblem",
          "ui_wait":
              "* ได้รับข้อมูลปัญหา การลงเวลาของคุณแล้ว \nกรุณารอการติดต่อกลับจากทีมงาน\nภายใน 5 นาที",
          "notification": "notification",
          "ui_notify": "แจ้งเตือน",
          "ui_claim": "เคลม LIKE",
          "ui_box_claim": "เคลม",
          "btn_claim": "รับคะแนน",
          "btn_claimNow": "เคลม เลย!",
          "ui_act": "กิจกรรม",
          "ui_news": "ข่าวสาร",
          "ui_course": "หลักสูตร",

          "pkgWelfare": "สวัสดิการ PKG",
          "ui_select": "เลือกรายการที่ต้องการ",
          "btn_basic": "สวัสดิการพื้นฐาน",
          "btn_flexible": "สวัสดิการยืดหยุ่น",
          "btn_health": "สวัสดิการสุขภาพ",
          "ui_basic":"พื้นฐาน",
          "ui_flexible":"ยืดหยุ่น",
          "ui_health":"สุขภาพ",
          "ui_contact": "สอบถามรายละเอียดกับทีมงาน",
          "pkgwelfarebasic": "pkgwelfarebasic",
          "ui_headBasic": "สวัสดิการพื้นฐาน",
          "ui_detail": "รายละเอียดสวัสดิการพื้นฐาน",
          "ui_welfarecity": "สวัสดิการสิทธิตามกฏหมายแรงงานของแต่ละประเทศ",
          "ui_welfareout": "สวัสดิการนอกเหนือจากที่กฎหมายกำหนด",
          "ui_welfareout1": "สวัสดิการช่วยเหลือพิธีมงคลสมรส",
          "ui_welfareout2": "สวัสดิการช่วยเหลือคลอดบุตร",
          "ui_welfareout3": "สวัสดิการช่วยเหลือเพื่อการอุปสมบท",
          "ui_welfareout4":
              "สวัสดิการช่วยเหลืองานศพ : ครอบครัวสมาชิก / เครือญาติสมาชิก",
          "ui_welfareout5": "สวัสดิการประกันอุบัติเหตุกลุ่ม",
          "ui_welfareout6": "สวัสดิการช่วยเหลือประกันกลุ่มสุขภาพ",
          "ui_welfareout7": "สวัสดิการประกันชีวิตกลุ่ม",
          "ui_welfareout8":
              "สวัสดิการช่วยเหลือตรวจสุขภาพประจำปี ( เฉพาะกลุ่มงาน )",
          "ui_welfareout9": "สวัสดิการช่วยเหลือทุนการศึกษาสมาชิก",
          "ui_welfareout10": "สวัสดิการรางวัลอายุงาน",
          "ui_welfareout11":
              "สวัสดิการเครื่องแบบสมาชิก \n( ไม่รวมชุดฟอร์มช่าง )",
          "ui_welfareout12": "สวัสดิการงบเลี้ยงส่งสมาชิกลาออก",
          "ui_welfareout13": "สวัสดิการเงินสมทบอายุงานกรณีลาออก",
          "ui_welfareout14": "สวัสดิการเบี้ยเลี้ยงปฏิบัติงานต่างประเทศ / ",
          "ui_welfareout15": "ปฏิบัติงานนอกสถานที่ / ฝึกอบรม",
          "ui_welfareout16": "สวัสดิการค่าที่พัก",
          "ui_helpbudget": "ส่วนที่องค์กรมีงบช่วยเหลือ",
          "pkgwelfarebasicform": "pkgwelfarebasicform",
          "ui_headBasicform": "สวัสดิการพื้นฐาน",
          "ui_status": "สถานะ",
          "dp_select": "เลือก",
          "dp_member": "สมาชิกประจำ",
          "dp_test": "สมาชิกทดลองงาน",
          "dp_hire": "สมาชิกสัญญาจ้าง",
          "dp_Advisor": "ที่ปรึกษา",
          "ui_datework": "กรุณาเลือกวันที่",
          "ui_remarkdatework":
              "ระบุวันที่ จัดงานต่างๆ เช่น งานบวช งานแต่ง ฯลฯ ตามเงื่อนไขที่แจ้งไว้ และถ้ากรณีลาคลอดใส่วันที่เริ่มทำการลาคลอดด้วยทุกครั้ง",
          "tf_datework": "วว/ดด/ปปปป",
          "ui_selectwelfare": "เลือกสวัสดิการ",
          "ui_remarkwelfare":
              "กรณีลาบวชเกิน15วันขึ้นไป ให้เลือก >>>>> งานบวช (บวชเกิน15วัน) และใส่วันที่กลับมาทำงานในช่องวันที่กลับมาทำงานสำหรับคนลาบวชเดิน15วันด้วย!!",
          "dp_welfareselect": "เลือกรายการสวัสดิการ",
          "dp_marri": "งานมงคลสมรส",
          "dp_monk": "งานบวช",
          "dp_monk15": "งานบวช (บวชเกิน15วัน)",
          "dp_baby": "คลอดบุตร",
          "dp_gran":
              "งานศพ เครือญาติสมาชิก (บิดา - มารดาของคู่สมรส และ พี่น้องร่วมบิดา-มารดาเดียวกัน ปู่ ย่า ตา ยาย )",
          "dp_father": "งานศพ ครอบครัวสมาชิก (บิดา - มารดา บุตร คู่สมรส )",
          "dp_advisor":
              "งานศพ สมาชิกกลุ่มที่ปรึกษารวมถึง บิดา - มารดา คู่สมรส และ บุตร ของที่ปรึกษา เสียชีวิต",
          "ui_link": "แนบ link การ์ดงานพิธีต่างๆ/ใบเกิด",
          "ui_remarklink": "(ถ้ายังไม่มีการ์ดให้ไปใส่อีกข้อนึง)",
          "ui_showlink": "link ที่อัพโหลด",
          "ui_card": "สำหรับยังไม่มีการ์ดงานพิธี",
          "dp_cardselect": "เลือก",
          "dp_nocard": "ยังไม่มีการ์ด",
          "ui_bank": "เลขบัญชีในการรับเงิน (เฉพาะกสิกรไทยเท่านั้น)",
          "ui_remarkbank": "*ถ้ามี 0 นำหน้าให้ใส่ - เช่น 0-********* *",
          "tf_typingbank": "พิมพ์เลขที่บัญชี",
          "ui_remarkbasic": "หมายเหตุ",
          "tf_typingnote": "พิมพ์หมายเหตุ",
          "ui_monk": "สำหรับหัวข้อ*งานบวช (บวชเกิน15วัน)*",
          "ui_remarkmonk": "*กรุณาใส่วันที่กลับมาทำงานด้วยคะ*",
          "btn_savebasic": "บันทึกข้อมูล",
          "btn_nextbasic": "ถัดไป",
          "btn_beforebasic": "ก่อนหน้า",
          "pkgwelfareflexible": "pkgwelfareflexible",
          "ui_headFlexible": "สวัสดิการยืดหยุ่น",
          "ui_amount": "วงเงินสวัสดิการยืดหยุ่น",
          "ui_amount1": "วงเงินที่เบิกได้",

          "btn_apply": "คีย์เบิกสวัสดิการ",
          "ui_history": "รายการเบิก",
          "ui_history_all": "รายการเบิกเคลมล่าสุด",

          "ui_recive": "เบิกไปแล้ว",
          "ui_withdraw_money":"เบิก",
          "pkgwelfareflexiblewithdraw": "pkgwelfareflexiblewithdraw",
          "ui_headFlexibleWithdraw": "สวัสดิการยืดหยุ่น",
          "ui_amountWithdrow": "วงเงินคงเหลือที่เบิกได้",
          "ui_amount2": "จำนวนเงินที่ต้องการเบิก",
          "tf_amount": "พิมพ์จำนวนเงิน",
          "ui_grouptype": "หมวดหมู่ใบเสร็จ",
          "dr_select": "เลือกรายการ",
          "ui_uploadimmage": "อัพโหลดใบเสร็จ",
          "ui_exImage": "ตัวอย่างใบเสร็จ",
          "ui_linkimmage": "ลิงค์รูป",
          "btn_uploadimmage": "อัพโหลดรูปภาพ",
          "btn_save": "บันทึกข้อมูล",
          "btn_details":"ยืนยันการเบิกสวัสดิการ",
          "btn_details2":"คุณต้องการเบิกสวัสดิการยืดหยุ่น",
          "btn_headhealth":"คุณต้องการเบิกสวัสดิการสุขภาพ",
          "btn_details3":"จำนวน ",
          "btn_details4":" บาท",
          "btn_details5":" กีบ",
          "btn_details6":"ใช่หรือไม่?",
          "btn_back":"กลับหน้าหลัก",
"ui_register_Esign":"ลงทะเบียนลายเซ็น",
          "pkgwelfareflexibleconfirm": "pkgwelfareflexibleconfirm",
          "ui_headFlexibleConfirm": "สวัสดิการยืดหยุ่น",
          "pkgwelfarehealth": "pkgwelfarehealth",
          "ui_headhealth": "สวัสดิการสุขภาพ",
          "ui_selectinsurance": "เลือกรายการที่ต้องการ",
          "btn_selectIPD": "สวัสดิการสุขภาพ IPD\n( ผู้ป่วยใน )",
          "btn_selectOPD": "สวัสดิการสุขภาพ OPD\n( ผู้ป่วยนอก )",
          "ui_conditionIPD": "เงื่อนไขสวัสดิการสุขภาพ IPD...",
          "ui_conditionOPD": "เงื่อนไขสวัสดิการสุขภาพ OPD...",
          "ui_messageIPD_thai1":
              "ค่ารักษาพยาบาลไม่เกิน 10,000 บาท/ครั้ง ตามเงื่อนไขการเบิกดังนี้ (รวมค่าห้อง/ค่าหมอ/ค่ายา)\n- จ่ายค่าห้อง 1,500 บาท/วัน\n- ค่าปรึกษาแพทย์เชี่ยวชาญเฉพาะโรค 2,000 บาท/ครั้ง\n- ค่ายาตามจริง ไม่เกินวงเงินรวม 10,000 บาท/ครั้ง\nค่าผ่าตัด 5,000 บาท/ครั้ง",
          "ui_messageIPD_thai":
              "เงื่อนไขสวัสดิการสุขภาพผู้ป่วยในและผู้ป่วยนอก \nเป็นไปตามเงื่อนไขตามประกาศของทีม PAO",
          "ui_messageIPD_rplc1":
              "ค่ารักษาพยาบาลไม่เกิน 4,500,000 กีบ/ครั้ง ตามเงื่อนไขการเบิกดังนี้ (รวมค่าห้อง/ค่าหมอ/ค่ายา)\n- จ่ายค่าห้อง 450,000 กีบ/วัน\n- ค่าปรึกษาแพทย์เชี่ยวชาญเฉพาะโรค 600,000 กีบ/ครั้ง\n- ค่ายาตามจริง ไม่เกินวงเงินรวม 4,500,000 กีบ/ครั้ง\nค่าผ่าตัด 4,500,000 กีบ/ครั้ง",
          "ui_messageIPD_rafco1":
              "ค่ารักษาพยาบาลไม่เกิน 500 /ครั้ง ตามเงื่อนไขการเบิกดังนี้ (รวมค่าห้อง/ค่าหมอ/ค่ายา)\n- จ่ายค่าห้อง 50 /วัน\n- ค่าปรึกษาแพทย์เชี่ยวชาญเฉพาะโรค 66.66 /ครั้ง\n- ค่ายาตามจริง ไม่เกินวงเงินรวม 500 /ครั้ง\nค่าผ่าตัด 500 /ครั้ง",
          "ui_messageOPD_thai1": "ค่ารักษาผู้ป่วยนอก วงเงิน 3,600 บาท ต่อปี",
          "ui_messageOPD_rplc1":
              "ค่ารักษาผู้ป่วยนอก วงเงิน 1,080,000 กีบ ต่อปี",
          "ui_messageOPD_rafco1": "ค่ารักษาผู้ป่วยนอก วงเงิน 110  ต่อปี",
          "ui_messageNOTE":
              "***ใช้สิทธิ์ได้เฉพาะสมาชิกประจำเท่านั้น\n***รักษาที่คลีนิคหรือโรงพยาบาลเท่านั้นกรณีซื้อยาตามร้านขายยาไม่สามารถเบิกได้นะจ๊ะ\n***ใบเสร็จที่ห้ามนำมาเบิก เช่น ใบเสร็จตรวจการตั้งครรภ์ไม่สามารถเบิกได้\nสามารถติดต่อสอบถามรายละเอียดได้ที่ PAO หรือสอบถามเบอร์ 152 153 154 และ 158",
          "btn_statusWithdrow": "สถานะการเบิก",
          "pkgwelfarehealthwithdraw": "pkgwelfarehealthwithdraw",
          "ui_headhealthwithdraw": "สวัสดิการสุขภาพ",
          "ui_statusWithdrawPKG": "สถานะการเบิกใช้ประกัน PKG",
          "ui_Used": "ใช้ไป",
          "ui_Balance": "คงเหลือ",
          "ui_IPD": "IPD ผู้ป่วยใน",
          "ui_OPD": "OPD ผู้ป่วยนอก ",
          "ui_create_lev": "ดำเนินการต่อ",
          "ui_amountWithdraw": "จำนวนเงินรวมที่ใช้ไป",
          "ui_listWithdraw": "รายการเบิกเคลม",
          "ui_claimtypeIPD": "เคลมประเภท IPD",
          "ui_claimtypeOPD": "เคลมประเภท OPD",
          "btn_key": "คีย์เบิกประกัน",
          "pkgwelfarehealthfrom": "pkgwelfarehealthfrom",
          "ui_headhealthfrom": "สวัสดิการสุขภาพ",
          "ui_headamount": "จำนวนเงินที่ให้ PAO โอนสำรองให้ กรณีเร่งด่วน",
          "tf_healthamount": "พิมพ์จำนวนเงิน",
          "ui_healthremark": "*กรณีที่ไม่ได้ให้ PAO สำรองเงินให้ กรุณาใส่ 0",
          "ui_headtype": "ประเภทการเบิกเคลม",
          "dp_selecthealth": "เลือกรายการ",
          "dp_out": "ผู้ป่วยนอก",
          "dp_in": "ผู้ป่วยใน",
          "ui_headhealthamountnet": "ค่าใช้จ่ายรวม",
          "tf_amountnet": "พิมพ์จำนวนเงิน",
          "ui_amountnetremark": "*จำนวนเงิน ทั้งหมด ที่จ่ายจริง ตามใบเสร็จ",
          "ui_headamountroom": "ค่าห้องพัก ที่นอนรักษา",
          "tf_amountroom": "พิมพ์จำนวนเงิน",
          "ui_roomremark":
              "*ค่าห้องพัก ที่นอนรักษา ที่ถูกคิดตามใบเสร็จ ( ถ้าไม่มีให้ใส่ 0 )",
          "ui_headamountday": "จำนวนวันที่นอนรักษาตัว",
          "tf_amounttday": "พิมพ์จำนวนวัน",
          "ui_dayremark":
              "* จำนวนวัน ที่ถูกคิดเงินตามใบเสร็จ (ถ้าไม่มียอดเงินให้ใส่ 0)",
          "btn_next": "ถัดไป",
          "pkgwelfarehealthfrom2": "pkgwelfarehealthfrom2",
          "ui_headhealthfrom2": "สวัสดิการสุขภาพ",
          "tf_remark": "*กรณีถ้าไม่มี กรุณาใส่ 0",
          "tf_typingamount": "พิมพ์จำนวนเงิน",
          "ui_visitingdoctor": "ค่าเยี่ยมเเพทย์ค่าตรวจรักษาของผู้ประกอบวิชาชีพ",
          "ui_amountday": "จำนวนวัน ในใบเสร็จ ที่จ่ายค่า “ แพทย์เยี่ยม ”",
          "tf_amountday": "พิมพ์จำนวนวัน",
          "ui_treatmentcost": "ค่ารักษาพยาบาลอื่นๆ",
          "ui_surgerycost": "ค่าการผ่าตัด",
          "ui_medication": "ค่ายากลับบ้าน",
          "btn_before": "ก่อนหน้า",
          "btn_next2": "ถัดไป",
          "pkgwelfarehealthfrom3": "pkgwelfarehealthfrom3",
          "ui_headhealthfrom3": "สวัสดิการสุขภาพ",
          "tf_remarknamebank": "*เช่น ศิริวรรณ ส่งศรี เป็นต้น",
          "tf_remarknumbank": "*ตย. **********",
          "tf_remarktel": "*ตย. ***********",
          "ui_doc": "เอกสารแนบ หรือ เอกสารตัวจริง",
          "btn_image": "อัพโหลดรูปภาพ",
          "ui_remark": "หมายเหตุ",
          "tf_typingremark": "พิมพ์รายละเอียด",
          "ui_namebank": "ข้อมูลการโอนเงินชื่อบัญชี",
          "tf_naembank": "พิมพ์รายละเอียด",
          "ui_numbank": "เลขที่บัญชี ธนาคารกสิกร ตามเลขที่บัญชีเงินเดือน",
          "tf_numbank": "พิมพ์เลขที่บัญชี",
          "ui_tel": "เบอร์โทรติดต่อผู้เบิก",
          "tf_tel": "พิมพ์เบอร์โทร",
          "btn_before3": "ก่อนหน้า",
          "btn_next3": "ถัดไป",
          "btn_disbursement":"รายละเอียดการเบิกสวัสดิการ",
          "btn_WelfareList":"รายการสวัสดิการ",
          "btn_amount_disbursement":"จำนวนเงินที่เบิก",
          "btn_transfer_account":"โอนเข้าบัญชี",
          "btn_recording_date":"วันที่บันทึก",
          "pkgwelfarehealthfrom4": "pkgwelfarehealthfrom4",
          "ui_headhealthfrom4": "สวัสดิการสุขภาพ",
          "ui_historymodify": "ข้อมูลประวัติการรักษา ชื่อสถานพยาบาล",
          "tf_historymodify": "พิมพ์รายละเอียด",
          "ui_namedoc": "ชื่อแพทย์ที่รักษา / แพทย์เจ้าของไข้",
          "tf_namedoc": "พิมพ์รายละเอียด",
          "ui_becurse": "สาเหตุของการเข้ารักษา",
          "tf_becurse": "พิมพ์รายละเอียด",
          "ui_drug": "ยาที่ได้รับ",
          "tf_drug": "พิมพ์รายละเอียด",
          "ui_typedrug": "*ระบุชนิดของยาที่ได้รับในการรักษาครั้งนี้",
          "btn_savefrom": "บันทึกข้อมูล",
          "btn_before4": "ก่อนหน้า",
          "pkgwelfarehealthconfirm": "pkgwelfarehealthconfirm",
          "ui_headwelfarehealthConfirm": "สวัสดิการสุขภาพ",
          "GTCbuyORsell": "GTCbuyORsell",
          "ui_GTCbuyORsell": "หุ้น GTC",
          "ui_GTCbuy": "ซื้อ",
          "ui_GTCsell": "ขาย",
          "ui_GTCbuycondition": "ดูเงื่อนไขการซื้อ",
          "ui_GTCbuycondition2": "ดูเงื่อนไขการขาย",
          "ui_GTCbuyRule": "เงื่อนไขการซื้อหุ้น GTC",
          "ui_GTCbuyRuleDetail":
              "สำหรับสมาชิกอายุงานบรรจุเป็นสมาชิกประจำ\nซื้อหุ้นต่อคนได้ตามที่ คกก. GTC แจ้งเงื่อนไข\nการซื้อในแต่ละครั้ง\nสำหรับสมาชิกแจ้งซื้อแล้ว แนบหลักฐาน\nการโอน likepoint ณ วันที่แจ้งด้วยค่ะ\n\nหากตรวจสอบแล้วยังไม่ได้โอนมา \nทีมงานขอยกเลิกหุ้นที่แจ้งซื้อ \nงานนี้ใครโอนก่อนมีสิทธิ์ก่อน",
          "btn_GTCbuyRuleOK": "ตกลง",
          "ui_GTCsellRule": "เงื่อนไขการขายหุ้น GTC",
          "ui_GTCsellRuleDetail":
              "1.สามารถขายได้ไม่เกิน 20.0%\nของหุ้นที่คุณถือครองอยู่\n2.สามารถขายได้ไม่เกิน 380.0 หุ้น\n( หรือประมาณ 10,000 บาท )\n3.สามารถขายได้ไม่เกินจำนวนหุ้นที่ GTC\nเปิดขายคงเหลืออยู่",
          "btn_GTCsellRuleOK": "ตกลง",
          "ui_GTCunit": "หุ้น",
          "ui_GTCbath": "บาท",
          "btn_GTCbuy": "ซื้อ",
          "ui_GTCheadBuy": "ซื้อหุ้น GTC",
          "ui_GTCbv": "ราคาต่อหุ้น BV",
          "ui_GTCnamesale": "ชื่อผู้ขาย",
          "ui_GTCnumsale": "จำนวนหุ้นที่เสนอขาย",
          "ui_GTCnumbuy": "จำนวนหุ้นต้องการซื้อ",
          "ui_GTCenterOnlyNum": "",
          "tf_GTCnumbuy": "กรอกจำนวนหุ้น",
          "ui_GTCrecive": "จำนวนหุ้นที่จะได้รับ",
          "ui_GTCbuyMoney": "จำนวนเงินที่ซื้อ",
          "ui_GTCcashout": "จำนวนเงินที่ต้องโอนจ่าย",
          "btn_GTCbuyindetail": "ซื้อ GTC",
          "ui_GTCdetail": "* รายละเอียดการซื้อ",
          "ui_GTCdetailtime": "ระยะเวลาในการทำรายการภายใน 30 นาที",
          "ui_GTCchanal": "ช่องทางการโอนจ่ายเงิน",
          "ui_GTCcashing": "กำลังชำระเงิน",
          "ui_GTCdetailcancle": "รายการจะถูกยกเลิกภายใน",
          "ui_GTCdetailcancle2": "30",
          "ui_GTCdetailcancle3": "นาที",
          "ui_GTCkeybuy": "เลขที่ธุรกรรมการซื้อ",
          "ui_GTCdetailbuy": "รายละเอียดการซื้อ GTC",
          "ui_GTCnumunit": "จำนวนหุ้นที่ซื้อ",
          "ui_GTCpriceperunit": "ราคาต่อหุ้น",
          "ui_GTCremark": "หมายเหตุ",
          "ui_GTCremarkdetail":
              "กรุณาสอบถามรายละเอียดการโอนเงินกับผู้ขายก่อนชำระเงินได้ผ่านทางช่องทางการติดต่อ\n",
          "ui_GTCremarkdetail2":
              "เมื่อดำเนินการโอนเงินแล้ว ทำการกดปุ่ม “ โอนเงินแล้ว ”",
          "btn_GTCcancel": "ยกเลิก",
          "btn_GTCmove": "โอนเงินแล้ว",
          "ui_GTCstatus": "สถานะ",
          "ui_GTCremarkdetail3":
              "กรณีโอนเงินแล้วไม่ได้รับการโอนหุ้น GTC จากผู้ขาย\n",
          "ui_GTCremarkdetail4":
              "ให้กดปุ่ม “ ร้องเรียน ” เพื่อติดต่อทีมคกก.GTC",
          "btn_GTCreport": "ร้องเรียน",
          "ui_GTCreport": "ร้องเรียน",
          "btn_GTCAppealcancle": "ยกเลิก",
          "ui_GTCAppealdetail": "รายละเอียด",
          "tf_GTCAppealType": "กรุณาพิมพ์รายละเอียด..",
          "btn_GTCAppealSend": "ส่งข้อมูล",
          "ui_GTCAppealSuccess": "ยืนยันเรียบร้อย",
          "ui_GTCAppealDetail":
              "ทางคกก. GTC ได้รับข้อมูล\nการร้องเรียนของคุณแล้ว\nเรียบร้อยค่ะ",
          "btn_GTCAppealok": "ตกลง",
          "ui_GTCtransuccess": "ทำรายการสำเร็จ",
          "ui_GTCdetailsuccess":
              "GTC ได้โอนเข้าบัญชีของคุณแล้ว\nสามารถตรวจสอบได้ที่ หน้าหลัก",
          "ui_GTCsuccess": "เรียบร้อย",
          "ui_GTCconnectsale": "ติดต่อผู้ขาย",
          "ui_GTCconnectsale2": "คุณสามารถติดต่อกับผู้ขายหุ้น\nได้ที่เบอร์",
          "btn_GTCconnectOK": "ตกลง",
          "ui_GTCnum": "จำนวนหุ้นที่ถือครอง",
          "ui_GTCguarantee": "หุ้นที่ถือครองติดค้ำ CU",
          "ui_GTCfree": "หุ้นที่ถือครองปลอดภาระ",
          "ui_GTCover": "จำนวนหุ้นขายได้ไม่เกิน",
          "ui_GTCforsale": "จำนวนหุ้นที่เสนอขาย",
          "tf_GTCforsale": "ใส่จำนวนหุ้นที่จะขาย",
          "ui_GTCtel": "เบอร์โทรติดต่อ",
          "tf_GTCtel": "กรอกเบอร์โทรสำหรับให้ผู้ซื้อติดต่อ",
          "ui_GTCforsaleDetail": "* ข้อมูลรายละเอียดการขาย",
          "ui_GTCsale": "ราคาขาย (BV)",
          "ui_GTCforsaleunit": "จำนวนหุ้นที่เสนอขาย",
          "ui_GTCforsalebath": "มูลค่าที่เสนอขาย",
          "ui_GTCdate": "วันที่เสนอขาย",
          "ui_theme": "ธีมและหน้าจอ",
          "ui_theme_0": "ใช้การตั้งค่าพื้นฐานของตัวเครื่อง",
          "ui_theme_1": "โหมดมืด",
          "ui_theme_2": "โหมดสว่าง",
          "ui_GTCcheck":
              "* กรุณาตรวจสอบรายละเอียดให้ถูกต้องทุกครั้ง ก่อนกดยืนยันการขาย",
          "btn_GTCok": "ยืนยัน",
          "ui_GTCconfirmSuccess": "ยืนยันเรียบร้อย",
          "ui_GTCreciveData": "ทางเราได้รับข้อมูลการขายหุ้น GTC\nของคุณ ",
          "ui_GTCreciveData2": "\nเรียบร้อยแล้วค่ะ",
          "btn_GTCsellSuccess": "ตกลง",
          "ui_GTCheadTransfre": "แจ้งการยืนยันโอนขายหุ้น GTC",
          "ui_GTCtransferDetail": "รายละเอียดการซื้อ GTC",
          "ui_GTCtransfersale": "ชื่อผู้ซื้อ",
          "ui_GTCtransfernumbuy": "จำนวนหุ้นที่ขอซื้อ",
          "ui_GTCtransferConfirm": "ยืนยันโอนหุ้น",
          "ui_GTCtransferunit": "ราคาต่อหุ้น",
          "ui_GTCtransfermoney": "จำนวนเงินที่คุณจะได้รับ",
          "btn_GTCtransfercancle": "ยกเลิก",
          "btn_GTCtransferOK": "ยืนยันโอนหุ้น",
          "ui_GTCheadersuccess": "ทำรายการสำเร็จ",
          "ui_GTCtransferdetail":
              "GTC ได้โอนหุ้นที่คุณขาย เข้าบัญชีของผู้ซื้อแล้วเรียบร้อย\nสามารถตรวจสอบยอดเงินได้ที่ หน้าหลัก",
          "btn_GTCtransferSuccess": "เรียบร้อย",
          "Leave": "Leave",
          "ui_ABSENCE": "อนุมัติ /\nใบลา",
          "btn_approve": "อนุมติใบลา",
          "btn_leave": "สร้างใบลา",
          "ui_typeleavecreate": "ประเภทใบลา : ",
          "dp_selectleavecreate": "เลือกประเภทใบลา",
          "ui_dateleavecreate": "เริ่มวันที่",
          "ui_dateleavetocreate": "ถึงวันที่",
          "sl_dateleave": "เลือกวันที่",
          "ui_timestart": "เริ่มวันที่",
          "ui_timestop": "ถึงวันที่",
          "tf_detail": "ใส่รายละเอียด",
          "ui_sumdate": "รวม / วัน",
          "ui_detailleave": "รายละเอียดการลา",
          "tf_typingdetailcreate": "พิมพ์รายละเอียด",
          "ui_personApprove": "ผู้อนุมัติใบลา",
          "dp_personApprove": "เลือกผู้อนุมัติ",
          "btn_createleave": "สร้างใบลา",
          "btn_cancelcreate": "ยกเลิก",
          "noti_leave": "อนุมัติใบลาเรียบร้อย",
          "noti_leave1": "อนุมัติใบลาสำเร็จ",
          "LeaveDetail": "LeaveDetail",
          "ui_LeaveDetail": "รายละเอียดในการทำใบลา",
          "ui_name": "ชื่อ : ",
          "ui_typeleave": "ประเภทใบลา",
          "ui_dateleave": "วันที่ลา",
          "ui_dateleaveto": "ถึงวันที่",
          "ui_sumdateleave": "รวมจำนวนวันที่ลา",
          "ui_dateleave_day": "จำนวนการลา / วัน",
          "ui_DetailLeave": "รายละเอียด : ",
          "ui_datekey": "วันที่คีย์ข้อมูล : ",
          "btn_approveDetail": "อนุมัติ",
          "btn_notApproveDetail": "ไม่อนุมัติ",
          "ui_approvesuccess": "อนุมัติเรียบร้อย",
          "ui_approvedetail": "คุณได้อนุมัติใบลา ของ\nคุณ ",
          "ui_approvedetailsuccess": "\nเรียบร้อยแล้วค่ะ",
          "ui_approvesuccessCan": "ไม่อนุมัติเรียบร้อย",
          "ui_approvedetailCan": "คุณไม่อนุมัติใบลา ของ\nคุณ ",
          "ui_approvedetailsuccessCan": "\nโปรดแจ้งสมาชิกด้วย",
          "ui_createLeaveSuccess": "สร้างใบลาเรียบร้อย",
          "ui_createdetail":
              "ใบลาของคุณได้ถูกสร้าง\nและส่งให้ผู้รับใช้ทีม เรียบร้อยแล้ว\nโปรดรอการอนุมัติ",
          "ui_approveok": "ตกลง",
          "timmi": "ติ๋มมี่ :",
          "leaveconfirm": "leaveconfirm",
          "ui_leaveconfirm": "ยืนยันการอนุมัติ",
          "ui_leaveYN": "คุณต้องการยืนยันการอนุมัติใช่หรือไม่",
          "ui_remarkleave": "หมายเหตุ :",
          "btn_leaveOK": "ตกลง",
          "btn_leaveCancel": "ยกเลิก",
          "createleaveconfirm": "createleaveconfirm",
          "ui_createleaveconfirm": "ยืนยันการสร้างใบลา",
          "ui_createleaveYN": "คุณต้องการสร้างใบลาใช่หรือไม่",
          "btn_createleaveOK": "ตกลง",
          "btn_createleaveCancel": "ยกเลิก",
          "mainidea": "mainidea",
          "ui_mainideahead": "PKG IDEA",
          "btn_newsidea": "ข่าวสารไอเดีย",
          "btn_vdo": "วีดีโอ",
          "btn_addidea": "ออกไอเดีย",
          "ui_by": "BY",
          "btn_sendidea": "ออกไอเดีย",
          "addidea": "addidea",
          "ui_headaddidea": "ออกไอเดีย",
          "ui_nameidea": "ชื่อไอเดีย",
          "tf_typingidea": "พิมพ์ชื่อไอเดีย",
          "ui_detailidea": "รายละเอียด / คำอธิบาย",
          "tf_typingdetail": "พิมพ์รายละเอียด",
          "btn_saveidea": "บันทึกข้อมูล",
          "ui_savesuccess": "บันทึกเรียบร้อย",
          "ui_thx": "ขอบคุณสำหรับการร่วมออกไอเดีย\nของคุณด้วยนะครับ",
          "btn_okidea": "ตกลง",
          "detailidea": "detailidea",
          "ui_headdetail": "รายละเอียด",
          "ui_post": "โพสต์โดย",
          "ui_vote": "ให้ดาว",
          "btn_savevote": "บันทึกข้อมูล",
          "ui_votesuccess": "บันทึกเรียบร้อย",
          "ui_votethx": "ขอบคุณสำหรับคะแนนโหวต\nของคุณด้วยนะครับ",
          "ui_voteok": "ตกลง",
          "ui_comment": "แสดงความคิดเห็น",
          "tf_Write": "พิมพ์ข้อความ...",
          "tf_levae_Write": "พิมพ์สาเหตุการลา...",
          "tf_levae_note": "สาเหตุการลา : ",
          "btn_hidecomments": "ซ่อนความคิดเห็น",
          "btn_comments": "ความคิดเห็น",
          "QRscan": "QRscan",
          "ui_QRscanhead": "สแกนคิวอาร์โค๊ด",
          "ui_QRscanSuccess": "เรียบร้อย",
          "ui_QRscanSuccessDetail": "ทางเราได้สร้าง กิจกรรม กดเคลม\nของคุณ",
          "ui_QRscanSuccessDetail2": "\nเรียบร้อยแล้วค่ะ",
          "ui_QRscanok": "ตกลง",
          "ui_QRscanSuccessDetail3": "เข้าสู่ระบบ WebConnect",
          "cew": "cew",
          "ui_cewhead": "บันทึก CEW",
          "btn_cewPerson": "บุคคล",
          "ui_cewTeam": "ทีม",
          "ui_cewTeamRec": "ทีมที่ได้รับ",
          "dp_cewTeam": "เลือกทีม",
          "ui_cewPersonRec": "สมาชิกที่ได้รับ",
          "tf_cewIdPerson": "ใส่รหัสสมาชิก / พิมพ์ชื่อ",
          "tf_cewBU": "ใส่ BU",
          "ui_cewType": "เลือกประเภท Cew",
          "btn_cewRoadmap": "แผนงาน",
          "btn_cewAtmosphere": "บรรยากาศ",
          "btn_cewGeneral": "ทั่วไป",
          "tf_cewTyping": "พิมพ์รายละเอียด..",
          "tf_cewNumLike": "ใส่จำนวนไลค์พอยท์",
          "dp_cewCategory": "เลือกหมวดหมู่",
          "ui_cewDetailAAM": "* กรณี AAM กรุณาเลือกเขต",
          "ui_cewDetailAAM2":
              "ส่วน FU BU คณะกรรมการ และ\nRPLC ให้เลือก “ ไม่มี ”",
          "dp_cewCounty": "เลือกเขต",
          "dp_cewTypecew": "เลือกประเภท CEW",

          "ui_cewGiveCew": "ผู้ออก CEW",
          "ui_cewDetailCew": "รายละเอียดการ CEW",
          "ui_cewPersonRec2": "ผู้รับ CEW",
          "ui_cewNumLike": "จำนวนไลค์พอยท์",
          "ui_cewUnitLike": "ไลค์",
          "ui_cewTypeCategory": "ประเภท / หมวดหมู่",
          "ui_cewDateCEW": "วันที่บันทึก CEW",
          "ui_cewDate": "วันที่บันทึก",
          "dp_cewBranch": "เลือกสาขา",
          "ui_cewConfirm": "ยืนยัน",
          "ui_cewSuccess": "บันทึกเรียบร้อย",
          "ui_cewSuccessDetail": "ทางเราได้รับข้อมูลการบันทึก CEW\nของคุณ",
          "ui_cewSuccessDetail2": "\nเรียบร้อยแล้วค่ะ",
          "ui_cewSuccessok": "ตกลง",
          "noit_saveCew": "บันทึก Cew สำเร็จ",
          "mainBitrix": "mainBitrix",
          "ui_mainBitrixhead": "ประเด็นงานของฉัน",
          "btn_mainBitrixRecJob": "ผู้รับงาน",
          "btn_mainBitrixSendJob": "ผู้จ่ายงาน",
          "ui_mainBitrixNumJob": "คุณมีประเด็นงานทั้งหมด",
          "ui_mainBitrixList": "รายการ",
          "ui_mainBitrixOverDeadline":
              "ยังไม่มีการรายงาน\nความก้าวหน้าเพิ่มเติมมา",
          "ui_mainBitrixOverDeadline2": "วันแล้ว",
          "btn_mainBitrixHideComment": "ซ่อนความคิดเห็น",
          "btn_mainBitrixComment": "ความคิดเห็น",
          "btn_mainBitrixAddComment": "รายงานความก้าวหน้า",
          "btn_mainBitrixTyping": "พิมพ์ข้อความ..",
          "btn_mainBitrixUploadImg": "แนบรูป",
          "btn_mainBitrixEdit": "แก้ไข",
          "ui_mainBitrixNotSuccess": "ดำเนินการไม่สำเร็จ",
          "ui_mainBitrixNotSuccessDetail": "1. กรุณากดปุ่ม ",
          "ui_mainBitrixNotSuccessDetail2":
              " ก่อนทุกครั้ง\nเพื่อทำการรับประเด็นงานของคุณ\n2. กรุณาพิมพ์รายงานความก้าวหน้า\n3. หากประเด็นงานของคุณแล้วเสร็จ\nเรียบร้อย กรุณากดปุ่ม Finish",
          "btn_mainBitrixNotSuccessok": "ตกลง",
          "ui_mainBitrixSuccess": "บันทึกเรียบร้อย",
          "ui_mainBitrixSuccessDetail":
              "ได้รับข้อมูลการส่งรายงาน\nการปิดประเด็นงานนี้\nของคุณแล้ว เรียบร้อยค่ะ",
          "btn_mainBitrixSuccessok": "ตกลง",
          "ui_mainBitrixCloseIssueSuccess": "บันทึกเรียบร้อย",
          "ui_mainBitrixCloseIssueSuccessDetail":
              "ได้รับข้อมูลการรายงาน\nการปิดประเด็นงานนี้ ของสมาชิก\n",
          "ui_mainBitrixCloseIssueSuccessDetail2": "\nเรียบร้อยแล้วค่ะ",
          "btn_mainBitrixCloseIssueSuccessok": "ตกลง",
          "ui_mainBitrixReturnSuccess": "บันทึกเรียบร้อย",
          "ui_mainBitrixReturnSuccessDetail":
              "ได้รับข้อมูลการส่ง Return\nประเด็นงานนี้ ของสมาชิก\n",
          "ui_mainBitrixReturnSuccessDetail2": "\nเรียบร้อยแล้วค่ะ",
          "btn_mainBitrixReturnSuccessok": "ตกลง",
          "ui_mainBitrixShowDetailHead": "คำอธิบายเพิ่มเติม",
          "ui_mainBitrixShowDetai": "1.ตรวจสอบประเด็นงานแล้วถูกต้อง\nกดปุ่ม ",
          "ui_mainBitrixShowDetai2":
              " ปิดงานได้เลย\n2.ตรวจสอบประเด็นงานแล้วไม่ถูกต้อง\nกดปุ่ม ",
          "ui_mainBitrixShowDetai3": " พร้อม ",
          "ui_mainBitrixShowDetai4":
              "แจ้งสาเหตุและข้อมูลที่ต้องการ\n3.คลิกเปลี่ยนกำหนดวันที่แล้วเสร็จใหม่\nทุกครั้งด้วย",
          "btn_mainBitrixShowDetaiContinue": "ดำเนินการต่อ",
          "likeCredit": "likeCredit",
          "ui_likeCredithead": "ไลค์ เครดิต",
          "ui_likeCreditPoint": "คะแนนของคุณ",
          "ui_likeCreditLastUpdate": "อัพเดทล่าสุด",
          "ui_likeCreditChange": "ใช้คะแนนแลกสิทธิพิเศษ",
          "ui_likeCreditUnitPoint": "คะแนน",
          "btn_likeCreditChange": "แลก",
          "ui_likeCreditConfirmChange": "ยืนยันการแลกคะแนน",
          "ui_likeCreditConfirmChangeDetail": "คุณต้องการใช้คะแนนแลก\n",
          "ui_likeCreditConfirmChangeDetail2": "เพิ่ม ใช่หรือไม่?\n",
          "ui_likeCreditConfirmChangeDetail3": "ใช้คะแนน",
          "ui_likeCreditConfirmChangeDetail4": "คะแนน",
          "btn_likeCreditConfirmok": "ตกลง",
          "btn_likeCreditConfirmcancel": "ยกเลิก",
          "NDA": "NDA",
          "ui_NDAhead": "สัญญาการรักษาข้อมูลที่เป็นความลับ",
          "ui_NDAtitle":
              "สัญญาการรักษาข้อมูลที่เป็นความลับ\n(Non-disclosure Agreement)",
          "ui_NDAdetail":
              "“ข้อมูลที่เป็นความลับ” หมายความถึง ข้อมูลใดๆ รวมทั้งข้อมูลของบุคคลภายนอกที่ฝ่าย ผู้ให้ข้อมูลได้เปิดเผยแก่ฝ่ายผู้รับข้อมูล และฝ่ายผู้ให้ข้อมูลประสงค์ให้ฝ่ายผู้รับข้อมูลเก็บรักษาข้อมูลดังกล่าวไว้เป็นความลับและ/หรือความลับทางการค้าของฝ่ายผู้ให้ข้อมูล โดยข้อมูลดังกล่าวจะเกี่ยวข้องกับผลงานวิจัยเดิม ผลงานวิจัยของโครงการ และ/หรือการดำเนินงานโครงการ แผนและ/หรือแนวทางการวิจัย และ/หรือแผนทางการตลาด ซึ่งรวมถึงแต่ไม่จำกัดเฉพาะกระบวนการ ขั้นตอนวิธี โปรแกรมคอมพิวเตอร์ (รหัสต้นฉบับ รหัสจุดหมาย โปรแกรมปฏิบัติการ และฐานข้อมูลที่ใช้เชื่อมต่อโปรแกรมคอมพิวเตอร์) แบบ ต้นแบบ ภาพวาด สูตร เทคนิค การพัฒนาผลิตภัณฑ์ ข้อมูลการทดลอง และข้อมูลอื่นใดที่เกี่ยวข้องกับผลงานวิจัยเดิม ผลงานวิจัยของโครงการ และ/หรือการดำเนินงานโครงการ ขอให้ท่านอ่านสัญญาอย่างถี่ถ้วน พร้อมลงนามยินยอมตามข้อตกลง",
          "btn_ReadDoc": "ตรวจสอบเอกสาร",
          "btn_sign": "เซ็นเอกสาร",
          "btn_signP1": "เซ็นเอกสาร P1",
          "btn_signP2": "เซ็นเอกสาร P2",
          "more": "more",
          "ui_morehead": "เมนูทั้งหมด",
          "ui_toolsWork": "เครื่องมือทำงาน / งาน",
          "ui_HWWTF": "HWWTF",
          "btn_ToolsBU": "เครื่องมือ\nทำงาน BU",
          "btn_TA": "ลงเวลา\nทำงาน",
          "btn_Cew": "บันทึก\nCEW",
          "ui_agreement": "ข้อมูลสัญญา",
          "btn_loanCU": "สัญญา\nเงินกู้สามัญ",
          "btn_nonAgreement": "สัญญา\nไม่เปิดเผย\nข้อมูล",
          "ui_MVP": "เมนูทดสอบ",
          "btn_nitroSign": "อนุมัติเอกสาร\n     การเงิน",
          "btn_CS24": "แอพ CS24",
          "Nitrosign": "เซ็นเอกสาร",
          "ui_NitroListhead": "อนุมัติเอกสาร",
          "ui_Nitrohead": "เซ็นเอกสาร",
          "ui_Nitroreference": "เลขอ้างอิง",
          "ui_Nitrostatus": "สถานะ",
          "btn_NitroSign": "เซ็น",
          "ui_NitroSelect": "เลือก",
          "ui_NitroSelectDetail": "คุณจะใช้ลายเซ็น\nที่มีอยู่หรือสร้างใหม่",
          "ui_NitroSelectExist": "ที่มีอยู่",
          "ui_NitroSelectNew": "สร้างใหม่",
          "ui_NitroAgree": "ยินยอม",
          "ui_NitroAgreeDetail":
              "คุณยินยอมที่จะให้ใช้\nลายเซ็น อิเล็กทรอนิกส์\nที่มีอยู่ หรือไม่",
          "ui_NitroAgreeOK": "ยินยอม",
          "ui_NitroAgreeNO": "ไม่ ยินยอม",
          "ui_sign_cap1": "ลายเซ็นต์ของคุณจะถูกบันทึกให้เป็นอัตโนมัติทันที",
          "ui_sign_cap2": "เมื่อคุณกดปุ่ม",
          "ui_sign_cap3": "ยืนยันเซ็นต์เอกสาร",
          "ui_sign_cap4": "ด้านล่าง",
          "ui_sign_name": "หนังสือรับรอง : ",
          "ui_sign_status": "สถานะ : ",
          "ui_sign_status_done": "สำเร็จ",
          "ui_sign_status_paddind": "รอเซ็นยืนยัน",
          "btn_sign_1": "ยืนยันเซ็นเอกสาร",
          "btn_sign_2": "กลับหน้าหลัก",
          "ui_doc_emtpy": "ไม่มีรายการเอกสารอนุมัติ",
          "ui_doc_emtpy_hr": "ไม่มีรายการเอกสารอนุมัติจาก HR",
          "ui_doc_emtpy_pms": "ไม่มีรายการเอกสารอนุมัติจาก PMS",
          "CS24": "CS24",
          "ui_CS24head": "รายชื่อที่ค้นพบ",
          "ui_CS24head2": "รายละเอียดข้อมูลลูกค้า",
          "btn_seeDetail": "ดูข้อมูลส่วนตัว",
          "ui_detailBU": "ข้อมูลแต่ละ BU",
          "btn_buPMS": "PMS",
          "btn_buAAM": "AAM",
          "btn_buPMG": "PMG",
          "btn_buPCC": "PCC",
          "btn_buRPLC": "RPLC",
          "btn_buRAFCO": "RAFCO",
          "alert_not_found_likewallet": " Phone number not found like wallet",
          "test": "test",
          "ui_totalHeldShares": "หุ้นถือครอง",
          "ui_holdShareReturns": "จากหุ้นที่ถือครอง",
          "ui_waitHeldShares": "หุ้นรอถือครอง",
          "ui_titleAlert3": "แจ้งเตือน",
          "ui_detailAlert4": "กรุณาเซ็นเอกสาร",
          "btn_HRSign": "อนุมัติเอกสาร\nบุคคลากร",
          "pending_sign": "รอเซ็นยืนยัน",
          "document_detail": "รายละเอียดเอกสาร",
          "report_invalid_document": "กดที่นี่! หากเอกสารไม่ถูกต้อง",
          "report_invalid_document_detail": "รายละเอียด",
          "btn_report_invalid_document": "ยืนยัน",
          "ui_here": "ที่นี่!",
          "ui_regis_signature":
              "ไม่พบลายเซ็นในฐานข้อมูล\nกรุณาขึ้นทะเบียนลายเซ็นของท่าน",
          "ui_click_here_to_sign": "กดที่นี่! เพื่อลงนามเอกสาร",
          "ui_register_twoFA": "ลงทะเบียน 2FA",
          "not_support_NFC":
              "เครื่องของท่านไม่รองรับระบบ NFC\n สำหรับการใช้งาน YubiKey\n กด ถัดไป เพื่อลงทะเบียนการใช้งาน OTP",
          "USER_OTP":
          "กด ถัดไป เพื่อใช้งาน OTP",
          "support_NFC": "เว็บยังไม่รองรับระบบ NFC\n สำหรับการใช้งาน YubiKey\n กด ถัดไป เพื่อลงทะเบียนการใช้งาน OTP",

          "not_support_NFC_verify":
              "เครื่องของท่านไม่รองรับระบบ NFC\n สำหรับการใช้งาน YubiKey\n กด ถัดไป เพื่อใช้งาน OTP",
          "btn_register_twoFA": "ลงทะเบียน",
          "btn_register_OTP": "ลงทะเบียน OTP",
          "btn_comfirm":"ยืนยัน",
          "ui_success_yubikey": "คุณได้ลงทะเบียน YubiKey เรียบร้อยแล้ว",
          "ui_success_yubikey_verify": "ใช้งาน YubiKey เรียบร้อยแล้ว",
          "ui_success_otp": "คุณได้ลงทะเบียน OTP เรียบร้อยแล้ว",
          "ui_success_otp_sending": "ระบบกำลังทำการส่ง OTP เรียบร้อย",
          "ui_success_otp_send":
              "ระบบทำการส่งรหัส OTP ไปยัง Telegram เรียบร้อยแล้ว",
          "ui_success_otp_error":"ระบบการส่งรหัส OTP มีปัญหา",
          "ui_success_otp_error1":"ขออภัยขณะนี้ ระบบการส่งรหัส OTP มีปัญหา กรุณาลองใหม่อีกครั้ง",
          "ui_success_otp_verify_header": "ยืนยันรหัส OTP",
          "ui_otp_here": "กรอก OTP ที่ได้รับจาก Telegram",
          "ui_success_otp_verify": "เข้าใช้งานระบบ OTP เรียบร้อยแล้ว",
          "ui_success_otp_title": "ยืนยันการใช้งาน OTP สำเร็จ",
          "ui_waiting": "กรุณารอสักครู่...",
          "ui_duplicate_otp":
              "เครื่องของท่านไม่รองรับระบบ NFC\n สำหรับการใช้งาน YubiKey",
          "ui_duplicate_otp_detail": "ท่านได้ทำการลงทะเบียน OTP ไปแล้ว",
          "ui_touch_yubikey": "แตะ YubiKey\n กับโทรศัพท์ของคุณ",
          "ui_touch_yubikey_verify": "กับโทรศัพท์ของคุณ",
          "ui_touch_yubikey_verify_1": "YubiKey นี้ได้มีการลงทะเบียนไปแล้ว",
          "ui_register_twoFA_header": "ระบบความปลอดภัย กลุ่มประชากิจ",
          "ui_scan_yubikey": "สแกน YubiKey",
          "ui_change_to_otp": "กดที่นี่! เพื่อเปลี่ยนเป็น OTP",
          "ui_please_unlock": "กรุณาทำการปลดล็อค",
          "ui_unlock": "ปลดล็อค",
          "ui_to_sign_in": " เพื่อเข้าใช้งาน MS24",
          "privacy_policy": "นโยบายความเป็นส่วนตัว",
          "btn_PMSSign": "เอกสารการ\nทำงาน",
          "ui_notification_list": "ไม่มีรายการแจ้งเตือน",
          "ui_delete_l": "ลบข่าวสารทั้งหมด",
          "ui_delete_1": "กรุณาอ่านข่าวสารของท่านให้ครบถ้วน",
          "ui_delete_2": "ก่อนทำการลบข้อมูลข่าวทั้งหมด",
          "ui_delete_3": "ลบทั้งหมด",
          "ui_delete_4": "ยกเลิก",
          "ui_delete_5": "มีข่าวสารที่คุณยังไม่ได้อ่าน",
          "ui_approval_document": "ไม่มีรายการเอกสารอนุมัติ",
          "ui_approval_1": "ตักเตือน",
          "ui_approval_2": "สัญญาประกัน",
          "ui_approval_3": "หน่วยงาน BU",
          "btn_nonAgreement_1": "สัญญาไม่เปิด\nเผยข้อมูล",
          "btn_nonAgreement_head": "สัญญาไม่เปิดเผยข้อมูล",
          "ui_likecredit_t": "แลกสิทธิพิเศษ",
          "ui_likecredit_1": "คุณต้องการที่จะใช้คะแนนแลก",
          "ui_likecredit_2": "สิทธิพิเศษหรือไม่",
          "ui_profile": "ข้อมูลส่วนตัว",
          "ui_birthday": "วันเดือนปีเกิด",
          "ui_IDcard": "เลขบัตรประชาชน",
          "ui_number": "เบอร์โทรศัพท์",
          "ui_numberlikewallet": "เบอร์โทรศัพท์ผูกบัญชี",
          "ui_email": "อีเมล์",
          "ui_security_p": "ระบบความปลอดภัย",
          "ui_profile_e": "แก้ไขข้อมูลส่วนตัว",
          "ui_life": " อายุงาน นับจากวันบรรจุ",
          "ui_Likepoint_complete":"ได้รับ Likepoint สำเร็จ",
          "ui_rewardCU":
              "คุณได้รับ Reward ประจำวันจากกองทุน Meta CU เรียบร้อยแล้วค่ะ",
          "ui_jobmenu":
              "คุณมีประเด็นงานใหม่ กรุณากดรับงานที่เมนู\"ประเด็นงานของฉัน\"",
          "ui_noti":
              "มีแจ้งเตือนใหม่รอให้คุณอ่าน กรุณากดอ่านที่เมนู\"กระดิ่งแจ้งเตือน\" ",
          "ui_news_s": "มีข่าวใหม่รอให้คุณอ่าน กรุณากดอ่านที่เมนู \"ข่าวสาร\"",
          "ui_fin":
              "มีเอกสารรอคุณอนุมัติ กรุณากดที่เมนู \"อนุมัติเอกสารด้านการเงิน\"",
          "ui_hr_approval":
              "มีเอกสารรอคุณอนุมัติ กรุณากดที่เมนู\"อนุมัติเอกสารด้านบุคคลากร\"",
          "ui_wi_approval":
              "มีเอกสารรอคุณอนุมัติ กรุณากดที่เมนู\"อนุมัติเอกสารด้านกระบวนการ\"",
          "ui_fullname": "ชื่อ-นามสกุล",
          "ui_confirm_exchange": "แลก likecredit เรียบร้อย",
          "ui_OKR": "ข้อมูล OKR",
          "ui_paste": "วาง",
          "ui_Kip": "กีบ",
          "ui_otp_build_TG": "ใส่รหัส",
          "ui_otp_build_TG_2":
              "เราได้ส่ง SMS พร้อมรหัสเปิดใช้งานไปยังเบอร์โทรศัพท์ของคุณ",
          "ui_otp_build_TG_3": "กรอก OTP ที่ได้รับจาก SMS",
          "btn_signupTG": "สมัคร Telegram เรียบร้อยแล้ว",
          "btn_signupTG_m": "คุณได้ทำการสมัคร Telegram เรียบร้อยแล้ว",
          "ui_wealth": "ความมั่งคั่ง",
          "ui_time": "เวลา",
          "ui_health": "สุขภาพ",
          "ui_bodyinfo": "น้ำหนัก / ส่วนสูง",
          "ui_bodyinfo_cm": "ซม.",
          "ui_bodyinfo_kg": "กก.",
          "ui_disease_risk": "ภาวะเสี่ยงโรค",
          "ui_within_criteria": "เกณฑ์สุขภาพ",
          "ui_health_normal": "ปกติ",
          "ui_goodhealth_normal": "สุุขภาพดี",
          "ui_health_bmi": "ค่าดัชนีมวลร่างกาย",
          "ui_health_activity": "กิจกรรมออกกำลังกาย",
          "ui_health_total": "จำนวน / ครั้ง",
          "ui_ta_status": "สถานะ TA",
          "ui_ta_late": "สาย",
          "ui_ta_sick": "ป่วย",
          "ui_ta_levae": "ลากิจ",
          "ui_ta_uncheck": "ไม่ได้ลงเวลา",
          "ui_ta_totaltime": "จำนวนที่ลา / ปี",
          "ui_ta_missing": "วันหาย",
          "ui_ta_missing_TA": "ไม่ได้ลงเวลา",
          "ui_ta_total_average": "ค่าเฉลี่ยรวม",
          "ui_ta_stampedchart": "วันมาทำงาน",
          "ui_ta_levaechart": "ลากิจ",
          "ui_ta_levaechart_general": "ในสิทธิ์ปกติ",
          "ui_ta_levaechart_over": "เกินสิทธิ์",
          "ui_ta_levaechart_pfs": "ปรับ PFS",
          "ui_ta_alltotal": "รวมทั้งหมด",
          "ui_days": "วัน",
          "ui_box_TAmoring": "เช้า",
          "ui_box_TAafternoon": "บ่าย",
          "ui_box_TAallday": "ทั้งวัน",
          "ui_box_moring": "เช้า: 8:15 - 12:00.",
          "ui_box_afternoon": "บ่าย: 13:00 - 17:00.",
          "ui_box_afternoon2": "บ่าย: 13:00 - 15:30.",
          "ui_box_afternoon3": "บ่าย: 13:00 - 16:00.",
          "ui_box_allday": "ทั้งวัน:  8:15 - 17:00.",
          "ui_box_allmem": "ใจถึงใจ",
          "ui_finace_doc": "อนุมัติเอกสารการเงิน",
          "ui_finace_general": "เอกสาร\nการเงิน",
          "ui_finace_HR": "เอกสารการ\nทำงาน",
          "ui_finace_PMS": "เอกสาร\nบุคลากร",
          "ui_work_issues": "ประเด็นงาน",
          "ui_work_give": "ผู้จ่ายงาน",
          "ui_workissues": "ประเด็นงาน",
          "dp_select_time": "เลือกช่วงเวลา",
          "ui_LikeWallet": "ไลค์วอลเลท",
          "ui_chart_sicked": "ลาป่วย",
          "ui_chart_sicked_general": "ป่วยทั่วไป",
          "ui_chart_sicked_totaltime": "รวมทั้งหมด",
          "ui_chart_sicked_accident": "อุบัติเหตุ",
          "ui_day":"วัน",
          "ui_chart_vacation": "ลาพักร้อน",
          "ui_chart_vacation_totaltime": "รวมทั้งหมด",
          "ui_chart_vacation_dayoff": "วันหยุด",
          "ui_doorscan": "เปิดประตู",
          "ui_doorscan_barrir": "ประตูไม้กั้น",
          "ui_doorscan_meeting": "ประตูห้องประชุม",
          "ui_doorscan_howto": "วิธีใช้งานเมนูเปิดประตู,",
          "ui_doorscan_tap": " กดที่ปุ่มด้านล่าง ",
          "ui_doorscan_toopen": "เพื่อเปิดประตู",
          "ui_input": "พิมพ์ข้อความ",
          "ui_tranfer": "โอน",
          "ui_resive": "รับ",
          "ui_lockall": "ล๊อคทั้งหมด",
          "ui_sicked": "ป่วย",
          "ui_oversicked": "ป่วยเกินสิทธิ์",
          "ui_Leaveofabsence": "ลากิจ",
          "ui_Leaveofabsenceover": "ลากิจเกินสิทธิ์",
          "ui_vacation": "ลาพักร้อน",
          "ui_meeting": "ประชุม / สัมมนา",
          "ui_missing_morning": "ไม่ได้ลงเวลาตอนเช้า",
          "ui_missing_afternoon": "ไม่ได้ลงเวลาตอนเย็น",
          "ui_comfirm": "ยืนยัน",
          "ui_deny": "ปฏิเสธ",
          "ui_timminoti": " มีใบลาที่ยังไม่อนุมัติ",
          "ui_history_emtpy": " ยังไม่มีข้อมูลรายการเบิกเคลม",
          "ui_timminoti_documents": "มีเอกสารที่ยังไม่เซ็น",
          "ui_timminoti_claimed": "มีรายการที่ยังไม่กดเคลม",
          "ui_timminoti2": "รายการ",
          "ui_timminoti3": "ค่ะ ",
          "ui_cilck": "CLICK.",
          "ui_noti_emtpy": "ไม่มีรายการแจ้งเตือน",
          "ui_news_emtpy": "ไม่มีข่าวสารใหม่",
          "ui_course_emtpy": "ไม่มีหลักสูตรใหม่",

          "ui_leave_emtpy": "ไม่มีรายการอนุมัติใบลา",
          "ui_scanning": "กำลังสแกน....",
          "ui_timmi_give": "ให้คะแนนฉันหน่อย",
          "ui_timmi_send": "ส่งคะแนนให้กับ MS24 หน่อยสิ",
          "ui_timmi_used":
              "รู้สึกอย่างไร เมื่อได้เข้ามาใช้งานแอพลิเคชั่นของเรา",
          "ui_timmi_tell": "มีอะไรอยากจะบอกเรามั้ย..",
          "ui_timmi_SKIP": "เอาไว้ก่อน",
          "ui_timmi_done": "ให้คะแนน",
          "ui_brith_skip": "ข้าม",
          "ui_date_lev": "วันที่ลา : ",
          "ui_chart_max": "เต็ม",
          "ui_tamissing_emtpy": "ยอดเยี่ยม คุณไม่มีวันหาย",
          "ui_save_health" : "บันทึก\nกิจกรรม",
          "ui_join_health":" เข้ากลุ่ม",
          "ui_join_health2":" สุขภาพ",
          "dp_cewtype":"เลือกรูปแบบ",
          "ui_cew_selcet":"ประเภท Cew",
          "ui_issues_selcete":"หัวข้อ : ",
          "ui_issues_date":"วันที่มอบหมายงาน : ",
          "ui_issues_note":"โน๊ต : ",
          "ui_cewinput": "พิมพ์ข้อความ..",
          "ui_levae_detail":"รายละเอียดการทำใบลา",
          "ui_levae_type":"ประเภทใบลา",
          "ui_levae_time": "ช่วงเวลา",
          "ui_timestartt":"ช่วงเวลา",
          "ui_timestopp":"ถึงเวลา",
          "ui_levae_sumday": "จำนวนการลา / วัน",
          "ui_detail_personApprove": "ผู้อนุมัติ",
          "btn_claim_new":"เคลม เลย!",
          "ui_Not_approved":"ช่วยบอกเราหน่อย",
          "ui_Not_approved2":"ว่าทำไมไม่อนุมัติ",
          "ui_finace":"การเงิน",
          "ui_emp":"บุคคลากร",
          "ui_work":"การทำงาน",
          "ui_doc_emtpy":"ไม่มีเอกสารใหม่",
          "ui_error":"พบข้อผิดพลาด",

        },
        //************************************************************************************ */
        //Vietnamese

        //************************************************************************************ */
        //Laos
        "la": {
          //language
          "Language": "ພາສາ",
          //onboarding screen
          "onboarding title 1": "MPC Driven Security",
          "onboarding body 1": "sdsdsddsd",
          "Home": "ဘာ"
        },
        //************************************************************************************ */
        //Khmer
      };
}
