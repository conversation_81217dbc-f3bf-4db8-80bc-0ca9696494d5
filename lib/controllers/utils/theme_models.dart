import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_ms24/controllers/utils/color.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ThemeController extends GetxController {
  var isDarkTheme = true.obs;
  var selectedTheme =
      0.obs; //0 = device default, 1 = dark theme, 2 = light theme
  final Brightness platformBrightness = WidgetsBinding.instance
      .platformDispatcher.platformBrightness; //get device default theme

  @override
  void onInit() {
    super.onInit();
    loadThemeFromStorage(); // เรียกใช้ฟังก์ชันโหลดสถานะธีมเมื่อ Controller ถูกสร้าง
  }

  void changeThemeBy(int index) {
    selectedTheme.value = index;
    selectTheme();
  }

  void selectTheme() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    if (selectedTheme.value == 1) {
      Get.changeTheme(darkTheme);
      selectedTheme.value = 1;
      await prefs.setInt('theme', 1);
      print('darkTheme');
    } else if (selectedTheme.value == 2) {
      Get.changeTheme(lightTheme);
      selectedTheme.value = 2;
      await prefs.setInt('theme', 2);
      print('lightTheme');
    } else if (selectedTheme.value == 0) {
      if (platformBrightness == Brightness.dark) {
        Get.changeTheme(darkTheme);
        selectedTheme.value = 0;
        await prefs.setInt('theme', 0);
        print('darkTheme');
      } else {
        Get.changeTheme(lightTheme);
        selectedTheme.value = 0;
        await prefs.setInt('theme', 0);
        print('lightTheme');
      }
    }
  }

  // ฟังก์ชันสำหรับสลับธีม
  // void toggleTheme() async {
  //  SharedPreferences prefs = await SharedPreferences.getInstance();
  //  if (isDarkTheme.value) {
  //    Get.changeTheme(lightTheme); // ใช้ธีมสว่าง
  //    isDarkTheme.value = false;
  //    await prefs.setBool('isDarkTheme', false); // บันทึกสถานะธีม
  //    print('lightTheme');
  //  } else {
  //    Get.changeTheme(darkTheme); // ใช้ธีมมืด
  //    isDarkTheme.value = true;
  //    await prefs.setBool('isDarkTheme', true); // บันทึกสถานะธีม
  //    print('darkTheme');
  //  }
  //  // isDarkTheme.value != isDarkTheme.value;
  // }

  // ฟังก์ชันโหลดสถานะธีมจาก LocalStorage
  void loadThemeFromStorage() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    selectedTheme.value = prefs.getInt('theme') ?? 0; // โหลดสถานะธีม
    if (selectedTheme.value == 1) {
      Get.changeTheme(darkTheme); // ใช้ธีมมืดถ้าเป็น 1
    } else if (selectedTheme.value == 2) {
      Get.changeTheme(lightTheme); // ใช้ธีมสว่างถ้าเป็น 2
    } else if (selectedTheme.value == 0) {
      if (platformBrightness == Brightness.dark) {
        Get.changeTheme(darkTheme);
      } else {
        Get.changeTheme(lightTheme);
      }
    }
  }
}

// กำหนดธีมสว่าง

final ThemeData lightTheme = ThemeData(
  primaryColor: AppColors.White,
  hintColor: AppColors.White,
  scaffoldBackgroundColor: Colors.white,
  textTheme: const TextTheme(
    bodyLarge: TextStyle(color: Colors.black),
    bodyMedium: TextStyle(color: Colors.black87),
  ),
  colorScheme: ColorScheme.light(
    primary: AppColors.White,
    onPrimary: AppColors.White,
    secondary: AppColors.bluePurple,
    onSecondary: AppColors.PurpleBlack,
    background: AppColors.LightGrayishWhite,
    onBackground: AppColors.bluePurple,
    shadow: AppColors.BrightYellow,
    scrim: AppColors.bluePurple,
    inversePrimary: AppColors.SeaGreen,
    onPrimaryFixed: AppColors.darkPurple.withOpacity(0.2),

    //สีที่ใช้ทั้ง2โหมด
    secondaryContainer: AppColors.SeaGreen, //สีเขียว
    onSecondaryContainer: AppColors.GrayishWhite, //สีเทา
    secondaryFixedDim: AppColors.lavenderPurple, //สีม่วง
    surfaceBright: AppColors.BrightYellow, //สีเหลือง
    secondaryFixed: AppColors.PastelPink, //สีชมพู
    tertiary: AppColors.bluePurple, //สีน้ำเงินม่วง
    tertiaryFixed: AppColors.LightGrayishWhite, //สีฃาว
    tertiaryFixedDim: AppColors.DullWhite, //สีขาวหม่น
    tertiaryContainer: AppColors.PigBloodRed, //สีแดง
    surface: AppColors.White,
    surfaceContainer: AppColors.BrightYellow,
    surfaceTint: AppColors.DullPurple,
    primaryContainer: AppColors.DullPurple,
    surfaceContainerHigh: AppColors.BrightYellow,
    surfaceContainerHighest: AppColors.BrightYellow,
    surfaceDim: AppColors.lightSeaGreen,
  ),
);

// กำหนดธีมมืด
final ThemeData darkTheme = ThemeData(
  primaryColor: AppColors.darkPurple,
  hintColor: AppColors.PurpleBlack,
  scaffoldBackgroundColor: AppColors.PurpleBlack,
  appBarTheme: AppBarTheme(
    color: Colors.black,
    iconTheme: IconThemeData(color: Colors.white),
  ),
  colorScheme: ColorScheme.dark(
    primary: AppColors.darkPurple,
    onPrimary: AppColors.PurpleBlack,
    secondary: AppColors.bluePurple,
    onSecondary: AppColors.DullWhite,
    background: AppColors.bluePurple,
    onBackground: AppColors.GrayishWhite,
    shadow: Colors.transparent,
    scrim: AppColors.LightGrayishWhite,
    inversePrimary: AppColors.bluePurple,
    onPrimaryFixed: AppColors.darkPurple,
    //สีที่ใช้ทั้ง2โหมด
    secondaryContainer: AppColors.SeaGreen, //สีเขียว
    onSecondaryContainer: AppColors.GrayishWhite, //สีเทา
    secondaryFixedDim: AppColors.lavenderPurple, //สีม่วง
    surfaceBright: AppColors.BrightYellow, //สีเหลือง
    secondaryFixed: AppColors.PastelPink, //สีชมพู
    tertiary: AppColors.bluePurple, //สีน้ำเงินม่วง
    tertiaryFixed: AppColors.LightGrayishWhite, //สีฃาว
    tertiaryFixedDim: AppColors.DullWhite, //สีขาวหม่น
    tertiaryContainer: AppColors.PigBloodRed, //สีแดง
    surface: AppColors.BluePurple,
    surfaceContainer: AppColors.DullPurple,
    surfaceTint: AppColors.Grayish,
    primaryContainer: AppColors.DullPurple,
    surfaceContainerHigh: AppColors.darkPurple,
    surfaceContainerHighest: AppColors.bluePurple,
    surfaceDim: AppColors.DarkPurplish,
  ),
);
