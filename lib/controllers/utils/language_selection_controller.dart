import 'dart:ui';

import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import '../../Models/language.dart';


class LanguageSelectionController extends GetxController {
  late GetStorage languageBox;
  late int selectedLanguageIndex;
  late Locale currentLocale;
  @override
  void onInit() {
    super.onInit();
    languageBox = GetStorage();
    selectedLanguageIndex = languageBox.read('languageIndex') ?? 1;
    currentLocale = Locale(listOfLanguages[selectedLanguageIndex].subtag);
    updateLanguage();
  }

  Future<void> selectLanguage(int index) async {
    selectedLanguageIndex = index;
    currentLocale = Locale(listOfLanguages[index].subtag);
    languageBox.write('languageIndex', selectedLanguageIndex);
    update(); // 🟢 เพิ่มบรรทัดนี้
  }

  
  Future<void> updateLanguage() async {
    Get.updateLocale(currentLocale);
    languageBox.write('languageIndex', selectedLanguageIndex);
  }
}
