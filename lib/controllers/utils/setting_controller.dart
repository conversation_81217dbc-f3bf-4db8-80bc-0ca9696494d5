import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'dart:io' as io;

import 'package:package_info_plus/package_info_plus.dart';
import 'package:permission_handler/permission_handler.dart';

class SettingController extends GetxController {
  RxString os = "".obs;
  RxString currentVersion = "".obs;
  RxString versionInStore = "".obs;
  RxString updateUrl = "".obs;
  RxBool closeHead = false.obs;

  @override
  void onInit() async {
    super.onInit();
    await Permission.notification.request();
    // await Permission.camera.request();
    await getOS();
    // await getVersion();
  }

  Future<dynamic> getOS() async {
    try {
      os.value = io.Platform.operatingSystem;
      update();
    } catch (e) {
      if (kDebugMode) {
        print("error getFeelWell =>$e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  changeCloseHead(input) {
    closeHead.value = input;
    update();
  }
}
