import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';

import '../internal/CewController/CewController.dart';

class AppWidget {
  static normalText(context, text, double? size, color, FontWeight weight) {
    return Text(
      text,
      textAlign: TextAlign.left,
      style: TextStyle(
        // fontFamily: 'SukhumvitSet-Text',
        fontFamily: 'Sukhumvit Set',
        fontSize: size,
        color: color,
        fontWeight: weight,
      ),
      softWrap: true, // ช่วยให้ข้อความขึ้นบรรทัดใหม่อัตโนมัติ
    );
  }

  static normalTextMs(context, text, double? size, color, FontWeight weight) {
    return Text(
      text,
      textAlign: TextAlign.left,
      style: TextStyle(
        // fontFamily: 'SukhumvitSet-Text',
        fontFamily: 'SF Pro Rounded',
        fontSize: size,
        color: color,
        fontWeight: weight,
      ),
      softWrap: true, // ช่วยให้ข้อความขึ้นบรรทัดใหม่อัตโนมัติ
    );
  }

  static normalTextCenter(
      context, text, double? size, color, FontWeight weight) {
    return Text(
      text,
      style: TextStyle(
        fontFamily: 'SukhumvitSet-Bold',
        fontSize: size,
        color: color,
        fontWeight: weight,
      ),
      textAlign: TextAlign.center, // จัดข้อความให้อยู่กึ่งกลางแนวนอน
    );
  }

  static normalText2(context, text, double? size, color, FontWeight weight) {
    return Text(
      text,
      textAlign: TextAlign.left,
      style: TextStyle(
        // fontFamily: 'SukhumvitSet-Text',
        fontFamily: 'SukhumvitSet-Bold',
        fontSize: size,
        color: color,
        fontWeight: weight,
      ),
      overflow: TextOverflow.ellipsis,
      maxLines: 2,
    );
  }

  static normaldouble(context, double, double? size, color, FontWeight weight) {
    return Text(
      double,
      textAlign: TextAlign.left,
      style: TextStyle(
        // fontFamily: 'SukhumvitSet-Text',
        fontFamily: 'SukhumvitSet-Bold',
        fontSize: size,
        color: color,
        fontWeight: weight,
      ),
    );
  }

  static overflowText(context, text, double? size, color, FontWeight weight) {
    return Text(
      text,
      textAlign: TextAlign.left,
      maxLines: 1,
      overflow: TextOverflow.ellipsis, // Ensure overflow is visible
      softWrap: true, // Ensure text wraps automatically
      style: TextStyle(
        // fontFamily: 'SukhumvitSet-Text',
        fontFamily: 'SukhumvitSet-Bold',
        fontSize: size,
        color: color,
        fontWeight: weight,
      ),
    );
  }

  static spacenormalText(context, text, double? size, color, FontWeight weight,
      {required double letterSpacing}) {
    return Text(
      text,
      textAlign: TextAlign.left,
      style: TextStyle(
          fontFamily: 'SukhumvitSet-Text',
          fontSize: size,
          color: color,
          fontWeight: weight,
          letterSpacing: letterSpacing),
    );
  }

  static normalTextRX(
      context, RxString rxString, double? size, color, FontWeight weight) {
    return Obx(
      () => Text(
        rxString.value, // Accessing the actual string value from RxString
        softWrap: true,
        textAlign: TextAlign.right,
        overflow: TextOverflow.ellipsis,
        style: TextStyle(
          fontFamily: 'SukhumvitSet-Text',
          fontSize: size,
          color: color,
          fontWeight: weight,
        ),
      ),
    );
  }

//ลงมาบรรทัดใหม่อัตโนมัติ
  static normalTextOverflow(
      context, RxString rxString, double? size, color, FontWeight weight) {
    return Obx(
      () => Text(
        rxString.value, // Accessing the actual string value from RxString
        softWrap: true,
        textAlign: TextAlign.left,
        overflow: TextOverflow.ellipsis, // หรือใช้ TextOverflow.fade
        maxLines: 1,
        style: TextStyle(
          fontFamily: 'SukhumvitSet-Text',
          fontSize: size,
          color: color,
          fontWeight: weight,
        ),
      ),
    );
  }

  static normalTextDynamic(
      context, str, double? size, color, FontWeight weight) {
    return Text(
      str, // Accessing the actual string value from RxString
      textAlign: TextAlign.left,
      style: TextStyle(
        fontFamily: 'SukhumvitSet-Text',
        fontSize: size,
        color: color,
        fontWeight: weight,
      ),
    );
  }

  static normalTextS(context, text, double? size, color, FontWeight weight) {
    return Text(
      text,
      textAlign: TextAlign.left,
      style: TextStyle(
        fontFamily: 'Sukhumvit Set',
        fontSize: size,
        color: color,
        fontWeight: weight,
        shadows: <Shadow>[
          Shadow(
            offset: const Offset(0, 1),
            blurRadius: 5.0,
            color: const Color(0xFF000000).withOpacity(0.1),
          ),
        ],
      ),
    );
  }

  static boldText(
      context, String text, double? size, color, FontWeight weight) {
    return Text(text,
        textAlign: TextAlign.left,
        style: TextStyle(
          fontFamily: 'Prompt-Medium',
          fontSize: size,
          color: color,
          fontWeight: weight,
          // letterSpacing: 0.2,
        ));
  }

  static boldTextS(context, text, double? size, color, FontWeight weight) {
    return Text(text,
        textAlign: TextAlign.left,
        style: TextStyle(
          fontFamily: 'Sukhumvit Set',
          fontSize: size,
          color: color,
          fontWeight: weight,
          // letterSpacing: 0.2,
          shadows: <Shadow>[
            Shadow(
              offset: const Offset(0, 1),
              blurRadius: 5.0,
              color: const Color(0xFF000000).withOpacity(0.1),
            ),
          ],
        ));
  }

  static InputnormalText(context, double? _width, double? _height, headText,
      double? size, color, weight, controller) {
    return Stack(
      children: [
        SizedBox(
          width: _width,
          height: _height,
          child: Text(
            headText,
            textAlign: TextAlign.left,
            style: TextStyle(
              fontFamily: 'Sukhumvit Set',
              fontSize: size,
              color: color,
              fontWeight: weight,
              letterSpacing: 0.2,
            ),
          ),
        ),
        SizedBox(
          width: _width,
          height: _height,
          child: TextField(
            controller: controller,
            style: TextStyle(
              fontFamily: 'Prompt',
              fontSize: size,
              color: const Color(0xFF707070),
              fontWeight: weight,
              letterSpacing: 0.2,
            ),
            decoration: InputDecoration(
              isDense: true,
              contentPadding: EdgeInsets.fromLTRB(0, 0.025.sh, 0, 0),
              border: InputBorder.none,
              hintText: 'ว่าง',
              hintStyle: TextStyle(
                fontFamily: 'Prompt',
                color: const Color(0xFF707070),
                fontSize: size,
                fontWeight: weight,
                letterSpacing: 0.2,
              ),
            ),
          ),
        ),
      ],
    );
  }

  static showDialogPage(context, page) {
    showDialog(
      barrierDismissible: true,
      context: context,
      useSafeArea: false,
      builder: (_) => page,
    );
  }

  static showDialogPageSlide(context, page) {
    showGeneralDialog(
      barrierLabel: "showGeneralDialog",
      barrierDismissible: true,
      barrierColor: Colors.black.withOpacity(0.6),
      transitionDuration: const Duration(milliseconds: 300),
      context: context,
      pageBuilder: (context, _, __) {
        return page;
      },
      transitionBuilder: (_, animation1, __, child) {
        return SlideTransition(
          position: Tween(
            begin: const Offset(0, 1),
            end: const Offset(0, 0),
          ).animate(animation1),
          child: child,
        );
      },
    );
  }

  static selectImage(BuildContext context) {
    CewController cewCtr = Get.find<CewController>();
    return showModalBottomSheet(
        context: context,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        builder: (BuildContext bc) {
          return Container(
            padding: EdgeInsets.symmetric(vertical: 24.h, horizontal: 16.w),
            height: 250.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                ElevatedButton.icon(
                  icon: Icon(Icons.photo_library,
                      color: Theme.of(context).colorScheme.onSecondary),
                  label: Text(
                    'อัพโหลดรูปจากเครื่อง'.tr,
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: Theme.of(context).colorScheme.onSecondary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor:
                        Theme.of(context).colorScheme.primary.withOpacity(0.3),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                    padding: EdgeInsets.symmetric(vertical: 14),
                  ),
                  onPressed: () async {
                    await cewCtr.getImageDirectory(context);
                    Navigator.pop(context);
                  },
                ),
                SizedBox(height: 12),
                ElevatedButton.icon(
                  icon: Icon(Icons.add_a_photo,
                      color: Theme.of(context).colorScheme.onSecondary),
                  label: Text(
                    'ถ่ายรูป'.tr,
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: Theme.of(context).colorScheme.onSecondary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor:
                        Theme.of(context).colorScheme.primary.withOpacity(0.3),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                    padding: EdgeInsets.symmetric(vertical: 14),
                  ),
                  onPressed: () async {
                    await cewCtr.getImageCamera(context);
                    Navigator.pop(context);
                  },
                ),
                SizedBox(height: 24),
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Text(
                    "ยกเลิก",
                    style: TextStyle(
                      color: Colors.black54,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          );
        });
  }

  static Future<void> _checkAndRequestStoragePermission() async {
    PermissionStatus status = await Permission.storage.request();
    if (status.isGranted) {
    } else {
      print("Storage permission denied");
    }
  }
}
