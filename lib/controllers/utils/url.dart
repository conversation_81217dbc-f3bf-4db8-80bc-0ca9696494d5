import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:mapp_ms24/configs/app_config_service.dart';

class AppUrl extends GetxController {
  AppConfigService appConfig = Get.find<AppConfigService>();
  static String apiCenterLink = "";

  @override
  void onInit() async {
    if (kIsWeb) {
      apiCenterLink = 'https://agilesoftgroup.com/MS24_uat';  // prod environment for web
    } else {
    apiCenterLink = appConfig.endpointAPI;  // ตรวจสอบว่าตัวแปรนี้ได้ค่าถูกต้อง
    if (apiCenterLink.isEmpty) {
      print("apiCenterLink is not set correctly!");
    } else {
    }
  }
  }
  static String apiCF = "https://agilesoftgroup.com/PMS";
  static String apiLink =
      // 'https://4hqtnu10id.execute-api.ap-southeast-1.amazonaws.com/latest'; //api ที่ทำเอง อยู่บน lambda
      'https://mhmo3nnbr5.execute-api.ap-southeast-1.amazonaws.com/latest'; //api ที่ทำเอง อยู่บน lambda

  // static String apiLink = 'http://172.16.1.166:5000'; // local


  static String sentlinenotify = "$apiCenterLink/sentlinenotify";

  static String checkLeaveRuleExecute = "$apiCenterLink/checkLeaveRuleExecute";


  static String uploadS3_voice =
      'https://une5sgp9aj.execute-api.ap-southeast-1.amazonaws.com/dev/api/v1/uploadS3_Voice'; // uploadS3
  static String uploadS3 =
      'https://une5sgp9aj.execute-api.ap-southeast-1.amazonaws.com/dev/api/v1/uploadS3_Center'; // uploadS3
  static String OldsendTelegramPKG =
      'https://dockerapi-ci.prachakij.com/jwtauth'; // OldsendTelegramPKG
  static String likewallrt =
      'https://new.likepoint.io/getBalanceByphoneNumber'; // likewallrt
  static String Addresslikewallrt =
      'https://new.likepoint.io/getAddressByphoneNumber'; // หา Address likewallrt
  static String updateToBCT =
      'https://us-central1-agslearn.cloudfunctions.net/ApiAppMS24'; // updateToBCT

  static String sendToBCT =
      'https://us-central1-agslearn.cloudfunctions.net/ApiAppMS24';
  static String member = "$apiCenterLink/Loaddetailmember";
  static String login = "$apiCenterLink/loginAnonymous";
  static String ShareCU = "$apiCenterLink/search-ShareCU";
  static String Saving = "$apiCenterLink/search-Savingmoney";
  static String GTC = "$apiCenterLink/search-GTC";
  static String insertfeedback = "$apiCenterLink/insertfeedback";
  static String BV = "$apiCenterLink/search-GTCbv";
  static String MCU = "$apiCenterLink/search-getMCU";
  static String HoldMCU = "$apiCenterLink/search-getHoldMCU";
  static String getRewardMCU = "$apiCenterLink/search-getRewardMCU";
  static String getRewardHoldMCU = "$apiCenterLink/getRewardHoldMCU";
  static String checkPFS = "$apiCenterLink/checkPFS";
  static String checkSalary = "$apiCenterLink/checkSalary";
  static String purpose = "$apiCenterLink/loadPurpose";
  static String purposeTeam = "$apiCenterLink/loadPurposeTeam";
  static String approveName = "$apiCenterLink/search-ApproveName";
  static String Loaddetailbyphone = "$apiCenterLink/Loaddetailbyphone";
  static String typeLeave = "$apiCenterLink/search-TypeLeave";
  static String loadnotificationAll = "$apiCenterLink/loadnotificationAll"; // เอาไว้ลบที่อ่านแล้ว
  static String loadNotification = "$apiCenterLink/Notification";
  static String notificationStatus = "$apiCenterLink/NotificationStatus";//ดึงข้อมูลสถานะแจ้งเตือน
  static String updateNotiStatus = "$apiCenterLink/updateNotiStatus";
  static String processCew = "$apiCenterLink/processCew";
  static String checkclaim = "$apiCenterLink/checkclaim";
  static String tranferclaimlike = "$apiCenterLink/tranferclaimlike";
  static String insertTimeprocess = "$apiCenterLink/insertTimeprocess";
  static String absenceList = "$apiCenterLink/absenceList";
  static String searchBU = "$apiCenterLink/searchBU";
  static String createAbsence = "$apiCenterLink/createAbsence";
  static String checkWorkdays = "$apiCenterLink/CheckWorkdays";
  static String checkDate = "$apiCenterLink/CheckDate";
  static String healthBMI = "$apiCenterLink/healthBMI";
  static String sendCEW = "$apiCenterLink/sendCEW";
  static String CheckTime = "$apiCenterLink/searchCheckIn";
  static String Eexercise = "$apiCenterLink/Eexercise";
  static String LostDay = "$apiCenterLink/LostDay";
  static String Proportion = "$apiCenterLink/proportion";
  static String Calculate = "$apiCenterLink/Calculate";
  static String BirthDay = "$apiCenterLink/BirthDay";
  static String searchTeamCew = "$apiCenterLink/searchTeamCew";
  static String processQrcode = "$apiCenterLink/processQrcodeV2";
  static String getTime = "$apiCenterLink/get-time";
  static String insertTokenNotify = "$apiCenterLink/insertTokenNotify";
  static String searchTokenNotify = "$apiCenterLink/searchTokenNotify";
  static String searchCheckIn = "$apiCenterLink/searchCheckIn";
  static String searchPerson = "$apiCenterLink/searchPerson";
  static String searchTimeCard = "$apiCenterLink/searchTimeCard";
  static String checkOut = "$apiCenterLink/check-out";
  static String searchTokenBitrix = "$apiCenterLink/search-TokenBitrix";
  static String loaddetailmember = "$apiCenterLink/Loaddetailmember";
  static String searchDropdown = "$apiCenterLink/searchDropdown";
  static String loaddetailmemberall = "$apiCenterLink/detailmemberall";
  static String Typescew = "$apiCenterLink/Typescew";
  static String withdrawal = "$apiCenterLink/search-benefits-resilient";
  static String welfare = "$apiCenterLink/Welfare-balance";
  static String GetServiceTG = "$apiCenterLink/GetServiceTG";
  static String CreatServiceTG = "$apiCenterLink/CreatServiceTG";
  static String searchNitrosign = "$apiCenterLink/searchNitrosign";
  static String updateLinkPdfNDA = "$apiCenterLink/updateLinkPdfNDA";
  static String WelfareRecords = "$apiCenterLink/WelfareRecords";
  static String TransferActManual = "$apiCenterLink/UpdateTransferActManual";
  static String updateStatusLike = "$apiCenterLink/updateStatusLike";
  static String OKRinformation = "$apiCenterLink/OKRinformation";
  static String GTCguarantee = "$apiCenterLink/search-GTCguarantee";
  static String searchDebtV2 = "$apiCenterLink/searchDebtV2";
  static String ConfigApp = "$apiCenterLink/searchConfigApp";
  static String healthYear = "$apiCenterLink/search-healthYear";
  static String sendNotify = "$apiCenterLink/sendNotify";
  static String searchhealth = "$apiCenterLink/search-healthYear";
  static String searchTokenLine = "$apiCenterLink/searchTokenLine";
  static String uploadRecord = '$apiCenterLink/uploadRecordV1';
  static String GetRecordHisV1 ='$apiCenterLink/GetRecordHisV1';
  static String question = "$apiCenterLink/questions";


  static String SignatureNDA =
      "https://mhmo3nnbr5.execute-api.ap-southeast-1.amazonaws.com/latest/img_Signature_NDA";
  static String updateLinkPdfNDASuccess =
      "$apiCenterLink/updateLinkPdfNDASuccess";
  static String searchNDAPPP7 = "$apiCenterLink/searchNDAPPP7";
  static String guaranteemoney = "$apiCenterLink/search-Guaranteemoney";
  static String RstID = "$apiCenterLink/search-RstID";
  static String sumDate = "$apiCenterLink/sumDate";
  static String tokenNotifyById = "$apiCenterLink/search-TokenNotifyById";
  static String  checkTWOFA= "$apiCenterLink/checkTWOFA";
  static String uploadRecrod = "$apiCenterLink/uploadRecordV1";
  // static String insertTimeprocess = "$apiCenterLink/MS24/insertTimeprocess";

  ///API LIKE
  static String checkLIKECREDIT = "https://apicredit.prachakij.com/userBalance";
  static String getAddress = "https://new.likepoint.io/getAddressByphoneNumber";

  ///API Door
  static String openDoorIQ =
      "https://pkg-physical-api.agilesoftgroup.com/openDoor";
  static String openDoorPMG = "https://pmgsecurity.agilesoftgroup.com/openDoor";

  static String getProfileById = '$apiLink/getProfileById';

  static String saveActivity = '$apiLink/saveActivity';

  static String saveLogErrorMApp = '$apiLink/saveLogErrorMapp';



  static String checkCanGetLikePOI = '$apiLink/checkCanGetLikePOI';
  static String insertTransactionPOI = '$apiLink/insertTransactionPOI';
  static String payLikeLockPMSPoint = "$apiCF/poi/payLike";

}
