import 'dart:convert';
import 'dart:io';

import 'package:ags_authrest2/ags_authrest.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:http/io_client.dart';
import 'package:mapp_ms24/controllers/internal/securestorage_controller.dart';
import 'package:mapp_ms24/controllers/utils/alert.dart';
import 'package:mapp_ms24/controllers/utils/loader.dart';
import 'package:mapp_ms24/controllers/utils/url.dart';
import 'package:http/http.dart' as http;

class AppApi {
  static Future<SecurityContext> get globalContext async {
    // Note: Not allowed to load the same certificate
    final sslCert1 = await rootBundle.load('assets/cert/certificate.pem');
    // final sslCert2 = await rootBundle.load('assets/cert/certificate2.pem');

    SecurityContext sc = SecurityContext(withTrustedRoots: false);
    sc.setTrustedCertificatesBytes(sslCert1.buffer.asInt8List());
    // sc.setTrustedCertificatesBytes(sslCert2.buffer.asInt8List());
    return sc;
  }

  static callDocker(
    String url,
    String path,
    String port,
    Map jsonMap,
  ) async {
    // สร้าง Basic Authentication Header
    String username = 'ags-ci';
    String password = 'cdWmW,o5HlNPd}gzEZlkbzCJ(zDtP)';
    String basicAuth =
        'Basic ' + base64Encode(utf8.encode('$username:$password'));

    if (kIsWeb) {
      // ใช้ http package สำหรับ Web
      try {
        final response = await http.post(
          Uri.parse(url),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': basicAuth,
            'path': path,
            'port': port,
          },
          body: jsonEncode(jsonMap),
        );

        if (response.statusCode == 200) {
          return json.decode(response.body); // แปลงผลลัพธ์เป็น JSON
        } else {
          throw Exception(
              'Failed to call Docker from Web: ${response.statusCode}');
        }
      } catch (e) {
        throw e;
      }
    } else {
      // ใช้ HttpClient สำหรับ Mobile
      try {
        HttpClient httpClient = HttpClient();
        HttpClientRequest request = await httpClient.postUrl(Uri.parse(url));
        request.headers.set('Content-Type', 'application/json');
        request.headers.set('Authorization', basicAuth);
        request.headers.set('path', path);
        request.headers.set('port', port);
        request.add(utf8.encode(json.encode(jsonMap)));

        HttpClientResponse response = await request.close();

        if (response.statusCode == 200) {
          String reply = await response.transform(utf8.decoder).join();
          httpClient.close();
          return json.decode(reply);
        } else {
          httpClient.close();
          throw Exception(
              'Failed to call Docker from Mobile: ${response.statusCode}');
        }
      } catch (e) {
        throw e;
      }
    }
  }

  static post(String url, Map jsonMap) async {
    final SecureStorage secureStorage = SecureStorage();
    var token = await secureStorage.readSecureData("accessToken");
    var urlApi = Uri.parse(url);

    Map<String, String> requestHeaders = {
      'content-type': 'application/json',
      'x-api-key': "FIF12OExFj6uoE83hskYI2t03o8WSVF53H3vu9ky",
      'Authorization': token ?? "none",
    };

    var response = await http.post(urlApi,
        headers: requestHeaders, body: jsonEncode(jsonMap));
    var res = json.decode(response.body);
    return res;
  }

  static postNormal(String url, Map jsonMap) async {
    var urlApi = Uri.parse(url);
    Map<String, String> requestHeaders = {
      'Content-Type': 'application/json',
      'x-api-key': 'FIF12OExFj6uoE83hskYI2t03o8WSVF53H3vu9ky'
    };
    var response = await http.post(urlApi,
        headers: requestHeaders, body: jsonEncode(jsonMap));

    var res = json.decode(response.body);
    return res;
  }

  static postHttps(String uri, Map data) async {
    var url = Uri.parse(uri);
    Map<String, String> requestHeaders = {'content-type': 'application/json'};
    var response =
        await http.post(url, headers: requestHeaders, body: jsonEncode(data));
    var dataRes = json.decode(response.body);
    return dataRes;
  }

  static Future<dynamic> get(String url) async {
    final SecureStorage secureStorage = SecureStorage();
    var token = await secureStorage.readSecureData("accessToken");
    var urlApi = Uri.parse(url);
    Map<String, String> requestHeaders = {
      'content-type': 'application/json',
      'x-api-key': "FIF12OExFj6uoE83hskYI2t03o8WSVF53H3vu9ky",
      'Authorization': token ?? "none",
    };
    var response = await http.get(urlApi, headers: requestHeaders);
    if (response.statusCode == 200) {
      var res = json.decode(response.body);
      return res;
    } else {
      throw Exception('Failed to load data');
    }
  }

  static Future<dynamic> getLikepoint(String url) async {
    var urlApi = Uri.parse(url);
    Map<String, String> requestHeaders = {
      'content-type': 'application/json',
      'x-api-key': "841JEGYD93NOCDXWKx6ra8NMDw00Pzvu",
    };
    var response = await http.get(urlApi, headers: requestHeaders);
    if (response.statusCode == 200) {
      var res = json.decode(response.body);
      return res;
    } else {
      throw Exception('Failed to load data');
    }
  }

  static Future<dynamic> apiGetRequest(String url) async {
    if (kIsWeb) {
      try {
        final response = await http.get(
          Uri.parse(url),
          headers: {'Content-Type': 'application/json'},
        );

        if (response.statusCode == 200) {
          return json.decode(response.body);
        } else {
          throw Exception(
              'Failed to load data from Web: ${response.statusCode}');
        }
      } catch (e) {
        throw e;
      }
    } else {
      try {
        HttpClient httpClient = HttpClient();
        HttpClientRequest request = await httpClient.getUrl(Uri.parse(url));
        request.headers.set('Content-Type', 'application/json');

        HttpClientResponse response = await request.close();

        if (response.statusCode == 200) {
          String reply = await response.transform(utf8.decoder).join();
          httpClient.close();
          return json.decode(reply);
        } else {
          httpClient.close();
          throw Exception(
              'Failed to load data from Mobile: ${response.statusCode}');
        }
      } catch (e) {
        throw e;
      }
    }
  }

  static Future<dynamic> callAPIjwt(String method, String url, Map data) async {
    try {
      var auth = Ags_restauth();
      auth.SECERT_JWT = 'T8zZ6C5n@wfRVrk9';
      auth.R_USER = 'MAPP_MS24';
      var headers = {
        'Authorization': auth.genTokenEncryp(),
        'Content-Type': 'application/json'
      };
      var body = json.encode(data);
      var request = http.Request(method, Uri.parse(url));
      request.body = json.encode(auth.encrypbody(body));
      request.headers.addAll(headers);
      http.StreamedResponse response = await request.send();

      var dataRaw = await response.stream.bytesToString();
      var responseData = json.decode(dataRaw);

      return responseData;
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      return false;
    }
  }

  static Future<dynamic> callAPIjwt_unencry(
      String method, String url, Map data) async {
    try {
      var auth = Ags_restauth();
      auth.SECERT_JWT = 'T8zZ6C5n@wfRVrk9';
      auth.R_USER = 'MAPP_MS24';

      String token = auth.genTokenEncryp();

      var headers = {
        'Authorization': token,
        'Content-Type': 'application/json'
      };
      var body = json.encode(data);

      var request = http.Request(method, Uri.parse(url));
      request.body = body;
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();
      String dataRaw = await response.stream.bytesToString();
      // แปลงข้อมูลตอบกลับเป็น JSON
      var responseData = json.decode(dataRaw);
      return responseData;
    } catch (e) {
      if (kDebugMode) {
      }
      return false;
    }
  }

  static Future<dynamic> getForWeb(String url) async {
    var urlApi = Uri.parse(url);
    Map<String, String> requestHeaders = {
      'content-type': 'application/json',
    };
    var response = await http.get(urlApi, headers: requestHeaders);
    if (response.statusCode == 200) {
      var res = json.decode(response.body);
      return res;
    } else {
      throw Exception('Failed to load data');
    }
  }

  static Future<String> apiRequest2(String url, List jsonMap) async {
    if (kIsWeb) {
      try {
        final response = await http.post(
          Uri.parse(url),
          headers: {'Content-Type': 'application/json'},
          body: jsonEncode(jsonMap),
        );

        if (response.statusCode == 200) {
          return response.body;
        } else {
          throw Exception(
              'Failed to load data from Web: ${response.statusCode}');
        }
      } catch (e) {
        throw e;
      }
    } else {
      try {
        HttpClient httpClient = HttpClient();
        HttpClientRequest request = await httpClient.postUrl(Uri.parse(url));
        request.headers.set('Content-Type', 'application/json');
        request.add(utf8.encode(json.encode(jsonMap)));

        HttpClientResponse response = await request.close();

        if (response.statusCode == 200) {
          String reply = await response.transform(utf8.decoder).join();
          httpClient.close();
          return reply;
        } else {
          httpClient.close();
          throw Exception(
              'Failed to load data from Mobile: ${response.statusCode}');
        }
      } catch (e) {
        throw e;
      }
    }
  }
  static Future<dynamic> apiRequest(String url,  jsonMap) async {
    try {
      if (kIsWeb) {
        final response = await http.post(
          Uri.parse(url),
          headers: {
            'Content-Type': 'application/json',
          },
          body: jsonEncode(jsonMap),
        );

        if (response.statusCode == 200) {
          final responseBody = jsonDecode(response.body);

          // ✅ ตรวจว่าเป็น List หรือ Map
          return responseBody;
        } else {
          throw Exception('Web Error: ${response.statusCode}');
        }
      } else {
        HttpClient httpClient = HttpClient();
        HttpClientRequest request = await httpClient.postUrl(Uri.parse(url));
        request.headers.set('Content-Type', 'application/json');
        request.headers.set('x-api-key', 'hjwsduripfksefghtrwdmngjtieowpsskfgtriuyt');
        request.add(utf8.encode(json.encode(jsonMap)));

        HttpClientResponse response = await request.close();

        if (response.statusCode == 200) {
          String reply = await response.transform(utf8.decoder).join();
          httpClient.close();
          return jsonDecode(reply);
        } else {
          httpClient.close();
          throw Exception('Mobile Error: ${response.statusCode}');
        }
      }
    } catch (e) {
      print("❌ apiRequest error: $e");
      rethrow;
    }
  }


  static Future<String> apiRequestweb(String url, Map jsonMap) async {
    final response = await http.post(
      Uri.parse(url),
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': 'hjwsduripfksefghtrwdmngjtieowpsskfgtriuyt',
      },
      body: jsonEncode(jsonMap),
    );

    if (response.statusCode == 200) {
      return response.body;
    } else {
      throw Exception('Failed to load data');
    }
  }

  static Future<dynamic> postLikePoint(String url, Map jsonMap) async {
    try {
      final urlApi = Uri.parse(url);
      final requestHeaders = {
        'Content-Type': 'application/json',
        'x-api-key': "841JEGYD93NOCDXWKx6ra8NMDw00Pzvu",
      };

      final response = await http.post(
        urlApi,
        headers: requestHeaders,
        body: jsonEncode(jsonMap),
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      // ป้องกัน error เฉพาะ Web ที่ใช้ Platform หรือ IO
      rethrow;
    }
  }
}
