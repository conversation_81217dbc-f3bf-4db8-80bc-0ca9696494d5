import 'dart:async';
import 'dart:convert';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:get/get.dart';
import 'package:mapp_ms24/controllers/utils/connect.dart';
import 'package:mapp_ms24/controllers/utils/url.dart';
import 'package:mobile_scanner/mobile_scanner.dart' as mobile_scanner;
import 'package:geolocator/geolocator.dart';

import 'internal/Profile/profileController.dart';

class verify extends GetxController {
  ProfileController profile = Get.find<ProfileController>();
  var scannedData = ''.obs;
  var scannedBody = ''.obs;
  var serviceList = <Map<String, dynamic>>[].obs;
  var isScanning = false.obs;
  var hasScanned = false.obs;
  var emp_code = ''.obs;
  var customerName = ''.obs;
  var phoneNumber = ''.obs;
  var selectedService = 'PKG Verification'.obs;
  final scannerController = mobile_scanner.MobileScannerController();
  var employee_data = <Map<String, dynamic>>[].obs;
  var isLoading = false.obs;
  var scanMode = "work".obs; // ✅ ค่าเริ่มต้นเป็น 'ยืนยันการทำงาน'
  var isProcessing = false.obs;
  final String customerApiUrl =
      'http://0.0.0.0:8787/MS24_uat/Loaddetailbyphone';
  final String telegramApiUrl = "http://your-server-ip:3000/notify";
  final String? employeeId = Get.arguments;

  var Bu_list = <String>[].obs; // ✅ ต้องเป็น List<String>
  var selectedBU = ''.obs; // ✅ เพิ่มตัวแปรเก็บค่าที่
  Timer? _timer;
  RxBool isSharing = false.obs; // ใช้ GetX ในการจัดการสถานะ
  StreamSubscription<Position>? _positionStream;

  final String webhookUrl =
      "https://n8n-deploy.agilesoftgroup.com/webhook-test/live-location"; // URL Webhook ของ n8n

  final TextEditingController customerNameController = TextEditingController();
  @override
  void onInit() {
    super.onInit();
    fetchBUData();
    fetchEmployeeData();
    fetchService();

    _determinePosition();
    resetState(); // รีเซ็ตค่าต่างๆ
  }

  @override
  void onClose() {
    scannerController.dispose();
    super.onClose();
  }

  void resetState() {
    isScanning.value = false;
  }

  void startScanning() {
    if (!isScanning.value) {
      isScanning.value = true;
      scannerController.start();
    }
  }

  void stopScanning() {
    if (isScanning.value) {
      isScanning.value = false;
      scannerController.stop();
    }
  }

  Future<void> fetchEmployeeData() async {
    try {
      var data = {
        'phone': profile.responseMember?.phone_like ?? '',
        'bu': profile.responseMember?.company_management ?? ''
      };

      isLoading.value = true;

      final response =
          await AppApi.callAPIjwt("POST", AppUrl.Loaddetailbyphone, data);

      if (response is Map<String, dynamic> &&
          response.containsKey('status') &&
          response['status'].toString() == '200') {
        var decodedResponse = jsonEncode(response); // ✅ ใช้ jsonEncode แทน
        var dataRes = jsonDecode(decodedResponse);

        if (dataRes is Map &&
            dataRes.containsKey('result') &&
            dataRes['result'] is List) {
          employee_data.assignAll(
            dataRes['result']
                .map<Map<String, dynamic>>((emp) => {
                      'id': profile.responseMember?.id ?? '',
                      'division_name': emp['division_name'] ?? '',
                      'name_th': emp['name_th'] ?? '',
                      'surname_th': emp['surname_th'] ?? '',
                      'nickname': emp['nickname'] ?? '',
                      'employment_type': emp['employment_type'] ?? '',
                      'branch_ctt': emp['branch_ctt'] ?? '',
                      'phone_like': profile.responseMember?.phone_like ?? '',
                      'url_photo_card_new': emp['url_photo_card_new'] ?? ''
                    })
                .toList(),
          );
        } else {
          employee_data.clear();
        }
      } else {
        print("❌ API Request Failed with Status Code: ${response['status']}");
      }
    } catch (e, stacktrace) {
      print(stacktrace);
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> fetchBUData() async {
    try {
      isLoading.value = true;

      final response = await AppApi.callAPIjwt("POST", AppUrl.searchBU, {});

      if (response['status'].toString() == '200') {
        var decodedResponse = jsonEncode(response);
        var dataRes = jsonDecode(decodedResponse);

        if (dataRes is Map &&
            dataRes.containsKey('result') &&
            dataRes['result'] is List) {
          List<String> buNames = dataRes['result']
              .map<String>((item) => item['company_management'].toString())
              .toList();

          Bu_list.assignAll(buNames);

          if (Bu_list.isNotEmpty) {
            selectedBU.value = Bu_list.first;
          }
        } else {
          Bu_list.clear();
        }
      } else {
        print("❌ API Request Failed with Status Code: ${response['status']}");
      }
    } catch (e, stacktrace) {
      print(stacktrace);
    } finally {
      isLoading.value = false;
    }
  }

  String convertPhoneToInternationalFormat(String phone) {
    if (phone.startsWith("0")) {
      return "+66${phone.substring(1)}";
    }
    return phone;
  }

  Future<Position> _determinePosition() async {
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      throw Exception('❌ Location services are disabled.');
    }

    // ✅ 2. ตรวจสอบสิทธิ์การเข้าถึง GPS
    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        throw Exception('❌ Location permissions are denied.');
      }
    }

    if (permission == LocationPermission.deniedForever) {
      throw Exception('❌ Location permissions are permanently denied.');
    }

    // ✅ 3. ดึงพิกัดปัจจุบัน
    return await Geolocator.getCurrentPosition(
      desiredAccuracy: LocationAccuracy.high,
    );
  }

  Future<void> fetchService() async {
    try {
      var data = {"emp_code": profile.responseMember?.id ?? ''};

      isLoading.value = true; // ✅ เริ่มโหลดข้อมูล

      final response =
          await AppApi.callAPIjwt("POST", AppUrl.GetServiceTG, data);

      if (int.tryParse(response['status'].toString()) == 200) {
        if (response.containsKey('result')) {
          var dataRes = response['result'];

          if (dataRes is Map<String, dynamic> &&
              dataRes.containsKey('result') &&
              dataRes['result'] is List) {
            serviceList
                .assignAll(List<Map<String, dynamic>>.from(dataRes['result']));

            // ✅ ตรวจสอบ `max_running`
            scannedData.value = (dataRes['max_running'] ?? 0).toString();
          } else {
            serviceList.clear();
          }
        } else {
          print("⚠️ Response missing 'result' key.");
        }
      } else {
        print("❌ API Request Failed with Status Code: ${response['status']}");
      }
    } catch (e) {
      print("❌ Error fetching Service data: $e");
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> fetchCustomerData(String phone, String bu) async {
    if (phone.isEmpty) {
      Get.snackbar("แจ้งเตือน", "กรุณากรอกเบอร์โทรศัพท์",
          snackPosition: SnackPosition.BOTTOM);
      return;
    }

    String formattedPhone = convertPhoneToInternationalFormat(phone);

    try {
      var data = {"phone": formattedPhone, "bu": bu};

      final response =
          await AppApi.callAPIjwt("POST", AppUrl.Loaddetailbyphone, data);

      if (response is Map<String, dynamic> &&
          response.containsKey('status') &&
          response['status'].toString() == '200') {
        var responseData = response;

        if (responseData['result'] is List &&
            responseData['result'].isNotEmpty) {
          var customer = responseData['result'][0];

          customerName.value =
              "${customer['name_th'] ?? ''} ${customer['surname_th'] ?? ''}"
                  .trim();
          customerNameController.text = customerName.value;

          Get.snackbar("สำเร็จ", "โหลดข้อมูลลูกค้าสำเร็จ",
              snackPosition: SnackPosition.BOTTOM);
        } else {
          Get.snackbar("แจ้งเตือน", "ไม่พบข้อมูลลูกค้า",
              snackPosition: SnackPosition.BOTTOM);
        }
      } else {
        Get.snackbar("แจ้งเตือน", "ไม่พบข้อมูลลูกค้า",
            snackPosition: SnackPosition.BOTTOM);
      }
    } catch (e) {
      Get.snackbar("เกิดข้อผิดพลาด", "โหลดข้อมูลล้มเหลว",
          snackPosition: SnackPosition.BOTTOM);
    }
  }

  Future<void> createServiceTG(chatId, messageThreadId) async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        return;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        return;
      }

      // ✅ ดึงพิกัดปัจจุบัน
      Position position;
      try {
        position = await Geolocator.getCurrentPosition(
            desiredAccuracy: LocationAccuracy.high);
      } catch (e) {
        return;
      }

      // ✅ ป้องกัน `null`
      final serviceData = {
        "case_id": scannedData.value ?? '',
        "phone_number": phoneNumber.value ?? '',
        "customer_name": customerName.value ?? '',
        "bu": selectedBU.value ?? '',
        "emp_code": profile.responseMember?.id ?? '',
        "emp_name":
            "${profile.responseMember?.name_th ?? ''} ${profile.responseMember?.surname_th ?? ''}"
                .trim(),
        "selected_service": selectedService.value ?? '',
        "qr_scanned": false,
        "scanned_at": DateTime.now().toIso8601String(),
        "message_thread_id": messageThreadId ?? '',
        "chat_id": chatId ?? '',
        "latitude": position.latitude, // ✅ ใช้ค่าจาก GPS
        "longitude": position.longitude, // ✅ ใช้ค่าจาก GPS
        "created_at": DateTime.now().toIso8601String(),
      };

      final response =
          await AppApi.callAPIjwt("POST", AppUrl.CreatServiceTG, serviceData);

      if (response is Map<String, dynamic> &&
          response.containsKey('status') &&
          response['status'] == 200) {
      } else {
        print(
            "❌ บันทึกข้อมูลไปที่ CreatServiceTG ล้มเหลว: ${response.toString()}");
      }
    } catch (e) {
      print("❌ API Error (CreatServiceTG): $e");
    }
  }

  Future<void> notifyTelegram() async {
    Position position = await Geolocator.getCurrentPosition(
      desiredAccuracy: LocationAccuracy.high,
    );

    final String TELEGRAM_BOT_TOKEN =
        "**********************************************";
    final String TELEGRAM_CHAT_ID = "-1002346141851";

    final String createTopicUrl =
        "https://api.telegram.org/bot$TELEGRAM_BOT_TOKEN/createForumTopic";
    final String sendPhotoUrl =
        "https://api.telegram.org/bot$TELEGRAM_BOT_TOKEN/sendPhoto";
    final String qrUrl =
        "https://n8n-deploy.agilesoftgroup.com/webhook/generate-qr";

    final topicData = {
      "chat_id": TELEGRAM_CHAT_ID,
      "name": "Case ${scannedData.value} - ${customerName.value}"
    };

    try {
      final createTopicResponse = await http.post(
        Uri.parse(createTopicUrl),
        headers: {"Content-Type": "application/json"},
        body: jsonEncode(topicData),
      );

      if (createTopicResponse.statusCode == 200) {
        var topicResponseData = jsonDecode(createTopicResponse.body);
        if (topicResponseData['ok']) {
          String topicId =
              topicResponseData['result']['message_thread_id'].toString();

          await createServiceTG(TELEGRAM_CHAT_ID, topicId);

          final qrData = {
            "case_id": scannedData.value,
            "name": "Case ${scannedData.value} - ${customerName.value}",
            "selectedService": selectedService.value,
            "emp_code": profile.responseMember?.id,
            "phone": phoneNumber.value,
            "message_thread_id": topicId,
            "latitude": position.latitude,
            "longitude": position.longitude,
          };

          final getQrResponse = await http.post(
            Uri.parse(qrUrl),
            headers: {"Content-Type": "application/json"},
            body: jsonEncode(qrData),
          );

          if (getQrResponse.statusCode != 200) {
            Get.snackbar("ล้มเหลว", "สร้าง QR Code ไม่สำเร็จ!",
                snackPosition: SnackPosition.BOTTOM);
            return;
          }

          final qrResponseData = jsonDecode(getQrResponse.body);
          final String generatedQrUrl = qrResponseData["qr_url"];

          // ✅ 2. ส่ง QR Code เป็นรูปไปที่ Telegram Topic
          final sendPhotoResponse = await http.post(
            Uri.parse(sendPhotoUrl),
            headers: {"Content-Type": "application/json"},
            body: jsonEncode({
              "chat_id": TELEGRAM_CHAT_ID,
              "message_thread_id": topicId,
              "photo": generatedQrUrl,
              "caption":
                  "📢 ลูกค้า *${customerName.value}* ได้ลงทะเบียนบริการ *${selectedService.value}* แล้ว!\n\n"
                      "📌 *โปรดสแกน QR CODE เพื่อยืนยันการทำงาน*"
                      "\n\n📷 QR Code สำหรับ Case ID: *${scannedData.value}*"
                      "\n\n รหัสผู้คีย์ข้อมูล: *${profile.responseMember?.id}*",
              "parse_mode": "Markdown"
            }),
          );

          if (sendPhotoResponse.statusCode == 200) {
            await fetchService();
            Get.snackbar("สำเร็จ", "แจ้งเตือน Telegram แล้ว!",
                snackPosition: SnackPosition.BOTTOM);
          } else {
            Get.snackbar("ล้มเหลว", "ส่ง QR Code ไปยัง Telegram ไม่สำเร็จ!",
                snackPosition: SnackPosition.BOTTOM);
          }
        } else {
          Get.snackbar("ล้มเหลว", "สร้าง Topic ไม่สำเร็จ!",
              snackPosition: SnackPosition.BOTTOM);
        }
      } else {
        Get.snackbar("ล้มเหลว", "สร้าง Topic ไม่สำเร็จ!",
            snackPosition: SnackPosition.BOTTOM);
      }
    } catch (e) {
      Get.snackbar("เกิดข้อผิดพลาด", "API Telegram มีปัญหา!",
          snackPosition: SnackPosition.BOTTOM);
    }
  }

  Future<void> verifyEmpolyee(case_id, topicId) async {
    try {
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      final qrData = {
        "case_id": case_id,
        "my_lat": position.latitude,
        "my_lng": position.longitude,
        "emp_code": profile.responseMember?.id, // แก้เป็นค่าจริง
        "message_thread_id": topicId,
      };

      // ✅ เรียก API เพื่อสร้าง QR Code
      final getQrResponse = await http.post(
        Uri.parse(
            'https://n8n-deploy.agilesoftgroup.com/webhook/generate-confirm-qr'),
        headers: {"Content-Type": "application/json"},
        body: jsonEncode(qrData),
      );

      if (getQrResponse.statusCode == 200) {
        final responseData = jsonDecode(getQrResponse.body);
        final String? qrUrl = responseData["qr_url"];

        if (qrUrl != null && qrUrl.isNotEmpty) {
          // ✅ แสดง QR Code Popup
          Get.dialog(
            AlertDialog(
              title: Text("🔍 QR Code สำหรับ Case ID: $case_id"),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Image.network(qrUrl,
                      width: 250, height: 250, fit: BoxFit.cover),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Get.back(),
                  child: const Text("ปิด"),
                ),
              ],
            ),
            barrierDismissible: true, // ✅ กดนอก Popup เพื่อปิด
          );
        } else {
          print("❌ ไม่พบ QR Code ใน Response");
        }
      } else {
        print(
            "❌ API Request Failed: ${getQrResponse.statusCode} - ${getQrResponse.body}");
      }
    } catch (e) {
      print("🚨 เกิดข้อผิดพลาด: $e");
    }
  }

  void handleBarcodeScan(capture) async {
    if (hasScanned.value) return;

    final List barcodes = capture.barcodes;

    if (barcodes.isNotEmpty) {
      final String rawData = barcodes.first.rawValue ?? '';

      if (rawData.isEmpty) {
        return;
      }

      final String qrUrl =
          "https://api.qrserver.com/v1/create-qr-code/?size=600x600&data=${Uri.encodeComponent(rawData)}";

      Map<String, dynamic>? jsonData;
      try {
        jsonData = jsonDecode(rawData);
      } catch (e) {
        jsonData = null;
      }

      scannedBody.value = rawData;
      hasScanned.value = true;
      isScanning.value = false;
      scannerController.stop();

      if (jsonData != null) {
        jsonData["qr_url"] = qrUrl;
        jsonData.forEach((key, value) {});

        await confirmScan(
          caseId: jsonData["case_id"] ?? "",
          selectedService: jsonData["selectedService"] ?? "",
          messageThreadId: jsonData["message_thread_id"] ?? "",
          phone: profile.responseMember?.phone_like ?? "",
          empCode: jsonData["emp_code"] ?? "",
          qrUrl: jsonData["qr_url"] ?? qrUrl,
          chat_id: '', // ✅ ใช้ qrUrl ที่สร้างไว้
          latitude: jsonData["latitude"] ?? '',
          longitude: jsonData["longitude"] ?? '',
        );
      } else {
        print("📄 Raw Data: $rawData");
        print("🔗 QR Code URL: $qrUrl");
      }
    }
  }

  Future<void> confirmScan({
    required String caseId,
    required String selectedService,
    required String messageThreadId,
    required String phone,
    required String empCode,
    required String qrUrl,
    required String chat_id,
    required latitude,
    required longitude,
  }) async {
    final String url =
        "https://n8n-deploy.agilesoftgroup.com/webhook/confirm-scan";

    Position position = await Geolocator.getCurrentPosition(
      desiredAccuracy: LocationAccuracy.high,
    );

    final Map<String, dynamic> requestBody = {
      "case_id": caseId,
      "selectedService": selectedService,
      "message_thread_id": messageThreadId,
      "phone": phone,
      "emp_code": empCode,
      "qr_url": qrUrl,
      "latitude": latitude, // ใช้ 0.0 ถ้าไม่ได้ค่าจาก GPS
      "longitude": longitude,
      "my_lat": position.latitude, // ใช้ 0.0 ถ้าไม่ได้ค่าจาก GPS
      "my_long": position.longitude,
      "chat_id": chat_id,
    };

    try {
      final response = await http.post(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json",
        },
        body: jsonEncode(requestBody),
      );

      sendLiveLocation(chat_id, messageThreadId);

      if (response.statusCode == 200) {
        calculate_distand(messageThreadId, latitude, longitude,
            position.latitude, position.longitude);
      } else {
        print(
            "❌ Confirm Scan Failed: ${response.statusCode} - ${response.body}");
      }
    } catch (e) {
      print("🚨 Error: $e");
    }
  }

  Future<void> calculate_distand(
      messageThreadId, emp_lat, emp_long, mylat, mylong) async {
    final String url =
        "https://n8n-deploy.agilesoftgroup.com/webhook/calculate-distan";

    Position? position;
    try {
      position = await _determinePosition();
    } catch (e) {
      position = null;
    }

    final Map<String, dynamic> requestBody = {
      "message_thread_id": messageThreadId,
      "my_lat": position?.latitude ?? 0.0,
      "my_lng": position?.longitude ?? 0.0,
      "latitude": emp_lat,
      "longitude": emp_long,
    };

    try {
      final response = await http.post(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json",
        },
        body: jsonEncode(requestBody),
      );

      if (response.statusCode == 200) {
        print("✅ Confirm Scan Success: ${response.body}");
      } else {
        print(
            "❌ Confirm Scan Failed: ${response.statusCode} - ${response.body}");
      }
    } catch (e) {
      print("🚨 Error: $e");
    }
  }

  void handleBarcodeScanEMP(capture) async {
    if (hasScanned.value) return;

    final List barcodes = capture.barcodes;

    if (barcodes.isNotEmpty) {
      final String rawData = barcodes.first.rawValue ?? '';

      if (rawData.isEmpty) {
        return;
      }

      final String qrUrl =
          "https://api.qrserver.com/v1/create-qr-code/?size=1000x1000&data=${Uri.encodeComponent(rawData)}&ecc=H";

      Map<String, dynamic>? jsonData;
      try {
        jsonData = jsonDecode(rawData);
      } catch (e) {
        jsonData = null;
      }

      scannedBody.value = rawData;
      hasScanned.value = true;
      isScanning.value = false;
      scannerController.stop();

      if (jsonData != null) {
        jsonData["qr_url"] = qrUrl;
        jsonData.forEach((key, value) {});
      } else {
        print("📄 Raw Data: $rawData");
        print("🔗 QR Code URL: $qrUrl");
      }
    }
  }

  Future<void> confirmScanEMP({
    required String caseId,
    required String selectedService,
    required String messageThreadId,
    required String phone,
    required String empCode,
    required String qrUrl,
    required String chat_id,
  }) async {
    final String url =
        "https://n8n-deploy.agilesoftgroup.com/webhook/confirm-scanEMP";

    Position? position;
    try {
      position = await _determinePosition();
    } catch (e) {
      position = null;
    }

    final Map<String, dynamic> requestBody = {
      "case_id": caseId,
      "selectedService": selectedService,
      "message_thread_id": messageThreadId,
      "phone": phone,
      "emp_code": empCode,
      "qr_url": qrUrl,
      "latitude": position?.latitude ?? 0.0,
      "longitude": position?.longitude ?? 0.0,
      "chat_id": chat_id,
    };

    try {
      final response = await http.post(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json",
        },
        body: jsonEncode(requestBody),
      );

      if (response.statusCode == 200) {
        // calculate_distance(messageThreadId: messageThreadId);
      } else {
        print(
            "❌ Confirm Scan Failed: ${response.statusCode} - ${response.body}");
      }
    } catch (e) {
      print("🚨 Error: $e");
    }
  }

  Future<void> verifyCustomer(mobile_scanner.BarcodeCapture capture) async {
    if (hasScanned.value) return;

    hasScanned.value = true;

    Position? position;
    if (capture.barcodes.isNotEmpty) {
      final String rawData = capture.barcodes.first.rawValue ?? '';

      if (rawData.isEmpty) {
        hasScanned.value = false;
        return;
      }

      Map<String, dynamic>? jsonData;
      try {
        jsonData = jsonDecode(rawData);
      } catch (e) {
        hasScanned.value = false; // ✅ รีเซ็ตค่า ถ้าข้อมูลผิดพลาด
        return;
      }

      if (jsonData != null) {
        String caseId = jsonData["case_id"] ?? "";
        String messageThreadId = jsonData["message_thread_id"] ?? "";
        String empCode = jsonData["emp_code"] ?? "";
        double empLat = jsonData["my_lat"] ?? 0.0;
        double empLng = jsonData["my_lng"] ?? 0.0;

        try {
          // ✅ ดึงตำแหน่งปัจจุบัน
          position = await Geolocator.getCurrentPosition(
            desiredAccuracy: LocationAccuracy.high,
          );

          var data = {
            "caseId": caseId,
            "message_thread_id": messageThreadId,
            "empCode": empCode,
            "emp_lat": empLat,
            "emp_lng": empLng,
            "latitude": position.latitude, // ✅ ค่าพิกัดปัจจุบัน
            "longitude": position.longitude,
          };

          final response = await http.post(
            Uri.parse(
                'https://n8n-deploy.agilesoftgroup.com/webhook/confirm-work'),
            headers: {
              "Content-Type": "application/json",
            },
            body: jsonEncode(data),
          );

          if (response.statusCode == 200) {
            sendLiveLocation('-1002346141851', messageThreadId);

            Get.snackbar("สำเร็จ", "ยืนยันงานเรียบร้อย!",
                snackPosition: SnackPosition.BOTTOM);
          } else {
            Get.snackbar("ผิดพลาด", "ไม่สามารถยืนยันงานได้!",
                snackPosition: SnackPosition.BOTTOM);
          }
        } catch (e) {
          Get.snackbar("ผิดพลาด", "เกิดข้อผิดพลาดระหว่างส่งข้อมูล!",
              snackPosition: SnackPosition.BOTTOM);
        }
      }
    }

    Future.delayed(Duration(seconds: 3), () {
      hasScanned.value = false; // ✅ รีเซ็ตค่าหลังจาก 3 วินาที
    });
  }

  /// 📍 ดึงตำแหน่งปัจจุบันและส่งไป Telegram
  Future<void> sendLiveLocation(chatId, messageThreadId) async {
    final String TELEGRAM_BOT_TOKEN =
        "**********************************************";
    final String TELEGRAM_CHAT_ID = "-1002346141851";
    final String TELEGRAM_USER_ID = "5977488991";
    try {
      Position position = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high);

      double latitude = position.latitude;
      double longitude = position.longitude;

      final response = await http.post(
        Uri.parse(
            "https://n8n-deploy.agilesoftgroup.com/webhook/live-location"),
        headers: {"Content-Type": "application/json"},
        body: jsonEncode({
          "user_id": TELEGRAM_USER_ID,
          "chat_id": chatId,
          "message_thread_id": messageThreadId,
          "latitude": latitude,
          "longitude": longitude
        }),
      );

      print("📩 ส่งพิกัดไปยัง n8n: ${response.body}");
    } catch (e) {
      print("❌ Error sending location to n8n: $e");
    }
  }

  void startSharingLocation() {
    isSharing.value = true;

    const LocationSettings locationSettings = LocationSettings(
      accuracy: LocationAccuracy.high,
      distanceFilter: 5, // อัปเดตทุก 10 เมตร
    );

    _positionStream =
        Geolocator.getPositionStream(locationSettings: locationSettings)
            .listen((Position position) {
      sendLocationToN8n(position.latitude, position.longitude);
    });
  }

  void stopSharingLocation() {
    _positionStream?.cancel();
    isSharing.value = false;
  }

  Future<void> sendLocationToN8n(double latitude, double longitude) async {
    final response = await http.post(
      Uri.parse(webhookUrl),
      headers: {"Content-Type": "application/json"},
      body: jsonEncode({"latitude": latitude, "longitude": longitude}),
    );

    if (response.statusCode == 200) {
      print("📍 Location sent to n8n: $latitude, $longitude");
    } else {
      print("❌ Failed to send location to n8n: ${response.body}");
    }
  }
}
