// Import ใหม่สำหรับ Flutter Web
import 'dart:html' as html;
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

Future<void> registerIframe(String viewType, String url) async {
  WidgetsBinding.instance.addPostFrameCallback((_) {
    try {
      final iframe = html.IFrameElement()
        ..src = url
        ..style.border = 'none'
        ..width = "${Get.width}"
        ..height = "${Get.height}";

      html.document.body!.append(iframe);
    } catch (e) {
      print("registerIframe error : $e");
    }
  });
}
