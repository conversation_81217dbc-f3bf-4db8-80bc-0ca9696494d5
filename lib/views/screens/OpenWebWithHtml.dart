import 'dart:html' as html; // สำหรับจัดการ iframe HTML
import 'package:flutter/foundation.dart' show kIsWeb; // ใช้เพื่อตรวจสอบว่าแอปกำลังทำงานบนเว็บ
import 'package:flutter/material.dart';
import 'dart:ui' as ui; // สำหรับใช้ platformViewRegistry บนเว็บ


class IframeWebPage extends StatelessWidget {
  IframeWebPage() {
    if (kIsWeb) {
      // ตรวจสอบว่าเป็นเว็บก่อนสร้าง iframe
      final iframe = html.IFrameElement()
        ..src = 'https://www.google.com' // URL ของเว็บไซต์ที่ต้องการเปิด
        ..style.border = 'none'
        ..width = '100%'
        ..height = '100%';

      // ลงทะเบียน iframe กับ viewType
      // สำหรับ Flutter web ให้ใช้ `dart:html` view factory
      // ใช้ conditionally import platformViewRegistry
      // ignore: undefined_prefixed_name
      ui.platformViewRegistry.registerViewFactory(
        'iframeElement',
            (int viewId) => iframe,
      );
    }
  }


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('เปิดเว็บไซต์ใน Flutter Web')),
      body: Center(
        child: kIsWeb
            ? SizedBox(
          width: 800, // กำหนดขนาด iframe
          height: 600,
          child: HtmlElementView(viewType: 'iframeElement'), // แสดง iframe
        )
            : Text('This feature is only available on the web'), // Fallback สำหรับแพลตฟอร์มอื่น
      ),
    );
  }
}
