// import 'dart:convert';
// import 'dart:developer';
// import 'dart:io';
// import 'dart:ui';
//
// import 'package:ags_authrest2/ags_authrest.dart';
// import 'package:firebase_analytics/firebase_analytics.dart';
// import 'package:firebase_analytics/observer.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_inappwebview/flutter_inappwebview.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:fluttertoast/fluttertoast.dart';
// import 'package:image_picker/image_picker.dart';
// import 'package:load/load.dart';
// import 'package:mqtt_client/mqtt_client.dart';
// import 'package:mqtt_client/mqtt_server_client.dart';
// import 'package:scan/scan.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:http/http.dart' as http;
// import 'package:uuid/uuid.dart';
//
// import '../../library.dart';
// import 'package:easy_localization/easy_localization.dart';
//
// import '../home.dart';
//
// class QRscan extends StatefulWidget {
//   final ChromeSafariBrowser browser = new ChromeSafariBrowser();
//
//   @override
//   _QRscanState createState() => _QRscanState();
// }
//
// class _QRscanState extends State<QRscan> {
//   static FirebaseAnalytics analytics = FirebaseAnalytics();
//   static FirebaseAnalyticsObserver observer =
//       FirebaseAnalyticsObserver(analytics: analytics);
//
//   ScanController controller = ScanController();
//   String qrcode = 'Unknown';
//   var auth = Ags_restauth();
//   var uuid = Uuid();
//
//   @override
//   void initState() {
//     super.initState();
//
//     analytics.setCurrentScreen(screenName: "QRscan");
//     insertOpenMenu("QRscan");
//     checkinternet(context);
//     if (Platform.isAndroid) {
//       AndroidInAppWebViewController.setWebContentsDebuggingEnabled(true);
//     }
//     getInfo();
//   }
//
//   String? full_name_th_use = "";
//   getInfo() async {
//     SharedPreferences prefs = await SharedPreferences.getInstance();
//     var full_name_th = await prefs.getString('full_name_th');
//     setState(() {
//       full_name_th_use = full_name_th;
//     });
//   }
//
//   var datetime = "00-00-0000T00:00:00";
//   var numdate = "0";
//   getTime() async {
// //      showLoadingDialog();
//     String url = 'http://devdev.prachakij.com/nuiAPI/API_ms24/gettime.php';
// //    String url = 'https://a0df7e51.ngrok.io/agslearn/us-central1/ApiAppMS24';
//     Map map = {"menu": "gettime"};
//
//     final response = await apiRequest(url, map);
//     var jsonResponse = json.decode(response);
//     if (jsonResponse["date"] != "") {
//       setState(() {
//         datetime = jsonResponse["date"];
//         numdate = jsonResponse["numdate"];
//       });
//     } else {
//       print("not get time");
//     }
//   }
//
//   List tokenNotification = [];
//   getTokenNotification() async {
//     SharedPreferences prefs = await SharedPreferences.getInstance();
//     var id = await prefs.getString('id');
//
//     String url =
//         'https://mhmo3nnbr5.execute-api.ap-southeast-1.amazonaws.com/latest';
// //    String url = 'https://a0df7e51.ngrok.io/agslearn/us-central1/ApiAppMS24';
//     Map map = {"menu": "searchTokenNotifyById", "id": id.toString()};
//
//     final response = await apiRequest(url, map);
//     List jsonResponse = json.decode(response);
//     if (jsonResponse.length > 0) {
//       for (var i = 0; i < jsonResponse.length; i++) {
//         tokenNotification.add(jsonResponse[i]["tokenNotify"]);
//         print(tokenNotification);
//       }
//     } else {
//       print("not get time");
//     }
//   }
//
//   processQR(data) async {
//     showLoadingDialog();
//     await getTime();
//     await getTokenNotification();
//     SharedPreferences prefs = await SharedPreferences.getInstance();
//     var id = await prefs.getString('id');
//     var email1 = await prefs.getString('email1');
//     var phone_like = await prefs.getString('phone_like');
//     var fname = await prefs.getString('fname');
//     var lname = await prefs.getString('lname');
//     var company_management = await prefs.getString('division_name_gr');
//
//     if (id != "" && id != null && id != "null") {
//       String url =
//           "https://mhmo3nnbr5.execute-api.ap-southeast-1.amazonaws.com/latest";
//       Map map = {
//         "menu": "processQrcodeV2",
//         "datecheck": convertDateTime(datetime, "dd-MM-YYYY2").toString(),
//         "timecheck": convertDateTime(datetime, "THM").toString(),
//         "id": id.toString(),
//         "email1": email1.toString(),
//         "phone_like": phone_like..toString(),
//         "fname": fname.toString(),
//         "lname": lname.toString(),
//         "groupBU": company_management.toString(),
//         "detailQR": data.toString(),
//         "tokens": tokenNotification
//       };
//
//       final response = await apiRequest(url, map);
//       var jsonResponse = json.decode(response);
//       if (jsonResponse["statusCode"].toString() == "200") {
//         hideLoadingDialog();
//         setState(() {
//           statusAlertSuccess = 1;
//         });
//       } else {
//         print(jsonResponse["msg"].toString());
//         Fluttertoast.showToast(
//             msg: jsonResponse["msg"].toString(),
//             toastLength: Toast.LENGTH_SHORT,
//             gravity: ToastGravity.TOP,
//             backgroundColor: Colors.red,
//             textColor: Colors.black,
//             fontSize: 16.0);
//         hideLoadingDialog();
//         Navigator.pushReplacement(
//             context, MaterialPageRoute(builder: (context) => home(0)));
//       }
//     } else {
// //      alerterror(context,"รหัสสมาชิกหายไปจาก app MS กรุณา logout และ login อีกครั้ง");
//       Fluttertoast.showToast(
//           msg: "รหัสสมาชิกหายไปจาก app MS กรุณา logout และ login อีกครั้ง",
//           toastLength: Toast.LENGTH_SHORT,
//           gravity: ToastGravity.TOP,
//           backgroundColor: Colors.red,
//           textColor: Colors.black,
//           fontSize: 16.0);
//       hideLoadingDialog();
//     }
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return WillPopScope(
//       onWillPop: () {
//         Navigator.pushReplacement(
//             context, MaterialPageRoute(builder: (context) => home(0)));
//         return Future.value(true);
//       },
//       child: Scaffold(
//         body: Stack(
//           children: [
//             Center(
//               child: Container(
//                 color: Color(0xff302C49),
//               ),
//             ),
//             //****************************** head *********************** */
//             Container(
//               margin: EdgeInsets.only(
//                   top: mediaQuery(context, "h", 154),
//                   left: mediaQuery(context, "w", 67)),
//               child: GestureDetector(
//                 onTap: () {
//                   Navigator.pushReplacement(context,
//                       MaterialPageRoute(builder: (context) => home(0)));
//                 },
//                 child: Container(
//                   color: Colors.transparent,
//                   alignment: Alignment.center,
//                   height: mediaQuery(context, "h", 70),
//                   width: mediaQuery(context, "h", 70),
//                   child: Stack(
//                     children: [
//                       SvgPicture.string(
//                         '<svg viewBox="67.0 154.0 21.7 38.0" ><path transform="translate(55.75, 147.81)" d="M 17.79955863952637 25.18868064880371 L 32.17768859863281 10.82186222076416 C 33.24105834960938 9.758491516113281 33.24105834960938 8.038998603820801 32.17768859863281 6.986940383911133 C 31.11431694030762 5.92357063293457 29.39482688903809 5.934882164001465 28.33145332336426 6.986940383911133 L 12.0415210723877 23.26556205749512 C 11.01208686828613 24.29499435424805 10.98946189880371 25.94661521911621 11.96233177185059 27.00998306274414 L 28.32014274597168 43.40172958374023 C 28.85183143615723 43.93341445922852 29.55320167541504 44.19359970092773 30.24326133728027 44.19359970092773 C 30.93332099914551 44.19359970092773 31.63469123840332 43.93341445922852 32.1663818359375 43.40172958374023 C 33.22975158691406 42.33835601806641 33.22975158691406 40.61886596679688 32.1663818359375 39.56680679321289 L 17.79955863952637 25.18868064880371 Z" fill="#fcf6e4" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
//                         allowDrawingOutsideViewBox: true,
//                         height: mediaQuery(context, "h", 38),
//                       ),
//                     ],
//                   ),
//                 ),
//               ),
//             ),
//             Container(
//               alignment: Alignment.topCenter,
//               margin: EdgeInsets.only(top: mediaQuery(context, "h", 150)),
//               child: Text(
//                 "ui_QRscanhead".tr().toString(),
//                 style: TextStyle(
//                   fontFamily: 'SukhumvitSet-SemiBold',
//                   fontSize: mediaQuery(context, "h", 30),
//                   color: const Color(0xfffcf6e4),
//                   letterSpacing: mediaQuery(context, "h", 3),
//                   fontWeight: FontWeight.w700,
//                 ),
//                 textAlign: TextAlign.center,
//               ),
//             ),
//             //****************************** head *********************** */
//             Container(
//               alignment: Alignment.center,
//               child: Container(
//                 width: mediaQuery(context, "w", 650),
//                 height: mediaQuery(context, "h", 800),
//                 child: ScanView(
//                   controller: controller,
//                   // custom scan area, if set to 1.0, will scan full area
//                   scanAreaScale: .7,
//                   scanLineColor: Colors.green.shade400,
//                   onCapture: (data) async {
//                     controller.pause();
//                     print(data);
//                     // widget.browser.open(
//                     //     url: Uri.parse(data),
//                     //     options: ChromeSafariBrowserClassOptions(
//                     //         android: AndroidChromeCustomTabsOptions(
//                     //             enableUrlBarHiding: true,
//                     //             showTitle: true,
//                     //             shareState: CustomTabsShareState.SHARE_STATE_OFF),
//                     //         ios: IOSSafariOptions(barCollapsingEnabled: true)));
//                     //processQR(data.toString());
//                     bool a = await checkData(data);
//
//                     if(a) {
//                       print(true);
//                       authen(data);
//
//                     }else{
//                       print(false);
//                       processQR(data.toString());
//
//                     }
//
//                     // print(jsonDecode(jsonDecode(auth.decrypBodyStr(Jsondecodere['encrypData']))));
//                     // if(data!= null){
//                     //   print(data);
//                     //
//                     //   authen(data);
//                     //   print(data);
//                     // }else{
//                     //   processQR(data.toString());
//                     // }
//                     // do something
//                   },
//                 ),
//               ),
//             ),
//             Container(
//               alignment: Alignment.bottomRight,
//               padding: EdgeInsets.only(
//                   bottom: mediaQuery(context, "h", 100),
//                   right: mediaQuery(context, "w", 100)),
//               child: GestureDetector(
//                 onTap: () async {
//                   var image = await ImagePicker.platform
//                       .pickImage(source: ImageSource.gallery);
//                   print("image: $image");
//                   if (image == null) return;
//                   String? result = await Scan.parse(image.path);
//                   print("**************************");
//                   print("image: $result");
//                   await processQR(result);
//                   authen(result);
//                 },
//                 child: Image.asset(
//                   "assets/images/pkg/QRscan/download.png",
//                   height: mediaQuery(context, "h", 80),
//                   fit: BoxFit.fitHeight,
//                 ),
//               ),
//             ),
//             alertSuccess(),
//           ],
//         ),
//       ),
//     );
//   }
//
//   Future checkData(data) async {
//     try {
//       Map<String, dynamic> Jsondecodere = jsonDecode(data!);
//       if (Jsondecodere['encrypData'].isNotEmpty) {
//         print('data is encrypData');
//         return true;
//       }
//       print('data is not encrypData');
//       return false;
//     }catch(e){
//       print('check catch is String');
//       return false;
//     }
//   }
//   String? authn_id;
//   String? app_id;
//   String? uinqId;
//   String? mac_address;
//
//   Future authen(data) async {
//     showLoadingDialog();
//     SharedPreferences prefs = await SharedPreferences.getInstance();
//     var id = await prefs.getString('id');
//
//     var Jsondecodere = jsonDecode(data!);
//
//     print('Jsondecodere: $Jsondecodere');
//     // print typeof Jsondecodere;
//     var auth = Ags_restauth();
//     auth.SECERT_JWT = 'Eca)J.L@T9_vbgZ5MDpXUC';
//     auth.R_USER = 'yubikeypkg';
//
//
//     //  // dynamic str = "F85dCb9eapYS4SW9zJzaWJ6jkSYJd+PIZoVjWCxWRZ3A4dFsEMFGTuVnDMRyZjxw60y8dXsUX7M428BIMA4Gb0Zz8Bxz5XuSwya3u055dxDfijHMQiLWtGbEXvVzBxjhAT3XkTO7AClEe/8R94cZaw==";
//     // print(auth.decrypBodyStr(str));
//     print(auth.decrypBodyStr(Jsondecodere['encrypData']));
//     print(jsonDecode(auth.decrypBodyStr(Jsondecodere['encrypData'])));
//     final deBody =
//     jsonDecode(jsonDecode(auth.decrypBodyStr(Jsondecodere['encrypData'])));
//     // print(deBody);
//     // final JdeBody = jsonDecode(deBody);
//     // print(JdeBody['authn_id']);
//     print('deBody: $deBody');
//
//     print(id.toString());
//
//     if (deBody['authn_id'] == id.toString()) {
//       print('RPLC');
//       verify(deBody);
//       print(deBody['mqttsub']);
//       // listenws(deBody);
//       // topic['mqttsub']
//       listeningEvent(deBody);
//     } else {
//       // alert not iden
//       Fluttertoast.showToast(
//           msg: 'ไม่สามารถเข้าสู่ระบบได้',
//           toastLength: Toast.LENGTH_SHORT,
//           gravity: ToastGravity.TOP,
//           backgroundColor: Colors.red,
//           textColor: Colors.black,
//           fontSize: 16.0);
//       hideLoadingDialog();
//       Navigator.pushReplacement(
//           context, MaterialPageRoute(builder: (context) => home(0)));
//     }
//   }
//
//   Future verify(jdeBody) async {
//     // print(jdeBody);
//     var auth = Ags_restauth();
//     auth.SECERT_JWT = 'Eca)J.L@T9_vbgZ5MDpXUC';
//     auth.R_USER = 'yubikeypkg';
//     var headers = {
//       'Authorization': auth.genTokenEncryp(), // genTokenEncryp() or genToken()
//       'Content-Type': 'application/json'
//     };
//     var body = json.encode(jdeBody);
//     print('body: $body');
//     // print(body);
//
//     var request = http.Request('POST',
//         Uri.parse('https://agilesoftgroup.com/cf_yubitg/verifyTgotp'));
//     request.body = json
//         .encode(auth.encrypbody(body)); //encrypbody require genTokenEncryp()
//     request.headers.addAll(headers);
//     http.StreamedResponse response = await request.send();
//
//     if (response.statusCode == 200) {
//       var dataRaw = await response.stream.bytesToString();
//       var data = json.decode(dataRaw);
//       // print(data);
//     } else {
//       print(response.reasonPhrase);
//     }
//   }
//
//   Future<MqttServerClient> listeningEvent(topic) async {
//     String hosting = "wss://iac55c5c.ala.us-east-1.emqxsl.com/mqtt";
//     String Uname = "webconnect";
//     String Pass = "gtm5gvh_hzw5PNH4qpj";
//     // String topic = dotenv.env['topicAcleda'].toString();
//     var pong = 0;
//
//     MqttServerClient client = MqttServerClient(hosting, 'flutter_client');
//     client.keepAlivePeriod = 60;
//     client.logging(on: true);
//     client.onConnected = onConnected;
//     client.onDisconnected = onDisconnected;
//     client.onSubscribed = onSubscribed;
//     client.useWebSocket = true;
//     client.port = 8084;
//
//
//     final connMessage = MqttConnectMessage()
//         .authenticateAs(Uname, Pass)
//         .withWillTopic('willtopic')
//         .withWillMessage('Will message')
//         .startClean()
//         .withWillQos(MqttQos.atLeastOnce);
//     client.connectionMessage = connMessage;
//     try {
//       await client.connect();
//     } catch (e) {
//       print('Exception: $e');
//       client.disconnect();
//     }
//     String topicMq = 'mqttpkg/' + topic['mqttsub'];
//     print(topicMq);
//     client.subscribe(topicMq, MqttQos.atMostOnce);
//     client.updates!.listen((List<MqttReceivedMessage<MqttMessage?>>? c) {
//       final recMess = c![0].payload as MqttPublishMessage;
//       final pt =
//       MqttPublishPayload.bytesToStringAsString(recMess.payload.message);
//       var auth = Ags_restauth();
//       auth.SECERT_JWT = 'Eca)J.L@T9_vbgZ5MDpXUC';
//       auth.R_USER = 'yubikeypkg';
//
//       var authDecode = jsonDecode(jsonDecode(auth.decrypBodyStr(pt)));
//       print(authDecode);
//
//       if(authDecode['status'] == 'success'){
//         print('success');
//         client.disconnect();
//         return setState(() {
//           hideLoadingDialog();
//           statusAlertSuccess = 2;
//         });
//       }else{
//         Fluttertoast.showToast(
//             msg: 'ไม่สามารถเข้าสู่ระบบได้',
//             toastLength: Toast.LENGTH_SHORT,
//             gravity: ToastGravity.TOP,
//             backgroundColor: Colors.red,
//             textColor: Colors.black,
//             fontSize: 16.0);
//         hideLoadingDialog();
//         Navigator.pushReplacement(
//             context, MaterialPageRoute(builder: (context) => home(0)));
//       }
//     });
//     return client;
//   }
//
//   Future<void> onConnected() async {
//     print('EXAMPLE::OnConnected client callback - Client connection was sucessful');
//   }
//
//   Future<void> onDisconnected() async {
//     print(
//         'EXAMPLE::OnDisconnected client callback - Client disconnection');
//   }
//
//   Future<void> onSubscribed(String topic) async {
//     print('EXAMPLE::Subscription confirmed for topic $topic');
//   }
//
//   var statusAlertSuccess = 0;
//   Widget alertSuccess() {
//     if (statusAlertSuccess == 1) {
//       return ClipRect(
//         child: BackdropFilter(
//           filter: ImageFilter.blur(sigmaX: 1.97, sigmaY: 1.97),
//           child: Container(
//             alignment: Alignment.topCenter,
//             margin: EdgeInsets.only(top: mediaQuery(context, "h", 300)),
//             decoration: BoxDecoration(
//               color: const Color(0x00bebebe),
//             ),
//             child: Container(
//               width: mediaQuery(context, "w", 520),
//               height: mediaQuery(context, "h", 550),
//               decoration: BoxDecoration(
//                 borderRadius:
//                     BorderRadius.circular(mediaQuery(context, "h", 80)),
//                 color: const Color(0xfffcf6e4),
//               ),
//               child: Stack(
//                 children: [
//                   Container(
//                     alignment: Alignment.topCenter,
//                     margin: EdgeInsets.only(top: mediaQuery(context, "h", 82)),
//                     child: SvgPicture.string(
//                       '<svg viewBox="163.0 0.0 50.0 50.0" ><path transform="translate(160.43, -1.93)" d="M 27.57070350646973 6.61553430557251 C 22.14501762390137 6.61553430557251 17.04413795471191 8.728425025939941 13.20761585235596 12.56494903564453 C 9.371090888977051 16.40147399902344 7.25820255279541 21.50235176086426 7.25820255279541 26.92803764343262 C 7.25820255279541 32.35371780395508 9.371092796325684 37.4546012878418 13.20761680603027 41.2911262512207 C 17.04413986206055 45.12765121459961 22.14501953125 47.24053955078125 27.57070350646973 47.24053955078125 C 32.99638748168945 47.24053955078125 38.09726333618164 45.12765121459961 41.93379211425781 41.2911262512207 C 45.77031326293945 37.4546012878418 47.88320541381836 32.35372161865234 47.88320541381836 26.92803764343262 C 47.88320541381836 21.50235176086426 45.77031326293945 16.40147399902344 41.933837890625 12.56494903564453 C 38.09736251831055 8.728425025939941 32.99638748168945 6.61553430557251 27.57070350646973 6.61553430557251 Z M 27.57070350646973 1.928033590316772 C 41.37783050537109 1.928033590316772 52.57070159912109 13.12090492248535 52.57070159912109 26.92803764343262 C 52.57070159912109 40.73516845703125 41.37783050537109 51.92803955078125 27.57070350646973 51.92803955078125 C 13.76357555389404 51.92803955078125 2.570701122283936 40.73516845703125 2.570701122283936 26.92803955078125 C 2.570701122283936 13.12090969085693 13.76357173919678 1.928035974502563 27.57070350646973 1.928035974502563 Z M 24.44570159912109 36.30303955078125 L 30.69570350646973 36.30303955078125 L 30.69570350646973 42.55304336547852 L 24.44570159912109 42.55304336547852 L 24.44570159912109 36.30303955078125 Z M 24.44570159912109 11.30303478240967 L 30.69570350646973 11.30303478240967 L 30.69570350646973 30.05303573608398 L 24.44570159912109 30.05303573608398 L 24.44570159912109 11.30303478240967 Z" fill="#934d0f" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
//                       allowDrawingOutsideViewBox: true,
//                       fit: BoxFit.fitHeight,
//                       height: mediaQuery(context, "h", 50),
//                     ),
//                   ),
//                   Container(
//                     alignment: Alignment.topCenter,
//                     margin: EdgeInsets.only(top: mediaQuery(context, "h", 155)),
//                     child: Text(
//                       "ui_QRscanSuccess".tr().toString(),
//                       style: TextStyle(
//                         fontFamily: 'SukhumvitSet-Bold',
//                         fontSize: mediaQuery(context, "h", 33),
//                         color: const Color(0xff302c49),
//                         letterSpacing: mediaQuery(context, "h", 1.32),
//                         fontWeight: FontWeight.w700,
//                         height: 0.9393939393939394,
//                       ),
//                       textAlign: TextAlign.center,
//                     ),
//                   ),
//                   Container(
//                     alignment: Alignment.topCenter,
//                     margin: EdgeInsets.only(top: mediaQuery(context, "h", 210)),
//                     child: Text(
//                       "ui_QRscanSuccessDetail".tr().toString() +
//                           " " +
//                           full_name_th_use.toString() +
//                           "ui_QRscanSuccessDetail2".tr().toString(),
//                       style: TextStyle(
//                         fontFamily: 'SukhumvitSet-Medium',
//                         fontSize: mediaQuery(context, "h", 28),
//                         color: const Color(0xff302c49),
//                         letterSpacing: mediaQuery(context, "h", 1.12),
//                         fontWeight: FontWeight.w500,
//                         height: 1.5357142857142858,
//                       ),
//                       textAlign: TextAlign.center,
//                     ),
//                   ),
//                   Container(
//                     alignment: Alignment.topCenter,
//                     margin: EdgeInsets.only(top: mediaQuery(context, "h", 379)),
//                     child: GestureDetector(
//                       onTap: () {
//                         Navigator.pushReplacement(context,
//                             MaterialPageRoute(builder: (context) => home(0)));
//                       },
//                       child: Container(
//                         alignment: Alignment.center,
//                         width: mediaQuery(context, "w", 250),
//                         height: mediaQuery(context, "h", 90),
//                         decoration: BoxDecoration(
//                           borderRadius: BorderRadius.circular(
//                               mediaQuery(context, "h", 30)),
//                           color: const Color(0xff7f420c),
//                           boxShadow: [
//                             BoxShadow(
//                               color: const Color(0x1a000000),
//                               offset: Offset(0, 1),
//                               blurRadius: 2,
//                             ),
//                           ],
//                         ),
//                         child: Text(
//                           "ui_QRscanok".tr().toString(),
//                           style: TextStyle(
//                             fontFamily: 'SukhumvitSet-Bold',
//                             fontSize: mediaQuery(context, "h", 30),
//                             color: const Color(0xfffcf6e4),
//                             letterSpacing: mediaQuery(context, "h", 1.2),
//                             fontWeight: FontWeight.w700,
//                             // height: 1.0333333333333334,
//                             shadows: [
//                               Shadow(
//                                 color: const Color(0x26000000),
//                                 offset: Offset(0, 1),
//                                 blurRadius: 1,
//                               )
//                             ],
//                           ),
//                           textAlign: TextAlign.center,
//                         ),
//                       ),
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         ),
//       );
//     }
//     else if(statusAlertSuccess == 2){
//       return ClipRect(
//         child: BackdropFilter(
//           filter: ImageFilter.blur(sigmaX: 1.97, sigmaY: 1.97),
//           child: Container(
//             alignment: Alignment.topCenter,
//             margin: EdgeInsets.only(top: mediaQuery(context, "h", 300)),
//             decoration: BoxDecoration(
//               color: const Color(0x00bebebe),
//             ),
//             child: Container(
//               width: mediaQuery(context, "w", 520),
//               height: mediaQuery(context, "h", 550),
//               decoration: BoxDecoration(
//                 borderRadius:
//                 BorderRadius.circular(mediaQuery(context, "h", 80)),
//                 color: const Color(0xfffcf6e4),
//               ),
//               child: Stack(
//                 children: [
//                   Container(
//                     alignment: Alignment.topCenter,
//                     margin: EdgeInsets.only(top: mediaQuery(context, "h", 82)),
//                     child: SvgPicture.string(
//                       '<svg viewBox="163.0 0.0 50.0 50.0" ><path transform="translate(160.43, -1.93)" d="M 27.57070350646973 6.61553430557251 C 22.14501762390137 6.61553430557251 17.04413795471191 8.728425025939941 13.20761585235596 12.56494903564453 C 9.371090888977051 16.40147399902344 7.25820255279541 21.50235176086426 7.25820255279541 26.92803764343262 C 7.25820255279541 32.35371780395508 9.371092796325684 37.4546012878418 13.20761680603027 41.2911262512207 C 17.04413986206055 45.12765121459961 22.14501953125 47.24053955078125 27.57070350646973 47.24053955078125 C 32.99638748168945 47.24053955078125 38.09726333618164 45.12765121459961 41.93379211425781 41.2911262512207 C 45.77031326293945 37.4546012878418 47.88320541381836 32.35372161865234 47.88320541381836 26.92803764343262 C 47.88320541381836 21.50235176086426 45.77031326293945 16.40147399902344 41.933837890625 12.56494903564453 C 38.09736251831055 8.728425025939941 32.99638748168945 6.61553430557251 27.57070350646973 6.61553430557251 Z M 27.57070350646973 1.928033590316772 C 41.37783050537109 1.928033590316772 52.57070159912109 13.12090492248535 52.57070159912109 26.92803764343262 C 52.57070159912109 40.73516845703125 41.37783050537109 51.92803955078125 27.57070350646973 51.92803955078125 C 13.76357555389404 51.92803955078125 2.570701122283936 40.73516845703125 2.570701122283936 26.92803955078125 C 2.570701122283936 13.12090969085693 13.76357173919678 1.928035974502563 27.57070350646973 1.928035974502563 Z M 24.44570159912109 36.30303955078125 L 30.69570350646973 36.30303955078125 L 30.69570350646973 42.55304336547852 L 24.44570159912109 42.55304336547852 L 24.44570159912109 36.30303955078125 Z M 24.44570159912109 11.30303478240967 L 30.69570350646973 11.30303478240967 L 30.69570350646973 30.05303573608398 L 24.44570159912109 30.05303573608398 L 24.44570159912109 11.30303478240967 Z" fill="#934d0f" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
//                       allowDrawingOutsideViewBox: true,
//                       fit: BoxFit.fitHeight,
//                       height: mediaQuery(context, "h", 50),
//                     ),
//                   ),
//                   Container(
//                     alignment: Alignment.topCenter,
//                     margin: EdgeInsets.only(top: mediaQuery(context, "h", 155)),
//                     child: Text(
//                       "ui_QRscanSuccess".tr().toString(),
//                       style: TextStyle(
//                         fontFamily: 'SukhumvitSet-Bold',
//                         fontSize: mediaQuery(context, "h", 33),
//                         color: const Color(0xff302c49),
//                         letterSpacing: mediaQuery(context, "h", 1.32),
//                         fontWeight: FontWeight.w700,
//                         height: 0.9393939393939394,
//                       ),
//                       textAlign: TextAlign.center,
//                     ),
//                   ),
//                   Container(
//                     alignment: Alignment.topCenter,
//                     margin: EdgeInsets.only(top: mediaQuery(context, "h", 210)),
//                     child: Text(
//                       "ui_QRscanSuccessDetail3".tr().toString() +
//                           " " +
//                           "ui_QRscanSuccessDetail2".tr().toString(),
//                       style: TextStyle(
//                         fontFamily: 'SukhumvitSet-Medium',
//                         fontSize: mediaQuery(context, "h", 28),
//                         color: const Color(0xff302c49),
//                         letterSpacing: mediaQuery(context, "h", 1.12),
//                         fontWeight: FontWeight.w500,
//                         height: 1.5357142857142858,
//                       ),
//                       textAlign: TextAlign.center,
//                     ),
//                   ),
//                   Container(
//                     alignment: Alignment.topCenter,
//                     margin: EdgeInsets.only(top: mediaQuery(context, "h", 379)),
//                     child: GestureDetector(
//                       onTap: () {
//                         Navigator.pushReplacement(context,
//                             MaterialPageRoute(builder: (context) => home(0)));
//                       },
//                       child: Container(
//                         alignment: Alignment.center,
//                         width: mediaQuery(context, "w", 250),
//                         height: mediaQuery(context, "h", 90),
//                         decoration: BoxDecoration(
//                           borderRadius: BorderRadius.circular(
//                               mediaQuery(context, "h", 30)),
//                           color: const Color(0xff7f420c),
//                           boxShadow: [
//                             BoxShadow(
//                               color: const Color(0x1a000000),
//                               offset: Offset(0, 1),
//                               blurRadius: 2,
//                             ),
//                           ],
//                         ),
//                         child: Text(
//                           "ui_QRscanok".tr().toString(),
//                           style: TextStyle(
//                             fontFamily: 'SukhumvitSet-Bold',
//                             fontSize: mediaQuery(context, "h", 30),
//                             color: const Color(0xfffcf6e4),
//                             letterSpacing: mediaQuery(context, "h", 1.2),
//                             fontWeight: FontWeight.w700,
//                             // height: 1.0333333333333334,
//                             shadows: [
//                               Shadow(
//                                 color: const Color(0x26000000),
//                                 offset: Offset(0, 1),
//                                 blurRadius: 1,
//                               )
//                             ],
//                           ),
//                           textAlign: TextAlign.center,
//                         ),
//                       ),
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         ),
//       );
//     }else{
//       return Container();
//     }
//   }
//
// }
