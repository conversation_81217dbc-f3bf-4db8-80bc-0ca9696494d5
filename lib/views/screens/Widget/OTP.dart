
import 'dart:ui';


import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:mapp_ms24/controllers/internal/authController/OTPCobtroller.dart';


import 'library.dart';

class Yubikey {
  static String id_member = OtpController.id_member;
  OtpController otpCtrl = Get.find<OtpController>();
  static TextEditingController _textController = TextEditingController();

  static onNotSupportOTP(context) async {
    dialogWithReqOTPBTN(
        context,
        "ui_success_otp_verify_header".tr,
        "not_support_NFC_verify".tr,
        "btn_next".tr,
        "btn_cancel".tr,
        false,
        true);
  }

  static useYubi(context) async {
    return onInsertUbikey(context, true);
  }

  static registerYubi(context) async {
    return onInsertUbikey(context, false);
  }

  static onInsertUbikey(context, bool varify) {
    OtpController.nfcListener(context, varify);
    if (varify) {
      dialogWithReqOTP(context, "ui_scan_yubikey".tr, "ui_touch_yubikey".tr,
          "btn_cancel".tr, "ui_change_to_otp".tr, false, true);
    } else {
      popupDialog(context, "ui_scan_yubikey".tr, "ui_touch_yubikey".tr,
          "btn_cancel".tr, false, true);
    }
  }

  static onWaitingOTP(context) {
    Navigator.of(context, rootNavigator: true).pop();
    popupDialog(
        context, "btn_register_OTP".tr, "ui_waiting".tr, "", false, true);
    OtpController.RegisTgotp(context, id_member);
  }

  static onSendingOTP(context) async {
    Navigator.of(context, rootNavigator: true).pop();
    popupDialog(context, "ui_success_otp_sending".tr, "ui_waiting".tr, "",
        false, false);
    if (!OtpController.statusOTP) {
      await OtpController.RegisTgotpManual(id_member);
    }
  }

  static onVerifyOTP(context) {
    dialogWithTextField(context, "ui_success_otp_verify_header".tr,
        "ui_success_otp_send".tr, "btn_next".tr, "ui_otp_here".tr, false, true);
  }

  static onWaitingAlertPressed(context, bool varify) {
    popupDialog(
        context, "ui_scan_yubikey".tr, "ui_waiting".tr, "", false, true);
    if (varify) {
      OtpController.varifyYubikey(context);
    } else {
      OtpController.registerYubikey(context);
    }
  }

  static onSuccessAlertPressed(context, header, title) {
    popupDialog(context, header, title, "btn_ok".tr, true, true);
  }

  static onDuplicate(context, title, detail) {
    popupDialog(context, title, detail, "btn_ok".tr, false, false);
  }

  static onSuccessAlertPressedWithRegisOTP(context) {
    dialogWithRegisOTP(context, "ui_register_twoFA".tr, "ui_success_yubikey".tr,
        "btn_register_OTP".tr, "", true, true);
  }

  static onErrorAlertPressed(context, String error) {
    popupDialog(context, "ui_titleAlert3".tr, error, "btn_ok".tr, false, false);
  }

  static popupDialog(contextMain, title, message, textBTN, type, img) {
    return showDialog(
      barrierDismissible: false,
      context: contextMain,
      builder: (BuildContext context) {
        return BackdropFilter(
          filter: ImageFilter.blur(
            sigmaX: 5.0,
            sigmaY: 5.0,
          ),
          child: AlertDialog(
            titlePadding: EdgeInsets.fromLTRB(
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 82),
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 0),
            ),
            contentPadding: EdgeInsets.fromLTRB(
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 23),
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 0),
            ),
            backgroundColor: const Color(0xff302C49).withOpacity(0.6),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.all(
                Radius.circular(
                  mediaQuery(context, 'h', 80),
                ),
              ),
              side: BorderSide(
                width: mediaQuery(context, "h", 1),
                color: const Color(0xff5F569B),
              ),
            ),
            title: Padding(
              padding: EdgeInsets.only(
                bottom: mediaQuery(context, "h", 21.3),
              ),
              child: SvgPicture.asset(
                !type
                    ? "assets/images/pkg/yubikey/otp_L.svg"
                    : "assets/images/pkg/yubikey/otp_L.svg",
                allowDrawingOutsideViewBox: true,
                fit: BoxFit.fitHeight,
                height: mediaQuery(context, "h", 100),
                width: mediaQuery(context, "w", 100),
              ),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Text(
                  title,
                  style: TextStyle(
                    fontFamily: 'SukhumvitSet-Bold',
                    fontSize: mediaQuery(context, "h", 33),
                    color: const Color(0xffFEE095),
                    letterSpacing: mediaQuery(context, 'h', 1.32),
                    height: 0.8857142857142857,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(
                  height: mediaQuery(context, 'h', 30),
                ),
                img
                    ?
                SvgPicture.asset(
                        "assets/images/pkg/yubikey/otp_L.svg",
                        height: mediaQuery(context, "h", 150),
                        fit: BoxFit.fitHeight,
                      )
                    : Container(),
                SizedBox(
                  height: mediaQuery(context, 'h', 10),
                ),
                Padding(
                  padding: EdgeInsets.only(
                    right: mediaQuery(context, "h", 100),
                    left: mediaQuery(context, "h", 100),
                  ),
                  child: Text(
                    message,
                    style: TextStyle(
                      fontFamily: 'Sukhumvit Set',
                      fontSize: 12,
                      color: const Color(0xffF6F6F6),
                      letterSpacing: mediaQuery(context, "h", 1.12),
                      height: 1.3928571428571428,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                SizedBox(
                  height: mediaQuery(context, 'h', 71),
                ),
                textBTN != ""
                    ? Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: <Widget>[
                          GestureDetector(
                            onTap: () {
                              Navigator.pop(context, true);
                              // Loader.hide2(context);
                            },
                            child: SizedBox(
                              width: mediaQuery(context, 'w', 250),
                              height: mediaQuery(context, 'h', 80),
                              child: Container(
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(
                                      mediaQuery(context, "h", 30)),
                                  color: const Color(0xff5F569B),
                                  boxShadow: const [
                                    BoxShadow(
                                      color: Color(0x1a000000),
                                      offset: Offset(0, 1),
                                      blurRadius: 2,
                                    ),
                                  ],
                                ),
                                child: Text(
                                  textBTN.toString(),
                                  style: TextStyle(
                                    fontFamily: 'SukhumvitSet-Bold',
                                    fontSize: mediaQuery(context, "h", 30),
                                    color: const Color(0xfffcf6e4),
                                    letterSpacing:
                                        mediaQuery(context, "h", 1.2),
                                    fontWeight: FontWeight.w700,
                                    height: 1.0333333333333334,
                                    shadows: const [
                                      Shadow(
                                        color: Color(0x26000000),
                                        offset: Offset(0, 1),
                                        blurRadius: 1,
                                      )
                                    ],
                                  ),
                                  textHeightBehavior: const TextHeightBehavior(
                                      applyHeightToFirstAscent: false),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          ),
                        ],
                      )
                    : Container(),
                textBTN != ""
                    ? SizedBox(
                        height: mediaQuery(context, 'h', 75),
                      )
                    : Container(),
              ],
            ),
          ),
        );
      },
    );
  }

  static dialogWithRegisOTP(
      contextMain, title, message, textConfirm, textBTN, type, img) {
    return showDialog(
      barrierDismissible: false,
      context: contextMain,
      builder: (BuildContext context) {
        return BackdropFilter(
          filter: ImageFilter.blur(
            sigmaX: 5.0,
            sigmaY: 5.0,
          ),
          child: AlertDialog(
            titlePadding: EdgeInsets.fromLTRB(
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 82),
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 0),
            ),
            contentPadding: EdgeInsets.fromLTRB(
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 23),
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 0),
            ),
            backgroundColor: const Color(0xff302C49).withOpacity(0.6),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.all(
                Radius.circular(
                  mediaQuery(context, 'h', 80),
                ),
              ),
              side: BorderSide(
                width: mediaQuery(context, "h", 1),
                color: const Color(0xff5F569B),
              ),
            ),
            title: Padding(
              padding: EdgeInsets.only(
                bottom: mediaQuery(context, "h", 21.3),
              ),
              child: SvgPicture.string(
                !type
                    ? '<svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M13 6.5C13.715 6.5 14.3 7.085 14.3 7.8V13C14.3 13.715 13.715 14.3 13 14.3C12.285 14.3 11.7 13.715 11.7 13V7.8C11.7 7.085 12.285 6.5 13 6.5ZM12.987 0C5.811 0 0 5.824 0 13C0 20.176 5.811 26 12.987 26C20.176 26 26 20.176 26 13C26 5.824 20.176 0 12.987 0ZM13 23.4C7.254 23.4 2.6 18.746 2.6 13C2.6 7.254 7.254 2.6 13 2.6C18.746 2.6 23.4 7.254 23.4 13C23.4 18.746 18.746 23.4 13 23.4ZM14.3 19.5H11.7V16.9H14.3V19.5Z" fill="#FEE095"/></svg>'
                    : '<svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg"> <circle cx="14" cy="14" r="12.25" stroke="#FEE095" stroke-width="2.5"/><path d="M9 14.5L12.75 18L19 11" stroke="#FEE095" stroke-width="2.5" stroke-linecap="round"/></svg>',
                allowDrawingOutsideViewBox: true,
                fit: BoxFit.fitHeight,
                height: mediaQuery(context, "h", 50),
                width: mediaQuery(context, "h", 50),
              ),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Text(
                  title,
                  style: TextStyle(
                    fontFamily: 'SukhumvitSet-Bold',
                    fontSize: mediaQuery(context, "h", 33),
                    color: const Color(0xffFEE095),
                    letterSpacing: mediaQuery(context, 'h', 1.32),
                    height: 0.8857142857142857,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(
                  height: mediaQuery(context, 'h', 30),
                ),
                img
                    ? Image.asset(
                        "assets/images/pkg/yubikey/yubikey.png",
                        height: mediaQuery(context, "h", 150),
                        fit: BoxFit.fitHeight,
                      )
                    : Container(),
                SizedBox(
                  height: mediaQuery(context, 'h', 10),
                ),
                Padding(
                  padding: EdgeInsets.only(
                    right: mediaQuery(context, "h", 100),
                    left: mediaQuery(context, "h", 100),
                  ),
                  child: Text(
                    message,
                    style: TextStyle(
                      fontFamily: 'SukhumvitSet-Medium',
                      fontSize: mediaQuery(context, "h", 28),
                      color: message == "not_support_NFC".tr
                          ? const Color(0xffFF8282)
                          : const Color(0xffF6F6F6),
                      letterSpacing: mediaQuery(context, "h", 1.12),
                      height: 1.3928571428571428,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                SizedBox(
                  height: mediaQuery(context, 'h', 71),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    GestureDetector(
                      onTap: () {
                        onWaitingOTP(contextMain);
                      },
                      child: SizedBox(
                        width: mediaQuery(context, 'w', 250),
                        height: mediaQuery(context, 'h', 80),
                        child: Container(
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(
                                mediaQuery(context, "h", 30)),
                            color: const Color(0xffFEE095),
                            boxShadow: const [
                              BoxShadow(
                                color: Color(0x1a000000),
                                offset: Offset(0, 1),
                                blurRadius: 2,
                              ),
                            ],
                          ),
                          child: Text(
                            textConfirm.toString(),
                            style: TextStyle(
                              fontFamily: 'SukhumvitSet-Bold',
                              fontSize: mediaQuery(context, "h", 30),
                              color: const Color(0xff5F569B),
                              letterSpacing: mediaQuery(context, "h", 1.2),
                              fontWeight: FontWeight.w700,
                              height: 1.0333333333333334,
                              shadows: const [
                                Shadow(
                                  color: Color(0x26000000),
                                  offset: Offset(0, 1),
                                  blurRadius: 1,
                                )
                              ],
                            ),
                            textHeightBehavior: const TextHeightBehavior(
                                applyHeightToFirstAscent: false),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                textBTN != ""
                    ? SizedBox(
                        height: mediaQuery(context, 'h', 20),
                      )
                    : Container(),
                textBTN != ""
                    ? Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: <Widget>[
                          GestureDetector(
                            onTap: () {
                              Navigator.pop(context, true);
                              // Loader.hide2(context);
                            },
                            child: SizedBox(
                              width: mediaQuery(context, 'w', 250),
                              height: mediaQuery(context, 'h', 80),
                              child: Container(
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(
                                      mediaQuery(context, "h", 30)),
                                  color: const Color(0xff5F569B),
                                  boxShadow: const [
                                    BoxShadow(
                                      color: Color(0x1a000000),
                                      offset: Offset(0, 1),
                                      blurRadius: 2,
                                    ),
                                  ],
                                ),
                                child: Text(
                                  textBTN.toString(),
                                  style: TextStyle(
                                    fontFamily: 'SukhumvitSet-Bold',
                                    fontSize: mediaQuery(context, "h", 30),
                                    color: const Color(0xfffcf6e4),
                                    letterSpacing:
                                        mediaQuery(context, "h", 1.2),
                                    fontWeight: FontWeight.w700,
                                    height: 1.0333333333333334,
                                    shadows: const [
                                      Shadow(
                                        color: Color(0x26000000),
                                        offset: Offset(0, 1),
                                        blurRadius: 1,
                                      )
                                    ],
                                  ),
                                  textHeightBehavior: const TextHeightBehavior(
                                      applyHeightToFirstAscent: false),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          ),
                        ],
                      )
                    : Container(),
                SizedBox(
                  height: mediaQuery(context, 'h', 75),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  static dialogWithReqOTP(
      contextMain, title, message, textBTN, textPath, type, img) {
    return showDialog(
      barrierDismissible: false,
      context: contextMain,
      builder: (BuildContext context) {
        return BackdropFilter(
          filter: ImageFilter.blur(
            sigmaX: 5.0,
            sigmaY: 5.0,
          ),
          child: AlertDialog(
            titlePadding: EdgeInsets.fromLTRB(
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 82),
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 0),
            ),
            contentPadding: EdgeInsets.fromLTRB(
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 23),
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 0),
            ),
            backgroundColor: const Color(0xff302C49).withOpacity(0.6),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.all(
                Radius.circular(
                  mediaQuery(context, 'h', 80),
                ),
              ),
              side: BorderSide(
                width: mediaQuery(context, "h", 1),
                color: const Color(0xff5F569B),
              ),
            ),
            title: Padding(
              padding: EdgeInsets.only(
                bottom: mediaQuery(context, "h", 21.3),
              ),
              child: SvgPicture.string(
                !type
                    ? '<svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M13 6.5C13.715 6.5 14.3 7.085 14.3 7.8V13C14.3 13.715 13.715 14.3 13 14.3C12.285 14.3 11.7 13.715 11.7 13V7.8C11.7 7.085 12.285 6.5 13 6.5ZM12.987 0C5.811 0 0 5.824 0 13C0 20.176 5.811 26 12.987 26C20.176 26 26 20.176 26 13C26 5.824 20.176 0 12.987 0ZM13 23.4C7.254 23.4 2.6 18.746 2.6 13C2.6 7.254 7.254 2.6 13 2.6C18.746 2.6 23.4 7.254 23.4 13C23.4 18.746 18.746 23.4 13 23.4ZM14.3 19.5H11.7V16.9H14.3V19.5Z" fill="#FEE095"/></svg>'
                    : '<svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg"> <circle cx="14" cy="14" r="12.25" stroke="#FEE095" stroke-width="2.5"/><path d="M9 14.5L12.75 18L19 11" stroke="#FEE095" stroke-width="2.5" stroke-linecap="round"/></svg>',
                allowDrawingOutsideViewBox: true,
                fit: BoxFit.fitHeight,
                height: mediaQuery(context, "h", 50),
                width: mediaQuery(context, "h", 50),
              ),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Text(
                  title,
                  style: TextStyle(
                    fontFamily: 'SukhumvitSet-Bold',
                    fontSize: mediaQuery(context, "h", 33),
                    color: const Color(0xffFEE095),
                    letterSpacing: mediaQuery(context, 'h', 1.32),
                    height: 0.8857142857142857,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(
                  height: mediaQuery(context, 'h', 30),
                ),
                img
                    ? Image.asset(
                        "assets/images/pkg/yubikey/yubikey.png",
                        height: mediaQuery(context, "h", 150),
                        fit: BoxFit.fitHeight,
                      )
                    : Container(),
                SizedBox(
                  height: mediaQuery(context, 'h', 10),
                ),
                Padding(
                  padding: EdgeInsets.only(
                    right: mediaQuery(context, "h", 100),
                    left: mediaQuery(context, "h", 100),
                  ),
                  child: Text(
                    message,
                    style: TextStyle(
                      fontFamily: 'SukhumvitSet-Medium',
                      fontSize: mediaQuery(context, "h", 28),
                      color: const Color(0xffF6F6F6),
                      letterSpacing: mediaQuery(context, "h", 1.12),
                      height: 1.3928571428571428,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                SizedBox(
                  height: mediaQuery(context, 'h', 71),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    GestureDetector(
                      onTap: () {
                        Navigator.pop(context, true);
                        // Loader.hide2(context);
                      },
                      child: SizedBox(
                        width: mediaQuery(context, 'w', 250),
                        height: mediaQuery(context, 'h', 80),
                        child: Container(
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(
                                mediaQuery(context, "h", 30)),
                            color: const Color(0xff5F569B),
                            boxShadow: const [
                              BoxShadow(
                                color: Color(0x1a000000),
                                offset: Offset(0, 1),
                                blurRadius: 2,
                              ),
                            ],
                          ),
                          child: Text(
                            textBTN.toString(),
                            style: TextStyle(
                              fontFamily: 'SukhumvitSet-Bold',
                              fontSize: mediaQuery(context, "h", 30),
                              color: const Color(0xfffcf6e4),
                              letterSpacing: mediaQuery(context, "h", 1.2),
                              fontWeight: FontWeight.w700,
                              height: 1.0333333333333334,
                              shadows: const [
                                Shadow(
                                  color: Color(0x26000000),
                                  offset: Offset(0, 1),
                                  blurRadius: 1,
                                )
                              ],
                            ),
                            textHeightBehavior: const TextHeightBehavior(
                                applyHeightToFirstAscent: false),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: mediaQuery(context, 'h', 26),
                ),
                GestureDetector(
                  onTap: () {
                    OtpController.SendTgotp(contextMain, id_member);
                  },
                  child: Text(
                    textPath,
                    style: TextStyle(
                      fontFamily: 'SukhumvitSet-Text',
                      fontSize: mediaQuery(context, "h", 28),
                      color: Colors.red,
                      letterSpacing: mediaQuery(context, "h", 1.5),
                      fontWeight: FontWeight.w700,
                      decoration: TextDecoration.underline,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                SizedBox(
                  height: mediaQuery(context, 'h', 75),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  static dialogWithReqOTPBTN(
      contextMain, title, message, textConfirm, textBTN, type, img) {
    return showDialog(
      barrierDismissible: false,
      context: contextMain,
      builder: (BuildContext context) {
        return BackdropFilter(
          filter: ImageFilter.blur(
            sigmaX: 5.0,
            sigmaY: 5.0,
          ),
          child: AlertDialog(
            titlePadding: EdgeInsets.fromLTRB(
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 82),
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 0),
            ),
            contentPadding: EdgeInsets.fromLTRB(
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 23),
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 0),
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.all(
                Radius.circular(
                  mediaQuery(context, 'h', 80),
                ),
              ),
              side: BorderSide(
                width: mediaQuery(context, "h", 1),
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            title: Padding(
              padding: EdgeInsets.only(
                bottom: mediaQuery(context, "h", 21.3),
              ),
              child: SvgPicture.string(
                !type
                    ? '<svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M13 6.5C13.715 6.5 14.3 7.085 14.3 7.8V13C14.3 13.715 13.715 14.3 13 14.3C12.285 14.3 11.7 13.715 11.7 13V7.8C11.7 7.085 12.285 6.5 13 6.5ZM12.987 0C5.811 0 0 5.824 0 13C0 20.176 5.811 26 12.987 26C20.176 26 26 20.176 26 13C26 5.824 20.176 0 12.987 0ZM13 23.4C7.254 23.4 2.6 18.746 2.6 13C2.6 7.254 7.254 2.6 13 2.6C18.746 2.6 23.4 7.254 23.4 13C23.4 18.746 18.746 23.4 13 23.4ZM14.3 19.5H11.7V16.9H14.3V19.5Z" fill="#FEE095"/></svg>'
                    : '<svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg"> <circle cx="14" cy="14" r="12.25" stroke="#FEE095" stroke-width="2.5"/><path d="M9 14.5L12.75 18L19 11" stroke="#FEE095" stroke-width="2.5" stroke-linecap="round"/></svg>',
                allowDrawingOutsideViewBox: true,
                fit: BoxFit.fitHeight,
                height: mediaQuery(context, "h", 50),
                width: mediaQuery(context, "h", 50),
              ),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Text(
                  title,
                  style: TextStyle(
                    fontFamily: 'SukhumvitSet-Bold',
                    fontSize: mediaQuery(context, "h", 33),
                    color: Theme.of(context).colorScheme.onSecondary,
                    letterSpacing: mediaQuery(context, 'h', 1.32),
                    height: 0.8857142857142857,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(
                  height: mediaQuery(context, 'h', 30),
                ),
                img
                    ? Image.asset(
                        "assets/images/pkg/yubikey/yubikey.png",
                        height: mediaQuery(context, "h", 150),
                        fit: BoxFit.fitHeight,
                      )
                    : Container(),
                SizedBox(
                  height: mediaQuery(context, 'h', 10),
                ),
                Padding(
                  padding: EdgeInsets.only(
                    right: mediaQuery(context, "h", 100),
                    left: mediaQuery(context, "h", 100),
                  ),
                  child: Text(
                    message,
                    style: TextStyle(
                      fontFamily: 'SukhumvitSet-Medium',
                      fontSize: mediaQuery(context, "h", 28),
                      color: message == "not_support_NFC".tr
                          ? const Color(0xffFF8282)
                          : const Color(0xffF6F6F6),
                      letterSpacing: mediaQuery(context, "h", 1.12),
                      height: 1.3928571428571428,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                SizedBox(
                  height: mediaQuery(context, 'h', 71),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    GestureDetector(
                      onTap: () {
                        Navigator.pop(context, true);
                        OtpController.SendTgotp(contextMain, id_member);
                      },
                      child: SizedBox(
                        width: mediaQuery(context, 'w', 250),
                        height: mediaQuery(context, 'h', 80),
                        child: Container(
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(
                                mediaQuery(context, "h", 30)),
                            color: const Color(0xffFEE095),
                            boxShadow: const [
                              BoxShadow(
                                color: Color(0x1a000000),
                                offset: Offset(0, 1),
                                blurRadius: 2,
                              ),
                            ],
                          ),
                          child: Text(
                            textConfirm.toString(),
                            style: TextStyle(
                              fontFamily: 'SukhumvitSet-Bold',
                              fontSize: mediaQuery(context, "h", 30),
                              color: Theme.of(context).colorScheme.onSecondary,
                              letterSpacing: mediaQuery(context, "h", 1.2),
                              fontWeight: FontWeight.w700,
                              height: 1.0333333333333334,
                              shadows: const [
                                Shadow(
                                  color: Color(0x26000000),
                                  offset: Offset(0, 1),
                                  blurRadius: 1,
                                )
                              ],
                            ),
                            textHeightBehavior: const TextHeightBehavior(
                                applyHeightToFirstAscent: false),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                textBTN != ""
                    ? SizedBox(
                        height: mediaQuery(context, 'h', 20),
                      )
                    : Container(),
                textBTN != ""
                    ? Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: <Widget>[
                          GestureDetector(
                            onTap: () {
                              Navigator.pop(context, true);
                              // Loader.hide2(context);
                            },
                            child: SizedBox(
                              width: mediaQuery(context, 'w', 250),
                              height: mediaQuery(context, 'h', 80),
                              child: Container(
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(
                                      mediaQuery(context, "h", 30)),
                                  color: Theme.of(context).colorScheme.secondaryFixed.withOpacity(0.6),
                                  boxShadow: const [
                                    BoxShadow(
                                      color: Color(0x1a000000),
                                      offset: Offset(0, 1),
                                      blurRadius: 2,
                                    ),
                                  ],
                                ),
                                child: Text(
                                  textBTN.toString(),
                                  style: TextStyle(
                                    fontFamily: 'SukhumvitSet-Bold',
                                    fontSize: mediaQuery(context, "h", 30),
                                    color: const Color(0xfffcf6e4),
                                    letterSpacing:
                                        mediaQuery(context, "h", 1.2),
                                    fontWeight: FontWeight.w700,
                                    height: 1.0333333333333334,
                                    shadows: const [
                                      Shadow(
                                        color: Color(0x26000000),
                                        offset: Offset(0, 1),
                                        blurRadius: 1,
                                      )
                                    ],
                                  ),
                                  textHeightBehavior: const TextHeightBehavior(
                                      applyHeightToFirstAscent: false),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          ),
                        ],
                      )
                    : Container(),
                SizedBox(
                  height: mediaQuery(context, 'h', 75),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  static dialogWithTextField(
      contextMain, title, message, textBTN, textField, type, img) {
    return showDialog(
      barrierDismissible: false,
      context: contextMain,
      builder: (BuildContext context) {
        return BackdropFilter(
          filter: ImageFilter.blur(
            sigmaX: 5.0,
            sigmaY: 5.0,
          ),
          child: AlertDialog(
            titlePadding: EdgeInsets.fromLTRB(
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 82),
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 0),
            ),
            contentPadding: EdgeInsets.fromLTRB(
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 23),
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 0),
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.all(
                Radius.circular(
                  mediaQuery(context, 'h', 80),
                ),
              ),
              side: BorderSide(
                width: mediaQuery(context, "h", 1),
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            title: Padding(
              padding: EdgeInsets.only(
                bottom: mediaQuery(context, "h", 21.3),
              ),
              child: SvgPicture.string(
                !type
                    ? '<svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M13 6.5C13.715 6.5 14.3 7.085 14.3 7.8V13C14.3 13.715 13.715 14.3 13 14.3C12.285 14.3 11.7 13.715 11.7 13V7.8C11.7 7.085 12.285 6.5 13 6.5ZM12.987 0C5.811 0 0 5.824 0 13C0 20.176 5.811 26 12.987 26C20.176 26 26 20.176 26 13C26 5.824 20.176 0 12.987 0ZM13 23.4C7.254 23.4 2.6 18.746 2.6 13C2.6 7.254 7.254 2.6 13 2.6C18.746 2.6 23.4 7.254 23.4 13C23.4 18.746 18.746 23.4 13 23.4ZM14.3 19.5H11.7V16.9H14.3V19.5Z" fill="#FEE095"/></svg>'
                    : '<svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg"> <circle cx="14" cy="14" r="12.25" stroke="#FEE095" stroke-width="2.5"/><path d="M9 14.5L12.75 18L19 11" stroke="#FEE095" stroke-width="2.5" stroke-linecap="round"/></svg>',
                allowDrawingOutsideViewBox: true,
                fit: BoxFit.fitHeight,
                height: mediaQuery(context, "h", 50),
                width: mediaQuery(context, "h", 50),
              ),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Text(
                  title,
                  style: TextStyle(
                    fontFamily: 'SukhumvitSet-Bold',
                    fontSize: mediaQuery(context, "h", 33),
                    color: Theme.of(context).colorScheme.onSecondary,
                    letterSpacing: mediaQuery(context, 'h', 1.32),
                    height: 0.8857142857142857,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(
                  height: mediaQuery(context, 'h', 30),
                ),
                img
                    ? Image.asset(
                        "assets/images/pkg/yubikey/yubikey.png",
                        height: mediaQuery(context, "h", 150),
                        fit: BoxFit.fitHeight,
                      )
                    : Container(),
                SizedBox(
                  height: mediaQuery(context, 'h', 10),
                ),
                Padding(
                  padding: EdgeInsets.only(
                    right: mediaQuery(context, "h", 100),
                    left: mediaQuery(context, "h", 100),
                  ),
                  child: Text(
                    message,
                    style: TextStyle(
                      fontFamily: 'SukhumvitSet-Medium',
                      fontSize: mediaQuery(context, "h", 28),
                      color: Theme.of(context).colorScheme.onSecondary,
                      letterSpacing: mediaQuery(context, "h", 1.12),
                      height: 1.3928571428571428,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                textField != ""
                    ? SizedBox(
                        height: mediaQuery(context, 'h', 10),
                      )
                    : Container(),
                Stack(
                  children: [
                    Container(
                      alignment: Alignment.topRight,
                      margin:
                          EdgeInsets.only(top: mediaQuery(context, 'h', 70)),
                      child: TextButton(
                        child: Text(
                          "ui_paste".tr,
                          style: TextStyle(
                              fontSize: mediaQuery(context, 'h', 25),
                              color: Theme.of(context).colorScheme.onSecondary),
                        ),
                        onPressed: () async {
                          ClipboardData? cdata =
                              await Clipboard.getData(Clipboard.kTextPlain);
                          _textController.text = cdata!.text!;
                          ClipboardData? text =
                              await Clipboard.getData(Clipboard.kTextPlain);
                          RegExp exp = RegExp(r'\b[0-9]{6}\b');
                          Iterable<Match> matches =
                              exp.allMatches(text!.text!);
                          List<String?> numbers =
                              matches.map((match) => match.group(0)).toList();
                          String finalString = numbers.join(",");
                          _textController.text = finalString;
                        },
                      ),
                    ),
                    textField != ""
                        ? Container(
                            margin: EdgeInsets.only(
                              left: mediaQuery(context, "h", 150),
                              right: mediaQuery(context, "h", 110),
                            ),
                            child: TextField(
                              cursorWidth: mediaQuery(context, "h", 2),
                              textAlign: TextAlign.center,
                              inputFormatters: [
                                FilteringTextInputFormatter.allow(
                                    RegExp(r'[0-9]'))
                              ],
                              autofocus: true,
                              keyboardType: TextInputType.number,
                              // controller: _textController,
                              maxLength: 6,
                              maxLines: 1,
                              style: TextStyle(
                                color: Theme.of(context).colorScheme.onSecondary,
                                fontSize: mediaQuery(context, "h", 33),
                                fontFamily: "SukhumvitSet-Text",
                              ),
                              decoration: InputDecoration(
                                counterText: '',
                                labelText: textField,
                                floatingLabelAlignment:
                                    FloatingLabelAlignment.center,
                                labelStyle: TextStyle(
                                  color:Theme.of(context).colorScheme.onSecondary,
                                  fontSize: mediaQuery(context, "h", 33),
                                  fontFamily: "SukhumvitSet-Text",
                                ),
            //
                                enabledBorder: UnderlineInputBorder(
                                  borderSide:
                                      BorderSide(color: Theme.of(context).colorScheme.onSecondary),
                                ),
                                focusedBorder: UnderlineInputBorder(
                                  borderSide:
                                      BorderSide(color:Theme.of(context).colorScheme.onSecondary),
                                ),
                              ),
                            ))
                        : Container(),
                  ],
                ),
                SizedBox(
                  height: mediaQuery(context, 'h', 33),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    GestureDetector(
                      onTap: () async {

                        OtpController.VerifyTgotp(
                            contextMain, id_member, _textController.text);
                      },
                      child: SizedBox(
                        width: mediaQuery(context, 'w', 250),
                        height: mediaQuery(context, 'h', 80),
                        child: Container(
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(
                                mediaQuery(context, "h", 30)),
                            color: const Color(0xff5F569B),
                            boxShadow: const [
                              BoxShadow(
                                color: Color(0x1a000000),
                                offset: Offset(0, 1),
                                blurRadius: 2,
                              ),
                            ],
                          ),
                          child: Text(
                            textBTN.toString(),
                            style: TextStyle(
                              fontFamily: 'SukhumvitSet-Bold',
                              fontSize: mediaQuery(context, "h", 30),
                              color: const Color(0xfffcf6e4),
                              letterSpacing: mediaQuery(context, "h", 1.2),
                              fontWeight: FontWeight.w700,
                              height: 1.0333333333333334,
                              shadows: const [
                                Shadow(
                                  color: Color(0x26000000),
                                  offset: Offset(0, 1),
                                  blurRadius: 1,
                                )
                              ],
                            ),
                            textHeightBehavior: const TextHeightBehavior(
                                applyHeightToFirstAscent: false),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: mediaQuery(context, 'h', 75),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
