import 'dart:convert';
import 'dart:io';
import 'dart:ui';
import 'package:date_picker_plus/date_picker_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_svg/svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:mapp_ms24/controllers/internal/BenefitController%20/BenefitController.dart';
import 'package:mapp_ms24/controllers/internal/BitrixController/bitrixController.dart';
import 'package:mapp_ms24/controllers/internal/CewController/CewController.dart';
import 'package:mapp_ms24/controllers/internal/FinaceController/NDAdoc/NDAcontroller.dart';
import 'package:mapp_ms24/controllers/internal/HealthController/HealthController.dart';
import 'package:mapp_ms24/controllers/internal/LeaveController/LeaveApprovalController.dart';
import 'package:mapp_ms24/controllers/internal/NotiController/NotifiController.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/controllers/internal/Tacontroller/Tacontroller.dart';
import 'package:mapp_ms24/controllers/internal/Tacontroller/timePageController.dart';
import 'package:mapp_ms24/controllers/internal/WorkIssuse/WorkIssuseController.dart';
import 'package:mapp_ms24/controllers/internal/authController/AuthController.dart';
import 'package:mapp_ms24/controllers/internal/authController/OTPCobtroller.dart';
import 'package:mapp_ms24/controllers/internal/authController/PinController.dart';
import 'package:mapp_ms24/controllers/internal/daysWork/daysWorkController.dart';
import 'package:mapp_ms24/controllers/internal/notification_controller.dart';
import 'package:mapp_ms24/controllers/internal/wealthController/wealthController.dart';
import 'package:mapp_ms24/controllers/internal/yubikey/QrScanController.dart';
import 'package:mapp_ms24/controllers/login_controller/login.dart';
import 'package:mapp_ms24/controllers/utils/widget.dart';
import 'package:mapp_ms24/views/screens/Widget/Widget.dart';

import 'package:open_store/open_store.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:dart_jsonwebtoken/dart_jsonwebtoken.dart';

import 'package:crypto/crypto.dart';
import 'package:ags_authrest2/ags_authrest.dart';
import 'package:http/http.dart' as http;

class AppHttps {
  static String OldsendTelegramPKG =
      'https://dockerapi-ci.prachakij.com/jwtauth';
  static String sendTelegramPKG =
      'https://oxphgjyvu2.execute-api.ap-southeast-1.amazonaws.com/latest/sendTelegramOptional';

  static apiRequestDockerAgs(String url, Map jsonMap) async {
    var path = '/switchUrl2img/5200';
    final response = await apiRequest('$url$path', jsonMap);
    var jsonResponse = json.decode(response);
    return "";
  }

  static postTG_PKG(String url, List<dynamic> jsonMap) async {
    try {
      var path = '/send_telegram_pkg/5015';
      final response = await apiRequest2('$url$path', jsonMap);
      var jsonResponse = json.decode(response);
      return jsonResponse;
    } catch (e) {
      print(e);
    }
  }

  static postTG_BCT(String url, List<dynamic> jsonMap) async {
    var path = '/send_telegram_app/5015';
    final response = await apiRequest2('$url$path', jsonMap);
    var jsonResponse = json.decode(response);
    return jsonResponse;
  }

  static postTG_PGH(String url, List<dynamic> jsonMap) async {
    var path = '/send_telegram_pgh/5015';
    final response = await apiRequest2('$url$path', jsonMap);
    var jsonResponse = json.decode(response);
    return jsonResponse;
  }

  static encryptAGS(Map data) {
    var auth = Ags_restauth();
    auth.SECERT_JWT =
        "A^Amps9@_Um_=^-9tfwJ&d&!pFqgppnK9=JWtFrJqxq=m=5H*3U@%&f%R@+Nsymz&@aC_8tbq6gYjM*R#6mqJ!7A^ZPYwAG3P!8C*dR2zuc33";
    auth.R_USER = "dockerapi-22022203889234";
    var body = json.encode(data);
    return auth.encrypbody(body);
  }

  static Old_apiRequestDockerAgs(String url, Map jsonMap) async {
    HttpClient httpClient = new HttpClient();
    HttpClientRequest request = await httpClient.postUrl(Uri.parse(url));
    request.headers.set('content-type', 'application/json');
    request.headers.set('port', '5200');
    request.headers.set('path', 'switchUrl2img');
    request.headers.set('Authorization',
        'Basic YWdzLWNpOmNkV21XLG81SGxOUGR9Z3pFWmxrYnpDSih6RHRQKQ==');
    request.add(utf8.encode(json.encode(jsonMap)));
    HttpClientResponse response = await request.close();
    // todo - you should check the response.statusCode
    String reply = await response.transform(utf8.decoder).join();
    httpClient.close();
    return reply;
  }

  static Old_postTG_PKG(String url, List<dynamic> jsonMap) async {
    HttpClient httpClient = HttpClient();
    HttpClientRequest request = await httpClient.postUrl(Uri.parse(url));
    request.headers.set('content-type', 'application/json');
    request.headers.set('port', '5015');
    request.headers.set('path', 'send_telegram_pkg');
    request.headers.set('Authorization',
        'Basic YWdzLWNpOmNkV21XLG81SGxOUGR9Z3pFWmxrYnpDSih6RHRQKQ==');
    request.add(utf8.encode(json.encode(jsonMap)));
    HttpClientResponse response = await request.close();
    // todo - you should check the response.statusCode
    String reply = await response.transform(utf8.decoder).join();
    httpClient.close();
    return json.decode(reply);
  }

  static Old_postTG_BCT(String url, List<dynamic> jsonMap) async {
    HttpClient httpClient = HttpClient();
    HttpClientRequest request = await httpClient.postUrl(Uri.parse(url));
    request.headers.set('content-type', 'application/json');
    request.headers.set('port', '5015');
    request.headers.set('path', 'send_telegram_app');
    request.headers.set('Authorization',
        'Basic YWdzLWNpOmNkV21XLG81SGxOUGR9Z3pFWmxrYnpDSih6RHRQKQ==');
    request.add(utf8.encode(json.encode(jsonMap)));
    HttpClientResponse response = await request.close();
    // todo - you should check the response.statusCode
    String reply = await response.transform(utf8.decoder).join();
    httpClient.close();
    return json.decode(reply);
  }

  static Old_postTG_PGH(String url, List<dynamic> jsonMap) async {
    HttpClient httpClient = HttpClient();
    HttpClientRequest request = await httpClient.postUrl(Uri.parse(url));
    request.headers.set('content-type', 'application/json');
    request.headers.set('port', '5015');
    request.headers.set('path', 'send_telegram_pgh');
    request.headers.set('Authorization',
        'Basic YWdzLWNpOmNkV21XLG81SGxOUGR9Z3pFWmxrYnpDSih6RHRQKQ==');
    request.add(utf8.encode(json.encode(jsonMap)));
    HttpClientResponse response = await request.close();
    // todo - you should check the response.statusCode
    String reply = await response.transform(utf8.decoder).join();
    httpClient.close();
    return json.decode(reply);
  }
}

mediaQuery(context, String type, double value) {
//  BuildContext context;
  double _height = MediaQuery.of(context).size.height;
  double _width = MediaQuery.of(context).size.width;

  double widthScreen = 828;
  double heightScreen = 1792;

  if (type == "height" || type == "h") {
    return (_height * (value / heightScreen));
  } else if (type == "width" || type == "w") {
    return (_width * (value / widthScreen));
  }
}

mediaQuery2(context, String type, double value) {
//  BuildContext context;
  double _height = MediaQuery.of(context).size.height;
  double _width = MediaQuery.of(context).size.width;

  double widthScreen = 426;
  double heightScreen = 640;

  if (type == "height" || type == "h") {
    return (_height * (value / heightScreen));
  } else if (type == "width" || type == "w") {
    return (_width * (value / widthScreen));
  }
}

cuttext(text, numCut) {
  var numstring = text.length;
  if (numstring > numCut) {
    return text.substring(0, numCut) + "...";
  } else {
    return text;
  }
}

convertTimeIn(String time, String select) {
  var timesplit = time.split(":");
  if (select == "H") {
    return timesplit[0];
  } else {
    return timesplit[1];
  }
}

convertTimeToGMT(date) {
  var dateSplit = date.split("+");
  var dateTime = DateTime.parse(dateSplit[0] + "-04:00");
  var duration = dateTime.timeZoneOffset;
  return dateTime.toIso8601String() +
      "-${duration.inHours.toString().padLeft(2, '0')}:${(duration.inMinutes - (duration.inHours * 60)).toString().padLeft(2, '0')}";
}

convertDateTime(String date, String select) {
  var datetimeSplit = date.split("T");
  var datesplit = datetimeSplit[0].split("-");
  var timesplit = datetimeSplit[1].split(":");
  if (select == "D") {
    return datesplit[0] + "/" + datesplit[1] + "/" + datesplit[2];
  } else if (select == "dd/MM/YYYY mm:ss") {
    return datesplit[2] +
        "/" +
        datesplit[1] +
        "/" +
        datesplit[0] +
        " " +
        timesplit[0] +
        ":" +
        timesplit[1];
  } else if (select == "dd/MM/YYYY") {
    return datesplit[2] + "/" + datesplit[1] + "/" + datesplit[0];
  } else if (select == "dd/MM/YYYY2") {
    return datesplit[2] + " / " + datesplit[1] + " / " + datesplit[0];
  } else if (select == "dd/MM/YYYY3") {
    return datesplit[0] + "/" + datesplit[1] + "/" + datesplit[2];
  } else if (select == "dd-MM-YYYY") {
    return datesplit[0] + "-" + datesplit[1] + "-" + datesplit[2];
  } else if (select == "dd-MM-YYYY2") {
    return datesplit[2] + "-" + datesplit[1] + "-" + datesplit[0];
  } else if (select == "DD") {
    return datesplit[0];
  } else if (select == "MM") {
    return datesplit[1];
  } else if (select == "YYYY") {
    return datesplit[2];
  } else if (select == "YY") {
    return datesplit[2].substring(2, 4);
  } else if (select == "DDB") {
    return datesplit[2] + "-" + datesplit[1] + "-" + datesplit[0];
  } else if (select == "DTDB") {
    return datesplit[2] +
        "-" +
        datesplit[1] +
        "-" +
        datesplit[0] +
        " " +
        datetimeSplit[1];
  } else if (select == "T") {
    return datetimeSplit[1];
  } else if (select == "TH") {
    return timesplit[0];
  } else if (select == "TM") {
    return timesplit[1];
  } else if (select == "TS") {
    return timesplit[2];
  } else if (select == "THM") {
    return timesplit[0] + ":" + timesplit[1];
  } else {
    return "select not fond";
  }
}

convertDateTimeFromDB(String date, String select) {
  var datetimeSplit = date.split("T");
  var datesplit = datetimeSplit[0].split("-");
  var timesplit = datetimeSplit[1].split(":");
  if (select == "D") {
    return datesplit[0] + "/" + datesplit[1] + "/" + datesplit[2];
  } else if (select == "dd-MM-yyyy") {
    return datesplit[2] + "-" + datesplit[1] + "-" + datesplit[0];
  } else if (select == "DD") {
    return datesplit[2];
  } else if (select == "MM") {
    return datesplit[1];
  } else if (select == "YYYY") {
    return datesplit[0];
  } else {
    return "select not fond";
  }
}

convertDateTime2(date, select) {
  var datetimeSplit = date.split(" ");
  var datesplit = datetimeSplit[0].split("-");
  var timesplit = datetimeSplit[1].split(":");
  if (select == "D") {
    return datesplit[0] + "/" + datesplit[1] + "/" + datesplit[2];
  } else if (select == "dd/MM/YYYY mm:ss") {
    return datesplit[2] +
        "/" +
        datesplit[1] +
        "/" +
        datesplit[0] +
        " " +
        timesplit[0] +
        ":" +
        timesplit[1];
  } else if (select == "dd/MM/YYYY") {
    return datesplit[2] + "/" + datesplit[1] + "/" + datesplit[0];
  } else if (select == "DDB") {
    return datesplit[2] + "-" + datesplit[1] + "-" + datesplit[0];
  } else if (select == "DTDB") {
    return datesplit[2] +
        "-" +
        datesplit[1] +
        "-" +
        datesplit[0] +
        " " +
        datetimeSplit[1];
  } else if (select == "T") {
    return datetimeSplit[1];
  } else if (select == "TH") {
    return timesplit[0];
  } else if (select == "TM") {
    return timesplit[1];
  } else if (select == "TS") {
    return timesplit[2];
  } else if (select == "THM") {
    return timesplit[0] + ":" + timesplit[1];
  } else {
    return "select not fond";
  }
}

alerterror(context, textMessage) {
  showDialog<String>(
    context: context,
    builder: (BuildContext context) => AlertDialog(
      title: const Text('Error'),
      content: Text(textMessage.toString()),
      actions: <Widget>[
        // TextButton(
        //   onPressed: () => Navigator.pop(context, 'Cancel'),
        //   child: const Text('Cancel'),
        // ),
        TextButton(
          onPressed: () => Navigator.pop(context, 'OK'),
          child: const Text('OK'),
        ),
      ],
    ),
  );
}

info(context, textMessage) {
  showDialog<String>(
    context: context,
    builder: (BuildContext context) => AlertDialog(
      title: const Text('Info'),
      content: Text(textMessage.toString()),
      actions: <Widget>[
        // TextButton(
        //   onPressed: () => Navigator.pop(context, 'Cancel'),
        //   child: const Text('Cancel'),
        // ),
        TextButton(
          onPressed: () => Navigator.pop(context, 'OK'),
          child: const Text('OK'),
        ),
      ],
    ),
  );
}

success(context, textMessage) {
  showDialog<String>(
    context: context,
    builder: (BuildContext context) => AlertDialog(
      title: const Text('Success'),
      content: Text(textMessage.toString()),
      actions: <Widget>[
        // TextButton(
        //   onPressed: () => Navigator.pop(context, 'Cancel'),
        //   child: const Text('Cancel'),
        // ),
        TextButton(
          onPressed: () => Navigator.pop(context, 'OK'),
          child: const Text('OK'),
        ),
      ],
    ),
  );
}

confirm(context, textMessage) {
  showDialog<String>(
    context: context,
    builder: (BuildContext context) => AlertDialog(
      title: const Text('Success'),
      content: Text(textMessage.toString()),
      actions: <Widget>[
        TextButton(
          onPressed: () => Navigator.pop(context, 'OK'),
          child: const Text('OK'),
        ),
        TextButton(
          onPressed: () => Navigator.pop(context, 'Cancel'),
          child: const Text('Cancel'),
        ),
      ],
    ),
  );
}

alertDialog(context, title, message, textBTN) {
  return showDialog(
    barrierDismissible: false,
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        titlePadding: EdgeInsets.fromLTRB(
          mediaQuery(context, 'h', 0),
          mediaQuery(context, 'h', 82),
          mediaQuery(context, 'h', 0),
          mediaQuery(context, 'h', 0),
        ),
        contentPadding: EdgeInsets.fromLTRB(
          mediaQuery(context, 'h', 0),
          mediaQuery(context, 'h', 23),
          mediaQuery(context, 'h', 0),
          mediaQuery(context, 'h', 0),
        ),
        backgroundColor: Color(0xfffcf6e4),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(
            Radius.circular(
              mediaQuery(context, 'h', 80),
            ),
          ),
          // side: BorderSide(
          //   width: mediaQuery(context, "h", 5),
          //   color: Color(0xff47246A),
          // ),
        ),
        title: Container(
          child: Padding(
            padding: EdgeInsets.only(
              bottom: mediaQuery(context, "h", 21.3),
            ),
            child: SvgPicture.string(
              '<svg viewBox="167.0 0.0 50.0 50.0" ><path transform="translate(164.43, -1.93)" d="M 27.57070350646973 6.61553430557251 C 22.14501762390137 6.61553430557251 17.04413795471191 8.728425025939941 13.20761585235596 12.56494903564453 C 9.371090888977051 16.40147399902344 7.25820255279541 21.50235176086426 7.25820255279541 26.92803764343262 C 7.25820255279541 32.35371780395508 9.371092796325684 37.4546012878418 13.20761680603027 41.2911262512207 C 17.04413986206055 45.12765121459961 22.14501953125 47.24053955078125 27.57070350646973 47.24053955078125 C 32.99638748168945 47.24053955078125 38.09726333618164 45.12765121459961 41.93379211425781 41.2911262512207 C 45.77031326293945 37.4546012878418 47.88320541381836 32.35372161865234 47.88320541381836 26.92803764343262 C 47.88320541381836 21.50235176086426 45.77031326293945 16.40147399902344 41.933837890625 12.56494903564453 C 38.09736251831055 8.728425025939941 32.99638748168945 6.61553430557251 27.57070350646973 6.61553430557251 Z M 27.57070350646973 1.928033590316772 C 41.37783050537109 1.928033590316772 52.57070159912109 13.12090492248535 52.57070159912109 26.92803764343262 C 52.57070159912109 40.73516845703125 41.37783050537109 51.92803955078125 27.57070350646973 51.92803955078125 C 13.76357555389404 51.92803955078125 2.570701122283936 40.73516845703125 2.570701122283936 26.92803955078125 C 2.570701122283936 13.12090969085693 13.76357173919678 1.928035974502563 27.57070350646973 1.928035974502563 Z M 24.44570159912109 36.30303955078125 L 30.69570350646973 36.30303955078125 L 30.69570350646973 42.55304336547852 L 24.44570159912109 42.55304336547852 L 24.44570159912109 36.30303955078125 Z M 24.44570159912109 11.30303478240967 L 30.69570350646973 11.30303478240967 L 30.69570350646973 30.05303573608398 L 24.44570159912109 30.05303573608398 L 24.44570159912109 11.30303478240967 Z" fill="#934d0f" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
              allowDrawingOutsideViewBox: true,
              fit: BoxFit.fitHeight,
              height: mediaQuery(context, "h", 50),
              width: mediaQuery(context, "h", 50),
            ),
          ),
        ),
        content: Container(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Text(
                title,
                style: TextStyle(
                  fontFamily: 'SukhumvitSet-Bold',
                  fontSize: mediaQuery(context, "h", 33),
                  color: const Color(0xff4b4b4b),
                  letterSpacing: mediaQuery(context, 'h', 1.32),
                  height: 0.8857142857142857,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(
                height: mediaQuery(context, 'h', 10),
              ),
              Padding(
                padding: EdgeInsets.only(
                  right: mediaQuery(context, "h", 100),
                  left: mediaQuery(context, "h", 100),
                ),
                child: Text(
                  message,
                  style: TextStyle(
                    fontFamily: 'SukhumvitSet-Medium',
                    fontSize: mediaQuery(context, "h", 28),
                    color: const Color(0xff4b4b4b),
                    letterSpacing: mediaQuery(context, "h", 1.12),
                    height: 1.3928571428571428,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              SizedBox(
                height: mediaQuery(context, 'h', 71),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  GestureDetector(
                    onTap: () {
                      Navigator.pop(context, true);
                      // Loader.hide2(context);
                    },
                    child: SizedBox(
                      width: mediaQuery(context, 'w', 250),
                      height: mediaQuery(context, 'h', 90),
                      child: Container(
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(
                              mediaQuery(context, "h", 30)),
                          color: const Color(0xff7f420c),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0x1a000000),
                              offset: Offset(0, 1),
                              blurRadius: 2,
                            ),
                          ],
                        ),
                        child: Text(
                          textBTN.toString(),
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Bold',
                            fontSize: mediaQuery(context, "h", 30),
                            color: const Color(0xfffcf6e4),
                            letterSpacing: mediaQuery(context, "h", 1.2),
                            fontWeight: FontWeight.w700,
                            height: 1.0333333333333334,
                            shadows: [
                              Shadow(
                                color: const Color(0x26000000),
                                offset: Offset(0, 1),
                                blurRadius: 1,
                              )
                            ],
                          ),
                          textHeightBehavior: TextHeightBehavior(
                              applyHeightToFirstAscent: false),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(
                height: mediaQuery(context, 'h', 75),
              ),
            ],
          ),
        ),
      );
    },
  );
}

showAlertDialog(context, imgDark, imgBright, message) {
  showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return Container(
          child: BackdropFilter(
            filter: ImageFilter.blur(
              sigmaX: 2.5,
              sigmaY: 2.5,
            ),
            child: AlertDialog(
              backgroundColor: Theme.of(context).colorScheme.primary,
              titlePadding: EdgeInsets.all(1.0),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(
                  Radius.circular(
                    mediaQuery(context, 'h', 90),
                  ),
                ),
              ),
              title: Container(
                width: mediaQuery(context, 'w', 650),
                height: mediaQuery(context, 'h', 650),
                decoration: BoxDecoration(
                  borderRadius:
                      BorderRadius.circular(mediaQuery(context, "h", 90)),
                  color: Theme.of(context).colorScheme.primary,
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0x1a000000),
                      offset: Offset(0, 1),
                      blurRadius: 2,
                    ),
                  ],
                ),
                child: Container(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: <Widget>[
                      Container(
                        padding: EdgeInsets.only(
                          top: mediaQuery(context, 'h', 32),
                        ),
                        child: Text(
                          'ลงเวลาเรียบร้อย',
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Text',
                            fontSize: mediaQuery(context, "h", 30),
                            color: Theme.of(context).colorScheme.onSecondary,
                            fontWeight: FontWeight.w700,
                            letterSpacing:
                                mediaQuery(context, "h", 1.4000000000000001),
                            height: 2.0,
                          ),
                        ),
                      ),
                      Container(
                        child: Image(
                          image:
                              AssetImage(Get.isDarkMode ? imgDark : imgBright),
                          width: mediaQuery(context, 'w', 130),
                          height: mediaQuery(context, 'h', 130),
                        ),
                      ),
                      Container(
                        alignment: Alignment.center,
                        padding:
                            EdgeInsets.only(top: mediaQuery(context, 'h', 20)),
                        child: Column(
                          children: [
                            Container(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    message,
                                    style: TextStyle(
                                      fontFamily: 'SukhumvitSet-Text',
                                      fontSize: mediaQuery(context, "h", 25),
                                      color: Theme.of(context)
                                          .colorScheme
                                          .onSecondary,
                                      fontWeight: FontWeight.w500,
                                      letterSpacing: mediaQuery(
                                          context, "h", 1.4000000000000001),
                                      height: 2.0,
                                    ), // เปิดให้ตัดบรรทัดใหม่เมื่อข้อความยาวเกิน
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            ),
                            Padding(
                              padding: EdgeInsets.only(
                                  top: mediaQuery(context, 'h', 50)),
                              child: GestureDetector(
                                onTap: () {
                                  // AppSettings.openAppSettings();
                                  Navigator.pop(context);
                                },
                                child: Container(
                                  alignment: Alignment.center,
                                  width: mediaQuery(context, "w", 500),
                                  height: mediaQuery(context, "h", 100),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(
                                        mediaQuery(context, "h", 50)),
                                    color: Theme.of(context)
                                        .colorScheme
                                        .secondaryContainer
                                        .withOpacity(0.7),
                                    border: Border.all(
                                      color: Theme.of(context)
                                          .colorScheme
                                          .secondaryContainer,
                                      width: 1,
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        "ตกลง",
                                        style: TextStyle(
                                          fontFamily: 'SukhumvitSet-Bold',
                                          fontSize:
                                              mediaQuery(context, "h", 35),
                                          color: Theme.of(context)
                                              .colorScheme
                                              .onSecondary,
                                          letterSpacing:
                                              mediaQuery(context, "h", 1.2),
                                          shadows: [
                                            Shadow(
                                              color: const Color(0x26000000),
                                              offset: Offset(0, 1),
                                              blurRadius: 1,
                                            )
                                          ],
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      });
}

showAlertDialog_wel(context, message) {
  showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return Container(
          child: BackdropFilter(
            filter: ImageFilter.blur(
              sigmaX: 2.5,
              sigmaY: 2.5,
            ),
            child: AlertDialog(
              backgroundColor: Theme.of(context).colorScheme.primary,
              titlePadding: EdgeInsets.all(1.0),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(
                  Radius.circular(
                    mediaQuery(context, 'h', 90),
                  ),
                ),
              ),
              title: Container(
                width: mediaQuery(context, 'w', 650),
                height: mediaQuery(context, 'h', 650),
                decoration: BoxDecoration(
                  borderRadius:
                      BorderRadius.circular(mediaQuery(context, "h", 90)),
                  color: Theme.of(context).colorScheme.primary,
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0x1a000000),
                      offset: Offset(0, 1),
                      blurRadius: 2,
                    ),
                  ],
                ),
                child: Container(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: <Widget>[
                      Container(
                        alignment: Alignment.center,
                        padding:
                            EdgeInsets.only(top: mediaQuery(context, 'h', 20)),
                        child: Column(
                          children: [
                            Container(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    message,
                                    style: TextStyle(
                                      fontFamily: 'SukhumvitSet-Text',
                                      fontSize: mediaQuery(context, "h", 25),
                                      color: Theme.of(context)
                                          .colorScheme
                                          .onSecondary,
                                      fontWeight: FontWeight.w500,
                                      letterSpacing: mediaQuery(
                                          context, "h", 1.4000000000000001),
                                      height: 2.0,
                                    ), // เปิดให้ตัดบรรทัดใหม่เมื่อข้อความยาวเกิน
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            ),
                            Padding(
                              padding: EdgeInsets.only(
                                  top: mediaQuery(context, 'h', 50)),
                              child: GestureDetector(
                                onTap: () {
                                  // AppSettings.openAppSettings();
                                  Navigator.pop(context);
                                },
                                child: Container(
                                  alignment: Alignment.center,
                                  width: mediaQuery(context, "w", 500),
                                  height: mediaQuery(context, "h", 100),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(
                                        mediaQuery(context, "h", 50)),
                                    color: Theme.of(context)
                                        .colorScheme
                                        .secondaryContainer
                                        .withOpacity(0.7),
                                    border: Border.all(
                                      color: Theme.of(context)
                                          .colorScheme
                                          .secondaryContainer,
                                      width: 1,
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        "ตกลง",
                                        style: TextStyle(
                                          fontFamily: 'SukhumvitSet-Bold',
                                          fontSize:
                                              mediaQuery(context, "h", 35),
                                          color: Theme.of(context)
                                              .colorScheme
                                              .onSecondary,
                                          letterSpacing:
                                              mediaQuery(context, "h", 1.2),
                                          shadows: [
                                            Shadow(
                                              color: const Color(0x26000000),
                                              offset: Offset(0, 1),
                                              blurRadius: 1,
                                            )
                                          ],
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      });
}

ErrorshowAlertDialog(context, title, message) {
  showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return Container(
          child: BackdropFilter(
            filter: ImageFilter.blur(
              sigmaX: 2.5,
              sigmaY: 2.5,
            ),
            child: AlertDialog(
              backgroundColor: Theme.of(context).colorScheme.primary,
              titlePadding: EdgeInsets.all(1.0),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(
                  Radius.circular(
                    mediaQuery(context, 'h', 90),
                  ),
                ),
              ),
              title: Container(
                width: mediaQuery(context, 'w', 650),
                height: mediaQuery(context, 'h', 450),
                decoration: BoxDecoration(
                  borderRadius:
                      BorderRadius.circular(mediaQuery(context, "h", 90)),
                  color: Theme.of(context).colorScheme.primary,
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0x1a000000),
                      offset: Offset(0, 1),
                      blurRadius: 2,
                    ),
                  ],
                ),
                child: Container(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: <Widget>[
                      Container(
                        padding: EdgeInsets.only(
                          top: mediaQuery(context, 'h', 32),
                        ),
                        child: Text(
                          title,
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Text',
                            fontSize: mediaQuery(context, "h", 30),
                            color: Theme.of(context).colorScheme.onSecondary,
                            fontWeight: FontWeight.w700,
                            letterSpacing:
                                mediaQuery(context, "h", 1.4000000000000001),
                            height: 2.0,
                          ),
                        ),
                      ),
                      Container(
                        alignment: Alignment.center,
                        padding:
                            EdgeInsets.only(top: mediaQuery(context, 'h', 20)),
                        child: Column(
                          children: [
                            Container(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    message,
                                    style: TextStyle(
                                      fontFamily: 'SukhumvitSet-Text',
                                      fontSize: mediaQuery(context, "h", 25),
                                      color: Theme.of(context)
                                          .colorScheme
                                          .onSecondary,
                                      fontWeight: FontWeight.w500,
                                      letterSpacing: mediaQuery(
                                          context, "h", 1.4000000000000001),
                                      height: 2.0,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Padding(
                              padding: EdgeInsets.only(
                                  top: mediaQuery(context, 'h', 60)),
                              child: GestureDetector(
                                onTap: () {
                                  // AppSettings.openAppSettings();
                                  Navigator.pop(context);
                                },
                                child: Container(
                                  alignment: Alignment.center,
                                  width: mediaQuery(context, "w", 500),
                                  height: mediaQuery(context, "h", 100),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(
                                        mediaQuery(context, "h", 50)),
                                    color: Theme.of(context)
                                        .colorScheme
                                        .secondaryContainer
                                        .withOpacity(0.7),
                                    border: Border.all(
                                      color: Theme.of(context)
                                          .colorScheme
                                          .secondaryContainer,
                                      width: 1,
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        "ตกลง",
                                        style: TextStyle(
                                          fontFamily: 'SukhumvitSet-Bold',
                                          fontSize:
                                              mediaQuery(context, "h", 35),
                                          color: Theme.of(context)
                                              .colorScheme
                                              .onSecondary,
                                          letterSpacing:
                                              mediaQuery(context, "h", 1.2),
                                          shadows: [
                                            Shadow(
                                              color: const Color(0x26000000),
                                              offset: Offset(0, 1),
                                              blurRadius: 1,
                                            )
                                          ],
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      });
}

LogoutAlertDialog(context, title, message) {
  CenterController centerCon = Get.find<CenterController>();

  showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return Container(
          child: BackdropFilter(
            filter: ImageFilter.blur(
              sigmaX: 2.5,
              sigmaY: 2.5,
            ),
            child: AlertDialog(
              backgroundColor: Theme.of(context).colorScheme.primary,
              titlePadding: EdgeInsets.all(1.0),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(
                  Radius.circular(
                    mediaQuery(context, 'h', 90),
                  ),
                ),
              ),
              title: Container(
                width: mediaQuery(context, 'w', 300),
                height: mediaQuery(context, 'h', 450),
                decoration: BoxDecoration(
                  borderRadius:
                      BorderRadius.circular(mediaQuery(context, "h", 90)),
                  color: Theme.of(context).colorScheme.primary,
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0x1a000000),
                      offset: Offset(0, 1),
                      blurRadius: 2,
                    ),
                  ],
                ),
                child: Container(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: <Widget>[
                      Container(
                        padding: EdgeInsets.only(
                          top: mediaQuery(context, 'h', 32),
                        ),
                        child: Text(
                          title,
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Text',
                            fontSize: mediaQuery(context, "h", 30),
                            color: Theme.of(context).colorScheme.onSecondary,
                            fontWeight: FontWeight.w700,
                            letterSpacing:
                                mediaQuery(context, "h", 1.4000000000000001),
                            height: 2.0,
                          ),
                        ),
                      ),
                      Container(
                        alignment: Alignment.center,
                        padding:
                            EdgeInsets.only(top: mediaQuery(context, 'h', 20)),
                        child: Column(
                          children: [
                            Container(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    message,
                                    style: TextStyle(
                                      fontFamily: 'SukhumvitSet-Text',
                                      fontSize: mediaQuery(context, "h", 25),
                                      color: Theme.of(context)
                                          .colorScheme
                                          .onSecondary,
                                      fontWeight: FontWeight.w500,
                                      letterSpacing: mediaQuery(
                                          context, "h", 1.4000000000000001),
                                      height: 2.0,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Padding(
                                  padding: EdgeInsets.only(
                                      top: mediaQuery(context, 'h', 60)),
                                  child: GestureDetector(
                                    onTap: () {
                                      // AppSettings.openAppSettings();
                                      Navigator.pop(context);
                                    },
                                    child: Container(
                                      alignment: Alignment.center,
                                      width: mediaQuery(context, "w", 200),
                                      height: mediaQuery(context, "h", 80),
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(
                                            mediaQuery(context, "h", 50)),
                                        color: Theme.of(context)
                                            .colorScheme
                                            .surfaceBright
                                            .withOpacity(0.4),
                                        border: Border.all(
                                          color: Theme.of(context)
                                              .colorScheme
                                              .surfaceBright,
                                          width: 1,
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Text(
                                            "ยกเลิก",
                                            style: TextStyle(
                                              fontFamily: 'SukhumvitSet-Bold',
                                              fontSize:
                                                  mediaQuery(context, "h", 35),
                                              color: Theme.of(context)
                                                  .colorScheme
                                                  .secondary,
                                              letterSpacing:
                                                  mediaQuery(context, "h", 1.2),
                                              shadows: [
                                                Shadow(
                                                  color:
                                                  Theme.of(context)
                                                      .colorScheme
                                                      .onSecondary,
                                                  offset: Offset(0, 1),
                                                  blurRadius: 1,
                                                )
                                              ],
                                            ),
                                            textAlign: TextAlign.center,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                                SizedBox(
                                  width: mediaQuery(context, 'w', 50),
                                ),
                                Padding(
                                  padding: EdgeInsets.only(
                                      top: mediaQuery(context, 'h', 60)),
                                  child: GestureDetector(
                                    onTap: () async {
                                      centerCon.logout();
                                      Get.reloadAll();
                                    },
                                    child: Container(
                                      alignment: Alignment.center,
                                      width: mediaQuery(context, "w", 200),
                                      height: mediaQuery(context, "h", 80),
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(
                                            mediaQuery(context, "h", 50)),
                                        color: Theme.of(context)
                                            .colorScheme
                                            .secondaryContainer
                                            .withOpacity(0.7),
                                        border: Border.all(
                                          color: Theme.of(context)
                                              .colorScheme
                                              .secondaryContainer,
                                          width: 1,
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Text(
                                            "ตกลง",
                                            style: TextStyle(
                                              fontFamily: 'SukhumvitSet-Bold',
                                              fontSize:
                                                  mediaQuery(context, "h", 35),
                                              color: Theme.of(context)
                                                  .colorScheme
                                                  .onSecondary,
                                              letterSpacing:
                                                  mediaQuery(context, "h", 1.2),
                                              shadows: [
                                                Shadow(
                                                  color:
                                                      const Color(0x26000000),
                                                  offset: Offset(0, 1),
                                                  blurRadius: 1,
                                                )
                                              ],
                                            ),
                                            textAlign: TextAlign.center,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      });
}

showTopSnackBar(BuildContext context, String message) {
  final overlay = Overlay.of(context);
  final overlayEntry = OverlayEntry(
    builder: (context) => Positioned(
      top: 50, // Adjust the position (distance from top of the screen)
      left: 20,
      right: 20,
      child: Material(
        color: Colors.transparent,
        child: Container(
          padding: EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.teal.shade400,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black26,
                blurRadius: 10,
                spreadRadius: 1,
              ),
            ],
          ),
          child: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.greenAccent),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  message,
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    ),
  );
}

buildShow(BuildContext context, TextEditingController controller,
    Function insertTimeProcessLate) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true, // เพื่อให้เลื่อนขึ้นตามแป้นพิมพ์
    builder: (BuildContext context) {
      return Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context)
              .viewInsets
              .bottom, // ทำให้เลื่อนขึ้นตามแป้นพิมพ์
        ),
        child: Container(
          padding: EdgeInsets.only(
            left: 20.w,
            right: 20.w,
          ),
          height: 396.h,
          width: 392.w,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(10.r),
              topRight: Radius.circular(10.r),
            ),
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.85),
                offset: const Offset(0, 4),
                blurRadius: 10,
                spreadRadius: 0,
              ),
            ],
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Theme.of(context).colorScheme.primary,
                Theme.of(context).colorScheme.onPrimary,
              ],
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // ... Your current UI
              AppWidget.normalText(
                context,
                'ช่วยบอกเราหน่อย'.tr,
                16.sp,
                Theme.of(context).colorScheme.onSecondary,
                FontWeight.w400,
              ),
              AppWidget.normalText(
                context,
                'ว่าทำไมถึงมาทำงานสาย'.tr,
                16.sp,
                Theme.of(context).colorScheme.onSecondary,
                FontWeight.w400,
              ),
              SizedBox(
                height: 15.h,
              ),
              Image.asset(
                Get.isDarkMode
                    ? "assets/ADD/Cause_lateness_D.png"
                    : "assets/ADD/Cause_lateness_B.png",
                height: mediaQuery(context, "h", 125),
                fit: BoxFit.fitHeight,
              ),
              SizedBox(
                height: 5.h,
              ),
              TextField(
                controller: controller,
                cursorColor: Colors.black,

                // obscureText: true,
                decoration: InputDecoration(
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(
                        color: Theme.of(context)
                            .colorScheme
                            .onSecondary
                            .withOpacity(0.5)),
                  ),
                  focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(
                        color: Theme.of(context)
                            .colorScheme
                            .onSecondary
                            .withOpacity(0.5)),
                  ),
                  hintText: 'ใส่เหตุผลในการมาสาย'.tr,
                  hintStyle: TextStyle(
                    fontFamily: 'SukhumvitSet-Text',
                    fontSize: 12.sp,
                    color: Theme.of(context).colorScheme.scrim,
                    fontWeight: FontWeight.w400,
                  ),
                  contentPadding: EdgeInsets.symmetric(vertical: 16.h),
                  // Adjust as needed
                  alignLabelWithHint:
                      true, // Align the label (hintText) with the input text
                ),
                style: TextStyle(
                  fontFamily: 'SukhumvitSet-Text',
                  fontSize: 14.sp,
                  color: Theme.of(context).colorScheme.scrim,
                  fontWeight: FontWeight.w400,
                ),
                textAlign: TextAlign.left, // Center the input text
              ),
              Container(
                padding: EdgeInsets.only(
                  top: 50.h,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Center(
                      child: InkWell(
                        onTap: () {
                          String causeLateness = controller.text;
                          insertTimeProcessLate(context, causeLateness);
                          Get.back();
                        },
                        child: Container(
                            height: 45.h,
                            width: 250.w,
                            decoration: BoxDecoration(
                              borderRadius:
                                  const BorderRadius.all(Radius.circular(20)),
                              color: Theme.of(context)
                                  .colorScheme
                                  .secondaryContainer
                                  .withOpacity(0.7),
                              border: Border.all(
                                width: 1.w,
                                color: Theme.of(context)
                                    .colorScheme
                                    .secondaryContainer,
                              ),
                            ),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                AppWidget.normalText(
                                  context,
                                  'btn_comfirm'.tr,
                                  16.sp,
                                  Theme.of(context).colorScheme.onSecondary,
                                  FontWeight.w600,
                                ),
                              ],
                            )),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    },
  );
}

String? _selectedOption;

Widget buildRadioOption(String value, BuildContext context) {
  return RadioListTile<String>(
    value: value,
    groupValue: _selectedOption,
    onChanged: (String? newValue) {
      _selectedOption = _selectedOption;
    },
    title: AppWidget.normalText(
      context,
      value,
      16.sp,
      Theme.of(context).colorScheme.onSecondary,
      FontWeight.w400,
    ),
    activeColor: Color(0xffFFDD77),
  );
}

buildShowQuery(
    BuildContext context,
    TextEditingController controller,
    Function updateTimeProcess,
    String question,
    String choicesA,
    String choicesB,
    String choicesC,
    String answer,
    String runID,
    int status1,
    int status2,
    int status3) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    builder: (BuildContext context) {
      String selectedChoice = ""; // ตัวแปรเก็บคำตอบที่เลือก

      return StatefulBuilder(
        builder: (context, setState) {
          return Padding(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
            ),
            child: Container(
              padding: EdgeInsets.only(left: 20.w, right: 20.w, top: 50.h),
              height: 430.h,
              width: 392.w,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(10.r),
                  topRight: Radius.circular(10.r),
                ),
                boxShadow: [
                  BoxShadow(
                    color:
                        Theme.of(context).colorScheme.primary.withOpacity(0.85),
                    offset: const Offset(0, 4),
                    blurRadius: 10,
                    spreadRadius: 0,
                  ),
                ],
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Theme.of(context).colorScheme.primary,
                    Theme.of(context).colorScheme.onPrimary,
                  ],
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  AppWidget.normalText(
                    context,
                    'ชวนเล่นตอบคำถาม ก่อนเลิกงาน'.tr,
                    16.sp,
                    Theme.of(context).colorScheme.onSecondary,
                    FontWeight.w700,
                  ),
                  SizedBox(height: 15.h),
                  Image.asset(
                    Get.isDarkMode
                        ? "assets/question_D.png"
                        : "assets/question_B.png",
                    height: 50.h,
                    width: 53.w,
                    fit: BoxFit.fitHeight,
                  ),
                  SizedBox(height: 15.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      AppWidget.normalText(
                        context,
                        'คำถาม:'.tr,
                        14.sp,
                        Theme.of(context).colorScheme.onSecondary,
                        FontWeight.w700,
                      ),
                      SizedBox(width: 5.w),
                      AppWidget.normalText(
                        context,
                        question,
                        14.sp,
                        Theme.of(context).colorScheme.onSecondary,
                        FontWeight.w500,
                      ),
                    ],
                  ),
                  SizedBox(height: 15.h),
                  // ✅ ใช้ RadioListTile เพื่อให้ผู้ใช้เลือกคำตอบ
                  Column(
                    children: [
                      Container(
                        margin: EdgeInsets.only(left: 30.w),
                        child: InkWell(
                          onTap: () {
                            setState(() {
                              selectedChoice = choicesA;
                            });
                          },
                          child: Row(
                            children: [
                              Image.asset(
                                selectedChoice == choicesA
                                    ? 'assets/Checkmark.png'
                                    : 'assets/Ellipse146.png',
                                height: 20.h,
                                width: 20.w,
                                // color: selectedChoice == choicesB  ? Colors.green : Colors.grey,
                              ),
                              SizedBox(width: 10.w),
                              AppWidget.normalText(
                                context,
                                choicesA,
                                14.sp,
                                Theme.of(context).colorScheme.onSecondary,
                                FontWeight.w400,
                              ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(width: 320.w, child: Liner(context)),
                      Container(
                        margin: EdgeInsets.only(left: 30.w),
                        child: InkWell(
                          onTap: () {
                            setState(() {
                              selectedChoice = choicesB;
                            });
                          },
                          child: Row(
                            children: [
                              Image.asset(
                                selectedChoice == choicesB
                                    ? 'assets/Checkmark.png'
                                    : 'assets/Ellipse146.png',
                                height: 20.h,
                                width: 20.w,
                                // color: selectedChoice == choicesB  ? Colors.green : Colors.grey,
                              ),
                              SizedBox(width: 10.w),
                              AppWidget.normalText(
                                context,
                                choicesB,
                                14.sp,
                                Theme.of(context).colorScheme.onSecondary,
                                FontWeight.w400,
                              ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(width: 320.w, child: Liner(context)),
                      Container(
                        margin: EdgeInsets.only(left: 30.w),
                        child: InkWell(
                          onTap: () {
                            setState(() {
                              selectedChoice = choicesC;
                            });
                          },
                          child: Row(
                            children: [
                              Image.asset(
                                selectedChoice == choicesC
                                    ? 'assets/Checkmark.png'
                                    : 'assets/Ellipse146.png',
                                height: 20.h,
                                width: 20.w,
                                // color: selectedChoice == choicesB  ? Colors.green : Colors.grey,
                              ),
                              SizedBox(width: 10.w),
                              AppWidget.normalText(
                                context,
                                choicesC,
                                14.sp,
                                Theme.of(context).colorScheme.onSecondary,
                                FontWeight.w400,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 30.h),
                  Center(
                    child: InkWell(
                      onTap: () {
                        Get.back();
                        if (selectedChoice.isNotEmpty) {
                          if (answer == selectedChoice) {
                            updateTimeProcess();
                          } else {
                            Get.snackbar(
                              "แจ้งเตือน",
                              "คำตอบไม่ถูกต้อง ลองใหม่อีกครั้ง",
                              backgroundColor: Colors.redAccent,
                              colorText: Colors.white,
                            );
                            // Get.back();
                          }
                        } else {
                          Get.snackbar(
                            "แจ้งเตือน",
                            "กรุณาเลือกคำตอบก่อนกดปุ่มยืนยัน",
                            backgroundColor: Colors.redAccent,
                            colorText: Colors.white,
                          );
                        }
                      },
                      child: Container(
                        height: 46.h,
                        width: 235.w,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.all(Radius.circular(20)),
                          color: Theme.of(context)
                              .colorScheme
                              .secondaryContainer
                              .withOpacity(0.7),
                          border: Border.all(
                            width: 1.w,
                            color: Theme.of(context)
                                .colorScheme
                                .secondaryContainer,
                          ),
                        ),
                        child: Center(
                          child: AppWidget.normalText(
                            context,
                            'btn_ok'.tr,
                            16.sp,
                            Theme.of(context).colorScheme.onSecondary,
                            FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      );
    },
  );
}

showAlertDialogQR(context, imgDark, imgBright, message) {
  showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return Container(
          child: BackdropFilter(
            filter: ImageFilter.blur(
              sigmaX: 2.5,
              sigmaY: 2.5,
            ),
            child: AlertDialog(
              backgroundColor: Theme.of(context).colorScheme.primary,
              titlePadding: EdgeInsets.all(1.0),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(
                  Radius.circular(
                    mediaQuery(context, 'h', 90),
                  ),
                ),
              ),
              title: Container(
                width: mediaQuery(context, 'w', 650),
                height: mediaQuery(context, 'h', 650),
                decoration: BoxDecoration(
                  borderRadius:
                      BorderRadius.circular(mediaQuery(context, "h", 90)),
                  color: Theme.of(context).colorScheme.primary,
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0x1a000000),
                      offset: Offset(0, 1),
                      blurRadius: 2,
                    ),
                  ],
                ),
                child: Container(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: <Widget>[
                      Container(
                        padding: EdgeInsets.only(
                          top: mediaQuery(context, 'h', 32),
                        ),
                        child: Text(
                          'เรียบร้อย',
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Text',
                            fontSize: mediaQuery(context, "h", 30),
                            color: Theme.of(context).colorScheme.onSecondary,
                            fontWeight: FontWeight.w700,
                            letterSpacing:
                                mediaQuery(context, "h", 1.4000000000000001),
                            height: 2.0,
                          ),
                        ),
                      ),
                      Container(
                        child: Image(
                          image:
                              AssetImage(Get.isDarkMode ? imgDark : imgBright),
                          width: mediaQuery(context, 'w', 130),
                          height: mediaQuery(context, 'h', 130),
                        ),
                      ),
                      Container(
                        alignment: Alignment.center,
                        padding:
                            EdgeInsets.only(top: mediaQuery(context, 'h', 20)),
                        child: Column(
                          children: [
                            Container(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    message,
                                    style: TextStyle(
                                      fontFamily: 'SukhumvitSet-Text',
                                      fontSize: mediaQuery(context, "h", 25),
                                      color: Theme.of(context)
                                          .colorScheme
                                          .onSecondary,
                                      fontWeight: FontWeight.w500,
                                      letterSpacing: mediaQuery(
                                          context, "h", 1.4000000000000001),
                                      height: 2.0,
                                    ), // เปิดให้ตัดบรรทัดใหม่เมื่อข้อความยาวเกิน
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            ),
                            Padding(
                              padding: EdgeInsets.only(
                                  top: mediaQuery(context, 'h', 50)),
                              child: GestureDetector(
                                onTap: () {
                                  // AppSettings.openAppSettings();
                                  Navigator.pop(context);
                                },
                                child: Container(
                                  alignment: Alignment.center,
                                  width: mediaQuery(context, "w", 500),
                                  height: mediaQuery(context, "h", 100),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(
                                        mediaQuery(context, "h", 50)),
                                    color: Theme.of(context)
                                        .colorScheme
                                        .secondaryContainer
                                        .withOpacity(0.7),
                                    border: Border.all(
                                      color: Theme.of(context)
                                          .colorScheme
                                          .secondaryContainer,
                                      width: 1,
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        "ตกลง",
                                        style: TextStyle(
                                          fontFamily: 'SukhumvitSet-Bold',
                                          fontSize:
                                              mediaQuery(context, "h", 35),
                                          color: Theme.of(context)
                                              .colorScheme
                                              .onSecondary,
                                          letterSpacing:
                                              mediaQuery(context, "h", 1.2),
                                          shadows: [
                                            Shadow(
                                              color: const Color(0x26000000),
                                              offset: Offset(0, 1),
                                              blurRadius: 1,
                                            )
                                          ],
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      });
}

logout(context) async {
  Get.offAllNamed('login');
  GetStorage().remove('usernameLogin');
  GetStorage().remove('passwordLogin');
  GetStorage().remove('stepPin');
  GetStorage().remove('pinCode');
  Get.delete<authController>();
  Get.delete<OtpController>();
  Get.delete<ProfileController>();
  Get.delete<PinController>();
  Get.delete<BenefitController>();
  Get.delete<BitrixController>();
  Get.delete<CewController>();
  Get.delete<DaysWorkController>();
  Get.delete<NDAcontroller>();
  Get.delete<HealthController>();
  Get.delete<LeaveAndAppController>();
  Get.delete<NotiController>();
  Get.delete<Tacontroller>();
  Get.delete<TimeController>();
  Get.delete<WealthController>();
  Get.delete<WorkIssuseController>();
  Get.delete<NotifyController>();
  Get.delete<CenterController>();
  Get.delete<QrScanController>();
  Get.reset();
}

Widget buildDropdown(
  BuildContext context,
  RxString title,
  List<RxString> items,
  void Function(String value) updateSelectedCEWType,
  RxString selectedValue, // New parameter for storing the selected value
) {
  return Container(
    margin: EdgeInsets.only(left: 20.w, right: 20.w),
    height: 55.h,
    decoration: BoxDecoration(
      color: Theme.of(context).colorScheme.primary,
      borderRadius: BorderRadius.circular(8.r),
      border: Border.all(
        color: Colors.grey.shade300,
        width: 1.0.w,
      ),
    ),
    child: Container(
      margin: EdgeInsets.only(left: 10.w),
      child: DropdownButton<String>(
        borderRadius: BorderRadius.circular(8.r),
        dropdownColor: Theme.of(context).colorScheme.primary,
        hint: Row(
          children: [
            SizedBox(width: 10.w),
            Image.asset(
              'assets/ADD/Stick-note.png',
              width: 24,
              height: 24,
            ),
            SizedBox(width: 10.w),
            Expanded(
              // ใช้ Expanded เพื่อให้ข้อความขยายและไม่ล้น
              child: AppWidget.normalTextRX(
                context,
                selectedValue.isNotEmpty ? selectedValue : title,
                14.sp,
                Theme.of(context).colorScheme.onSecondary,
                FontWeight.w400,
              ),
            ),
          ],
        ),
        isExpanded: true,
        underline: const SizedBox(),
        icon: Container(
          margin: EdgeInsets.only(right: 15.w),
          child: SizedBox(
            width: 24.w,
            height: 24.h,
            child: Image.asset('assets/ADD/Expand_down_light.png',
                color: Theme.of(context).colorScheme.onSecondary),
          ),
        ),
        style: TextStyle(
          fontFamily: 'SukhumvitSet-Text',
          fontSize: 15.sp,
          color: Theme.of(context).colorScheme.onSecondary,
          fontWeight: FontWeight.w400,
        ),
        onChanged: (String? value) {
          // Store selected value

          updateSelectedCEWType(value!);
          // Handle dropdown value change
        },
        items: items.map((RxString rxString) {
          String stringValue =
              rxString.value; // Extract the string value from RxString
          return DropdownMenuItem<String>(
            value: stringValue,
            child: AppWidget.normalTextRX(
              context,
              stringValue.obs,
              14.sp,
              Theme.of(context).colorScheme.onSecondary,
              FontWeight.w400,
            ),
          );
        }).toList(),
      ),
    ),
  );
}

insertOpenMenu(menuOpen) async {
  SharedPreferences prefs = await SharedPreferences.getInstance();
  var id = await prefs.getString('id');
  var fname = await prefs.getString('fname');
  var lname = await prefs.getString('lname');

  String url =
      'https://mhmo3nnbr5.execute-api.ap-southeast-1.amazonaws.com/latest/insertOpenMenu';
  Map map = {
    "menuOpen": menuOpen.toString(),
    "id": id.toString(),
    "fname": fname.toString(),
    "lname": lname.toString()
  };

  final response = await apiRequest(url, map);
  var jsonResponse = json.decode(response);
  if (jsonResponse["status"] == "AddSuccess") {
    print('add open menu success!');
  } else {
    print('No add open menu!');
  }
}

openAppLikeWallet() async {
  String os = Platform.operatingSystem;

  if (os == "ios") {
    try {
      await launch("likewallet://");
    } on PlatformException catch (e) {
      OpenStore.instance.open(
          androidAppBundleId: "likewallet.likewallet",
          appStoreId: "1492241404");
    } finally {
      await launch("likewallet://");
      OpenStore.instance.open(
          androidAppBundleId: "likewallet.likewallet",
          appStoreId: "1492241404");
    }
  } else if (os == "android") {
    try {} catch (e) {
      const url =
          'https://play.google.com/store/apps/details?id=likewallet.likewallet&hl=en';
      if (await canLaunch(url)) {
        await launch(url);
      } else {
        throw 'Could not launch $url';
      }
    }
  } else {
    Fluttertoast.showToast(
        msg: "device not support",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.TOP,
        backgroundColor: Colors.red,
        textColor: Colors.black,
        fontSize: 16.0);
  }
}

openAppldx() async {
  SharedPreferences prefs = await SharedPreferences.getInstance();
  var phone_like = await prefs.getString('phone_like');
  var codePhone = phone_like!.substring(0, 3);
  var Phone = phone_like.substring(3, phone_like.length);

  String os = Platform.operatingSystem;

  if (phone_like != "" && phone_like != null && phone_like != "null") {
    if (os == "ios") {
      try {
        await launch(
            "ldx://topage/loginautofromapp/${codePhone.toString()}/${Phone.toString()}");
      } on PlatformException catch (e) {
        OpenStore.instance.open(
            androidAppBundleId: "com.prachakij.ldx", appStoreId: "1504852835");
      } finally {
        await launch("ldx://");
        OpenStore.instance.open(
            androidAppBundleId: "com.prachakij.ldx", appStoreId: "1504852835");
      }
    } else if (os == "android") {
      try {
        await launch(
            "https://LDXtopage/loginautofromapp/${codePhone.toString()}/${Phone.toString()}");
      } catch (e) {
        const url =
            'https://play.google.com/store/apps/details?id=com.prachakij.ldx';
        if (await canLaunch(url)) {
          await launch(url);
        } else {
          throw 'Could not launch $url';
        }
      }
    } else {
      Fluttertoast.showToast(
          msg: "device not support",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.TOP,
          backgroundColor: Colors.red,
          textColor: Colors.black,
          fontSize: 16.0);
    }
  } else {
    Fluttertoast.showToast(
        msg: "phone is null",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.TOP,
        backgroundColor: Colors.red,
        textColor: Colors.black,
        fontSize: 16.0);
  }
}

openAppldxold() async {
  String os = Platform.operatingSystem;

  if (os == "ios") {
    try {
      await launch("ldx://");
    } on PlatformException catch (e) {
      OpenStore.instance.open(
          androidAppBundleId: "com.prachakij.ldx", appStoreId: "1504852835");
    } finally {
      await launch("ldx://");
      (androidAppId: "com.prachakij.ldx", iOSAppId: "1504852835");
//        launch("https://apps.apple.com/us/app/id1504852835");
    }
  } else if (os == "android") {
    try {
      // await AppAvailability.launchApp("com.prachakij.ldx");
    } catch (e) {
      const url =
          'https://play.google.com/store/apps/details?id=com.prachakij.ldx';
      if (await canLaunch(url)) {
        await launch(url);
      } else {
        throw 'Could not launch $url';
      }
    }
  } else {
    Fluttertoast.showToast(
        msg: "device not support",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.TOP,
        backgroundColor: Colors.red,
        textColor: Colors.black,
        fontSize: 16.0);
  }
}

openAppPMS() async {
  String os = Platform.operatingSystem;

  if (os == "ios") {
    try {
      await launch("pms://");
    } on PlatformException catch (e) {
      OpenStore.instance.open(
          androidAppBundleId: "com.prachakij.pms_app",
          appStoreId: "1507877291");
    } finally {
      await launch("pms://");
      OpenStore.instance.open(
          androidAppBundleId: "com.prachakij.pms_app",
          appStoreId: "1507877291");
    }
  } else if (os == "android") {
    try {} catch (e) {
      const url =
          'https://play.google.com/store/apps/details?id=com.prachakij.pms_app';
      if (await canLaunch(url)) {
        await launch(url);
      } else {
        throw 'Could not launch $url';
      }
    }
  } else {
    Fluttertoast.showToast(
        msg: "device not support",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.TOP,
        backgroundColor: Colors.red,
        textColor: Colors.black,
        fontSize: 16.0);
  }
}

openAppCS24() async {
  String os = Platform.operatingSystem;

  if (os == "ios") {
    try {
      await launch("cs24://");
    } on PlatformException catch (e) {
      OpenStore.instance
          .open(androidAppBundleId: "ags.cs24", appStoreId: "1504613490");
    } finally {
      await launch("cs24://");
      OpenStore.instance
          .open(androidAppBundleId: "ags.cs24", appStoreId: "1504613490");
    }
  } else if (os == "android") {
    try {} catch (e) {
      const url =
          'https://play.google.com/store/apps/details?id=com.prachakij.cs24';
      if (await canLaunch(url)) {
        await launch(url);
      } else {
        throw 'Could not launch $url';
      }
    }
  } else {
    Fluttertoast.showToast(
        msg: "device not support",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.TOP,
        backgroundColor: Colors.red,
        textColor: Colors.black,
        fontSize: 16.0);
  }
}

openAppCS24autoLogin() async {
  SharedPreferences prefs = await SharedPreferences.getInstance();
  var userName = await prefs.getString('userName');
  var Password = await prefs.getString('Password');

  String os = Platform.operatingSystem;

  if (userName != "" && userName != null && userName != "null") {
    if (os == "ios") {
      try {
        await launch(
            "cs24://topage/loginautofromapp/${userName.toString()}/${Password.toString()}");
      } on PlatformException catch (e) {
        OpenStore.instance.open(
            androidAppBundleId: "com.ags.cs24App", appStoreId: "1504613490");
      } finally {
        await launch("cs24://");
        OpenStore.instance.open(
            androidAppBundleId: "com.ags.cs24App", appStoreId: "1504613490");
      }
    } else if (os == "android") {
      try {
        await launch(
            "https://CS24topage/loginautofromapp/${userName.toString()}/${Password.toString()}");
      } catch (e) {
        const url =
            'https://play.google.com/store/apps/details?id=com.prachakij.cs24';
        if (await canLaunch(url)) {
          await launch(url);
        } else {
          throw 'Could not launch $url';
        }
      }
    } else {
      Fluttertoast.showToast(
          msg: "device not support",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.TOP,
          backgroundColor: Colors.red,
          textColor: Colors.black,
          fontSize: 16.0);
    }
  } else {
    Fluttertoast.showToast(
        msg: "username is null",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.TOP,
        backgroundColor: Colors.red,
        textColor: Colors.black,
        fontSize: 16.0);
  }
}

openAppAAMServiceautoLogin() async {
  SharedPreferences prefs = await SharedPreferences.getInstance();
  var userName = await prefs.getString('userName');
  var Password = await prefs.getString('Password');

  String os = Platform.operatingSystem;

  if (userName != "" && userName != null && userName != "null") {
    if (os == "ios") {
      try {
        await launch(
            "AAMService://topage/loginautofromapp/${userName.toString()}/${Password.toString()}");
      } on PlatformException catch (e) {
        await launch("https://testflight.apple.com/join/XaL7muMx");
      } finally {
        await launch("AAMService://");
      }
    } else if (os == "android") {
      try {
        // await AppAvailability.launchApp("com.prachakij.cs24");
        await launch(
            "https://AAMServicetopage/loginautofromapp/${userName.toString()}/${Password.toString()}");
      } catch (e) {
        const url =
            'https://play.google.com/store/apps/details?id=com.prachakij.aam_service';
        if (await canLaunch(url)) {
          await launch(url);
        } else {
          throw 'Could not launch $url';
        }
      }
    } else {
      Fluttertoast.showToast(
          msg: "device not support",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.TOP,
          backgroundColor: Colors.red,
          textColor: Colors.black,
          fontSize: 16.0);
    }
  } else {
    Fluttertoast.showToast(
        msg: "username is null",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.TOP,
        backgroundColor: Colors.red,
        textColor: Colors.black,
        fontSize: 16.0);
  }
}

openAppPMSServiceautoLogin() async {
  SharedPreferences prefs = await SharedPreferences.getInstance();
  var userName = await prefs.getString('userName');
  var Password = await prefs.getString('Password');

  String os = Platform.operatingSystem;

  if (userName != "" && userName != null && userName != "null") {
    if (os == "ios") {
      try {
        await launch("https://mapp-pms-service.web.app/");
      } on PlatformException catch (e) {
        await launch("https://testflight.apple.com/join/KclETx9g");
      } finally {
        await launch("PMSService://");
      }
    } else if (os == "android") {
      try {
        // await AppAvailability.launchApp("com.prachakij.cs24");
        await launch("https://mapp-pms-service.web.app/");
      } catch (e) {
        const url =
            'https://play.google.com/store/apps/details?id=com.prachakij.pmsservice';
        if (await canLaunch(url)) {
          await launch(url);
        } else {
          throw 'Could not launch $url';
        }
      }
    } else {
      Fluttertoast.showToast(
          msg: "device not support",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.TOP,
          backgroundColor: Colors.red,
          textColor: Colors.black,
          fontSize: 16.0);
    }
  } else {
    Fluttertoast.showToast(
        msg: "username is null",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.TOP,
        backgroundColor: Colors.red,
        textColor: Colors.black,
        fontSize: 16.0);
  }
}

void openAppAAM() async {
  String os = Platform.operatingSystem;

  if (os == "ios") {
    try {
      await launch("aam://");
    } on PlatformException catch (e) {
      OpenStore.instance.open(
        androidAppBundleId: "com.aamfinancegroup.aam",
        appStoreId: "1521965273",
      );
    }
  } else if (os == "android") {
    try {} catch (e) {
      // If the app is not installed, open it in the Play Store
      const url =
          'https://play.google.com/store/apps/details?id=com.aamfinancegroup.aam';
      if (await canLaunch(url)) {
        await launch(url);
      } else {
        throw 'Could not launch $url';
      }
    }
  } else {
    Fluttertoast.showToast(
      msg: "Device not supported",
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.TOP,
      backgroundColor: Colors.red,
      textColor: Colors.black,
      fontSize: 16.0,
    );
  }
}

openAppRafco() async {
  String os = Platform.operatingSystem;

  if (os == "ios") {
    try {
      await launch("rafco://");
    } on PlatformException catch (e) {
      OpenStore.instance
          .open(androidAppBundleId: "com.rptn.rafco", appStoreId: "1529562321");
    } finally {
      await launch("rafco://");
      OpenStore.instance
          .open(androidAppBundleId: "com.rptn.rafco", appStoreId: "1529562321");
    }
  } else if (os == "android") {
    try {} catch (e) {
      const url =
          'https://play.google.com/store/apps/details?id=com.rptn.rafco';
      if (await canLaunch(url)) {
        await launch(url);
      } else {
        throw 'Could not launch $url';
      }
    }
  } else {
    Fluttertoast.showToast(
        msg: "device not support",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.TOP,
        backgroundColor: Colors.red,
        textColor: Colors.black,
        fontSize: 16.0);
  }
}

openAppOnepay() async {
  String os = Platform.operatingSystem;

  if (os == "ios") {
    try {
      await launch(
          "onepay://qr/00020101021233560004BCEL0106ONEPAY0216mch5c2f0404102fb031420210215194828520457325303418540115802LA6003VTE62840113AGS98765432100304test0515docjc28kl6a03a30719SVM200OPF01366304770813AGS12345678906304AD7C");
    } on PlatformException catch (e) {
      OpenStore.instance.open(
          androidAppBundleId: "com.bcel.bcelone", appStoreId: "654946527");
    } finally {
      await launch(
          "onepay://qr/00020101021233560004BCEL0106ONEPAY0216mch5c2f0404102fb031420210215194828520457325303418540115802LA6003VTE62840113AGS98765432100304test0515docjc28kl6a03a30719SVM200OPF01366304770813AGS12345678906304AD7C");
      OpenStore.instance.open(
          androidAppBundleId: "com.bcel.bcelone", appStoreId: "654946527");
    }
  } else if (os == "android") {
    try {
      const url =
          'onepay://qr/00020101021233560004BCEL0106ONEPAY0216mch5c2f0404102fb031420210215194828520457325303418540115802LA6003VTE62840113AGS98765432100304test0515docjc28kl6a03a30719SVM200OPF01366304770813AGS12345678906304AD7C';
      if (await canLaunch(url)) {
        await launch(url);
      } else {
        throw 'Could not launch $url';
      }
    } catch (e) {
      const url =
          'https://play.google.com/store/apps/details?id=com.bcel.bcelone';
      if (await canLaunch(url)) {
        await launch(url);
      } else {
        throw 'Could not launch $url';
      }
    }
  } else {
    Fluttertoast.showToast(
        msg: "device not support",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.TOP,
        backgroundColor: Colors.red,
        textColor: Colors.black,
        fontSize: 16.0);
  }
}

openAppRPLC() async {
  String os = Platform.operatingSystem;

  if (os == "ios") {
    try {
      await launch("rplcapp://");
    } on PlatformException catch (e) {
      OpenStore.instance.open(
          androidAppBundleId: "com.ruampattanaleasing.rplcapp",
          appStoreId: "1541438944");
    } finally {
      await launch("rplcapp://");
      OpenStore.instance.open(
          androidAppBundleId: "com.ruampattanaleasing.rplcapp",
          appStoreId: "1541438944");
    }
  } else if (os == "android") {
    try {} catch (e) {
      const url =
          'https://play.google.com/store/apps/details?id=com.ruampattanaleasing.rplc_app';
      if (await canLaunch(url)) {
        await launch(url);
      } else {
        throw 'Could not launch $url';
      }
    }
  } else {
    Fluttertoast.showToast(
        msg: "device not support",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.TOP,
        backgroundColor: Colors.red,
        textColor: Colors.black,
        fontSize: 16.0);
  }
}

sendNotificationByID(
    selectNoti,
    id,
    email,
    title,
    bodymessage,
    activity,
    link,
    link2,
    link3,
    link4,
    link5,
    link6,
    link7,
    link8,
    link9,
    link10,
    image) async {
  String url =
      'https://mhmo3nnbr5.execute-api.ap-southeast-1.amazonaws.com/latest';
  Map map = {
    "menu": "sendNotifyByID",
    "selectNoti": selectNoti.toString(),
    "id": id.toString(),
    "email": email.toString(),
    "title": title.toString(),
    "bodymessage": bodymessage.toString(),
    "activity": activity.toString(),
    "link": link.toString(),
    "link2": link2.toString(),
    "link3": link3.toString(),
    "link4": link4.toString(),
    "link5": link5.toString(),
    "link6": link6.toString(),
    "link7": link7.toString(),
    "link8": link8.toString(),
    "link9": link9.toString(),
    "link10": link10.toString(),
    "image": image.toString()
  };

  final response = await apiRequest(url, map);
  var jsonResponse = json.decode(response);
  if (jsonResponse["statusCode"].toString() == "200") {
    print("ส่ง notification แล้ว");
  } else {
    Fluttertoast.showToast(
        msg: jsonResponse["msg"].toString(),
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.TOP,
        backgroundColor: Colors.red,
        textColor: Colors.black,
        fontSize: 16.0);
  }
}

sentlinenotifyByID(id, message) async {
  String url =
      'https://mhmo3nnbr5.execute-api.ap-southeast-1.amazonaws.com/latest';
  Map map = {
    "menu": "sentlinenotifyByID",
    "id": id.toString(),
    "message": message.toString()
  };

  final response = await apiRequest(url, map);
  var jsonResponse = json.decode(response);
  if (jsonResponse["statusCode"].toString() == "200") {
    print("ส่ง line notify แล้ว");
  } else {
    Fluttertoast.showToast(
        msg: jsonResponse["msg"].toString(),
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.TOP,
        backgroundColor: Colors.red,
        textColor: Colors.black,
        fontSize: 16.0);
  }
}

Future<String> getRequestPHP(String url) async {
  HttpClient httpClient = HttpClient();
  HttpClientRequest request = await httpClient.getUrl(Uri.parse(url));
  HttpClientResponse response = await request.close();
  String reply = await response.transform(utf8.decoder).join();
  httpClient.close();
  return reply;
}

postNormal(String url, Map jsonMap) async {
  HttpClient httpClient = new HttpClient();
  HttpClientRequest request = await httpClient.postUrl(Uri.parse(url));
  request.headers.set('content-type', 'application/json');
  request.add(utf8.encode(json.encode(jsonMap)));

  HttpClientResponse response = await request.close();
  String reply = await response.transform(utf8.decoder).join();
  httpClient.close();

  return json.decode(reply);
}

postNormalWeb(String url, Map jsonMap) async {
  var urlApi = Uri.parse(url);
  Map<String, String> requestHeaders = {'Content-Type': 'application/json'};
  var response = await http.post(urlApi,
      headers: requestHeaders, body: jsonEncode(jsonMap));
  var res = json.decode(response.body);
  return res;
}

postNormalWeb2(String url, Map jsonMap) async {
  var headers = {
    'x-api-key': 'hjwsduripfksefghtrwdmngjtieowpsskfgtriuyt',
    'Content-Type': 'application/json'
  };
  var request = http.Request('POST', Uri.parse(url));
  request.body = json.encode(jsonMap);
  request.headers.addAll(headers);

  http.StreamedResponse response = await request.send();

  if (response.statusCode == 200) {
    print(await response.stream.bytesToString());
  } else {
    print(response.reasonPhrase);
  }
}

getNormalWeb(String url) async {
  var urlApi = Uri.parse(url);
  Map<String, String> requestHeaders = {
    'Content-Type': 'application/json',
    'x-api-key': 'hjwsduripfksefghtrwdmngjtieowpsskfgtriuyt'
  };
  var response = await http.get(urlApi, headers: requestHeaders);
  var res = json.decode(response.body);
  return res;
}

Future<String> apiRequest(String url, Map jsonMap) async {
  try {
    HttpClient httpClient = new HttpClient();
    HttpClientRequest request = await httpClient.postUrl(Uri.parse(url));
    request.headers.set('content-type', 'application/json');
    request.headers
        .set('x-api-key', 'hjwsduripfksefghtrwdmngjtieowpsskfgtriuyt');
    request.add(utf8.encode(json.encode(jsonMap)));
    HttpClientResponse response = await request.close();
    // todo - you should check the response.statusCode
    String reply = await response.transform(utf8.decoder).join();
    httpClient.close();
    return reply;
  } catch (e) {
    return e.toString();
  }
}

Future<String> apiRequestForTG(String url, Map jsonMap) async {
  HttpClient httpClient = new HttpClient();
  HttpClientRequest request = await httpClient.postUrl(Uri.parse(url));
  request.headers.set('content-type', 'application/json');
  request.headers
      .set('CF-Access-Client-Id', 'e3e45416e461a876d0a4be2301712453.access');
  request.headers.set('CF-Access-Client-Secret',
      '4c41a70209b80ec42d04e6bf194373b4a413425f69429054ae5636b4b7ae00cf');
  request.add(utf8.encode(json.encode(jsonMap)));
  HttpClientResponse response = await request.close();
  // todo - you should check the response.statusCode
  String reply = await response.transform(utf8.decoder).join();
  httpClient.close();
  return reply;
}

Future<String> apiRequest2(String url, List jsonMap) async {
  if (kIsWeb) {
    // สำหรับ Web ใช้ http package
    try {
      final response = await http.post(
        Uri.parse(url),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(jsonMap),
      );

      if (response.statusCode == 200) {
        return response.body;
      } else {
        throw Exception('Failed to load data from Web: ${response.statusCode}');
      }
    } catch (e) {
      throw e;
    }
  } else {
    // สำหรับ Mobile ใช้ HttpClient จาก dart:io
    try {
      HttpClient httpClient = HttpClient();
      HttpClientRequest request = await httpClient.postUrl(Uri.parse(url));
      request.headers.set('Content-Type', 'application/json');
      request.add(utf8.encode(json.encode(jsonMap)));

      HttpClientResponse response = await request.close();

      if (response.statusCode == 200) {
        String reply = await response.transform(utf8.decoder).join();
        httpClient.close();
        return reply;
      } else {
        httpClient.close();
        throw Exception(
            'Failed to load data from Mobile: ${response.statusCode}');
      }
    } catch (e) {
      throw e;
    }
  }
}

Future<String> oldApiRequestDockerAgs(String url, Map jsonMap) async {
  String Username = 'ags-ci';
  String Password = 'cdWmW,o5HlNPd}gzEZlkbzCJ(zDtP)';

  HttpClient httpClient = new HttpClient();
  httpClient.addCredentials(
      Uri.parse(url), "realm", HttpClientBasicCredentials(Username, Password));
  HttpClientRequest request = await httpClient.postUrl(Uri.parse(url));
  request.headers.set('content-type', 'application/json');
  request.headers.set('path', "switchUrl2img");
  request.headers.set('port', '5200');
  request.add(utf8.encode(json.encode(jsonMap)));
  HttpClientResponse response = await request.close();
  // todo - you should check the response.statusCode
  String reply = await response.transform(utf8.decoder).join();
  httpClient.close();
  return reply;
}

var token = "";
Future<String> apiPost(Uri url, Map jsonMap) async {
  try{
    print("apiPost called with URL: $url");
    var path = "switchUrl2img";
    var now = DateTime.now();
    String formattedDate = DateFormat('MM/dd').format(now);

    var key = utf8.encode("A^Amps9@_Um_=^-9tfwJ&d&!pFqgppnK9=JWtFrJqxq=m=5H*3U@%&f%R@+Nsymz&@aC_8tbq6gYjM*R#6mqJ!7A^ZPYwAG3P!8C*dR2zuc33");
    var bytes = utf8.encode(formattedDate);
    var digest = Hmac(sha256, key).convert(bytes);

    final jwt = JWT({
      "sub": "dockerapi-22022203889234",
      "iat": DateTime.now().toUtc().millisecondsSinceEpoch,
    });
    final token = jwt.sign(SecretKey('$digest'));
    print("token: $token");
    // return "Edok";
    final response = await http.post(
      url,
      headers: {
        'Content-Type': 'application/json',
        'path': path,
        'port': '5200',
        'Authorization': token,
      },
      body: json.encode(jsonMap),
    );
    print("token: $token");


    if (response.statusCode == 200) {
      print("reply: ${response.body}");
      return response.body;
    } else {
      throw Exception("API failed: ${response.statusCode}");
    }
  }catch(e){
    print("Error in apiPost: $e");
    return e.toString();
  }
}
//******************************************* widget *****************************************
Widget Modify(context) {
  return ClipRect(
    child: BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 30.0, sigmaY: 30.0),
      child: Container(
        width: 828.0,
        height: 1792.0,
        decoration: BoxDecoration(
          color: const Color(0x64302c49),
          boxShadow: [
            BoxShadow(
              color: const Color(0x41000000),
              offset: Offset(-20, 0),
              blurRadius: 35,
            ),
          ],
        ),
        child: Stack(
          children: <Widget>[
            Container(
              alignment: Alignment.topCenter,
              margin: EdgeInsets.only(top: mediaQuery(context, "h", 292)),
              child: Text(
                "ui_modify",
                style: TextStyle(
                  fontFamily: 'SukhumvitSet-SemiBold',
                  fontSize: mediaQuery(context, "h", 35),
                  color: const Color(0xfffee095),
                  letterSpacing: mediaQuery(context, "h", 1.75),
                  fontWeight: FontWeight.w700,
                  shadows: [
                    Shadow(
                      color: const Color(0x33000000),
                      offset: Offset(0, 3),
                      blurRadius: 5,
                    )
                  ],
                ),
                textAlign: TextAlign.center,
              ),
            ),
            Container(
              alignment: Alignment.topCenter,
              margin: EdgeInsets.only(
                  top: mediaQuery(context, "h", 441),
                  left: mediaQuery(context, "w", 118),
                  right: mediaQuery(context, "h", 153)),
              child: Text(
                "ui_modifyDetail",
                style: TextStyle(
                  fontFamily: 'SukhumvitSet-SemiBold',
                  fontSize: mediaQuery(context, "h", 28),
                  color: const Color(0xfffcf6e4),
                  letterSpacing: mediaQuery(context, "h", 1.4000000000000001),
                  fontWeight: FontWeight.w700,
                  shadows: [
                    Shadow(
                      color: const Color(0x33000000),
                      offset: Offset(0, 3),
                      blurRadius: 5,
                    )
                  ],
                ),
                textAlign: TextAlign.left,
              ),
            ),
          ],
        ),
      ),
    ),
  );
}
//******************************************* widget *****************************************
