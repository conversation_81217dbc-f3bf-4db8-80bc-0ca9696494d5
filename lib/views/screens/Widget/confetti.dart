
import 'package:confetti/confetti.dart';
import 'package:flutter/material.dart';

Widget confetti(ConfettiController controller) {
return ConfettiWidget(
confettiController: controller,
blastDirectionality: BlastDirectionality.explosive,
maxBlastForce: 5,
minBlastForce: 1,
emissionFrequency: 0.1,

// amoint of paticles that will pop-up at a time
numberOfParticles: 10,

// particles will come down
gravity: 0.4,

// start again as soon as the
// animation is finished
shouldLoop: false,

// assign colors of any choice
colors: const [
Color.fromARGB(255, 108, 200, 111),
Color.fromARGB(255, 237, 95, 142),
Color.fromARGB(255, 236, 203, 92),
Colors.white
],
);
}