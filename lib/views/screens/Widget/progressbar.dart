import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mapp_ms24/controllers/utils/color.dart';

Widget Progressbar(BuildContext context, double currentValue, double maxValue) {
  return CustomPaint(
    painter: ProgressBarPainter(
      progressborderColor: Colors.transparent,
      currentValue: currentValue,
      maxValue: maxValue,
      progressColor: Colors.transparent,
      backgroundColor: Theme.of(context).colorScheme.primary,
      borderColor:  Theme.of(context).colorScheme.inversePrimary,
      progressGradient: LinearGradient(
          colors: [Theme.of(context).colorScheme.tertiaryFixed, AppColors.DeepSeaGreen],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter),
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(10.0),
        topRight: Radius.circular(10.0),
      ),
      borderWidth: 1,
    ),
    child: Container(
      height: 40,
      width: 40,
    ),
  );
}

class ProgressBarPainter extends CustomPainter {
  final double currentValue;
  final double maxValue;
  final Color progressColor;
  final LinearGradient? progressGradient;
  final Color backgroundColor;
  final BorderRadius borderRadius;
  final double borderWidth;
  final Color? borderColor;
  final Color? progressborderColor;

  ProgressBarPainter(
      {required this.currentValue,
      required this.maxValue,
      required this.progressColor,
      required this.backgroundColor,
      required this.borderRadius,
      required this.borderWidth,
      this.borderColor,
      required this.progressborderColor,
      this.progressGradient});


  @override
  void paint(Canvas canvas, Size size) {
    final Paint backgroundPaint = Paint()
      ..color = backgroundColor
      ..strokeCap = StrokeCap.square
      ..strokeWidth = borderWidth;

    final double progressPercentage = currentValue / maxValue;
    final double progressHeight = size.height * progressPercentage;


    final RRect backgroundRect = RRect.fromRectAndCorners(
      Rect.fromPoints(Offset(0, 0), Offset(size.width, size.height)),
      topLeft: borderRadius.topLeft,
      topRight: borderRadius.topRight,
      bottomLeft: borderRadius.bottomLeft,
      bottomRight: borderRadius.bottomRight,
    );

    final RRect progressRect = RRect.fromRectAndCorners(
      Rect.fromPoints(
        Offset(0, size.height - progressHeight),
        Offset(size.width, size.height),
      ),
      topLeft: borderRadius.topLeft,
      topRight: borderRadius.topRight,
      bottomLeft: borderRadius.bottomLeft,
      bottomRight: borderRadius.bottomRight,
    );

    // Draw background
    canvas.drawRRect(backgroundRect, backgroundPaint);

    // Draw border for background
    final Paint backgroundBorderPaint = Paint()
      ..color = borderColor!
      ..style = PaintingStyle.stroke
      ..strokeWidth = borderWidth;
    canvas.drawRRect(backgroundRect, backgroundBorderPaint);

    if (progressGradient != null) {
      final Paint gaduianprogressColor = Paint()
        ..shader = progressGradient!.createShader(progressRect.outerRect)
        ..strokeCap = StrokeCap.square
        ..strokeWidth = size.height - borderWidth * 2;
      canvas.drawRRect(progressRect, gaduianprogressColor);

      // Draw border for progress
      final Paint progressBorderPaint = Paint()
        ..color = progressborderColor!
        ..style = PaintingStyle.stroke
        ..strokeWidth = borderWidth;
      canvas.drawRRect(progressRect, progressBorderPaint);
    } else {
      final Paint gaduianprogressColor = Paint()
        ..color = progressColor
        ..strokeCap = StrokeCap.square
        ..strokeWidth = size.height - borderWidth * 2;
      canvas.drawRRect(progressRect, gaduianprogressColor);

      // Draw border for progress
      final Paint progressBorderPaint = Paint()
        ..color = progressborderColor!
        ..style = PaintingStyle.stroke
        ..strokeWidth = borderWidth;
      canvas.drawRRect(progressRect, progressBorderPaint);
    }
    // Draw text at the top of progress
    TextPainter progressTextPainter = TextPainter(
      text: TextSpan(
        text: '30+', // ข้อความที่ต้องการแสดง
        style: TextStyle(
          fontFamily: 'SukhumvitSet-Text',
          fontSize: 12.h,
          color: AppColors.White,
          fontWeight: FontWeight.w600,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    progressTextPainter.layout(minWidth: 0, maxWidth: size.width);
    progressTextPainter.paint(
      canvas,
      Offset(
        (size.width - progressTextPainter.width) / 2, // ตำแหน่ง X เพื่อวางตัวหนังสือที่ตรงกลาง
        0 + 10, // ตำแหน่ง Y ที่ 0 เพื่อวางที่บนสุด
      ),
    );

    // Draw text at the bottom of progress
    TextPainter bottomTextPainter = TextPainter(
      text: TextSpan(
        text: currentValue.toStringAsFixed(0).toString(), // ข้อความที่ต้องการแสดง
        style:TextStyle(
          fontFamily: 'SukhumvitSet-Text',
          fontSize: 12.h,
          color: AppColors.White,
          fontWeight: FontWeight.w700,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    bottomTextPainter.layout(minWidth: 0, maxWidth: size.width);
    bottomTextPainter.paint(
      canvas,
      Offset(
        (size.width - bottomTextPainter.width) / 2, // ตำแหน่ง X เพื่อวางตัวหนังสือที่ตรงกลาง
        size.height - progressHeight + 10, // ตำแหน่ง Y ที่ตรงกลางล่าง
      ),
    );
    // Draw text at the bottom center of progress
    TextPainter bottominTextPainter = TextPainter(
      text: TextSpan(
        text: '0-18', // ข้อความที่ต้องการแสดง
        style: TextStyle(
          fontFamily: 'SukhumvitSet-Text',
          fontSize: 10.h,
          color: AppColors.White,
          fontWeight: FontWeight.w600,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    bottominTextPainter.layout(minWidth: 0, maxWidth: size.width);
    bottominTextPainter.paint(
      canvas,
      Offset(
        (size.width - bottominTextPainter.width) / 2, // ตำแหน่ง X เพื่อวางตัวหนังสือที่ตรงกลาง
        size.height - bottominTextPainter.height -10, // ตำแหน่ง Y ที่ตรงกลางล่าง
      ),
    );

  }


  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return true;
  }
}



