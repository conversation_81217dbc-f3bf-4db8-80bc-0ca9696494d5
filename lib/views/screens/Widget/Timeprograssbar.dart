import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mapp_ms24/controllers/utils/color.dart';

Widget STAMPEDProgressbar(
    BuildContext context, double currentValue, double maxValue) {
  return CustomPaint(
    painter: ProgressBarPainter(
      progressborderColor: Color(0xFFA596FF),
      currentValue: currentValue,
      maxValue: maxValue,
      progressColor: Color(0xFF322B63),
      backgroundColor: Color(0xFF1F1C2F),
      borderColor: Colors.transparent,
      borderRadius: BorderRadius.all(
        Radius.circular(8.0),
      ),
      borderWidth: 1.w,
    ),
    child: Container(
      height: 40.h,
      width: 40.w,
    ),
  );
}

Widget buildProgressbar(BuildContext context, uncurrentValue, maxValue) {
  var currentValue = double.parse(uncurrentValue.toString());
  return CustomPaint(
    painter: ProgressBarPainter(
      progressborderColor:
          currentValue >= maxValue ? Color(0xFFFF3E3E) : Colors.transparent,
      currentValue: currentValue,
      maxValue: maxValue,
      progressGradient: currentValue >= maxValue
          ? LinearGradient(colors: [
              Theme.of(context).colorScheme.tertiaryContainer,
              AppColors.BrightRed,
            ], begin: Alignment.bottomCenter, end: Alignment.topCenter)
          : LinearGradient(colors: [
        Theme.of(context).colorScheme.secondaryContainer,
              AppColors.DeepSeaGreen,
            ],begin: Alignment.topCenter, end: Alignment.bottomCenter),
      progressColor:
          currentValue >= maxValue ? AppColors.BrightRed : Colors.transparent,
      backgroundColor: Theme.of(context).colorScheme.primary,
      borderColor: currentValue >= maxValue ? AppColors.BrightRed : Theme.of(context).colorScheme.inversePrimary,
      borderRadius: const BorderRadius.all(Radius.circular(8.0)),
      borderWidth: 1,
    ),
    child: Container(
      height: Get.height,
      width: 40.w,
    ),
  );
}

Widget buildProgressbarCount(
    BuildContext context, double currentValue, double maxValue) {
  return CustomPaint(
    painter: ProgressBarCountPainter(
      progressborderColor:
          currentValue == maxValue ? Color(0xFFFF3E3E) : Colors.transparent,
      currentValue: currentValue,
      maxValue: maxValue,
      progressGradient: currentValue == maxValue
          ? LinearGradient(colors: [
              Color(0xFFFF3E3E).withOpacity(1),
              Color(0xFFFF3E3E).withOpacity(0),
            ], begin: Alignment.topCenter, end: Alignment.bottomCenter)
          : LinearGradient(colors: [
              Theme.of(context).colorScheme.secondaryContainer,
              AppColors.DeepSeaGreen,
            ], begin: Alignment.topCenter, end: Alignment.bottomCenter),
      progressColor: currentValue == maxValue ? Color(0xFFFF3E3E) : Theme.of(context).colorScheme.primary,
      backgroundColor: Theme.of(context).colorScheme.primary,
      borderColor: currentValue == maxValue ? Color(0xFFFF3E3E) : Theme.of(context).colorScheme.inversePrimary,
      borderRadius: BorderRadius.all(
        Radius.circular(8.0),
      ),
      borderWidth: 1,
    ),
    child: Container(
      height: 152.h,
      width: 40.w,
    ),
  );
}

class ProgressBarCountPainter extends CustomPainter {
  final double currentValue;
  final double maxValue;
  final Color progressColor;
  final LinearGradient? progressGradient;
  final Color backgroundColor;
  final BorderRadius borderRadius;
  final double borderWidth;
  final Color? borderColor;
  final Color? progressborderColor;

  ProgressBarCountPainter(
      {required this.currentValue,
      required this.maxValue,
      required this.progressColor,
      required this.backgroundColor,
      required this.borderRadius,
      required this.borderWidth,
      this.borderColor,
      required this.progressborderColor,
      this.progressGradient});

  @override
  void paint(Canvas canvas, Size size) {
    final Paint progressBorderPaint = Paint()
      ..color = progressborderColor!
      ..style = PaintingStyle.stroke
      ..strokeWidth = borderWidth;

    final Paint backgroundPaint = Paint()
      ..color = backgroundColor
      ..strokeCap = StrokeCap.square
      ..strokeWidth = borderWidth;

    double progressPercentage;
    double progressHeight;

    if (currentValue <= maxValue) {
      progressPercentage = currentValue / maxValue;
      if (currentValue / maxValue == 0) {
        progressPercentage = 0.1;
      }
      ;
      progressHeight = size.height * progressPercentage;
    } else {
      progressPercentage = 1.0 - (currentValue - maxValue) / maxValue;
      progressHeight = size.height - (size.height * progressPercentage);
    }

    final RRect backgroundRect = RRect.fromRectAndCorners(
      Rect.fromPoints(Offset(0, 0), Offset(size.width, size.height)),
      topLeft: borderRadius.topLeft,
      topRight: borderRadius.topRight,
      bottomLeft: borderRadius.bottomLeft,
      bottomRight: borderRadius.bottomRight,
    );

    final RRect progressRect = RRect.fromRectAndCorners(
      Rect.fromPoints(
        Offset(0, size.height - progressHeight),
        Offset(size.width, size.height),
      ),
      topLeft: borderRadius.topLeft,
      topRight: borderRadius.topRight,
      bottomLeft: borderRadius.bottomLeft,
      bottomRight: borderRadius.bottomRight,
    );

    // Draw background
    canvas.drawRRect(backgroundRect, backgroundPaint);

    // Draw border for background
    final Paint backgroundBorderPaint = Paint()
      ..color = borderColor!
      ..style = PaintingStyle.stroke
      ..strokeWidth = borderWidth;
    canvas.drawRRect(backgroundRect, backgroundBorderPaint);

    if (progressGradient != null) {
      final Paint gradientProgressColor = Paint()
        ..shader = progressGradient!.createShader(progressRect.outerRect)
        ..strokeCap = StrokeCap.square
        ..strokeWidth = size.height - borderWidth * 2;
      canvas.drawRRect(progressRect, gradientProgressColor);

      // Draw border for progress
      canvas.drawRRect(progressRect, progressBorderPaint);
    } else {
      final Paint solidProgressColor = Paint()
        ..color = progressColor
        ..strokeCap = StrokeCap.square
        ..strokeWidth = size.height - borderWidth * 2;
      canvas.drawRRect(progressRect, solidProgressColor);

      // Draw border for progress
      canvas.drawRRect(progressRect, progressBorderPaint);
    }

    // Draw text at the bottom of progress
    TextPainter bottomTextPainter = TextPainter(
      text: TextSpan(
        text: '${currentValue.toInt()}', // Display remaining percentage
        style: TextStyle(
          fontFamily: 'SukhumvitSet-Text',
          fontSize: 12.sp,
          color: Colors.white,
          fontWeight: FontWeight.w700,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    bottomTextPainter.layout(minWidth: 0, maxWidth: size.width);
    bottomTextPainter.paint(
      canvas,
      (currentValue / maxValue <= 0.15)
          ? Offset(
              (size.width - bottomTextPainter.width) / 2,
              size.height - progressHeight - 20,
            )
          : Offset(
              (size.width - bottomTextPainter.width) / 2,
              size.height - progressHeight + 5,
            ),
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    // TODO: implement shouldRepaint
    return true;
  }
}

class ProgressBarPainter extends CustomPainter {
  final double currentValue;
  final double maxValue;
  final Color progressColor;
  final LinearGradient? progressGradient;
  final Color backgroundColor;
  final BorderRadius borderRadius;
  final double borderWidth;
  final Color? borderColor;
  final Color? progressborderColor;

  ProgressBarPainter(
      {required this.currentValue,
      required this.maxValue,
      required this.progressColor,
      required this.backgroundColor,
      required this.borderRadius,
      required this.borderWidth,
      this.borderColor,
      required this.progressborderColor,
      this.progressGradient});

  @override
  void paint(Canvas canvas, Size size) {
    final Paint backgroundPaint = Paint()
      ..color = backgroundColor
      ..strokeCap = StrokeCap.square
      ..strokeWidth = borderWidth;

    double progressPercentage;
    double progressHeight;

    if (currentValue <= maxValue) {
      progressPercentage = currentValue / maxValue;
      if (currentValue / maxValue == 0) {
        progressPercentage = 0.1;
      }
      ;
      progressHeight = size.height * progressPercentage;
    } else {
      progressHeight = size.height;
    }

    final RRect backgroundRect = RRect.fromRectAndCorners(
      Rect.fromPoints(Offset(0, 0), Offset(size.width, size.height)),
      topLeft: borderRadius.topLeft,
      topRight: borderRadius.topRight,
      bottomLeft: borderRadius.bottomLeft,
      bottomRight: borderRadius.bottomRight,
    );

    final RRect progressRect = RRect.fromRectAndCorners(
      Rect.fromPoints(
        Offset(0, size.height - progressHeight),
        Offset(size.width, size.height),
      ),
      topLeft: borderRadius.topLeft,
      topRight: borderRadius.topRight,
      bottomLeft: borderRadius.bottomLeft,
      bottomRight: borderRadius.bottomRight,
    );

    // Draw background
    canvas.drawRRect(backgroundRect, backgroundPaint);

    // Draw border for background
    final Paint backgroundBorderPaint = Paint()
      ..color = borderColor!
      ..style = PaintingStyle.stroke
      ..strokeWidth = borderWidth;
    canvas.drawRRect(backgroundRect, backgroundBorderPaint);

    if (progressGradient != null) {
      final Paint gradientProgressColor = Paint()
        ..shader = progressGradient!.createShader(progressRect.outerRect)
        ..strokeCap = StrokeCap.square
        ..strokeWidth = size.height - borderWidth * 2;
      canvas.drawRRect(progressRect, gradientProgressColor);

      // Draw border for progress
      final Paint progressBorderPaint = Paint()
        ..color = progressborderColor!
        ..style = PaintingStyle.stroke
        ..strokeWidth = borderWidth;
      canvas.drawRRect(progressRect, progressBorderPaint);
    } else {
      final Paint solidProgressColor = Paint()
        ..color = progressColor
        ..strokeCap = StrokeCap.square
        ..strokeWidth = size.height - borderWidth * 2;
      canvas.drawRRect(progressRect, solidProgressColor);

      // Draw border for progress
      final Paint progressBorderPaint = Paint()
        ..color = progressborderColor!
        ..style = PaintingStyle.stroke
        ..strokeWidth = borderWidth;
      canvas.drawRRect(progressRect, progressBorderPaint);
    }

    // Draw text at the bottom of progress
    TextPainter bottomTextPainter = TextPainter(
      text: TextSpan(
        text: '${currentValue.toInt()}', // ข้อความที่ต้องการแสดง
        style: TextStyle(
          fontFamily: 'SukhumvitSet-Text',
          fontSize: 12.sp,
          color: currentValue >= maxValue ? Colors.white : Color(0xFFFEE095),
          fontWeight: FontWeight.w700,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    bottomTextPainter.layout(minWidth: 0, maxWidth: size.width);
    bottomTextPainter.paint(
      canvas,
      (currentValue / maxValue <= 0.15)
          ? Offset(
              (size.width - bottomTextPainter.width) /
                  2, // ตำแหน่ง X เพื่อวางตัวหนังสือที่ตรงกลาง
              size.height - progressHeight - 20 // ตำแหน่ง Y ที่ตรงกลางล่าง
              )
          : Offset(
              (size.width - bottomTextPainter.width) /
                  2, // ตำแหน่ง X เพื่อวางตัวหนังสือที่ตรงกลาง
              size.height - progressHeight + 5, // ตำแหน่ง Y ที่ตรงกลางล่าง
            ),
    );
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return true;
  }
}
