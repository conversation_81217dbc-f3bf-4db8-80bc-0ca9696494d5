import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';
import 'package:mapp_ms24/controllers/utils/color.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';
import 'package:mapp_ms24/views/screens/home/<USER>';

import '../../../controllers/utils/widget.dart';

Widget buildAppBar(context, title) {
  return Container(
    width: Get.width,
    height: 100.h,
    padding: const EdgeInsets.only(top:28,left: 10, right: 10),
    decoration: BoxDecoration(
      color: Theme.of(context).colorScheme.primary.withOpacity(1),
      boxShadow: [
        BoxShadow(
          color: const Color(0x26000000), // Shadow color with opacity
          offset: const Offset(0, 4),
          blurRadius: 10,
        ),
      ],
    ),
    child: Stack(
      children: [
        Center(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Align(
                alignment: Alignment.centerRight,
                child: InkWell(
                  onTap: () {
                    // Get.offAndToNamed('home');
                    Get.back();

                  },
                  child: SizedBox(
                    height: 34.h,
                    width: 34.w,
                    child: Center(
                      child: Container(
                        height: 14.h,
                        width: 9.w,
                        child: Image.asset(
                          'assets/ADD/back_Vector.png',
                          color: Theme.of(context).colorScheme.onSecondary,
                          scale: 1,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        Center(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              AppWidget.normalText(
                context,
                title,
                18.sp,
                Theme.of(context).colorScheme.onSecondary,
                FontWeight.w500,
              ),
            ],
          ),
        )
      ],
    ),
  );
}
Widget buildAppBarWithFuntion(context, title,function) {
  return Container(
    width: Get.width,
    height: 100.h,
    padding: const EdgeInsets.only(top:28,left: 10, right: 10),
    decoration: BoxDecoration(
      gradient: LinearGradient(
        colors: [
          Theme.of(context).colorScheme.primary,
          Theme.of(context).colorScheme.onPrimary,
        ],
        begin: Alignment.topCenter, // เริ่มต้น Gradient ที่มุมบนซ้าย
        end: Alignment.bottomCenter,
      ),
      boxShadow: [
        BoxShadow(
          color: const Color(0xFF000000), // Shadow color with opacity
          offset: const Offset(0, 4),
          blurRadius: 10,
        ),
      ],
    ),
    child: Stack(
      children: [
        Center(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Align(
                alignment: Alignment.centerRight,
                child: InkWell(
                  onTap: () async {
               await  function();
                    Get.back();
                  },
                  child: SizedBox(
                    height: 34.h,
                    width: 34.w,
                    child: Center(
                      child: Container(
                        height: 14.h,
                        width: 9.w,
                        child: Image.asset(
                          'assets/ADD/back_Vector.png',
                          scale: 1,
                          color: Theme.of(context).colorScheme.scrim,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        Center(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              AppWidget.normalText(
                context,
                title,
                18.sp,
                Theme.of(context).colorScheme.scrim,
                FontWeight.w500,
              ),
            ],
          ),
        )
      ],
    ),
  );
}

Widget buildAppBarWithIcon(context, icon, title) {
  return Container(
    width: Get.width,
    height: 90.h,
    padding: const EdgeInsets.only(top:20,left: 10, right: 10),
    decoration: BoxDecoration(
      color: const Color(0xFF2D2944).withOpacity(1),
      boxShadow: [
        BoxShadow(
          color: const Color(0x26000000), // Shadow color with opacity
          offset: const Offset(0, 4),
          blurRadius: 90,
        ),
      ],
    ),
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Align(
              alignment: Alignment.centerRight,
              child: InkWell(
                onTap: () {
                  // Get.offAndToNamed('home');
                  Get.back();
                },
                child: SizedBox(
                  height: 34.h,
                  width: 34.w,
                  child: Center(
                    child: SizedBox(
                      height: 15.h,
                      width: 18.w,
                      child: Image.asset(
                        'assets/ADD/back_Vector.png',
                        scale: 1,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: Colors.white,
            ),
            AppWidget.normalText(
              context,
              title,
              16.sp,
              const Color(0xFFF6F6F6),
              FontWeight.w500,
            ),
          ],
        ),
        SizedBox(
          height: 34.h,
          width: 34.w,
        )
      ],
    ),
  );
}

Widget buildAppBarWithFunction(context, title, namefun, img, funtion) {
  return  Container(
    width: Get.width,
    height: 63.h,
    decoration: BoxDecoration(
      color: Theme.of(context).colorScheme.primary.withOpacity(1),
      boxShadow: [
        BoxShadow(
          color: const Color(0x26000000), // Shadow color with opacity
          offset: const Offset(0, 4),
          blurRadius: 10,
        ),
      ],
    ),
    child: Container(
      padding: EdgeInsets.symmetric(horizontal: 10.w),
      child: Stack(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Align(
                alignment: Alignment.centerRight,
                child: InkWell(
                  onTap: () {
                    Get.back();
                  },
                  child: SizedBox(
                    height: 34.h,
                    width: 34.w,
                    child: Center(
                      child: Container(
                        height: 14.h,
                        width: 9.w,
                        child: Image.asset(
                          'assets/ADD/back_Vector.png',
                          scale: 1,
                          color: Theme.of(context).colorScheme.onSecondary,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          Center(
            child: Align(
              alignment: Alignment.center,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AppWidget.normalText(
                    context,
                    title,
                    18.sp,
                    Theme.of(context).colorScheme.onSecondary,
                    FontWeight.w500,
                  ),
                  SizedBox(
                    width: 30.w,
                  ),
                ],
              ),
            ),
          ),
          img !="" ?  Center(
            child: Align(
              alignment: Alignment.centerLeft,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  GestureDetector(
                    onTap: funtion, // Assign the function to onTap
                    child: Container(
                      height: 30.h,
                      width: 99.w,
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primary,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: const Color(0xFF302C49),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          AppWidget.normalText(
                            context,
                            namefun,
                            12.sp,
                            Theme.of(context).colorScheme.onSecondary.withOpacity(1),
                            FontWeight.w500,
                          ),
                          SizedBox(
                            width: 5.w,
                          ),
                          SizedBox(
                            height: 16.h,
                            width: 16.w,
                            child: Image.asset('assets/$img'),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ) : Container(),
        ],
      ),
    ),
  );
}

Widget buildSnackbar(context, text, color) {
  return Padding(
    padding: const EdgeInsets.only(top: 30.0),
    child: Container(
      width: 358.w,
      height: 56.h,
      padding: EdgeInsets.symmetric(horizontal: 12.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.r),
        color: Theme.of(context).colorScheme.onPrimaryFixed.withOpacity(0.2),
        border: Border.all(
          color: color,
          // Color(0xFF3EFFC5),
          width: 1.w,
        ),
      ),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 10.h),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SizedBox(
              height: 24.h,
              width: 24.w,
              child: Image.asset('assets/ADD/Exclamation-Circle_G.png'),
            ),
            SizedBox(
              width: 8.w,
            ),
            AppWidget.normalText(
              context,
              text,
              14.sp,
              Theme.of(context).colorScheme.secondaryContainer,
              FontWeight.w500,
            ),
          ],
        ),
      ),
    ),
  );
}

Widget buildSnackbarError(context, text) {
  return Padding(
    padding: const EdgeInsets.only(top: 20.0),
    child: Container(
      width: 358.w,
      height: 56.h,
      padding: EdgeInsets.symmetric(horizontal: 12.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.r),
        border: Border.all(
          color: const Color(0xFFFF3E3E),
          width: 1.w,
        ),
        boxShadow: const [
          BoxShadow(
            color: Color(0xFF000000), // เปลี่ยนค่า opacity ตามต้องการ
            offset: Offset(0, 4),
            blurRadius: 10,
            spreadRadius: 0,
          ),
        ],
        color: const Color(0xFFFF3E3E).withOpacity(0.2),
      ),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 10.h),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SizedBox(
              height: 24.h,
              width: 24.w,
              child: Image.asset('assets/ADD/Exclamation.png'),
            ),
            SizedBox(
              width: 8.w,
            ),
            AppWidget.normalText(
              context,
              text,
              14.sp,
              const Color(0xFFF6F6F6),
              FontWeight.w500,
            ),
          ],
        ),
      ),
    ),
  );
}

Widget buildEmtpy(context, text) {
  return Container(
    margin: EdgeInsets.only(top: 20.h),
    width: Get.width,
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          height: 24.h,
          width: 24.w,
          child: Image.asset('assets/ADD/Exclamation-Circle.png'),
        ),
        SizedBox(
          width: 8.w,
        ),
        AppWidget.normalText(
          context,
          text,
          14.sp,
          Theme.of(context).colorScheme.onSecondary,
          FontWeight.w500,
        ),
      ],
    ),
  );
}

Widget buildBirthday(context) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      SizedBox(
        height: 360.h,
        child: SliverFillRemaining(
          hasScrollBody: false,
          child: Container(
            decoration: BoxDecoration(
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF000000).withOpacity(0.9),
                  offset: const Offset(0, 4),
                  blurRadius: 4,
                ),
              ],
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  const Color(0xFF1F1C2F),
                  const Color(0xFF1F1C2F).withOpacity(0.6),
                ],
              ),
            ),
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Stack(
                    children: [
                      Image.asset('assets/Happy/Gift-Ribbin.png'),
                      Column(
                        children: [
                          SizedBox(
                            height: 97.h,
                          ),
                          SizedBox(
                            width: 375.w,
                            height: 185.h,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                AppWidget.normalText(
                                    context,
                                    'HAPPY BIRTH DAY :)',
                                    20.sp,
                                    const Color(0xFFF6F6F6),
                                    FontWeight.w600),
                                SizedBox(
                                  height: 20.h,
                                ),
                                SizedBox(
                                  width: 196.w,
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      AppWidget.normalText(
                                          context,
                                          'MAKE A WISH AND',
                                          12.sp,
                                          const Color(0xFFF6F6F6),
                                          FontWeight.w200),
                                      Row(
                                        children: [
                                          AppWidget.normalText(
                                              context,
                                              'CLICK THE LUCKY BUTTON ',
                                              12.sp,
                                              const Color(0xFFFEE095),
                                              FontWeight.w700),
                                          AppWidget.normalText(
                                              context,
                                              'BELOW',
                                              12.sp,
                                              const Color(0xFFFEE095),
                                              FontWeight.w400),
                                        ],
                                      ),
                                      SizedBox(
                                        height: 10.h,
                                      )
                                    ],
                                  ),
                                )
                              ],
                            ),
                          ),
                        ],
                      )
                    ],
                  ),
                  Container(
                      height: 52.h,
                      width: 357.w,
                      decoration: BoxDecoration(
                        borderRadius:
                            const BorderRadius.all(Radius.circular(10)),
                        color: const Color(0xFF322B63),
                        border: Border.all(
                          width: 1.w,
                          color: const Color(0xFFA596FF),
                        ),
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          AppWidget.normalText(
                            context,
                            'COMFIRM',
                            14.sp,
                            const Color(0xFFF6F6F6),
                            FontWeight.w400,
                          ),
                        ],
                      )),
                  SizedBox(
                    height: 10.h,
                  ),
                  Center(
                    child: InkWell(
                      onTap: () {
                        Get.back();
                      },
                      child: AppWidget.normalText(
                        context,
                        'SKIP',
                        14.sp,
                        const Color(0xFFF6F6F6),
                        FontWeight.w400,
                      ),
                    ),
                  )
                ],
              ),
            ),
          ),
        ),
      ),
    ],
  );
}

Widget Vote() {
  return Center(
    child: Container(
      width: 176.w,
      height: 34.h,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: 4, // Set the number of items you want in the list
        itemBuilder: (BuildContext context, int index) {
          return Container(
            margin: EdgeInsets.only(right: 10.w),
            // Adjust the spacing between items
            height: 34.h,
            width: 34.w,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: const Color(0xFF322B63),
              border: Border.all(
                width: 1.w,
                color: const Color(0xFFA596FF),
              ),
            ),
            child: Center(
              child: AppWidget.normalText(
                context,
                (index + 1)
                    .toString(), // Display item index or any other content
                14.sp,
                const Color(0xFFF6F6F6),
                FontWeight.w400,
              ),
            ),
          );
        },
      ),
    ),
  );
}

Widget Liner(context) {
  return Padding(
    padding: const EdgeInsets.only(top: 10.0, bottom: 10),
    child: Container(
      height: 0.5,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.scrim.withOpacity(0.2),
        border: Border.all(
          width: 0.5,
          color: Theme.of(context).colorScheme.scrim.withOpacity(0.2),
        ),
      ),
    ),
  );
}

Widget LinerGray(context) {
  return Padding(
    padding: const EdgeInsets.only(top: 10.0, bottom: 10),
    child: Container(
      height: 1.h,
      decoration: BoxDecoration(
        color: Theme.of(context)
            .colorScheme
            .onSecondaryContainer.withOpacity(1),
        border: Border.all(
          width: 0.5,
          color: Theme.of(context)
              .colorScheme
              .onSecondaryContainer.withOpacity(1),
        ),
      ),
    ),
  );
}

Widget Liner2(context) {
  return Padding(
    padding: const EdgeInsets.only(top: 10, bottom: 10),
    child: Container(
      height: 0.5.h,
      decoration: BoxDecoration(
        color:
        Get.isDarkMode
            ? AppColors.GrayishWhite.withOpacity(0.1)
            : AppColors.bluePurple.withOpacity(0.1),
        border: Border.all(
          width: 0.5,
          color: AppColors.GrayishWhite.withOpacity(0.1),
        ),
      ),
    ),
  );
}
Widget LinerGray2(context) {
  return Container(
    height: 1.h,
    decoration: BoxDecoration(
      color: Theme.of(context)
          .colorScheme
          .onSecondaryContainer.withOpacity(1),
      border: Border.all(
        width: 0.5,
        color: Theme.of(context)
            .colorScheme
            .onSecondaryContainer.withOpacity(1),
      ),
    ),
  );
}


Widget buttonSave(
    BuildContext context, String title, Future Function()? onTapCallback) {
  return Container(
    margin: EdgeInsets.only(top: 20.h),
    alignment: Alignment.topCenter,
    child: GestureDetector(
      onTap: () async {
        if (onTapCallback != null) {
          onTapCallback();
        }
      },
      child: Container(
        alignment: Alignment.center,
        width: 135.w,
        height: 50.h,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(50.0),
          color: const Color(0xff7f420c),
          boxShadow: const [
            BoxShadow(
              color: Color(0x4d000000),
              offset: Offset(0, 2),
              blurRadius: 20,
            ),
          ],
        ),
        child: AppWidget.boldText(
            context, title, 14.sp, const Color(0xFFF6F6F6), FontWeight.w400),
      ),
    ),
  );
}

enum InputType { text, number, email }


Widget buildFormInput(context, title, dec, InputType inputType,
    void Function(String value) update, String? leading) {
  TextInputType keyboardType;
  switch (inputType) {
    case InputType.text:
      keyboardType = TextInputType.text;
      break;
    case InputType.number:
      keyboardType = TextInputType.number;
      break;
    case InputType.email:
      keyboardType = TextInputType.emailAddress;
      break;
  }
  return Padding(
    padding: EdgeInsets.symmetric(vertical: 10.0.h),
    child: Container(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(left: 20.h),
            child: AppWidget.normalText(
              context, title, 14.sp, const Color(0xFFF6F6F6), FontWeight.w400),
        ),

          SizedBox(
            height: 10.h,
          ),
          Row(
            children: [
              Expanded(
                child: Container(
                  height: 60.h,
                  decoration: BoxDecoration(
                    color: const Color(0xFF302C49),
                    borderRadius: BorderRadius.circular(10.r),
                    boxShadow: const [
                      BoxShadow(
                        color: Color(0x40000000),
                        offset: Offset(0, 4),
                        blurRadius: 4,
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 8.0.w),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Container(
                            margin: EdgeInsets.only(left: 15.h),
                            width: Get.width,
                            height: Get.height,
                            child: Center(
                              child: TextField(
                                keyboardType: keyboardType,
                                onChanged: (text) {
                                  update;
                                },
                                onEditingComplete: () {
                                },
                                style: TextStyle(
                                  color: const Color(0xFFF6F6F6),
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w400,
                                ),
                                decoration: InputDecoration.collapsed(
                                  hintText: dec,
                                  hintStyle: TextStyle(
                                    color: const Color(0xFFF6F6F6)
                                        .withOpacity(0.6),
                                  ),
                                ),
                                maxLines: null, // Allow multiline input
                              ),
                            ),
                          ),
                        ),
                        leading != ''
                            ? AppWidget.normalText(
                                context,
                                leading,
                                14.sp,
                                const Color(0xFFF6F6F6),
                                FontWeight.w400,
                              )
                            : Container()
                      ],
                    ),
                  ),

                ),
              ),
            ],
          ),

        ],
      ),
    ),
  );
}



Widget buildFormBigInput(context, title, dec, InputType inputType,
    void Function(String value) update, String? leading) {
  TextInputType keyboardType;
  switch (inputType) {
    case InputType.text:
      keyboardType = TextInputType.text;
      break;
    case InputType.number:
      keyboardType = TextInputType.number;
      break;
    case InputType.email:
      keyboardType = TextInputType.emailAddress;
      break;
  }
  return Padding(
    padding: EdgeInsets.symmetric(vertical: 10.0.h),
    child: Container(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppWidget.normalText(
              context, title, 14.sp, const Color(0xFFF6F6F6), FontWeight.w400),
          SizedBox(
            height: 10.h,
          ),
          Row(
            children: [
              Expanded(
                child: Container(
                  height: 200.h,
                  decoration: BoxDecoration(
                    color: const Color(0xFF302C49),
                    borderRadius: BorderRadius.circular(8.r),
                    boxShadow: const [
                      BoxShadow(
                        color: Color(0x40000000),
                        offset: Offset(0, 4),
                        blurRadius: 4,
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 8.0.w),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Container(
                            padding: EdgeInsets.symmetric(vertical: 5.h),
                            width: Get.width,
                            height: Get.height,
                            child: Center(
                              child: TextField(
                                keyboardType: keyboardType,
                                onChanged: (text) {
                                  update;
                                },
                                onEditingComplete: () {
                                  update;
                                },
                                style: TextStyle(
                                  color: const Color(0xFFF6F6F6),
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w400,
                                ),
                                decoration: InputDecoration.collapsed(
                                  hintText: dec,
                                  hintStyle: TextStyle(
                                    color: const Color(0xFFF6F6F6)
                                        .withOpacity(0.6),
                                  ),
                                ),
                                maxLines: null, // Allow multiline input
                              ),
                            ),
                          ),
                        ),
                        leading != ''
                            ? AppWidget.normalText(
                                context,
                                leading,
                                14.sp,
                                const Color(0xFFF6F6F6),
                                FontWeight.w400,
                              )
                            : Container()
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    ),
  );
}

Widget buildDot(double height, double width, Color color) {
  return Stack(
    children: [
      Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: color, // Set your border color
            width: 2.0, // Set your border width
          ),
        ),
      ),
      Positioned(
        left: (width - width / 2) / 2,
        top: (height - height / 2) / 2,
        child: Container(
          height: height / 2,
          width: width / 2,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
            border: Border.all(
              color: color, // Set your border color
              width: 2.0, // Set your border width
            ),
          ),
        ),
      ),
    ],
  );
}
