import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mapp_ms24/controllers/utils/language_selection_controller.dart';
import 'package:mapp_ms24/controllers/utils/widget.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';

LanguageSelectionController langCtr = Get.put(LanguageSelectionController());

Widget buildMenuForm(context,height,width, pathImg, name, int data, status, type) {
  return SizedBox(
    width: Get.width,
    height: Get.height,
    child: Stack(
      children: [
        Column(
          children: [
            name == ''
                ? Container()
                : Align(
                    alignment: Alignment.center,
                    child: type == 1
                        ? Stack(
                            children: [
                              Container(
                                width: 54.w,
                                height: 54.h,
                                padding:
                                    EdgeInsets.only(left: 14.w, right: 14.w),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(20),
                                  border: Border.all(
                                    color: Theme.of(context).colorScheme.surfaceContainerHigh,
                                    width: 1.w,
                                  ),
                                  gradient:  LinearGradient(
                                    colors: [
                                      Theme.of(context).colorScheme.primary,
                                      Theme.of(context).colorScheme.onPrimary                                    ],
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                  ),
                                ),
                                child: Container(
                                    height: 24.h,
                                    width: 24.w,
                                    child: pathImg == ''
                                        ? Container()
                                        : SizedBox(
                                            height: 24.h,
                                            width: 24.w,
                                            child: Image.asset(
                                              'assets/$pathImg',
                                            ))),
                              ),
                              type == 1
                                  ? Positioned(
                                      top: 1.h,
                                      right: 1.w,
                                      child: buildDotOn(status))
                                  : Container(),
                            ],
                          )
                        : Stack(
                          children: [
                            Container(
                              width: 54.w,
                              height: 54.h,
                                padding: EdgeInsets.only(left: 14.w, right: 14.w),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(20),
                                  border: Border.all(
                                    color: Theme.of(context).colorScheme.surfaceContainerHighest,
                                    width: 1.w,
                                  ),
                                  gradient: LinearGradient(
                                    colors: [
                                      Theme.of(context).colorScheme.primary,
                                      Theme.of(context).colorScheme.onPrimary
                                    ],
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                  ),
                                ),
                                child: pathImg == ''
                                    ? Container()
                                    : SizedBox(
                                        height: height,
                                        width: width,
                                        child: Image.asset(
                                          'assets/$pathImg',
                                        ),
                                      ),
                              ),
                            data > 0
                                ? Container(
                              width: 11.w,
                              height: 11.h,
                              margin: EdgeInsets.only(
                                  left: mediaQuery(context, "h", 93)),
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: Colors
                                      .black, //                   <--- border color
                                  width: 1.3,
                                ),
                                borderRadius: BorderRadius.all(
                                    Radius.elliptical(15.0, 15.0)),
                                color: const Color(0xffff3e3e),
                                boxShadow: [
                                  BoxShadow(
                                    color: const Color(0x33000000),
                                    offset: Offset(0, 3),
                                    blurRadius: 5,
                                  ),
                                ],
                              ),
                            )
                                : Container(
                              width: mediaQuery(context, "h", 25),
                              height: mediaQuery(context, "h", 25),
                              margin: EdgeInsets.only(
                                  left: mediaQuery(context, "h", 93)),
                            ),
                          ],
                        ),
                  )
          ],
        ),
        Padding(
          padding: EdgeInsets.only(top: 60.0.h),
          child: SizedBox(
            height: 130.h,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Align(
                  alignment: Alignment.topCenter,
                  child: langCtr.selectedLanguageIndex == 1
                      ? AppWidget.normalText(context, name, 12.sp,
                      Theme.of(context).colorScheme.onSecondary, FontWeight.w400)
                      : Text(
                          name,
                          textAlign: TextAlign.center,
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            // fontFamily: 'SukhumvitSet-Text',
                            fontFamily: 'SukhumvitSet-Bold',
                            fontSize: 10.sp,
                            color: Theme.of(context).colorScheme.onSecondary,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                ),
              ],
            ),
          ),
        )
      ],
    ),
  );
}

Widget buildDotOn(status) {
  return Container(
    height: 11.h,
    width: 11.w,
    decoration: BoxDecoration(
      border: Border.all(
        color: Colors.black, //                   <--- border color
        width: 1.3,
      ),
      borderRadius: BorderRadius.all(Radius.elliptical(15.0, 15.0)),
      color: status == 0
          ? const Color(0xffff3e3e)
          : status == 1
              ? Colors.green
              : Colors.grey,
      boxShadow: const [
        BoxShadow(
          color: Color(0x33000000),
          offset: Offset(0, 3),
          blurRadius: 5,
        ),
      ],
    ),
  );
}
