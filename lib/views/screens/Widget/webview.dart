// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:flutter_inappwebview/flutter_inappwebview.dart';
//
// class WebViewController extends GetxController {
//
//   late InAppWebViewController webViewController;
//   var isLoading = false.obs;
//
//   @override
//   void onInit() {
//     super.onInit();
//   }
//
//   void onPageStarted() {
//     isLoading.value = true;
//   }
//
//   void onPageFinished() {
//     isLoading.value = false;
//   }
// }
//
// class WebViewPage extends StatelessWidget {
//
//   final url;
//   WebViewPage(this.url);
//
//   final WebViewController controller = Get.put(WebViewController());
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: Text('WebView Example'),
//       ),
//       body: Obx(() {
//         return Stack(
//           children: [
//             InAppWebView(
//               initialUrlRequest: URLRequest(url:  WebUri(url)),
//               initialOptions: InAppWebViewGroupOptions(
//                 android: AndroidInAppWebViewOptions(),
//                 ios: IOSInAppWebViewOptions(),
//               ),
//               onWebViewCreated: (controller) {
//                 this.controller.webViewController = controller;
//               },
//               onLoadStart: (controller, url) {
//                 this.controller.onPageStarted();
//               },
//               onLoadStop: (controller, url) {
//                 this.controller.onPageFinished();
//               },
//             ),
//             if (controller.isLoading.value)
//               Center(
//                 child: CircularProgressIndicator(),
//               ),
//           ],
//         );
//       }),
//     );
//   }
// }
//
