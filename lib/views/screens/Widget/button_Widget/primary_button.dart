import 'package:flutter/material.dart';

import 'package:mapp_ms24/configs/color_system.dart';

class PrimaryButton extends StatelessWidget {
  final String title;
  final VoidCallback onPressed;
  final bool isDisabled;
  final ButtonSize buttonSize;
  final IconData? iconData;
  final Color? backgroundColor;

  const PrimaryButton(
      {Key? key,
      required this.title,
      required this.onPressed,
      this.isDisabled = false,
      this.iconData,
      this.buttonSize = ButtonSize.full,
      this.backgroundColor})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 52,
      width: buttonSize.width,
      child: OutlinedButton(
        style: ButtonStyle(
          side: MaterialStateProperty.all<BorderSide>(
            const BorderSide(color: transparentColor),
          ),
          shape: MaterialStateProperty.all<RoundedRectangleBorder>(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(32),
            ),
          ),
          foregroundColor: MaterialStateProperty.all<Color>(mainWhite),
          backgroundColor: MaterialStateProperty.resolveWith<Color?>(
            (Set<MaterialState> states) {
              if (states.contains(MaterialState.disabled)) {
                return lighterGray;
              }
              if (backgroundColor != null) {
                return backgroundColor;
              }
              return primaryColor;
            },
          ),
          overlayColor: MaterialStateProperty.resolveWith<Color?>(
            (Set<MaterialState> states) {
              if (states.contains(MaterialState.pressed)) {
                return darkerPrimaryColor;
              }
              if (states.contains(MaterialState.hovered)) {
                return darkPrimaryColor;
              }
              return null; // Defer to the widget's default.
            },
          ),
        ),
        onPressed: isDisabled ? null : onPressed,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            iconData != null
                ? Container(
                    padding: const EdgeInsets.only(right: 8.0),
                    child: Icon(
                      iconData,
                      size: 18,
                    ),
                  )
                : Container(),
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w700,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

enum ButtonSize {
  smaller,
  small,
  medium,
  full,
}

extension ButtonSizeExtension on ButtonSize {
  double get width {
    switch (this) {
      case ButtonSize.smaller:
        return 115.0;
      case ButtonSize.small:
        return 135.0;
      case ButtonSize.medium:
        return 267.0;
      case ButtonSize.full:
        return double.infinity;
    }
  }
}