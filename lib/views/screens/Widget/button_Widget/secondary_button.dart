import 'package:flutter/material.dart';
import 'package:mapp_ms24/views/screens/Widget/button_Widget/primary_button.dart';

import 'package:mapp_ms24/configs/color_system.dart';

class SecondaryButton extends StatelessWidget {
  final String title;
  final VoidCallback onPressed;
  final bool isDisabled;
  final ButtonSize buttonSize;
  final IconData? iconData;
  final IconData? rightIconData;
  final Widget? iconSvg;
  final double? iconSize;
  final double? fontSize;
  final FontWeight? fontWeight;
  final Color? fontColor;
  final Color? backgroundColor;
  final Color? borderColor;
  final double? buttonWidth;
  final double? buttonHeight;
  final bool isLoading;
  final Color? overlayColor;

  const SecondaryButton(
      {Key? key,
      required this.title,
      required this.onPressed,
      this.isDisabled = false,
      this.buttonSize = ButtonSize.full,
      this.iconData,
      this.iconSvg,
      this.rightIconData,
      this.iconSize,
      this.fontSize,
      this.fontWeight,
      this.fontColor,
      this.backgroundColor,
      this.borderColor,
      this.buttonWidth,
      this.buttonHeight,
      this.overlayColor,
      this.isLoading = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: buttonHeight ?? 52,
      width: buttonWidth == null
          ? buttonSize.width
          : buttonWidth == 0
              ? null
              : buttonWidth,
      child: OutlinedButton(
        style: ButtonStyle(
          side: MaterialStateProperty.all<BorderSide>(
            BorderSide(color: borderColor ?? transparentColor),
          ),
          shape: MaterialStateProperty.all<RoundedRectangleBorder>(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(32),
            ),
          ),
          foregroundColor: MaterialStateProperty.resolveWith<Color?>(
            (Set<MaterialState> states) {
              if (states.contains(MaterialState.disabled)) {
                return mainWhite;
              }
              if (fontColor != null) {
                return fontColor;
              }
              return primaryColor;
            },
          ),
          backgroundColor: MaterialStateProperty.resolveWith<Color?>(
            (Set<MaterialState> states) {
              if (states.contains(MaterialState.disabled)) {
                return lighterGray;
              }
              if (backgroundColor != null) {
                return backgroundColor;
              }
              return secondaryColor;
            },
          ),
          overlayColor: MaterialStateProperty.resolveWith<Color?>(
            (Set<MaterialState> states) {
              if (states.contains(MaterialState.pressed)) {
                if (overlayColor != null) {
                  return overlayColor;
                }
                return darkerSecondaryColor;
              }
              if (states.contains(MaterialState.hovered)) {
                return darkSecondaryColor;
              }
              return null; // Defer to the widget's default.
            },
          ),
        ),
        onPressed: isDisabled ? null : onPressed,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            iconData != null
                ? Container(
                    padding: title != ""
                        ? const EdgeInsets.only(right: 8.0)
                        : const EdgeInsets.all(0),
                    child: Icon(
                      iconData,
                      size: iconSize ?? 18,
                    ),
                  )
                : const SizedBox.shrink(),
            iconSvg != null
                ? Container(
                    padding: const EdgeInsets.only(right: 8.0),
                    child: iconSvg,
                  )
                : const SizedBox.shrink(),
            title != ""
                ? Text(
                    title,
                    style: TextStyle(
                      fontSize: fontSize ?? 16,
                      fontWeight: fontWeight ?? FontWeight.w700,
                    ),
                  )
                : const SizedBox.shrink(),
            rightIconData != null
                ? Icon(
                    rightIconData,
                    size: iconSize ?? 18,
                  )
                : const SizedBox.shrink(),
            isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      color: primaryColor,
                      strokeWidth: 3,
                    ),
                  )
                : const SizedBox.shrink()
          ],
        ),
      ),
    );
  }
}