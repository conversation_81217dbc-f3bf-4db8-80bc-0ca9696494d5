import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'dart:io';
import 'package:flutter/foundation.dart'; // นำเข้า kIsWeb


class WebViewScreen extends StatefulWidget {
  final String initialUrl;

  WebViewScreen({Key? key, required this.initialUrl}) : super(key: key);

  @override
  _WebViewScreenState createState() => _WebViewScreenState();
}

class _WebViewScreenState extends State<WebViewScreen> {
  late WebViewController _controller;

  @override
  void initState() {
    super.initState();

    if (kIsWeb) {
      // ใช้ WebViewController สำหรับ Flutter Web
      _controller = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..loadRequest(Uri.parse(widget.initialUrl));
    } else {
      // ใช้ WebViewController สำหรับ Android/iOS
      _controller = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..loadRequest(Uri.parse(widget.initialUrl));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('WebView Screen'),
      ),
      body: WebViewWidget(controller: _controller),
    );
  }
}
