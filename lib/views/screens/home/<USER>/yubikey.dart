import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui';
import 'dart:js' as js;
import 'dart:html' as html;

import 'package:ags_authrest2/ags_authrest.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/controllers/login_controller/login.dart';
import 'package:mapp_ms24/controllers/utils/connect.dart';
import 'package:mapp_ms24/controllers/utils/url.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';
import 'package:mapp_ms24/views/screens/home/<USER>';
import 'package:mapp_ms24/views/screens/home/<USER>/appPremission.dart';

import 'package:nfc_manager/nfc_manager.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/services.dart';
import 'package:uni_links/uni_links.dart';
import 'package:uuid/uuid.dart';

class Record {
  static Record fromNdef(NdefRecord record) {
    if (record.typeNameFormat == NdefTypeNameFormat.nfcWellknown &&
        record.type.length == 1 &&
        record.type.first == 0x55) {
      return WellknownUriRecord.fromNdef(record);
    } else {
      return UnsupportedRecord(record);
    }
  }
}

class WellknownUriRecord implements Record {
  WellknownUriRecord({this.identifier, required this.uri});

  final Uint8List? identifier;

  final Uri uri;

  static WellknownUriRecord fromNdef(NdefRecord record) {
    final String prefix = NdefRecord.URI_PREFIX_LIST[record.payload.first];
    final Uint8List bodyBytes = record.payload.sublist(1);
    return WellknownUriRecord(
      identifier: record.identifier,
      uri: Uri.parse(prefix + utf8.decode(bodyBytes)),
    );
  }
}

class UnsupportedRecord implements Record {
  UnsupportedRecord(this.record);

  final NdefRecord record;

  static UnsupportedRecord fromNdef(NdefRecord record) {
    return UnsupportedRecord(record);
  }
}

class Yubikey {
  static String email = '';
  static String yubi_name = '';
  static String id_member = '';
  static String Grpc_Uri = "yubitg-nfh5zn25kq-as.a.run.app";
  static bool statusOTP = false;
  static TextEditingController _textController = TextEditingController();
  static var datetime = "00-00-0000T00:00:00";
  static var numdate = "0";

  static initState() {
    String nfcData = "No NFC data yet"; // ตัวแปรเพื่อเก็บข้อมูล NFC

    html.window.onMessage.listen((html.MessageEvent event) {
      if (event.data['type'] == 'nfc-data') {
        String nfcMessage = event.data['data'];

        nfcData = nfcMessage; // เก็บข้อมูล NFC ในตัวแปร
      }
    });
  }

  static getData() async {
    ProfileController profileController = Get.find<ProfileController>();

    email = profileController.responseMember!.email!;
    yubi_name = profileController.responseMember!.username!;
    id_member = profileController.responseMember!.id!;
  }

  static checkOTP() async {
    var id = id_member;
    Map data = {"personID": id};
    final response = await AppApi.callAPIjwt("POST", AppUrl.checkTWOFA, data);
    // var jsonResponse = json.decode(response);
    if (response["status"].toString() == "200" &&
        response["msg"][0]["ms_otp_tg"] != null) {
      statusOTP = true;
    } else {
      statusOTP = false;
    }
  }

  static String? otp;
  static String? mac;
  static sendTG_PKG(message) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var id = await prefs.getString('id');
    // var name = '${prefs.getString('fname')} ${prefs.getString('lname')}';
    List<dynamic> data = [
      {
        "emp": id,
        "fromdata": {"message": message}
      }
    ];

    AppHttps.postTG_PKG(AppHttps.sendTelegramPKG, data);
  }

  static String getOrCreateDeviceId() {
    var storedId = html.window.localStorage['device_id'];
    if (storedId == null) {
      var newId = Uuid().v4(); // สร้าง UUID ใหม่
      html.window.localStorage['device_id'] = newId;
      return newId;
    }
    return storedId;
  }

  static Future getMAc() async {
    getOrCreateDeviceId();
    var deviceInfo = DeviceInfoPlugin();
    if (kIsWeb) {
      // ดึงข้อมูลสำหรับ Web
      var webInfo = await deviceInfo.webBrowserInfo;
      return html.window.localStorage['device_id'];
    } else if (Platform.isIOS) {
      var iosDeviceInfo = await deviceInfo.iosInfo;
      return iosDeviceInfo.identifierForVendor; // unique ID on iOS
    } else if (Platform.isAndroid) {
      var androidDeviceInfo = await deviceInfo.androidInfo;
      return androidDeviceInfo.id; // unique ID on Android
    }
  }

  static Future Check_session() async {

    final prefs = await SharedPreferences.getInstance();
    final empId = prefs.getString('emp_id');
    final expireTimeStr = prefs.getString('session_expire');

    if (empId == null || expireTimeStr == null) {
      return false;
    }

    final expireTime = DateTime.parse(expireTimeStr);
    final now = DateTime.now();

    // ถ้าเลย expireTime แล้ว = session หมดอายุ
    if (now.isAfter(expireTime)) {
      await prefs.clear(); // clear session
      return false;
    }

    return true;
  }

  static useYubi(context) async {
    return _onNotSupportOTP(context);
    // _onInsertUbikey(context, true);
  }

  static _onNotSupportOTP(context) async {
    getData();
    SendTgotp(context, id_member);

    // dialogWithReqOTPBTN(context,"ui_success_otp_verify_header".tr.toString(),"USER_OTP".tr.toString(),"btn_next".tr.toString(),'', false, true);
  }

  static registerYubi(context) async {
    return _onInsertUbikey(context, false);
  }

  static _onInsertUbikey(context, bool varify) {
    nfcListener(context, varify);
    if (varify) {
      dialogWithReqOTP(
          context,
          "ui_scan_yubikey".tr.toString(),
          "ui_touch_yubikey".tr.toString(),
          "btn_cancel".tr.toString(),
          "ui_change_to_otp".tr.toString(),
          false,
          true);
    } else {
      popupDialog(
          context,
          "ui_scan_yubikey".tr.toString(),
          "ui_touch_yubikey".tr.toString(),
          "btn_cancel".tr.toString(),
          false,
          true);
    }
  }

  static _onWaitingOTP(context) {
    Navigator.of(context, rootNavigator: true).pop();
    popupDialog(context, "btn_register_OTP".tr.toString(),
        "ui_waiting".tr.toString(), "", false, true);
    RegisTgotp(context, id_member);
  }

  static _onSendingOTP(context) async {
    // Navigator.of(context, rootNavigator: true).pop();
    popupDialogWait(context, "ui_success_otp_sending".tr.toString(),
        "ui_waiting".tr.toString(), "", false, true);
    if (!statusOTP) {
      await RegisTgotpManual(id_member);
    }
  }

  static _onVerifyOTP(context) {
    dialogWithTextField(
        context,
        "ui_success_otp_verify_header".tr.toString(),
        "ui_success_otp_send".tr.toString(),
        "ui_comfirm".tr.toString(),
        "ui_otp_here".tr.toString(),
        false,
        true);
  }

  static _onWaitingAlertPressed(context, bool varify) {
    popupDialogWait(context, "ui_scan_yubikey".tr.toString(),
        "ui_waiting".tr.toString(), "", false, true);
    if (varify) {
      varifyYubikey(context);
    } else {
      registerYubikey(context);
    }
  }

  static _onSuccessAlertPressed(context, header, title) {
    popupDialogConfirm(
        context, header, title, "ui_contactkOK".tr, false, false);
    // popupDialogError(context,header,title,"ui_success_otp_verify_header".tr,false,false);
  }

  static _onSuccessAlertPressedVerify(context, header, title) {
    popupDialogError(
        context, header, title, "ui_success_otp_verify_header".tr, false, true);
    // popupDialogError(context,header,title,"ui_success_otp_verify_header".tr,false,false);
  }

  static _onDuplicate(context, title, detail) {
    popupDialog(context, title, detail, "ui_success_otp_verify_header".tr,
        false, false);
  }

  static _onSuccessAlertPressedWithRegisOTP(context) {
    dialogWithRegisOTP(
        context,
        "ui_register_twoFA".tr.toString(),
        "ui_success_yubikey".tr.toString(),
        "btn_register_OTP".tr.toString(),
        "",
        true,
        true);
  }

  static _onErrorAlertPressed(context, String error) {
    popupDialogError(context, "ui_success_otp_error".tr.toString(), error,
        "ui_TryAgain".tr, false, false);
  }

  static Future varifyYubikey(context) async {
    String? initialLink;

    initialLink = (await getInitialLink());
    try {
      var auth = Ags_restauth();
      auth.SECERT_JWT = 'fpw!vbe.epm9nhf6YPN';
      auth.R_USER = 'yubikeyms24';

      var headers = {
        'Authorization':
            auth.genTokenEncryp(), // genTokenEncryp() or genToken()
        'Content-Type': 'application/json'
      };

      var uniqueId = await getMAc();
      // var pathRoute = "verify";
      var bodyData = {
        "yubiId": otp?.substring(0, 12),
        "yubiAppname": "MS24",
        "yubiEmail": email,
        "mac_address": uniqueId,
        "yubiOtp": otp,
      };
      var body = json.encode(bodyData);
      var request = http.Request(
          'POST', Uri.parse('https://agilesoftgroup.com/cf_yubitg_ms/verify'));
      request.body = json
          .encode(auth.encrypbody(body)); //encrypbody require genTokenEncryp()
      request.headers.addAll(headers);
      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        var dataRaw = await response.stream.bytesToString();
        var data = json.decode(dataRaw);
        Navigator.of(context, rootNavigator: true).pop();
        if (data['status'] == 'success') {
          _onSuccessAlertPressed(context, "ui_scan_yubikey".tr.toString(),
              "ui_success_yubikey_verify".tr.toString());
          if (initialLink == null ||
              initialLink == "" ||
              initialLink == "null") {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (context) => HomeScreen()),
            );
          } else {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (context) => AppPermission()),
            );
          }
        } else {
          _onErrorAlertPressed(context, data['message']);
          // Navigator.push(
          //     context, MaterialPageRoute(builder: (context) => backupTA()));
        }
      } else {
        print(response.reasonPhrase);
      }
    } catch (e) {
      Navigator.of(context, rootNavigator: true).pop();
      _onErrorAlertPressed(context, "server error");
    }
  }

  static Future registerYubikey(context) async {
    try {
      var auth = Ags_restauth();
      auth.SECERT_JWT = 'fpw!vbe.epm9nhf6YPN';
      auth.R_USER = 'yubikeyms24';

      var headers = {
        'Authorization': auth.genTokenEncryp(),
        'Content-Type': 'application/json'
      };

      var bodyData = {
        "yubiAppname": "MS24",
        "yubiEmail": email,
        "yubiOtp": otp
      };

      var body = json.encode(bodyData);
      var request = http.Request('POST',
          Uri.parse('https://agilesoftgroup.com/cf_yubitg_ms/register'));
      request.body = json
          .encode(auth.encrypbody(body)); //encrypbody require genTokenEncryp()
      request.headers.addAll(headers);
      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        var dataRaw = await response.stream.bytesToString();
        var data = json.decode(dataRaw);
        Navigator.of(context, rootNavigator: true).pop();
        if (data['status'] == 'success') {
          sendTG_PKG(
              "คุณได้ทำการลงทะเบียน YUBIKEY สำหรับ 2FA MS เรียบร้อยแล้ว");
          if (!statusOTP) {
            _onSuccessAlertPressedWithRegisOTP(context);
          } else {
            _onSuccessAlertPressed(context, "ui_register_twoFA".tr.toString(),
                "ui_success_yubikey".tr.toString());
          }
        } else {
          _onErrorAlertPressed(context, data['message']);
        }
      } else {
        print(response.reasonPhrase);
      }
    } catch (e) {
      Navigator.of(context, rootNavigator: true).pop();
      _onErrorAlertPressed(context, "server error");
    }
  }

  static Future<void> RegisTgotpManual(emp_id) async {
    try {
      // final callgrpc = Cloudrun();
      //
      // callgrpc.SECERT_JWT = 'Eca)J.L@T9_vbgZ5MDpXUC';
      // callgrpc.R_USER = 'yubikeypkg';
      // callgrpc.Uri = Grpc_Uri;
      var auth = Ags_restauth();
      auth.SECERT_JWT = 'fpw!vbe.epm9nhf6YPN';
      auth.R_USER = 'yubikeyms24';

      var headers = {
        'Authorization':
            auth.genTokenEncryp(), // genTokenEncryp() or genToken()
        'Content-Type': 'application/json'
      };

      //var pathRoute = "registerTgotp";
      var bodyData = {
        "emp_id": emp_id.toString(),
      };

      var body = json.encode(bodyData);
      var request = http.Request('POST',
          Uri.parse('https://agilesoftgroup.com/cf_yubitg_ms/registerTgotp'));
      request.body = json
          .encode(auth.encrypbody(body)); //encrypbody require genTokenEncryp()
      request.headers.addAll(headers);
      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        var dataRaw = await response.stream.bytesToString();
        var data = json.decode(dataRaw);
        if (data['status'] == 'success') {
          sendTG_PKG(
              "คุณได้ทำการลงทะเบียน OTP Telegram สำหรับ 2FA MS เรียบร้อยแล้ว");
        }
        return;
      } else {
        print(response.reasonPhrase);
      }
    } catch (e) {
      return;
    }
  }

  static Future<void> RegisTgotp(context, emp_id) async {
    try {
      var auth = Ags_restauth();
      auth.SECERT_JWT = 'fpw!vbe.epm9nhf6YPN';
      auth.R_USER = 'yubikeyms24';

      var headers = {
        'Authorization': auth.genTokenEncryp(),
        'Content-Type': 'application/json'
      };

      var bodyData = {
        "emp_id": emp_id.toString(),
      };
      var body = json.encode(bodyData);
      var request = http.Request('POST',
          Uri.parse('https://agilesoftgroup.com/cf_yubitg_ms/registerTgotp'));
      request.body = json.encode(auth.encrypbody(body));
      request.headers.addAll(headers);
      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        var dataRaw = await response.stream.bytesToString();
        var data = json.decode(dataRaw);
        Navigator.of(context, rootNavigator: true).pop();
        if (data['status'] == 'success') {
          sendTG_PKG(
              "คุณได้ทำการลงทะเบียน OTP Telegram สำหรับ 2FA MS เรียบร้อยแล้ว");
          _onSuccessAlertPressed(context, "btn_register_OTP".tr.toString(),
              "ui_success_otp".tr.toString());
        } else {
          _onErrorAlertPressed(context, data['message']);
        }
      } else {
        print(response.reasonPhrase);
      }
    } catch (e) {
      Navigator.of(context, rootNavigator: true).pop();
      _onErrorAlertPressed(context, "server error");
    }
  }

  static Future SendTgotp(context, emp_id) async {
    _onSendingOTP(context);
    try {
      var auth = Ags_restauth();
      auth.SECERT_JWT = 'fpw!vbe.epm9nhf6YPN';
      auth.R_USER = 'yubikeyms24';

      var headers = {
        'Authorization': auth.genTokenEncryp(),
        'Content-Type': 'application/json'
      };

      var bodyData = {
        "emp_id": emp_id,
      };

      var body = json.encode(bodyData);
      var request = http.Request('POST',
          Uri.parse('https://agilesoftgroup.com/cf_yubitg_ms/sendTgotp'));
      request.body = json.encode(auth.encrypbody(body));
      request.headers.addAll(headers);
      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        var dataRaw = await response.stream.bytesToString();
        var data = json.decode(dataRaw);
        Navigator.of(context, rootNavigator: true).pop();
        if (data['status'] == 'success') {
          _onVerifyOTP(context);
          _onSuccessAlertPressedVerify(
              context,
              "ui_success_otp_sending".tr.toString(),
              "ui_success_otp_send".tr.toString());
        } else {
          _onErrorAlertPressed(context, data['message']);
        }
      } else {}
    } catch (e) {
      Navigator.of(context, rootNavigator: true).pop();
      _onErrorAlertPressed(context, "server error");
    }
  }

  static Future<void> VerifyTgotp(context, emp_id, otp) async {
    try {
      var auth = Ags_restauth();
      auth.SECERT_JWT = 'fpw!vbe.epm9nhf6YPN';
      auth.R_USER = 'yubikeyms24';

      var headers = {
        'Authorization': auth.genTokenEncryp(),
        'Content-Type': 'application/json'
      };
      var uniqueId = await getMAc();
      // var pathRoute = "verifyTgotp";
      var bodyData = {
        "emp_id": emp_id,
        "otp": otp,
        "mac_address": uniqueId,
      };

      var body = json.encode(bodyData);
      var request = http.Request('POST',
          Uri.parse('https://agilesoftgroup.com/cf_yubitg_ms/verifyTgotp'));
      request.body = json.encode(auth.encrypbody(body));
      request.headers.addAll(headers);
      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        var dataRaw = await response.stream.bytesToString();
        var data = json.decode(dataRaw);
        Navigator.of(context, rootNavigator: true).pop();
        if (data['status'] == 'success') {
          final prefs = await SharedPreferences.getInstance();

          await prefs.setString('emp_id', emp_id);
          final now = DateTime.now();
          final expireTime = now.add(Duration(hours: 12));
          await prefs.setString('session_expire', expireTime.toIso8601String());

          Navigator.pushReplacement(
            context,
            MaterialPageRoute(builder: (context) => HomeScreen()),
          );

          _onSuccessAlertPressed(context, "ui_success_otp_title".tr.toString(),
              "ui_success_otp_verify".tr.toString());
        } else {
          _onErrorAlertPressed(context, 'ui_success_otp_error1'.tr.toString());
        }
      } else {
        print(response.reasonPhrase);
      }
    } catch (e) {
      Navigator.of(context, rootNavigator: true).pop();
      _onErrorAlertPressed(context, "server error");
    }
  }

  static Future<void> nfcListener(context, bool varify) async {
    await getData();
    await checkOTP();
    initState();

    if (kIsWeb) {
      js.context.callMethod('startNfcScan', []);
      return;
    }

    final bool isAvailable = await NfcManager.instance.isAvailable();
    if (isAvailable) {
      NfcManager.instance.startSession(
          alertMessage: "ui_touch_yubikey".tr.toString(),
          onDiscovered: (NfcTag tag) async {
            final Ndef? tech = Ndef.from(tag);
            final NdefMessage? cachedMessage = tech!.cachedMessage;
            otp = '';
            if (cachedMessage != null) {
              for (int i in Iterable.generate(cachedMessage.records.length)) {
                final NdefRecord record = cachedMessage.records[i];
                final Record _record = Record.fromNdef(record);
                if (_record is WellknownUriRecord) {
                  otp = '${_record.uri}';
                  otp = otp!.split('#')[1];
                  Navigator.of(context, rootNavigator: true).pop();
                  _onWaitingAlertPressed(context, varify);
                  NfcManager.instance.stopSession();
                }
              }
            }
          });
    } else {
      Navigator.of(context, rootNavigator: true).pop();
      if (statusOTP) {
        if (varify) {
          _onNotSupportOTP(context);
        } else {
          _onDuplicate(context, "ui_duplicate_otp".tr.toString(),
              "ui_duplicate_otp_detail".tr.toString());
        }
      } else {
        dialogWithRegisOTP(
            context,
            "btn_register_OTP".tr.toString(),
            "not_support_NFC".tr.toString(),
            "btn_next".tr.toString(),
            "btn_cancel".tr.toString(),
            false,
            false);
      }
    }
  }

  static popupDialog(context_main, title, message, textBTN, type, img) {
    return showDialog(
      barrierDismissible: false,
      context: context_main,
      builder: (BuildContext context) {
        return BackdropFilter(
          filter: ImageFilter.blur(
            sigmaX: 5.0,
            sigmaY: 5.0,
          ),
          child: AlertDialog(
            titlePadding: EdgeInsets.fromLTRB(
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 82),
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 0),
            ),
            contentPadding: EdgeInsets.fromLTRB(
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 23),
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 0),
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.all(
                Radius.circular(
                  20.h,
                ),
              ),
              side: BorderSide(
                width: mediaQuery(context, "h", 1),
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            title: Container(
              child: Padding(
                padding: EdgeInsets.only(
                  bottom: mediaQuery(context, "h", 21.3),
                ),
                child: img ? Container() : Container(),
              ),
            ),
            content: SizedBox(
              width: 283.w,
              height: 336.h,
              child: Container(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    Text(
                      title,
                      style: TextStyle(
                        fontFamily: 'SukhumvitSet-Bold',
                        fontSize: mediaQuery(context, "h", 33),
                        color: Theme.of(context).colorScheme.onSecondary,
                        letterSpacing: mediaQuery(context, 'h', 1.32),
                        height: 0.8857142857142857,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(
                      height: mediaQuery(context, 'h', 30),
                    ),
                    img
                        ? Image.asset(
                            Get.isDarkMode
                                ? "assets/images/yubikey/otp_d.png"
                                : "assets/images/yubikey/otp_l.png",
                            height: mediaQuery(context, "h", 100),
                            fit: BoxFit.fitHeight,
                          )
                        : Image.asset(
                            Get.isDarkMode
                                ? "assets/images/yubikey/otpError_d.png"
                                : "assets/images/yubikey/otpError_l.png",
                            fit: BoxFit.fitHeight,
                            height: mediaQuery(context, "h", 100),
                            width: mediaQuery(context, "w", 100),
                          ),
                    SizedBox(
                      height: 10.h,
                    ),
                    Padding(
                      padding: EdgeInsets.only(
                        right: mediaQuery(context, "h", 100),
                        left: mediaQuery(context, "h", 100),
                      ),
                      child: Text(
                        message,
                        style: TextStyle(
                          fontFamily: 'SukhumvitSet-Medium',
                          fontSize: mediaQuery(context, "h", 28),
                          color: Theme.of(context).colorScheme.onSecondary,
                          letterSpacing: mediaQuery(context, "h", 1.12),
                          height: 1.7,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    SizedBox(
                      height: 30.h,
                    ),
                    textBTN != ""
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: <Widget>[
                              GestureDetector(
                                onTap: () {
                                  Navigator.pop(context, true);
                                  // Loader.hide2(context);
                                },
                                child: SizedBox(
                                  width: 235.w,
                                  height: 46.h,
                                  child: Container(
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(30.h),
                                      color: Theme.of(context)
                                          .colorScheme
                                          .secondaryContainer
                                          .withOpacity(0.6),
                                      border: Border.all(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .secondaryContainer,
                                        width: mediaQuery(context, "h", 2),
                                      ),
                                    ),
                                    child: Text(
                                      textBTN.toString(),
                                      style: TextStyle(
                                        fontFamily: 'SukhumvitSet-Bold',
                                        fontSize: mediaQuery(context, "h", 30),
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSecondary,
                                        letterSpacing:
                                            mediaQuery(context, "h", 1.2),
                                        fontWeight: FontWeight.w700,
                                        height: 1.0333333333333334,
                                        shadows: [
                                          Shadow(
                                            color: const Color(0x26000000),
                                            offset: Offset(0, 1),
                                            blurRadius: 1,
                                          )
                                        ],
                                      ),
                                      textHeightBehavior: TextHeightBehavior(
                                          applyHeightToFirstAscent: false),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          )
                        : Container(),
                    SizedBox(
                      height: 20.h,
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).pop(); // ปิด dialog
                      },
                      child: const Text(
                        "ยกเลิก",
                        style: TextStyle(color: Colors.black),
                      ),
                    ),
                    textBTN != ""
                        ? SizedBox(
                            height: mediaQuery(context, 'h', 75),
                          )
                        : Container(),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  static popupDialogWait(context_main, title, message, textBTN, type, img) {
    return showDialog(
      barrierDismissible: false,
      context: context_main,
      builder: (BuildContext context) {
        return BackdropFilter(
          filter: ImageFilter.blur(
            sigmaX: 5.0,
            sigmaY: 5.0,
          ),
          child: AlertDialog(
            titlePadding: EdgeInsets.fromLTRB(
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 82),
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 0),
            ),
            contentPadding: EdgeInsets.fromLTRB(
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 23),
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 0),
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.all(
                Radius.circular(
                  20.h,
                ),
              ),
              side: BorderSide(
                width: mediaQuery(context, "h", 1),
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            title: Container(
              child: Padding(
                padding: EdgeInsets.only(
                  bottom: mediaQuery(context, "h", 21.3),
                ),
                child: img ? Container() : Container(),
              ),
            ),
            content: SizedBox(
              width: 283.w,
              height: 202.h,
              child: Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    Text(
                      title,
                      style: TextStyle(
                        fontFamily: 'Sukhumvit Set',
                        fontSize: 16.sp,
                        color: Theme.of(context).colorScheme.onSecondary,
                        letterSpacing: mediaQuery(context, 'h', 1.32),
                        height: 0.8857142857142857,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(
                      height: mediaQuery(context, 'h', 30),
                    ),
                    img
                        ? Image.asset(
                            Get.isDarkMode
                                ? "assets/images/yubikey/otp_d.png"
                                : "assets/images/yubikey/otp_l.png",
                            height: mediaQuery(context, "h", 100),
                            fit: BoxFit.fitHeight,
                          )
                        : Image.asset(
                            Get.isDarkMode
                                ? "assets/images/yubikey/otpError_d.png"
                                : "assets/images/yubikey/otpError_l.png",
                            fit: BoxFit.fitHeight,
                            height: mediaQuery(context, "h", 100),
                            width: mediaQuery(context, "w", 100),
                          ),
                    SizedBox(
                      height: mediaQuery(context, 'h', 20),
                    ),
                    Padding(
                      padding: EdgeInsets.only(
                        right: 30.w,
                        left: 30.w,
                      ),
                      child: Text(
                        message,
                        style: TextStyle(
                          fontFamily: 'Sukhumvit Set',
                          fontSize: 14.sp,
                          color: Theme.of(context).colorScheme.onSecondary,
                          letterSpacing: mediaQuery(context, "h", 1.12),
                          height: 1.3928571428571428,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    SizedBox(
                      height: mediaQuery(context, 'h', 71),
                    ),
                    textBTN != ""
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: <Widget>[
                              GestureDetector(
                                onTap: () {
                                  Navigator.pop(context, true);
                                  // Loader.hide2(context);
                                },
                                child: SizedBox(
                                  width: 235.w,
                                  height: 46.h,
                                  child: Container(
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(30.h),
                                      color: Theme.of(context)
                                          .colorScheme
                                          .secondaryContainer
                                          .withOpacity(0.6),
                                      border: Border.all(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .secondaryContainer,
                                        width: mediaQuery(context, "h", 2),
                                      ),
                                    ),
                                    child: Text(
                                      textBTN.toString(),
                                      style: TextStyle(
                                        fontFamily: 'Sukhumvit Set',
                                        fontSize: 16.sp,
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSecondary,
                                        letterSpacing:
                                            mediaQuery(context, "h", 1.2),
                                        fontWeight: FontWeight.w700,
                                        height: 1.0333333333333334,
                                        shadows: [
                                          Shadow(
                                            color: const Color(0x26000000),
                                            offset: Offset(0, 1),
                                            blurRadius: 1,
                                          )
                                        ],
                                      ),
                                      textHeightBehavior: TextHeightBehavior(
                                          applyHeightToFirstAscent: false),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          )
                        : Container(),
                    textBTN != ""
                        ? SizedBox(
                            height: mediaQuery(context, 'h', 75),
                          )
                        : Container(),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  static popupDialogError(context_main, title, message, textBTN, type, img) {
    return showDialog(
      barrierDismissible: false,
      context: context_main,
      builder: (BuildContext context) {
        return BackdropFilter(
          filter: ImageFilter.blur(
            sigmaX: 5.0,
            sigmaY: 5.0,
          ),
          child: AlertDialog(
            titlePadding: EdgeInsets.fromLTRB(
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 82),
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 0),
            ),
            contentPadding: EdgeInsets.fromLTRB(
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 23),
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 0),
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.all(
                Radius.circular(
                  20.h,
                ),
              ),
              side: BorderSide(
                width: mediaQuery(context, "h", 1),
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            title: Container(
              child: Padding(
                padding: EdgeInsets.only(
                  bottom: mediaQuery(context, "h", 21.3),
                ),
                child: img ? Container() : Container(),
              ),
            ),
            content: SizedBox(
              width: 283.w,
              height: 320.h,
              child: Container(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    Text(
                      title,
                      style: TextStyle(
                        fontFamily: 'Sukhumvit Set',
                        fontSize: 14.sp,
                        color: Theme.of(context).colorScheme.onSecondary,
                        letterSpacing: mediaQuery(context, 'h', 1.32),
                        height: 0.8857142857142857,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(
                      height: mediaQuery(context, 'h', 30),
                    ),
                    img
                        ? Image.asset(
                            Get.isDarkMode
                                ? "assets/images/yubikey/otp_d.png"
                                : "assets/images/yubikey/otp_l.png",
                            height: mediaQuery(context, "h", 100),
                            fit: BoxFit.fitHeight,
                          )
                        : Image.asset(
                            Get.isDarkMode
                                ? "assets/images/yubikey/otpError_d.png"
                                : "assets/images/yubikey/otpError_l.png",
                            fit: BoxFit.fitHeight,
                            height: mediaQuery(context, "h", 100),
                            width: mediaQuery(context, "w", 100),
                          ),
                    SizedBox(
                      height: 20.h,
                    ),
                    Container(
                      width: 235.w,
                      child: Padding(
                        padding: EdgeInsets.only(
                          right: 20.w,
                          left: 20.w,
                        ),
                        child: Text(
                          message,
                          style: TextStyle(
                            fontFamily: 'Sukhumvit Set',
                            fontSize: 12.sp,
                            color: Theme.of(context).colorScheme.onSecondary,
                            letterSpacing: mediaQuery(context, "h", 1.12),
                            height: 1.7,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 30.h,
                    ),
                    textBTN != ""
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: <Widget>[
                              GestureDetector(
                                onTap: () {
                                  Navigator.pop(context, true);
                                  // Loader.hide2(context);
                                },
                                child: SizedBox(
                                  width: 235.w,
                                  height: 46.h,
                                  child: Container(
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(30.h),
                                      color: Theme.of(context)
                                          .colorScheme
                                          .secondaryContainer
                                          .withOpacity(0.6),
                                      border: Border.all(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .secondaryContainer,
                                        width: mediaQuery(context, "h", 2),
                                      ),
                                    ),
                                    child: Text(
                                      textBTN.toString(),
                                      style: TextStyle(
                                        fontFamily: 'Sukhumvit Set',
                                        fontSize: 14.sp,
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSecondary,
                                        letterSpacing:
                                            mediaQuery(context, "h", 1.2),
                                        height: 1.0333333333333334,
                                        shadows: [
                                          Shadow(
                                            color: const Color(0x26000000),
                                            offset: Offset(0, 1),
                                            blurRadius: 1,
                                          )
                                        ],
                                      ),
                                      textHeightBehavior: TextHeightBehavior(
                                          applyHeightToFirstAscent: false),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          )
                        : Container(),
                    SizedBox(
                      height: 20.h,
                    ),
                    TextButton(
                        onPressed: () {
                          Navigator.of(context).pop(); // ปิด dialog
                        },
                        child: Text(
                          'btn_cancel'.tr,
                          style: TextStyle(
                            fontFamily: 'Sukhumvit Set',
                            fontSize: 12.sp,
                            color: Theme.of(context).colorScheme.onSecondary,
                            letterSpacing: mediaQuery(context, "h", 1.2),
                            height: 1.0333333333333334,
                            shadows: [
                              Shadow(
                                color: const Color(0x26000000),
                                offset: Offset(0, 1),
                                blurRadius: 1,
                              )
                            ],
                          ),
                          textHeightBehavior: TextHeightBehavior(
                              applyHeightToFirstAscent: false),
                          textAlign: TextAlign.center,
                        )),
                    textBTN != ""
                        ? SizedBox(
                            height: mediaQuery(context, 'h', 75),
                          )
                        : Container(),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  static popupDialogConfirm(context_main, title, message, textBTN, type, img) {
    return showDialog(
      barrierDismissible: false,
      context: context_main,
      builder: (BuildContext context) {
        return BackdropFilter(
          filter: ImageFilter.blur(
            sigmaX: 5.0,
            sigmaY: 5.0,
          ),
          child: AlertDialog(
            titlePadding: EdgeInsets.fromLTRB(
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 82),
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 0),
            ),
            contentPadding: EdgeInsets.fromLTRB(
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 23),
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 0),
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.all(
                Radius.circular(
                  20.h,
                ),
              ),
              side: BorderSide(
                width: mediaQuery(context, "h", 1),
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            title: Container(
              child: Padding(
                padding: EdgeInsets.only(
                  bottom: mediaQuery(context, "h", 21.3),
                ),
                child: img ? Container() : Container(),
              ),
            ),
            content: SizedBox(
              width: 283.w,
              height: 283.h,
              child: Container(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    Text(
                      title,
                      style: TextStyle(
                        fontFamily: 'Sukhumvit Set',
                        fontSize: 16.sp,
                        color: Theme.of(context).colorScheme.onSecondary,
                        letterSpacing: mediaQuery(context, 'h', 1.32),
                        height: 0.8857142857142857,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(
                      height: mediaQuery(context, 'h', 30),
                    ),
                    img
                        ? Image.asset(
                            Get.isDarkMode
                                ? "assets/images/yubikey/otp_d.png"
                                : "assets/images/yubikey/otp_l.png",
                            height: mediaQuery(context, "h", 100),
                            fit: BoxFit.fitHeight,
                          )
                        : Image.asset(
                            Get.isDarkMode
                                ? "assets/images/yubikey/otpConfirm_D.png"
                                : "assets/images/yubikey/otpConfirm_l.png",
                            fit: BoxFit.fitHeight,
                            height: mediaQuery(context, "h", 100),
                            width: mediaQuery(context, "w", 100),
                          ),
                    SizedBox(
                      height: 10.h,
                    ),
                    Padding(
                      padding: EdgeInsets.only(
                        right: mediaQuery(context, "h", 100),
                        left: mediaQuery(context, "h", 100),
                      ),
                      child: Text(
                        message,
                        style: TextStyle(
                          fontFamily: 'Sukhumvit Set',
                          fontSize: 14.sp,
                          color: Theme.of(context).colorScheme.onSecondary,
                          letterSpacing: mediaQuery(context, "h", 1.12),
                          height: 1.7,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    SizedBox(
                      height: 30.h,
                    ),
                    textBTN != ""
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: <Widget>[
                              GestureDetector(
                                onTap: () {
                                  Navigator.pop(context, true);
                                  // Loader.hide2(context);
                                },
                                child: SizedBox(
                                  width: 235.w,
                                  height: 46.h,
                                  child: Container(
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(30.h),
                                      color: Theme.of(context)
                                          .colorScheme
                                          .secondaryContainer
                                          .withOpacity(0.6),
                                      border: Border.all(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .secondaryContainer,
                                        width: mediaQuery(context, "h", 2),
                                      ),
                                    ),
                                    child: Text(
                                      textBTN.toString(),
                                      style: TextStyle(
                                        fontFamily: 'Sukhumvit Set',
                                        fontSize: 16.sp,
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSecondary,
                                        letterSpacing:
                                            mediaQuery(context, "h", 1.2),
                                        fontWeight: FontWeight.w700,
                                        height: 1.0333333333333334,
                                        shadows: [
                                          Shadow(
                                            color: const Color(0x26000000),
                                            offset: Offset(0, 1),
                                            blurRadius: 1,
                                          )
                                        ],
                                      ),
                                      textHeightBehavior: TextHeightBehavior(
                                          applyHeightToFirstAscent: false),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          )
                        : Container(),
                    textBTN != ""
                        ? SizedBox(
                            height: mediaQuery(context, 'h', 75),
                          )
                        : Container(),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  static dialogWithRegisOTP(
      context_main, title, message, textConfirm, textBTN, type, img) {
    return showDialog(
      barrierDismissible: false,
      context: context_main,
      builder: (BuildContext context) {
        return BackdropFilter(
          filter: ImageFilter.blur(
            sigmaX: 5.0,
            sigmaY: 5.0,
          ),
          child: AlertDialog(
            titlePadding: EdgeInsets.fromLTRB(
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 82),
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 0),
            ),
            contentPadding: EdgeInsets.fromLTRB(
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 23),
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 0),
            ),
            backgroundColor:
                Theme.of(context).colorScheme.primary.withOpacity(0.9),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.all(
                Radius.circular(
                  mediaQuery(context, 'h', 80),
                ),
              ),
              side: BorderSide(
                width: mediaQuery(context, "h", 1),
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            title: Container(
              child: Padding(
                padding: EdgeInsets.only(
                  bottom: mediaQuery(context, "h", 21.3),
                ),
                child: SvgPicture.string(
                  !type
                      ? '<svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M13 6.5C13.715 6.5 14.3 7.085 14.3 7.8V13C14.3 13.715 13.715 14.3 13 14.3C12.285 14.3 11.7 13.715 11.7 13V7.8C11.7 7.085 12.285 6.5 13 6.5ZM12.987 0C5.811 0 0 5.824 0 13C0 20.176 5.811 26 12.987 26C20.176 26 26 20.176 26 13C26 5.824 20.176 0 12.987 0ZM13 23.4C7.254 23.4 2.6 18.746 2.6 13C2.6 7.254 7.254 2.6 13 2.6C18.746 2.6 23.4 7.254 23.4 13C23.4 18.746 18.746 23.4 13 23.4ZM14.3 19.5H11.7V16.9H14.3V19.5Z" fill="#FEE095"/></svg>'
                      : '<svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg"> <circle cx="14" cy="14" r="12.25" stroke="#FEE095" stroke-width="2.5"/><path d="M9 14.5L12.75 18L19 11" stroke="#FEE095" stroke-width="2.5" stroke-linecap="round"/></svg>',
                  allowDrawingOutsideViewBox: true,
                  fit: BoxFit.fitHeight,
                  color: Theme.of(context).colorScheme.secondaryContainer,
                  height: mediaQuery(context, "h", 50),
                  width: mediaQuery(context, "h", 50),
                ),
              ),
            ),
            content: Container(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Text(
                    title,
                    style: TextStyle(
                      fontFamily: 'SukhumvitSet-Bold',
                      fontSize: mediaQuery(context, "h", 33),
                      color: Theme.of(context).colorScheme.secondaryContainer,
                      letterSpacing: mediaQuery(context, 'h', 1.32),
                      height: 0.8857142857142857,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(
                    height: mediaQuery(context, 'h', 30),
                  ),
                  img
                      ? Image.asset(
                          "assets/images/yubikey/yubikey.png",
                          height: mediaQuery(context, "h", 150),
                          fit: BoxFit.fitHeight,
                        )
                      : Container(),
                  SizedBox(
                    height: mediaQuery(context, 'h', 10),
                  ),
                  Padding(
                    padding: EdgeInsets.only(
                      right: mediaQuery(context, "h", 100),
                      left: mediaQuery(context, "h", 100),
                    ),
                    child: Text(
                      message,
                      style: TextStyle(
                        fontFamily: 'SukhumvitSet-Medium',
                        fontSize: mediaQuery(context, "h", 28),
                        color: message == "not_support_NFC".tr.toString()
                            ? const Color(0xffFF8282)
                            : Theme.of(context).colorScheme.onSecondary,
                        letterSpacing: mediaQuery(context, "h", 1.12),
                        height: 1.3928571428571428,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  SizedBox(
                    height: mediaQuery(context, 'h', 71),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      GestureDetector(
                        onTap: () {
                          _onWaitingOTP(context_main);
                        },
                        child: SizedBox(
                          width: mediaQuery(context, 'w', 250),
                          height: mediaQuery(context, 'h', 80),
                          child: Container(
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(
                                  mediaQuery(context, "h", 30)),
                              color: Theme.of(context)
                                  .colorScheme
                                  .secondaryContainer
                                  .withOpacity(0.6),
                              boxShadow: [
                                BoxShadow(
                                  color: const Color(0x1a000000),
                                  offset: Offset(0, 1),
                                  blurRadius: 2,
                                ),
                              ],
                            ),
                            child: Text(
                              textConfirm.toString(),
                              style: TextStyle(
                                fontFamily: 'SukhumvitSet-Bold',
                                fontSize: mediaQuery(context, "h", 30),
                                color: Theme.of(context).colorScheme.primary,
                                letterSpacing: mediaQuery(context, "h", 1.2),
                                fontWeight: FontWeight.w700,
                                height: 1.0333333333333334,
                                shadows: [
                                  Shadow(
                                    color: const Color(0x26000000),
                                    offset: Offset(0, 1),
                                    blurRadius: 1,
                                  )
                                ],
                              ),
                              textHeightBehavior: TextHeightBehavior(
                                  applyHeightToFirstAscent: false),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  textBTN != ""
                      ? SizedBox(
                          height: mediaQuery(context, 'h', 20),
                        )
                      : Container(),
                  textBTN != ""
                      ? Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: <Widget>[
                            GestureDetector(
                              onTap: () {
                                Navigator.pop(context, true);
                                // Loader.hide2(context);
                              },
                              child: SizedBox(
                                width: mediaQuery(context, 'w', 250),
                                height: mediaQuery(context, 'h', 80),
                                child: Container(
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(
                                        mediaQuery(context, "h", 30)),
                                    color:
                                        Theme.of(context).colorScheme.primary,
                                    boxShadow: [
                                      BoxShadow(
                                        color: const Color(0x1a000000),
                                        offset: Offset(0, 1),
                                        blurRadius: 2,
                                      ),
                                    ],
                                  ),
                                  child: Text(
                                    textBTN.toString(),
                                    style: TextStyle(
                                      fontFamily: 'SukhumvitSet-Bold',
                                      fontSize: mediaQuery(context, "h", 30),
                                      color: Theme.of(context)
                                          .colorScheme
                                          .onSecondary,
                                      letterSpacing:
                                          mediaQuery(context, "h", 1.2),
                                      fontWeight: FontWeight.w700,
                                      height: 1.0333333333333334,
                                      shadows: [
                                        Shadow(
                                          color: const Color(0x26000000),
                                          offset: Offset(0, 1),
                                          blurRadius: 1,
                                        )
                                      ],
                                    ),
                                    textHeightBehavior: TextHeightBehavior(
                                        applyHeightToFirstAscent: false),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        )
                      : Container(),
                  SizedBox(
                    height: mediaQuery(context, 'h', 75),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  static dialogWithReqOTP(
      context_main, title, message, textBTN, textPath, type, img) {
    return showDialog(
      barrierDismissible: false,
      context: context_main,
      builder: (BuildContext context) {
        return BackdropFilter(
          filter: ImageFilter.blur(
            sigmaX: 5.0,
            sigmaY: 5.0,
          ),
          child: AlertDialog(
            titlePadding: EdgeInsets.fromLTRB(
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 82),
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 0),
            ),
            contentPadding: EdgeInsets.fromLTRB(
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 23),
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 0),
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.all(
                Radius.circular(
                  mediaQuery(context, 'h', 80),
                ),
              ),
              side: BorderSide(
                width: mediaQuery(context, "h", 1),
                color: Theme.of(context).colorScheme.onPrimary,
              ),
            ),
            content: Container(
              margin: EdgeInsets.only(
                top: mediaQuery(context, 'h', 80),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Text(
                    title,
                    style: TextStyle(
                      fontFamily: 'SukhumvitSet-Bold',
                      fontSize: mediaQuery(context, "h", 33),
                      color: Theme.of(context).colorScheme.onSecondary,
                      letterSpacing: mediaQuery(context, 'h', 1.32),
                      height: 0.8857142857142857,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(
                    height: mediaQuery(context, 'h', 30),
                  ),
                  img
                      ? Image.asset(
                          Get.isDarkMode
                              ? "assets/images/yubikey/yubikey_D.png"
                              : "assets/images/yubikey/yubikey_B.png",
                          height: mediaQuery(context, "h", 100),
                          fit: BoxFit.fitHeight,
                        )
                      : Container(),
                  SizedBox(
                    height: mediaQuery(context, 'h', 15),
                  ),
                  Padding(
                    padding: EdgeInsets.only(
                      right: mediaQuery(context, "h", 100),
                      left: mediaQuery(context, "h", 100),
                    ),
                    child: Text(
                      message,
                      style: TextStyle(
                        fontFamily: 'SukhumvitSet-Medium',
                        fontSize: mediaQuery(context, "h", 28),
                        color: Theme.of(context).colorScheme.onSecondary,
                        letterSpacing: mediaQuery(context, "h", 1.12),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  SizedBox(
                    height: mediaQuery(context, 'h', 30),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      GestureDetector(
                        onTap: () {
                          Navigator.pop(context, true);
                          // Loader.hide2(context);
                        },
                        child: SizedBox(
                          width: mediaQuery(context, 'w', 400),
                          height: mediaQuery(context, 'h', 80),
                          child: Container(
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(
                                  mediaQuery(context, "h", 50)),
                              color: Theme.of(context)
                                  .colorScheme
                                  .secondaryContainer
                                  .withOpacity(0.5),
                              border: Border.all(
                                color: Theme.of(context)
                                    .colorScheme
                                    .secondaryContainer,
                                width: mediaQuery(context, "h", 2),
                              ),
                            ),
                            child: Text(
                              textBTN.toString(),
                              style: TextStyle(
                                fontFamily: 'Sukhumvit Set',
                                fontSize: 14.sp,
                                color:
                                    Theme.of(context).colorScheme.onSecondary,
                                letterSpacing: mediaQuery(context, "h", 1.2),
                                fontWeight: FontWeight.w500,
                                shadows: [
                                  Shadow(
                                    color: const Color(0x26000000),
                                    offset: Offset(0, 1),
                                    blurRadius: 1,
                                  )
                                ],
                              ),
                              textHeightBehavior: TextHeightBehavior(
                                  applyHeightToFirstAscent: false),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: mediaQuery(context, 'h', 40),
                  ),
                  GestureDetector(
                    onTap: () {
                      SendTgotp(context_main, id_member);
                    },
                    child: Container(
                      child: Text(
                        textPath,
                        style: TextStyle(
                          fontFamily: 'Sukhumvit Set',
                          fontSize: 12.sp,
                          color: Theme.of(context).colorScheme.onSecondary,
                          letterSpacing: mediaQuery(context, "h", 1),
                          fontWeight: FontWeight.w400,
                          decoration: TextDecoration.underline,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                  SizedBox(
                    height: mediaQuery(context, 'h', 75),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  static dialogWithReqOTPBTN(
      context_main, title, message, textConfirm, textBTN, type, img) {
    getData();
    return showDialog(
      barrierDismissible: false,
      context: context_main,
      builder: (BuildContext context) {
        return BackdropFilter(
          filter: ImageFilter.blur(
            sigmaX: 5.0,
            sigmaY: 5.0,
          ),
          child: AlertDialog(
            titlePadding: EdgeInsets.fromLTRB(
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 82),
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 0),
            ),
            contentPadding: EdgeInsets.fromLTRB(
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 23),
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 0),
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.all(
                Radius.circular(
                  mediaQuery(context, 'h', 80),
                ),
              ),
              side: BorderSide(
                width: mediaQuery(context, "h", 1),
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            title: Container(
              child: Padding(
                padding: EdgeInsets.only(
                  bottom: mediaQuery(context, "h", 21.3),
                ),
                child: SvgPicture.string(
                  !type
                      ? '<svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M13 6.5C13.715 6.5 14.3 7.085 14.3 7.8V13C14.3 13.715 13.715 14.3 13 14.3C12.285 14.3 11.7 13.715 11.7 13V7.8C11.7 7.085 12.285 6.5 13 6.5ZM12.987 0C5.811 0 0 5.824 0 13C0 20.176 5.811 26 12.987 26C20.176 26 26 20.176 26 13C26 5.824 20.176 0 12.987 0ZM13 23.4C7.254 23.4 2.6 18.746 2.6 13C2.6 7.254 7.254 2.6 13 2.6C18.746 2.6 23.4 7.254 23.4 13C23.4 18.746 18.746 23.4 13 23.4ZM14.3 19.5H11.7V16.9H14.3V19.5Z" fill="#FEE095"/></svg>'
                      : '<svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg"> <circle cx="14" cy="14" r="12.25" stroke="#FEE095" stroke-width="2.5"/><path d="M9 14.5L12.75 18L19 11" stroke="#FEE095" stroke-width="2.5" stroke-linecap="round"/></svg>',
                  allowDrawingOutsideViewBox: true,
                  fit: BoxFit.fitHeight,
                  color: Theme.of(context).colorScheme.secondaryContainer,
                  height: mediaQuery(context, "h", 50),
                  width: mediaQuery(context, "h", 50),
                ),
              ),
            ),
            content: Container(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Text(
                    title,
                    style: TextStyle(
                      fontFamily: 'SukhumvitSet-Bold',
                      fontSize: mediaQuery(context, "h", 33),
                      color: Theme.of(context).colorScheme.secondaryContainer,
                      letterSpacing: mediaQuery(context, 'h', 1.32),
                      height: 0.8857142857142857,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(
                    height: mediaQuery(context, 'h', 30),
                  ),
                  img
                      ? Image.asset(
                          "assets/images/yubikey/yubikey.png",
                          height: mediaQuery(context, "h", 150),
                          fit: BoxFit.fitHeight,
                        )
                      : Container(),
                  SizedBox(
                    height: mediaQuery(context, 'h', 10),
                  ),
                  Padding(
                    padding: EdgeInsets.only(
                      right: mediaQuery(context, "h", 100),
                      left: mediaQuery(context, "h", 100),
                    ),
                    child: Text(
                      message,
                      style: TextStyle(
                        fontFamily: 'SukhumvitSet-Medium',
                        fontSize: mediaQuery(context, "h", 28),
                        color: message == "not_support_NFC".tr.toString()
                            ? const Color(0xffFF8282)
                            : Theme.of(context).colorScheme.onSecondary,
                        letterSpacing: mediaQuery(context, "h", 1.12),
                        height: 1.3928571428571428,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  SizedBox(
                    height: mediaQuery(context, 'h', 71),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      GestureDetector(
                        onTap: () {
                          // Navigator.pop(context, true);
                          SendTgotp(context_main, id_member);
                        },
                        child: SizedBox(
                          width: 235.w,
                          height: 46.h,
                          child: Container(
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(
                                  mediaQuery(context, "h", 30)),
                              color: Theme.of(context)
                                  .colorScheme
                                  .secondaryContainer
                                  .withOpacity(0.5),
                              boxShadow: [
                                BoxShadow(
                                  color: const Color(0x1a000000),
                                  offset: Offset(0, 1),
                                  blurRadius: 2,
                                ),
                              ],
                            ),
                            child: Text(
                              textConfirm.toString(),
                              style: TextStyle(
                                fontFamily: 'SukhumvitSet-Bold',
                                fontSize: mediaQuery(context, "h", 30),
                                color:
                                    Theme.of(context).colorScheme.onSecondary,
                                letterSpacing: mediaQuery(context, "h", 1.2),
                                fontWeight: FontWeight.w700,
                                height: 1.0333333333333334,
                                shadows: [
                                  Shadow(
                                    color: const Color(0x26000000),
                                    offset: Offset(0, 1),
                                    blurRadius: 1,
                                  )
                                ],
                              ),
                              textHeightBehavior: TextHeightBehavior(
                                  applyHeightToFirstAscent: false),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  textBTN != ""
                      ? SizedBox(
                          height: mediaQuery(context, 'h', 20),
                        )
                      : Container(),
                  textBTN != ""
                      ? Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: <Widget>[
                            GestureDetector(
                              onTap: () {
                                Navigator.pop(context, true);
                                // Loader.hide2(context);
                              },
                              child: SizedBox(
                                width: mediaQuery(context, 'w', 250),
                                height: mediaQuery(context, 'h', 80),
                                child: Container(
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(
                                        mediaQuery(context, "h", 30)),
                                    color:
                                        Theme.of(context).colorScheme.primary,
                                  ),
                                  child: Text(
                                    textBTN.toString(),
                                    style: TextStyle(
                                      fontFamily: 'SukhumvitSet-Bold',
                                      fontSize: mediaQuery(context, "h", 30),
                                      color: Theme.of(context)
                                          .colorScheme
                                          .onSecondary,
                                      letterSpacing:
                                          mediaQuery(context, "h", 1.2),
                                      fontWeight: FontWeight.w700,
                                      height: 1.0333333333333334,
                                      shadows: [
                                        Shadow(
                                          color: const Color(0x26000000),
                                          offset: Offset(0, 1),
                                          blurRadius: 1,
                                        )
                                      ],
                                    ),
                                    textHeightBehavior: TextHeightBehavior(
                                        applyHeightToFirstAscent: false),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        )
                      : Container(),
                  SizedBox(
                    height: mediaQuery(context, 'h', 75),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  static dialogWithTextField(
      context_main, title, message, textBTN, textField, type, img) {
    _textController.clear();
    return showDialog(
      barrierDismissible: false,
      context: context_main,
      builder: (BuildContext context) {
        return BackdropFilter(
          filter: ImageFilter.blur(
            sigmaX: 5.0,
            sigmaY: 5.0,
          ),
          child: AlertDialog(
            titlePadding: EdgeInsets.fromLTRB(
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 82),
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 0),
            ),
            contentPadding: EdgeInsets.fromLTRB(
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 23),
              mediaQuery(context, 'h', 0),
              mediaQuery(context, 'h', 0),
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.all(
                Radius.circular(20.h),
              ),
              side: BorderSide(
                width: mediaQuery(context, "h", 1),
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            title: Container(
              width: 283.w,
              height: 340.h,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Text(
                    title,
                    style: TextStyle(
                      fontFamily: 'Sukhumvit Set',
                      fontSize: 14.sp,
                      color: Theme.of(context).colorScheme.onSecondary,
                      letterSpacing: mediaQuery(context, 'h', 1.32),
                      height: 0.8857142857142857,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(
                    // height: mediaQuery(context, 'h', 30),
                    height: 20.h,
                  ),
                  img
                      ? Image.asset(
                          Get.isDarkMode
                              ? "assets/images/yubikey/Group_D.png"
                              : "assets/images/yubikey/Group_L.png",
                          height: 48.h,
                          width: 43.w,
                          fit: BoxFit.fitHeight,
                        )
                      : Container(),
                  SizedBox(
                    height: 20.h,
                  ),
                  Container(
                      width: 235.w,
                      child: Padding(
                        padding: EdgeInsets.only(
                          right: 20.w,
                          left: 20.w,
                        ),
                        // height: 73.h,
                        child: Text(
                          message,
                          style: TextStyle(
                            fontFamily: 'Sukhumvit Set',
                            fontSize: 12.h,
                            color: Theme.of(context).colorScheme.onSecondary,
                            letterSpacing: mediaQuery(context, "h", 1.7),
                            height: 1.7,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      )),
                  Padding(
                    padding: EdgeInsets.only(
                      right: mediaQuery(context, "w", 50),
                      left: mediaQuery(context, "w", 50),
                    ),
                    child: Text(
                      "ui_otp_here".tr.toString(),
                      style: TextStyle(
                        fontFamily: 'Sukhumvit Set',
                        fontSize: 12.sp,
                        color: Theme.of(context).colorScheme.secondaryFixed,
                        letterSpacing: mediaQuery(context, "h", 1.12),
                        height: 1.3928571428571428,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  // textField != "" ? SizedBox(
                  //   height: mediaQuery(context, 'h', 10),
                  // ):Container(),
                  Stack(
                    children: [
                      Container(
                        alignment: Alignment.centerRight,
                        margin: EdgeInsets.only(right: 30.w, top: 20.h),
                        child: InkWell(
                          onTap: () async {
                            ClipboardData? text =
                                await Clipboard.getData(Clipboard.kTextPlain);
                            RegExp exp = new RegExp(r'\b[0-9]{6}\b');
                            Iterable<Match> matches =
                                exp.allMatches(text!.text!);
                            List<String?> numbers =
                                matches.map((match) => match.group(0)).toList();
                            String finalString = numbers.join(",");
                            _textController.text = finalString;
                          },
                          child: Container(
                            alignment: Alignment.center,
                            width: 34.w,
                            height: 24.h,
                            // padding:
                            //     EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                            decoration: BoxDecoration(
                              border: Border.all(
                                color:
                                    Theme.of(context).colorScheme.onSecondary,
                                width: 1.w, // ขนาดของ border
                              ),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Text(
                              "ui_paste".tr.toString(),
                              style: TextStyle(
                                  fontSize: 10.sp,
                                  color: Theme.of(context)
                                      .colorScheme
                                      .onSecondary),
                            ),
                          ),
                        ),
                      ),
                      textField != ""
                          ? Container(
                              alignment: Alignment.center,
                              margin: EdgeInsets.only(
                                left: mediaQuery(context, "w", 70),
                                right: mediaQuery(context, "w", 70),
                              ),
                              child: TextField(
                                cursorColor:
                                    Theme.of(context).colorScheme.onSecondary,
                                cursorWidth: mediaQuery(context, "h", 2),
                                textAlign: TextAlign.center,
                                inputFormatters: [
                                  FilteringTextInputFormatter.allow(
                                      RegExp(r'[0-9]'))
                                ],
                                autofocus: true,
                                keyboardType: TextInputType.number,
                                controller: _textController,
                                maxLength: 6,
                                maxLines: 1,
                                style: TextStyle(
                                  color:
                                      Theme.of(context).colorScheme.onSecondary,
                                  fontSize: 12.sp,
                                  fontFamily: "Sukhumvit Set",
                                ),
                                decoration: InputDecoration(
                                  counterText: '',
                                  labelText: '',
                                  floatingLabelAlignment:
                                      FloatingLabelAlignment.center,
                                  labelStyle: TextStyle(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .secondaryFixed,
                                    fontSize: 12.sp,
                                    fontFamily: "Sukhumvit Set",
                                  ),
//                  icon: Image.asset(pictel),
                                  enabledBorder: UnderlineInputBorder(
                                    borderSide: BorderSide(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSecondary),
                                  ),
                                  focusedBorder: UnderlineInputBorder(
                                    borderSide: BorderSide(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSecondary),
                                  ),
                                ),
                              ))
                          : Container(),
                    ],
                  ),
                  SizedBox(
                    // height: mediaQuery(context, 'h', 30),
                    height: 20.h,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      GestureDetector(
                        onTap: () async {
                          VerifyTgotp(
                              context_main, id_member, _textController.text);
                        },
                        child: SizedBox(
                          width: 235.w,
                          height: 46.h,
                          child: Container(
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(30.sp),
                              color: Theme.of(context)
                                  .colorScheme
                                  .secondaryContainer
                                  .withOpacity(0.6),
                              border: Border.all(
                                color: Theme.of(context)
                                    .colorScheme
                                    .secondaryContainer,
                                width: mediaQuery(context, "h", 2),
                              ),
                            ),
                            child: Text(
                              textBTN.toString(),
                              style: TextStyle(
                                fontFamily: 'SukhumvitSet-Bold',
                                fontSize: mediaQuery(context, "h", 30),
                                color: Theme.of(context).colorScheme.secondary,
                                letterSpacing: mediaQuery(context, "h", 1.2),
                                fontWeight: FontWeight.w700,
                                height: 1.0333333333333334,
                                shadows: [
                                  Shadow(
                                    color:
                                        Theme.of(context).colorScheme.primary,
                                    offset: Offset(0, 1),
                                    blurRadius: 1,
                                  )
                                ],
                              ),
                              textHeightBehavior: TextHeightBehavior(
                                  applyHeightToFirstAscent: false),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: mediaQuery(context, 'h', 75),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
