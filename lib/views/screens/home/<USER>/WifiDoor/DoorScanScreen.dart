import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:mapp_ms24/controllers/utils/connect.dart';
import 'package:mapp_ms24/controllers/utils/theme_models.dart';
import 'package:mapp_ms24/controllers/utils/url.dart';

import '../../../../../controllers/utils/widget.dart';
import '../../../Widget/Widget.dart';

class DoorScanScreen extends StatefulWidget {
  const DoorScanScreen({super.key});

  @override
  State<DoorScanScreen> createState() => _DoorScanScreenState();
}

class _DoorScanScreenState extends State<DoorScanScreen> {
  late FToast fToast;
  @override
  void initState() {
    super.initState();
    fToast = FToast();
    // if you want to use context from globally instead of content we need to pass navigatorKey.currentContext!
    fToast.init(context);
  }

  final ThemeController themeController = Get.put(ThemeController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
            image: const DecorationImage(
              image: AssetImage('assets/ADD/Door_water_mark.png'),
              alignment: Alignment.bottomCenter,
              fit: BoxFit.fitWidth,
            ),
            gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Theme.of(context).colorScheme.primary,
                  Theme.of(context).colorScheme.onPrimary,
                ])),
        width: double.infinity.w,
        child: Padding(
          padding: const EdgeInsets.only(top: 20.0),
          child: Stack(
            children: [
              Container(
                  width: Get.width,
                  padding: EdgeInsets.only(top: 249.h),
                  child: builbuttom(context)),
              Column(
                children: [
                  buildAppBar(context, 'ui_doorscan'.tr),
                  buildBanner(context),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget buildBanner(context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          margin: EdgeInsets.only(left: 20.w, top: 20.h),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            color: Theme.of(context)
                .colorScheme
                .secondaryFixedDim
                .withOpacity(0.2),
            border: Border.all(
              color: Theme.of(context)
                  .colorScheme
                  .secondaryFixedDim
                  .withOpacity(0.5),
              width: 1,
            ),
          ),
          height: 47.h,
          width: 353.w,
          child: Padding(
            padding: const EdgeInsets.only(left: 20),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SizedBox(
                    height: 20.h,
                    width: 20.h,
                    child: Image.asset(Get.isDarkMode
                        ? 'assets/Exclamation-Circle-D.png'
                        : 'assets/ADD/Exclamation-Circle.png')),
                Container(
                  width: 10.w,
                ),
                AppWidget.normalText(
                  context,
                  'ui_doorscan_howto'.tr,
                  12.sp,
                  Theme.of(context).colorScheme.onSecondary,
                  FontWeight.w400,
                ),
                AppWidget.normalText(
                  context,
                  'ui_doorscan_tap'.tr,
                  13.sp,
                  Theme.of(context).colorScheme.onSecondary,
                  FontWeight.w700,
                ),
                AppWidget.normalText(
                  context,
                  'ui_doorscan_toopen'.tr,
                  12.sp,
                  Theme.of(context).colorScheme.onSecondary,
                  FontWeight.w400,
                ),
              ],
            ),
          ),
        )
      ],
    );
  }

  Widget builbuttom(context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        InkWell(
          onTap: () async {
            showCupertinoDialog(
              context: context,
              builder: (BuildContext context) {
                // Show the dialog
                return buildWait(context);
              },
            );

            Map obj = {"IP": "***********"};
            final openDoor =
                await AppApi.postHttps(AppUrl.openDoorIQ.toString(), obj);

            Future.delayed(const Duration(seconds: 3), () {
              Navigator.pop(context);
              if (openDoor["opened"]) {
                fToast.showToast(
                  child: buildSnackbar(
                    context,
                    'เปิดประตูสำเร็จ',
                    const Color(0xFF3EFFC5),
                  ),
                  gravity: ToastGravity.TOP,
                  toastDuration: const Duration(seconds: 2),
                );
              } else {
                fToast.showToast(
                  child: buildSnackbar(
                    context,
                    'เปิดประตูไม่สำเร็จ',
                    Colors.red,
                  ),
                  gravity: ToastGravity.TOP,
                  toastDuration: const Duration(seconds: 2),
                );
              }
            });
          },
          child: Container(
            height: 50.h,
            width: 207.w,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color:
                    Theme.of(context).colorScheme.onPrimaryFixed.withOpacity(1),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: const Color(0x26000000), // Shadow color with opacity
                  offset: const Offset(0, 4),
                  blurRadius: 10,
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(
                      height: 20.h,
                      width: 20.h,
                      child: Image.asset('assets/ADD/Devices.png'),
                    ),
                    Container(
                      width: 10.w,
                    ),
                    AppWidget.normalText(
                      context,
                      'ui_doorscan_barrir'.tr + " IQ service",
                      14.sp,
                      Theme.of(context).colorScheme.onSecondary.withOpacity(1),
                      FontWeight.w600,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        SizedBox(height: 30.h),
        InkWell(
          onTap: () async {
            showCupertinoDialog(
              context: context,
              builder: (BuildContext context) {
                return buildWait(context);
              },
            );

            Map obj = {"IP": "************"};

            final openDoor =
                await AppApi.postHttps(AppUrl.openDoorPMG.toString(), obj);

            Future.delayed(const Duration(seconds: 3), () {
              Navigator.pop(context);
              if (openDoor["opened"]) {
                fToast.showToast(
                  child: buildSnackbar(
                    context,
                    'เปิดประตูสำเร็จ',
                    const Color(0xFF3EFFC5),
                  ),
                  gravity: ToastGravity.TOP,
                  toastDuration: const Duration(seconds: 2),
                );
              } else {
                fToast.showToast(
                  child: buildSnackbar(
                    context,
                    'เปิดประตูไม่สำเร็จ',
                    Colors.red,
                  ),
                  gravity: ToastGravity.TOP,
                  toastDuration: const Duration(seconds: 2),
                );
              }
            });
          },
          child: Container(
            height: 50.h,
            width: 207.w,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color:
                    Theme.of(context).colorScheme.onPrimaryFixed.withOpacity(1),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: const Color(0x26000000), // Shadow color with opacity
                  offset: const Offset(0, 4),
                  blurRadius: 10,
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(
                      height: 20.h,
                      width: 20.h,
                      child: Image.asset('assets/ADD/Devices.png'),
                    ),
                    Container(
                      width: 10.w,
                    ),
                    AppWidget.normalText(
                      context,
                      'ui_doorscan_barrir'.tr + " PMG",
                      14.sp,
                      Theme.of(context).colorScheme.onSecondary.withOpacity(1),
                      FontWeight.w600,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

Widget buildWait(context) {
  return Center(
    child: Container(
      padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
      width: 122.w,
      height: 144.h,
      child: Column(
        children: [
          Stack(
            children: [
              Align(
                heightFactor: 1.5,
                alignment: Alignment.center,
                child: SizedBox(
                  height: 33.h,
                  width: 33.w,
                  child: Image.asset('assets/ADD/Share-play.png'),
                ),
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(1.0),
                    child: Stack(
                      children: [
                        SizedBox(
                          height: 50.h,
                          width: 50.w,
                          child: const CircularProgressIndicator(
                            value: 1,
                            strokeAlign: BorderSide.strokeAlignCenter,
                            color: Color(0xFFA596FF),
                          ),
                        ),
                        SizedBox(
                          height: 50.h,
                          width: 50.w,
                          child: const CircularProgressIndicator(
                            strokeAlign: BorderSide.strokeAlignCenter,
                            color: Color(0xFFFEE095),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              )
            ],
          ),
          SizedBox(
            height: 20.h,
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              AppWidget.normalText(
                context,
                'ui_scanning'.tr,
                14.sp,
                const Color(0xFFF6F6F6),
                FontWeight.w600,
              ),
              AppWidget.normalText(
                context,
                'ui_waiting'.tr,
                14.sp,
                const Color(0xFFF6F6F6),
                FontWeight.w600,
              ),
            ],
          ),
        ],
      ),
    ),
  );
}

class QuarterCircleClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    Path path = Path();
    path.moveTo(size.width / 2, size.height / 2);
    path.arcTo(
      Rect.fromCircle(
          center: Offset(size.width / 2, size.height / 2),
          radius: size.width / 2),
      0,
      -3 * pi / 2, // 3/4 of a circle
      true,
    );
    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) {
    return false;
  }
}
