import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mapp_ms24/configs/color_system.dart';
import 'package:mapp_ms24/controllers/internal/HealthController/HealthController.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/controllers/utils/color.dart';
import 'package:mapp_ms24/views/screens/OpenWebWithHtml.dart';
import 'package:mapp_ms24/views/screens/Widget/webview.dart';
import 'package:mapp_ms24/views/screens/webview_screen.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../../../controllers/utils/widget.dart';
import '../../Widget/Widget.dart';
import '../../Widget/progressbar.dart';
import '../../iframe.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../web_only.dart';

Widget buildHealthScreen(context) {
  final healthController = Get.put(HealthController());

  return SizedBox(
    width: Get.width,
    child: SingleChildScrollView(
      scrollDirection: Axis.vertical,
      child: Column(
        children: [
          buildBodyInfo(context, healthController.proportionModel?.height,
              healthController.proportionModel?.weight),
          Liner(context),
          buildDisease(context),
          SizedBox(height: 10.h),
          buildChart(context, healthController.eexerciseModel?.NumberExercises,
              healthController.eexerciseModel?.CountingDays),
        ],
      ),
    ),
  );
}

Widget buildBodyInfo(context, height, width) {
  return Row(
    children: [
      Container(
        child: AppWidget.normalText(context, 'ui_bodyinfo'.tr, 14.sp,
            Theme.of(context).colorScheme.scrim, FontWeight.w400),
      ),
      const Spacer(),
      Row(
        children: [
          AppWidget.normalText(context, '$height ', 14.sp,
              Theme.of(context).colorScheme.scrim, FontWeight.w600),
          AppWidget.normalText(context, 'ui_bodyinfo_cm'.tr, 12.sp,
              Theme.of(context).colorScheme.secondaryFixedDim, FontWeight.w400),
        ],
      ),
      SizedBox(
        width: 10.w,
      ),
      Row(
        children: [
          AppWidget.normalText(context, '$width ', 14.sp,
              Theme.of(context).colorScheme.scrim, FontWeight.w600),
          AppWidget.normalText(context, 'ui_bodyinfo_kg'.tr, 12.sp,
              Theme.of(context).colorScheme.secondaryFixedDim, FontWeight.w400),
        ],
      ),
    ],
  );
}

Widget buildDisease(context) {
  return Row(
    children: [
      Container(
        child: AppWidget.normalText(context, 'ui_disease_risk'.tr, 14.sp,
            Theme.of(context).colorScheme.scrim, FontWeight.w400),
      ),
      const Spacer(),
      Container(
        child: AppWidget.normalText(context, '-', 14.sp,
            Theme.of(context).colorScheme.scrim, FontWeight.w700),
      ),
    ],
  );
} //

Future<void> openUrl(String url) async {
  if (await canLaunch(url)) {
    await launch(url, forceSafariVC: false, forceWebView: false);
  } else {
    throw 'Could not launch $url';
  }
}

Widget buildChart(context, NumberExercises, CountingDays) {
  return GetBuilder<ProfileController>(builder: (profile) {
    return SizedBox(
      width: Get.width,
      child: Column(
        children: [
          Container(
            height: 0.5.h,
            decoration: BoxDecoration(
              color: const Color(0xFFFCF6E4).withOpacity(0.2),
              border: Border.all(
                width: 0.5.w,
                color: const Color(0xFFFCF6E4).withOpacity(0.2),
              ),
            ),
          ),
          SizedBox(
            height: 215.0.h,
            child: Row(
              children: [
                Row(
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(top: 10.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              AppWidget.normalText(
                                context,
                                'ui_within_criteria'.tr,
                                14.sp,
                                Theme.of(context).colorScheme.scrim,
                                FontWeight.w400,
                              ),
                            ],
                          ),
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            AppWidget.normalText(
                              context,
                              profile.bmi,
                              16.sp,
                              Theme.of(context).colorScheme.secondaryContainer,
                              FontWeight.w600,
                            ),
                            AppWidget.normalText(
                              context,
                              profile.bmiStatus,
                              12.sp,
                              Theme.of(context).colorScheme.scrim,
                              FontWeight.w500,
                            ),
                          ],
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            AppWidget.boldText(
                              context,
                              profile.bmi,
                              20.sp,
                              Theme.of(context).colorScheme.secondaryFixedDim,
                              FontWeight.w700,
                            ),
                            AppWidget.normalText(
                              context,
                              'KG/m2',
                              12.sp,
                              Theme.of(context).colorScheme.scrim,
                              FontWeight.w400,
                            ),
                            AppWidget.normalText(
                              context,
                              'ui_health_bmi'.tr,
                              12.sp,
                              Theme.of(context).colorScheme.secondaryFixed,
                              FontWeight.w500,
                            ),
                          ],
                        ),
                      ],
                    ),
                    Padding(
                      padding:
                          const EdgeInsets.only(left: 10, top: 15.0, right: 10),
                      child: Container(
                        height: 214.h,
                        child: Row(
                          children: [
                            SizedBox(
                              height: 192.h,
                              width: 48.w,
                              child: Image.asset('assets/ADD/human.png'),
                            ),
                            SizedBox(
                              height: 215.h,
                              width: 30.w,
                              child: Progressbar(
                                context,
                                double.parse(profile.bmi),
                                40,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                Container(
                  width: 0.5.w,
                  height: 215.h,
                  decoration: BoxDecoration(
                    color: const Color(0xFFFCF6E4).withOpacity(0.2),
                    border: Border.all(
                      width: 0.5.w,
                      color: const Color(0xFFFCF6E4).withOpacity(0.2),
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Container(
                    padding: EdgeInsets.only(top: 10.h),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            AppWidget.boldText(
                              context,
                              '$NumberExercises',
                              20.sp,
                              Theme.of(context).colorScheme.secondaryContainer,
                              FontWeight.w600,
                            ),
                            AppWidget.normalText(
                              context,
                              'ui_health_activity'.tr,
                              12.sp,
                              Theme.of(context).colorScheme.scrim,
                              FontWeight.w500,
                            ),
                            AppWidget.normalText(
                              context,
                              'ui_health_total'.tr,
                              12.sp,
                              Theme.of(context).colorScheme.secondaryFixedDim,
                              FontWeight.w500,
                            ),
                            AppWidget.normalText(
                              context,
                              '$CountingDays',
                              12.sp,
                              Theme.of(context).colorScheme.secondaryFixed,
                              FontWeight.w500,
                            ),
                          ],
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 20.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              SizedBox(
                                width: 48.w,
                                height: 78.h,
                                child: GestureDetector(
                                  onTap: () async {
                                    await launch(
                                        'https://script.google.com/macros/s/AKfycbzzFE0ClzjoX3G8e5UBnl_vQ_ypykbkbIdSo3_4Nu9Wsezya0o/exec?spreadsheet_id=1r7ma8Vv8dyTHv3w9TiBB_JXZIvQaKx6tVBaXGUrLq1s&form_name=Exercise');
                                  },
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      SizedBox(
                                        height: 24.h,
                                        width: 24.w,
                                        child: Image.asset(
                                            'assets/ADD/SD-card.png'),
                                      ),
                                      SizedBox(
                                        height: 10.h,
                                      ),
                                      Text(
                                        'ui_save_health'.tr,
                                        textAlign: TextAlign.center,
                                        maxLines: 3,
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                          // fontFamily: 'SukhumvitSet-Text',
                                          fontFamily: 'SukhumvitSet-Bold',
                                          fontSize: 10.sp,
                                          color: Theme.of(context)
                                              .colorScheme
                                              .scrim,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              SizedBox(
                                width: 50.w,
                                height: 78.h,
                                child: GestureDetector(
                                  onTap: () async {
                                    // launch('https://t.me/+v1o4UtqZ-C1hYWJz');
                                    await launch(
                                        'https://t.me/+5NmeWNXSyQY2YmE1');
                                    // Get.to(() => IframePage('https://t.me/+v1o4UtqZ-C1hYWJ'));
                                    // Get.to(() => HtmlElementView(viewType: 'https://t.me/+v1o4UtqZ-C1hYWJl'));
                                    // AndroidInAppWebViewController
                                    //     .setWebContentsDebuggingEnabled(true);
                                    // await browser.open(
                                    //     url: Uri.parse(
                                    //         "https://t.me/+v1o4UtqZ-C1hYWJl"),
                                    //     options: ChromeSafariBrowserClassOptions(
                                    //       android: AndroidChromeCustomTabsOptions(
                                    //         instantAppsEnabled: true,
                                    //         shareState:
                                    //         CustomTabsShareState.SHARE_STATE_OFF,
                                    //         showTitle: false,
                                    //         enableUrlBarHiding: true,
                                    //         isTrustedWebActivity: true,
                                    //         addDefaultShareMenuItem: false,
                                    //       ),
                                    //     ));
                                    // Navigator.push(
                                    //   context,
                                    //   MaterialPageRoute(
                                    //     builder: (context) => WebViewPage('https://t.me/+v1o4UtqZ-C1hYWJl'),
                                    //   ),
                                    // );
                                  },
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      SizedBox(
                                        height: 24.h,
                                        width: 24.w,
                                        child: Image.asset(
                                            'assets/ADD/Message.png'),
                                      ),
                                      SizedBox(
                                        height: 10.h,
                                      ),
                                      AppWidget.normalText(
                                          context,
                                          'ui_join_health'.tr,
                                          10.sp,
                                          Theme.of(context).colorScheme.scrim,
                                          FontWeight.w400),
                                      AppWidget.normalText(
                                          context,
                                          'ui_join_health2'.tr,
                                          10.sp,
                                          Theme.of(context).colorScheme.scrim,
                                          FontWeight.w400),
                                    ],
                                  ),
                                ),
                              )
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  });
}
