
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:get/get.dart';
// import 'package:loading_indicator/loading_indicator.dart';
// import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
// import 'package:mapp_ms24/controllers/utils/loader.dart';
//
// import '../../../../../controllers/internal/Tacontroller/Tacontroller.dart';
// import '../../../../../controllers/utils/widget.dart';
// import '../../../Widget/library.dart';
//
// class Tascreen extends StatefulWidget {
//   const Tascreen({super.key});
//
//   @override
//   State<Tascreen> createState() => _TascreenState();
// }
//
// class _TascreenState extends State<Tascreen> {
//   bool isUpdating = false;
//   Tacontroller taCtr = Get.find<Tacontroller>();
//   final id = '';
//   ProfileController profileCtr = Get.find<ProfileController>();
//   Tacontroller tacontroller = Get.find<Tacontroller>();
//   var datetime = "00-00-0000T00:00:00";
//   bool checking = true;
//
//   @override
//   void initState() {
//     super.initState();
//     initializePage();
//   }
//
//   void initializePage() async {
//     taCtr.taStatus;
//       // taCtr.checkinternet(cont ext);
//       await taCtr.getTime(context);
//       await taCtr.getlocation();
//       await taCtr.getsession();
//     checking = false;
//     print("checking $checking");
//     setState(() {});
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: Stack(
//         children: [
//           Container(
//             decoration: const BoxDecoration(
//               gradient: LinearGradient(
//                 colors: [
//                   Color(0xFF1F1C2F), // เพิ่มสีแรกที่นี่
//                   Color(0xFF0D0C14), // เพิ่มสีที่สองที่นี่
//                 ],
//                 begin: Alignment.topCenter, // เริ่มต้น Gradient ที่มุมบนซ้าย
//                 end: Alignment.bottomCenter,
//               ),
//             ),
//             height: 852.h,
//             width: Get.width,
//             child: Column(
//               children: [buildBody(context)],
//             ),
//           ),
//           checking
//           ? Container(
//             width: MediaQuery.of(context).size.width,
//             height: MediaQuery.of(context).size.height,
//             color: Colors.black12,
//             child: Center(
//               child: SizedBox(
//                 width: 60.w,
//                 child: const LoadingIndicator(
//                   indicatorType: Indicator.lineSpinFadeLoader,
//                   colors: [Colors.white],
//                 ),
//               ),
//             )
//           )
//           : Container(),
//         ],
//       ),
//     );
//   }
//
//   Widget buttonChecking(context) {
//     if (int.parse(convertDateTime(tacontroller.datetime, "TH")) < 12  &&  tacontroller.statusButtonMorning == 0 || taCtr.checkinta == 1) {
//       return Column(
//         children: [
//           GestureDetector(
//             onTap: () async {
//               tacontroller.getPerson(context);
//             },
//             child: Container(
//               padding: EdgeInsets.only(top: 70.h),
//               alignment: Alignment.topCenter,
//               child: SizedBox(
//                 width: 135.w,
//                 height: 50.h,
//                 child: Container(
//                   alignment: Alignment.center,
//                   width: 273.w,
//                   height: 101.h,
//                   decoration: BoxDecoration(
//                     borderRadius: BorderRadius.circular(51.0),
//                     gradient: const LinearGradient(
//                       begin: Alignment(0.0, -1.0),
//                       end: Alignment(0.0, 1.0),
//                       colors: [Color(0xfffee095), Color(0xfffdd163)],
//                       stops: [0.0, 1.0],
//                     ),
//                     boxShadow: const [
//                       BoxShadow(
//                         color: Color(0x4d000000),
//                         offset: Offset(0, 2),
//                         blurRadius: 20,
//                       ),
//                     ],
//                   ),
//                   child: AppWidget.normalText(context, 'btn_checkin'.tr, 14.sp,
//                       const Color(0xFF1F1C2F), FontWeight.w700),
//                 ),
//               ),
//             ),
//           ),
//         ],
//       );
//     }else{
//       return Container(
//         padding: EdgeInsets.only(top: 70.h),
//         alignment: Alignment.topCenter,
//         child: SizedBox(
//           width: 135.w,
//           height: 50.h,
//           child: Container(
//             alignment: Alignment.center,
//             width: 273.w,
//             height: 101.h,
//             decoration: BoxDecoration(
//               borderRadius: BorderRadius.circular(51.0),
//               gradient: const LinearGradient(
//                 begin: Alignment(0.0, -1.0),
//                 end: Alignment(0.0, 1.0),
//                 colors: [Color(0x33fee095), Color(0x33fdd163)],
//                 stops: [0.0, 1.0],
//               ),
//               boxShadow: const [
//                 BoxShadow(
//                   color: Color(0x0f000000),
//                   offset: Offset(0, 2),
//                   blurRadius: 20,
//                 ),
//               ],
//             ),
//             child: AppWidget.normalText(context, 'btn_checkin'.tr, 14.sp,
//                 const Color(0x73fcf6e4), FontWeight.w700),
//           ),
//         ),
//       );
//     }
//   }
//
//   Widget buttonCheckout(context) {
//     if (int.parse(convertDateTime(tacontroller.datetime, "TH")) >= 12 ) {
//       return Column(
//         children: [
//           GestureDetector(
//             onTap: () async {
//               tacontroller.getPerson(context);
//             },
//             child: Container(
//               padding: const EdgeInsets.all(10),
//               alignment: Alignment.topCenter,
//               child: Container(
//                 alignment: Alignment.center,
//                 width: 135.w,
//                 height: 50.h,
//                 decoration: BoxDecoration(
//                   borderRadius: BorderRadius.circular(51.0),
//                   color: const Color(0xff7f420c),
//                   boxShadow: const [
//                     BoxShadow(
//                       color: Color(0x4d000000),
//                       offset: Offset(0, 2),
//                       blurRadius: 20,
//                     ),
//                   ],
//                 ),
//                 child: AppWidget.normalText(context, 'btn_checkout'.tr, 14.sp,
//                     const Color(0xFFF6F6F6), FontWeight.w400),
//               ),
//             ),
//           ),
//         ],
//       );
//     }else{
//       return Container(
//         padding: const EdgeInsets.all(10),
//         alignment: Alignment.topCenter,
//         child: Container(
//           alignment: Alignment.center,
//           width: 135.w,
//           height: 50.h,
//           decoration: BoxDecoration(
//             borderRadius: BorderRadius.circular(51.0),
//             color: const Color(0x727f420c),
//             boxShadow: const [
//               BoxShadow(
//                 color: Color(0x22000000),
//                 offset: Offset(0, 2),
//                 blurRadius: 20,
//               ),
//             ],
//           ),
//           child: AppWidget.normalText(context, 'btn_checkout'.tr, 14.sp,
//               const Color(0x72fcf6e4), FontWeight.w400),
//         ),
//       );
//     }
//   }
//
//   Widget buildBody(context) {
//     return Column(
//       children: <Widget>[
//         Center(
//             child: Container(
//           color: const Color(0xff302C49),
//         )),
//         Container(
//           padding: EdgeInsets.only(top: 80.h),
//           child: Row(
//             crossAxisAlignment: CrossAxisAlignment.center,
//             children: [
//               Container(
//                 margin: EdgeInsets.only(left: 150.w),
//                 alignment: Alignment.center,
//                 child: AppWidget.normalText(context, 'ui_ta'.tr, 16.sp,
//                     const Color(0xFFF6F6F6), FontWeight.w700),
//               ),
//               Container(
//                 alignment: Alignment.center,
//
//                 padding: EdgeInsets.only(left: 80.w),
//                 child: InkWell(
//                   onTap: () {
//                     Navigator.pop(context);
//                   },
//                   child: const Icon(
//                     Icons.home,
//                     size: 25,
//                     color: Colors.white,
//                   ),
//                 ),
//               )
//             ],
//           ),
//         ),
//         buildAvata(),
//         buildDetail(),
//         if (isUpdating) LoadingDialog(),
//         buttonChecking(context),
//         buttonCheckout(context),
//       ],
//     );
//   }
//
//   Widget buildAvata() {
//     return Container(
//       padding: EdgeInsets.only(top: 25.h),
//       child: Container(
//         width: mediaQuery(context, "h", 200),
//         height: mediaQuery(context, "h", 200),
//         decoration: const BoxDecoration(
//           color: Color(0xFFFCF6e4),
//           shape: BoxShape.circle, // Make the container circular
//           boxShadow: [
//             BoxShadow(
//               color: Color(0x4d000000),
//               offset: Offset(0, 3),
//               blurRadius: 2,
//             ),
//           ],
//         ),
//         child: Container(
//           decoration: BoxDecoration(
//             borderRadius:
//             BorderRadius.all(Radius.elliptical(94.5, 94.5)),
//             image: DecorationImage(
//               image:   profileCtr.responseMember!.id.toString() == ""
//                   ? const NetworkImage(
//                       "https://s3-ap-southeast-1.amazonaws.com/storage-pkg/icon/icon1548914956default.png")
//                   : NetworkImage(
//                       "http://devdev.prachakij.com/PPP7/uploads/emp/${profileCtr.responseMember!.id.toString()}.jpg"),
//               fit: BoxFit.cover,
//             ),
//           ),
//         ),
//       ),
//     );
//   }
//
//   Widget buildDetail() {
//     return Container(
//       padding: EdgeInsets.only(top: 40.h),
//       child: Column(
//         children: [
//           AppWidget.normalText(
//               context,
//               '${'ui_date'.tr}'+ " " +  convertDateTime(tacontroller.datetime, "dd/MM/YYYY3"),
//               14.sp,
//               const Color(0xFFF6F6F6),
//               FontWeight.w700),
//           Padding(
//             padding: EdgeInsets.only(top: 15.h),
//             child: Column(
//               children: [
//                 AppWidget.normalText(context, 'ui_timeNow'.tr, 14.sp,
//                     const Color(0xFFF6F6F6), FontWeight.w700),
//                 SizedBox(height: 7.h),
//                 AppWidget.spacenormalText(
//                     context,
//                     convertDateTime(tacontroller.datetime, "T"),
//                     20.sp,
//                     const Color(0xFFF6F6F6),
//                     FontWeight.w700,
//                     letterSpacing: 5.0.h),
//               ],
//             ),
//           )
//         ],
//       ),
//     );
//   }
// }
//
// class LoadingDialog extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     return AlertDialog(
//       content: Row(
//         children: [
//           CircularProgressIndicator(),
//           SizedBox(width: 16),
//           Text("Loading..."),
//         ],
//       ),
//     );
//   }
// }

