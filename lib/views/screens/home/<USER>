import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:ui';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:draggable_fab/draggable_fab.dart';
import 'package:flutter/material.dart';
// import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:html' as html;
import 'package:get/get.dart';
import 'dart:io' as io;
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:mapp_ms24/Models/language.dart';
import 'package:mapp_ms24/configs/theme.dart';
import 'package:mapp_ms24/controllers/internal/BitrixController/bitrixController.dart';
import 'package:mapp_ms24/controllers/internal/FinaceController/FinaceController.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/controllers/internal/Profile/userController.dart';
import 'package:mapp_ms24/controllers/internal/wealthController/wealthController.dart';
import 'package:mapp_ms24/controllers/spin_controller/point_service.dart';
import 'package:mapp_ms24/controllers/utils/color.dart';
import 'package:mapp_ms24/controllers/utils/connect.dart';
import 'package:mapp_ms24/controllers/utils/loader.dart';
import 'package:mapp_ms24/controllers/utils/setting_controller.dart';
import 'package:mapp_ms24/controllers/utils/theme_models.dart';
import 'package:mapp_ms24/controllers/utils/updatePatch.controller.dart';
import 'package:mapp_ms24/controllers/utils/url.dart';
import 'package:mapp_ms24/views/alert_update_patch.dart';
import 'package:mapp_ms24/views/screens/Widget/Menuwidget.dart';
import 'package:mapp_ms24/views/screens/Widget/Widget.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/Timescreen.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/Workissues/likecredit.dart';
import 'package:mapp_ms24/views/screens/home/<USER>';
import 'package:mapp_ms24/views/screens/home/<USER>';
import 'package:mapp_ms24/views/screens/home/<USER>/unlockTwoFA.dart';
import 'package:mapp_ms24/views/screens/spinwheel/spinwheel.dart';
import 'package:mapp_ms24/views/screens/webview_screen.dart';
import 'package:microphone/microphone.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:restart_app/restart_app.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:shorebird_code_push/shorebird_code_push.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:url_launcher/url_launcher_string.dart';
import '../../../controllers/LikeCreditController.dart';
import '../../../controllers/annoucer_controller.dart';
import '../../../controllers/firebaseApi.dart';
import '../../../controllers/internal/HealthController/HealthController.dart';
import '../../../controllers/internal/LeaveController/LeaveApprovalController.dart';
import '../../../controllers/internal/MainMenuController/MenuController.dart';
import '../../../controllers/internal/NotiController/NotifiController.dart';
import '../../../controllers/internal/Tacontroller/Tacontroller.dart';
import '../../../controllers/internal/authController/OsController.dart';
import '../../../controllers/login_controller/login.dart';
import '../../../controllers/spin_controller/spin_controller.dart';
import '../../../controllers/utils/language_selection_controller.dart';
import '../../../controllers/utils/widget.dart';
import '../../../controllers/webview_tg_controller.dart';
import 'miniscreen/FinanceDoc/NDAdoc/introNDA.dart';
import 'miniscreen/HealthScreen.dart';
import 'miniscreen/WHTscreen.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/yubikey.dart';
import 'package:fluttertoast/fluttertoast.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with AutomaticKeepAliveClientMixin {
  LeaveAndAppController landAndCrt = Get.find<LeaveAndAppController>();

  ThemeController theme = Get.put(ThemeController());
  SpinController spinController = Get.put(SpinController());
  final LanguageSelectionController languageController =
      Get.put(LanguageSelectionController());
  UserController userProfile = Get.put(UserController());
  double fabTop = 0;
  double fabLeft = 0;
  var countPage = 0;

  @override
  bool get wantKeepAlive => true;

  var isLoading = false.obs;

  bool visibility = false;
  bool visibilityLikeWallet = false;
  final pageController = PageController();
  final detailPageController = PageController();
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  Tacontroller taCtr = Get.find<Tacontroller>();
  bool isContainerVisible = false;
  bool visibleVote = true;
  bool visibleBirthday = true;
  int currentIndex = 0;
  int currentPage = 0;
  NotiController notifyCtr = Get.find<NotiController>();
  MainMenuController mainCtr = Get.put(MainMenuController());
  ProfileController profileCtr = Get.find<ProfileController>();
  WealthController wealthCtr = Get.find<WealthController>();
  CenterController centerCon = Get.find<CenterController>();
  HealthController healthCtr = Get.find<HealthController>();
  FinaceController finaceCtr = Get.find<FinaceController>();
  BitrixController bitrixCtr = Get.find<BitrixController>();
  final mspCtr = Get.put(MspService());

  OsController OsCrt = Get.find<OsController>();
  final centerCtl = Get.put(SettingController());
  final updatePatchCtl = Get.put(UpdatePatchController());

  LeaveAndAppController leaveAndAppController =
      Get.find<LeaveAndAppController>();
  double containerHeight = 100.0.h; // Set an initial height
  bool checking = true;
  final ThemeController themeController = Get.put(ThemeController());
  TextEditingController _CauseLatenessController = TextEditingController();
  late Timer timer;
  LanguageSelectionController langCtr = Get.put(LanguageSelectionController());
  LikeCreditController likeCtr = Get.put(LikeCreditController());
  WebViewLikePointController webCtr = Get.put(WebViewLikePointController());
  SpinController spinCtr = Get.put(SpinController());
  var countLaeveList = 0;
  var MessageHead = "";
  DateTime currentTime = DateTime.now();
  final shorebirdCodePush = ShorebirdCodePush();

  Stream<String> clock() {
    return Stream<String>.periodic(Duration(seconds: 1), (int count) {
      return DateFormat('HH:mm:ss').format(DateTime.now());
    });
  }

  late FToast fToast;

  @override
  @override
  void initState() {
    super.initState();

    final member = profileCtr.responseMember!;

    timer = Timer.periodic(Duration(seconds: 1), (Timer t) => updateTime());
    checkLoading();
    profileCtr.loadImage();

    fToast = FToast();
    fToast.init(context);

    // กลุ่ม D: prepare logic
    profileCtr.oninit();
    webCtr.onInit();

    // โหลดข้อมูลสำคัญจำกัดเวลา 5 วิ
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadEssentialData(member);
      _loadBackgroundData(member); // ส่วนที่เหลือ
    });
  }

  Future<void> _loadEssentialData(member) async {
    try {
      await Future.wait([
        healthCtr.getDataEexercise(member.id),
        healthCtr.getDataProportio(member.id),
        profileCtr.getBIMinfo(),
        timeController.getDataTaScore(),
        timeController.getDataLostDay(member.id, member.country),
        formatPhoneNumberAsync(member.telNumber),
      ]).timeout(Duration(seconds: 5));
    } catch (e) {
      print("⛔ โหลดข้อมูลสำคัญไม่ครบใน 5 วิ: $e");
      fToast.showToast(
        child: Text("⚠️ โหลดข้อมูลบางส่วนไม่สำเร็จภายใน 5 วิ"),
        gravity: ToastGravity.BOTTOM,
      );
    }

    setState(() {});
  }

  Future<void> _loadBackgroundData(member) async {
    bitrixCtr.loadingAllProgress();
    profileCtr.getOKR();
    taCtr.getlocation();
    taCtr.getCheckin(context);
    _checkForUpdates(context);
  }


  checkSession() async {
    if (profileCtr.responseMember!.telegram_token != 'null' ||
        profileCtr.responseMember!.telegram_token != '') {
      bool sessionTwoFA = await Yubikey.Check_session();
      if (!sessionTwoFA) {
        Get.to(UnlockTwoFA());
      }
    }
  }

  Future<void> _checkForUpdates(BuildContext context) async {
    final updateAvailable =
        await shorebirdCodePush.isNewPatchAvailableForDownload();
    if (updateAvailable) {
      await shorebirdCodePush.downloadUpdateIfAvailable();
      final isNewPatchReadyToInstall =
          await shorebirdCodePush.isNewPatchReadyToInstall();
      if (isNewPatchReadyToInstall) {
        AppWidget.showDialogPageSlide(context, const AlertUpdatePatchPage());
      } else {}
    } else {}
  }

  void updateTime() {
    timer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          currentTime = DateTime.now();
        });
      }
    });
  }

  @override
  void dispose() {
    timer.cancel();
    super.dispose();
  }

  final PageController _pageController = PageController();
  final PageController _mspController = PageController();

  List _texts = [];

  getPerson(context) async {
    AppLoader.loader(context);
    if (profileCtr.responseMember!.id != "" &&
        profileCtr.responseMember!.id != null &&
        profileCtr.responseMember!.id != "null") {
      Map data = {"personID": profileCtr.responseMember!.id};
      final response1 =
          await AppApi.callAPIjwt("POST", AppUrl.searchPerson, data);

      if (response1["status"] == 200) {
        getTimeCard(context, response1["result"][0]["timeInType"]);
      } else {
        AppLoader.dismiss(context);
        const GetSnackBar(
          title: "ชื่อผู้ใช้ไม่ถูกต้อง",
          message: "รหัสสมาชิกหายไปจาก app MS กรุณา logout และ login อีกครั้ง ",
          duration: Duration(seconds: 3),
        );
      }
    } else {
      const GetSnackBar(
        title: "รหัสสมาชิกหายไปจาก app MS",
        message: "รหัสสมาชิกหายไปจาก app MS กรุณา logout และ login อีกครั้ง",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future getTimeCard(context, int timeInType) async {
    if (profileCtr.responseMember!.id != "" &&
        profileCtr.responseMember!.id != null &&
        profileCtr.responseMember!.id != "null") {
      Map data = {
        "timeInType": timeInType.toString(),
        "dayID": taCtr.numdate.toString()
      };

      final res = await AppApi.callAPIjwt("POST", AppUrl.searchTimeCard, data);

      if (res.length > 0) {
        if (int.parse(convertDateTime(taCtr.datetime, "TH")) < 12) {
          taCtr.morning(context, res["result"][0]["timeIN"].toString(),
              res["result"][0]["work"].toString());
          AppLoader.dismiss(context);
        } else {
          taCtr.goodbye(context, res["result"][0]["timeOUT"].toString());
          AppLoader.dismiss(context);
        }
      } else {
        const GetSnackBar(
          title: "ชื่อผู้ใช้ไม่ถูกต้อง",
          message: "ชื่อผู้ใช้ไม่ถูกต้อง กรุณาลองใหม่อีกครั้ง",
          duration: Duration(seconds: 3),
        );
      }
    } else {
      const GetSnackBar(
        title: "เลขวันของสัปดาห์ว่าง",
        message:
            "เลขวันของสัปดาห์ว่าง กรุณากลับไปหน้า home แล้ว เข้ามาอีกครั้ง",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<void> checkLoading() async {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      buildVote(context);
    });
    try {
      countPage = landAndCrt.testLeaveForm!.data!.length;
    } catch (e) {
      print(e);
    }
    try {
      if (landAndCrt.testLeaveForm!.data!.isEmpty) {
        countLaeveList = 1;
      } else {
        countLaeveList = 0;
      }
    } catch (e) {
      countLaeveList = 0;
    }
    checking = false;

    await finaceCtr.loadNumbersign();
    await profileCtr.getlikebalance();
    await leaveAndAppController.loadApprove();
    await notifyCtr.getNotificationInfo(profileCtr.responseMember!.id,
        profileCtr.responseMember!.company_management);
    await daysWorkController.getDataDaysWork(profileCtr.responseMember!.id);
    await daysWorkController
        .getDataDaysWorkOfMonth(profileCtr.responseMember!.id);
    checking = false;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      key: _scaffoldKey,
      body: Stack(
        children: [
          Container(
            height: Get.height,
            width: Get.width,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).primaryColor,
                  Theme.of(context).hintColor,
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
            child: Padding(
              padding: EdgeInsets.only(top: 10.0.h),
              child: Stack(
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: 120.0.h),
                    child: Column(
                      children: [
                        buildSelectMenu(context, taCtr.taStatus.value),
                        buildTimmi(context),
                        buildSelectScreen(context),
                      ],
                    ),
                  ),
                  profile(context),
                ],
              ),
            ),
          ),
          checking
              ? Container(
                  width: MediaQuery.of(context).size.width,
                  height: MediaQuery.of(context).size.height,
                  color: Colors.black12,
                  child: Center(
                    child: SizedBox(
                      width: 60.w,
                      child: const LoadingIndicator(
                        indicatorType: Indicator.lineSpinFadeLoader,
                        colors: [Colors.white],
                      ),
                    ),
                  ),
                )
              : Container(),
        ],
      ),
      endDrawer: buildShowDrawer(),
      floatingActionButton: buildChat(),
    );
  }

  Widget buildVote(context) {
    return Visibility(
      visible: visibleVote,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          AnimatedContainer(
            duration: const Duration(milliseconds: 500),
            height: visibleVote ? 490.h : 0,
            padding: EdgeInsets.only(top: 40.h, left: 10.w, right: 10.w),
            decoration: BoxDecoration(
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF000000).withOpacity(0.9),
                  offset: const Offset(0, 4),
                  blurRadius: 4,
                ),
              ],
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  const Color(0xFF1F1C2F),
                  const Color(0xFF1F1C2F).withOpacity(0.6),
                ],
              ),
            ),
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  SizedBox(
                    height: 75.h,
                    width: 69.w,
                    child: Image.asset('assets/Timmi/TimmiLovely.png'),
                  ),
                  SizedBox(height: 19.h),
                  AppWidget.normalText(
                    context,
                    'ui_timmi_give'.tr,
                    14.sp,
                    const Color(0xFFF6F6F6),
                    FontWeight.w400,
                  ),
                  SizedBox(height: 19.h),
                  Column(
                    children: [
                      AppWidget.normalText(
                        context,
                        'ui_timmi_send'.tr,
                        12.sp,
                        const Color(0xFFFEE095),
                        FontWeight.w600,
                      ),
                      AppWidget.normalText(
                        context,
                        'ui_timmi_used'.tr,
                        12.sp,
                        const Color(0xFFF6F6F6).withOpacity(0.6),
                        FontWeight.w400,
                      ),
                    ],
                  ),
                  SizedBox(height: 19.h),
                  Vote(),
                  SizedBox(height: 19.h),
                  Row(
                    children: [
                      Container(
                        height: 52.h,
                        decoration: BoxDecoration(
                          color: const Color(0xFF302C49),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: const Color(0xFFA596FF),
                            width: 1.w,
                          ),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(10.0),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              SizedBox(
                                width: 12.w,
                              ),
                              SizedBox(
                                width: 320.w,
                                height: 20.h,
                                child: TextField(
                                  cursorColor: Colors.black,
                                  controller: notifyCtr.voteEditingController,
                                  onChanged: (text) {
                                    notifyCtr.updateText;
                                  },
                                  onEditingComplete: () {
                                    notifyCtr.updateText;
                                  },
                                  style: TextStyle(
                                    color: const Color(0xFFF6F6F6),
                                    fontSize: 14.sp,
                                    fontWeight: FontWeight.w400,
                                  ),
                                  decoration: InputDecoration.collapsed(
                                    hintText: 'ui_timmi_tell'.tr,
                                    hintStyle: TextStyle(
                                      fontSize: 14.sp,
                                      fontFamily: 'SukhumvitSet-Text',
                                      color: const Color(0xFFF6F6F6)
                                          .withOpacity(0.6),
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  maxLines: null, // Allow multiline input
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 19.h,
                  ),
                  InkWell(
                    onTap: notifyCtr.voteEditingController.text.isEmpty
                        ? () {
                            print("empty");
                          }
                        : () {
                            print("work");
                          },
                    child: Container(
                      height: 52.h,
                      width: 357.w,
                      decoration: BoxDecoration(
                        borderRadius:
                            const BorderRadius.all(Radius.circular(10)),
                        color: notifyCtr.voteEditingController.text.isEmpty
                            ? Color(0xFF322B63).withOpacity(0.5)
                            : Color(0xFF322B63),
                        border: Border.all(
                          width: 1.w,
                          color: notifyCtr.voteEditingController.text.isEmpty
                              ? Color(0xFFA596FF).withOpacity(0.5)
                              : Color(0xFFA596FF),
                        ),
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          AppWidget.normalText(
                            context,
                            'ui_timmi_done'.tr,
                            14.sp,
                            notifyCtr.voteEditingController.text.isEmpty
                                ? Color(0xFFF6F6F6).withOpacity(0.5)
                                : Color(0xFFF6F6F6),
                            FontWeight.w400,
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 10.h,
                  ),
                  Center(
                    child: InkWell(
                      onTap: () {
                        setState(() {
                          visibleVote = !visibleVote;
                        });
                      },
                      child: AppWidget.normalText(
                        context,
                        'ui_timmi_SKIP'.tr,
                        14.sp,
                        const Color(0xFFF6F6F6).withOpacity(0.6),
                        FontWeight.w400,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildBirthday(context) {
    return Visibility(
      visible: visibleBirthday,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            child: Container(
              height: visibleBirthday ? 400.h : 0,
              decoration: BoxDecoration(
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF000000).withOpacity(0.9),
                    offset: const Offset(0, 4),
                    blurRadius: 4,
                  ),
                ],
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    const Color(0xFF1F1C2F),
                    const Color(0xFF1F1C2F).withOpacity(0.6),
                  ],
                ),
              ),
              child: Container(
                padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Stack(
                      children: [
                        Image.asset('assets/Happy/Gift-Ribbin.png'),
                        Column(
                          children: [
                            SizedBox(
                              height: 97.h,
                            ),
                            SizedBox(
                              width: 375.w,
                              height: 185.h,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  AppWidget.boldText(
                                      context,
                                      'HAPPY BIRTH DAY :)',
                                      20.sp,
                                      const Color(0xFFF6F6F6),
                                      FontWeight.w600),
                                  SizedBox(
                                    height: 20.h,
                                  ),
                                  SizedBox(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        AppWidget.normalText(
                                            context,
                                            'ui_Bd_pround'.tr,
                                            14.sp,
                                            const Color(0xFFF6F6F6),
                                            FontWeight.w400),
                                        Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            AppWidget.normalText(
                                                context,
                                                'ui_Bd_click'.tr,
                                                12.sp,
                                                const Color(0xFFFEE095),
                                                FontWeight.w700),
                                            SizedBox(width: 5.w),
                                            AppWidget.normalText(
                                                context,
                                                'ui_Bd_below'.tr,
                                                12.sp,
                                                const Color(0xFFFEE095),
                                                FontWeight.w400),
                                          ],
                                        ),
                                        SizedBox(
                                          height: 10.h,
                                        )
                                      ],
                                    ),
                                  )
                                ],
                              ),
                            ),
                          ],
                        )
                      ],
                    ),
                    Container(
                        height: 52.h,
                        width: 357.w,
                        decoration: BoxDecoration(
                          borderRadius:
                              const BorderRadius.all(Radius.circular(10)),
                          color: const Color(0xFF322B63),
                          border: Border.all(
                            width: 1.w,
                            color: const Color(0xFFA596FF),
                          ),
                        ),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            AppWidget.normalText(
                              context,
                              "${'ui_box_claim'.tr}",
                              16.sp,
                              const Color(0xFFF6F6F6),
                              FontWeight.w600,
                            ),
                            AppWidget.normalText(
                              context,
                              " 10,000  ",
                              16.sp,
                              const Color(0xFFFEE095),
                              FontWeight.w600,
                            ),
                            AppWidget.normalText(
                              context,
                              "${"ui_Lp_unit".tr}",
                              16.sp,
                              const Color(0xFFF6F6F6),
                              FontWeight.w600,
                            ),
                          ],
                        )),
                    SizedBox(
                      height: 10.h,
                    ),
                    Center(
                      child: InkWell(
                        onTap: () {
                          setState(() {
                            visibleBirthday = !visibleBirthday;
                          });
                        },
                        child: AppWidget.normalText(
                          context,
                          'ui_brith_skip'.tr,
                          14.sp,
                          Color(0xFFF6F6F6).withOpacity(0.6),
                          FontWeight.w400,
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget profile(BuildContext context) {
    final webviewCtl = Get.put(WebViewLikePointController());
    final profileCtl = Get.put(ProfileController());
    bool isDarkTheme = Theme.of(context).brightness == Brightness.dark;
    final mspCtr = Get.find<MspService>();

    String pocketBalance = mspCtr.pocketMember.pocket?.isNotEmpty == true &&
            mspCtr.pocketMember.pocket!.first.pocketBalance != null
        ? mspCtr.pocketMember.pocket!.first.pocketBalance!
            .toStringAsFixed(2)
            .replaceAllMapped(
                RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')
        : "0";

    final List<Widget> mspOption = [
      InkWell(
        onTap: () async {
          profileCtl.responseMember!.telNumber = await formatPhoneNumberAsync(
              profileCtl.responseMember!.telNumber!);
          profileCtl.responseMember!.telNumber.toString().substring(4);
          profileCtl.responseMember!.telNumber =
              '+66${profileCtl.responseMember!.telNumber}';

          webviewCtl.phoneEncode.value = base64Encode(utf8.encode(
              profileCtl.responseMember!.phone_like.toString().toString()));

          webviewCtl.firstName.value =
              profileCtl.responseMember!.name_th.toString();
          webviewCtl.lastName.value =
              profileCtl.responseMember!.surname_th.toString();

          webviewCtl.memberID.value = profileCtl.responseMember!.id.toString();

          String url;
          if (kIsWeb) {
            // If the platform is KisWeb, use the specified URL
            url = "https://minipoint.likepoint.io/"
                "/?phone=${Uri.encodeComponent(webviewCtl.phoneEncode.value)}"
                "&merchant=78ce7814-b9e9-4187-a139-2faf08cfdc6e"
                "&firstName=${Uri.encodeComponent(webviewCtl.firstName.value)}"
                "&lastName=${Uri.encodeComponent(webviewCtl.lastName.value)}"
                "&pkg_id_member=${Uri.encodeComponent(webviewCtl.memberID.value)}";
            await launch(url);
          } else {
            url =
                "${webviewCtl.urlMiniLikePoint == '' ? "https://minipoint.likepoint.io/" : webviewCtl.urlMiniLikePoint}"
                "/?phone=${Uri.encodeComponent(webviewCtl.phoneEncode.value)}"
                "&merchant=78ce7814-b9e9-4187-a139-2faf08cfdc6e"
                "&firstName=${Uri.encodeComponent(webviewCtl.firstName.value)}"
                "&lastName=${Uri.encodeComponent(webviewCtl.lastName.value)}"
                "&pkg_id_member=${Uri.encodeComponent(webviewCtl.memberID.value)}";

            await Navigator.of(context).push(MaterialPageRoute(
              builder: (context) => const WebViewPage(),
            ));

            await Get.to(() => const WebViewPage());
          }
        },
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset(
                  'assets/icon1.png',
                  width: 29.w,
                  height: 15.h,
                ),
                const SizedBox(width: 1),
                const Text(
                  "MS พอยท์",
                  style: TextStyle(
                    fontFamily: 'Sukhumvit Set',
                    fontSize: 10,
                    color: Color(0xFFA78AFE),
                    fontWeight: FontWeight.w400,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
            AppWidget.boldText(
              context,
              pocketBalance,
              14.sp,
              Theme.of(context).colorScheme.scrim,
              FontWeight.w600,
            ),
          ],
        ),
      ),
      InkWell(
        onTap: () async {
          if (Get.find<MspService>().teamId.ownerStatus.toString() == "false") {
            fToast.showToast(
              child: buildSnackbarError(
                  context, 'ไม่มีสิทธิ์เข้าจัดการกระเป๋าทีม'),
              gravity: ToastGravity.TOP,
              toastDuration: const Duration(seconds: 2),
            );
          } else {
            String url;
            if (kIsWeb) {
              url = "https://minipoint.likepoint.io/"
                  "/?phone=${Uri.encodeComponent(base64Encode(utf8.encode(mspCtr.teamId.teamId!)))}"
                  "&merchant=78ce7814-b9e9-4187-a139-2faf08cfdc6e";
              await launch(url);
            } else {
              url =
                  "${webviewCtl.urlMiniLikePoint == '' ? "https://minipoint.likepoint.io/" : webviewCtl.urlMiniLikePoint}"
                  "/?phone=${Uri.encodeComponent(base64Encode(utf8.encode(mspCtr.teamId.teamId!)))}"
                  "&merchant=78ce7814-b9e9-4187-a139-2faf08cfdc6e";

              await Navigator.of(context).push(MaterialPageRoute(
                builder: (context) => const WebViewPage(),
              ));

              await Get.to(() => const WebViewPage());
            }
          }
        },
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset(
                  'assets/MSP/teampoint.png',
                  width: 15.w,
                ),
                const SizedBox(width: 1),
                const Text(
                  "MS พอยท์",
                  style: TextStyle(
                    fontFamily: 'Sukhumvit Set',
                    fontSize: 10,
                    color: Color(0xFFA78AFE),
                    fontWeight: FontWeight.w400,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
            AppWidget.boldText(
              context,
              Get.find<MspService>().teamBalance.pocketBalance != null
                  ? Get.find<MspService>()
                      .teamBalance
                      .pocketBalance!
                      .toStringAsFixed(2)
                      .replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                          (Match m) => '${m[1]},')
                  : '0',
              14.sp,
              Theme.of(context).colorScheme.scrim,
              FontWeight.w600,
            ),
          ],
        ),
      ),
    ];

    return Stack(
      children: [
        AnimatedContainer(
          duration: const Duration(milliseconds: 500),
          padding: EdgeInsets.only(top: 30.h, left: 20.w, right: 20.w),
          decoration: BoxDecoration(
            gradient: visibility
                ? LinearGradient(
                    colors: [
                      Theme.of(context).colorScheme.onPrimary.withOpacity(0.5),
                      Theme.of(context).colorScheme.shadow.withOpacity(0.5),
                    ],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  )
                : LinearGradient(
                    colors: [
                      Theme.of(context).colorScheme.onPrimary.withOpacity(0),
                      Theme.of(context).colorScheme.onPrimary.withOpacity(0),
                    ],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
          ),
          height: visibility ? 300.0.h : 100.0.h,
          width: Get.width,
          child: SingleChildScrollView(
            physics: NeverScrollableScrollPhysics(),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    InkWell(
                      onTap: () {
                        Get.toNamed('UserDataScreen');
                      },
                      child: Stack(
                        children: [
                          SizedBox(
                            width: 36.0.w,
                            height: 36.0.h,
                            child: InkWell(
                              onTap: () {
                                Get.toNamed('PinlockScreen');
                              },
                              child: const CircleAvatar(
                                backgroundColor: AppColors.SeaGreen,
                              ),
                            ),
                          ),
                          Positioned(
                            left: 1.0.w,
                            top: 1.0.h,
                            child: SizedBox(
                              width: 34.0.w,
                              height: 34.0.h,
                              child: CircleAvatar(
                                backgroundColor: Colors.white,
                                backgroundImage: NetworkImage(
                                    // "https://n8n-cpdg.agilesoftgroup.com/webhook-test/get-image?id=6508074"),
                                "https://s3-ap-southeast-1.amazonaws.com/storage-pkg/icon/icon1548914956default.png"),
                                foregroundImage:
                                NetworkImage(profileCtl.imageProfile),

                                  // "https://linebotkeep-file.s3.ap-southeast-1.amazonaws.com/${profileCtr.responseMember!.id.toString()}_ms24.jpg"),
                                    // "http://devdev.prachakij.com/PPP7/uploads/emp/${profileCtr.responseMember!.id.toString()}.jpg"),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: 20.0.w),
                    InkWell(
                      onTap: () {
                        Get.toNamed('QrScanScreen');
                      },
                      child: SizedBox(
                        height: 25.0.h,
                        width: 24.0.w,
                        child: Container(
                            child: Image.asset('assets/logo/Scan_QR_Code.png')),
                      ),
                    ),
                    SizedBox(width: 25.0.w),
                    InkWell(
                      onTap: () {
                        Get.to(SoundPage());
                      },
                      child: SizedBox(
                        height: 20.0.h,
                        width: 20.0.w,
                        child: InkWell(
                          onTap: () {
                            Get.toNamed('Soundpage');
                          },
                          child: Container(child: Icon(Icons.mic)),
                        ),
                      ),
                    ),
                    // SizedBox(width: 20.0.w),

                    Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        SmoothPageIndicator(
                          controller: _mspController,
                          count: mspOption.length,
                          effect: SwapEffect(
                            dotColor: const Color(0xFFFEE095).withOpacity(0.5),
                            activeDotColor: const Color(0xFFFEE095),
                            dotHeight: 4.h,
                            dotWidth: 4.w,
                            spacing: 8,
                          ),
                        ),
                        SizedBox(height: 6.h),
                        SizedBox(
                          height: 50.h,
                          width: 100.w,
                          child: GetBuilder<MspService>(builder: (controller) {
                            return PageView.builder(
                              controller: _mspController,
                              itemCount: mspOption.length,
                              itemBuilder: (context, index) {
                                return mspOption[index];
                              },
                            );
                          }),
                        ),
                      ],
                    ),

                    // SizedBox(width: 20.0.w),
                    InkWell(
                      onTap: () {
                        Get.toNamed('DoorScanScreen');
                      },
                      child: SizedBox(
                        height: 30.0.h,
                        width: 30.0.w,
                        child:
                            Image.asset('assets/logo/smart-door_3679954.png'),
                      ),
                    ),
                    SizedBox(width: 20.0.w),
                    InkWell(
                      onTap: () {
                        Get.toNamed('NotiScreen');
                      },
                      child: SizedBox(
                        height: 30.0.h,
                        width: 30.0.w,
                        child: buildNotifyModify(),
                      ),
                    ),
                    SizedBox(width: 20.0.w),
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          _scaffoldKey.currentState!.openEndDrawer();
                        });
                      },
                      child: SizedBox(
                        height: 28.0.h,
                        width: 28.0.w,
                        child: Image.asset('assets/logo/Menu.png',
                            color: Theme.of(context).colorScheme.scrim),
                      ),
                    ),
                  ],
                ),
                ClipOval(
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
                    child: AnimatedOpacity(
                      duration: const Duration(milliseconds: 500),
                      opacity: visibility ? 1.0 : 0.0,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Liner(context),
                            buildDetail(context),
                          ],
                        ),
                      ),
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Column(
              children: [
                AnimatedPadding(
                  padding: EdgeInsets.only(top: visibility ? 290.h : 80.h),
                  duration: const Duration(milliseconds: 500),
                  child: Container(
                    height: 38.h,
                    width: 150.w,
                    decoration: BoxDecoration(
                      gradient: visibility
                          ? LinearGradient(
                              colors: [
                                Theme.of(context)
                                    .colorScheme
                                    .surfaceContainerHigh
                                    .withOpacity(0.05),
                                Theme.of(context)
                                    .colorScheme
                                    .surfaceContainerHigh,
                                Theme.of(context)
                                    .colorScheme
                                    .surfaceContainerHigh
                              ],
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                            )
                          : LinearGradient(
                              colors: [
                                Theme.of(context).colorScheme.primary,
                                Theme.of(context).colorScheme.primary,
                                Theme.of(context).colorScheme.primary,
                              ],
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                            ),
                      borderRadius: BorderRadius.vertical(
                        bottom: Radius.elliptical(
                          MediaQuery.of(context).size.width,
                          160.0.h,
                        ),
                      ),
                    ),
                    child: ClipOval(
                      child: BackdropFilter(
                        filter: ImageFilter.blur(sigmaY: 1, sigmaX: 1),
                        child: SizedBox(
                          width: Get.width,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      visibility = !visibility;
                                    });
                                  },
                                  child: SizedBox(
                                    height: 18.0.h,
                                    width: 18.0.w,
                                    child: Image.asset(
                                      visibility
                                          ? 'assets/ADD/Expand_up_double .png'
                                          : 'assets/ADD/Expand_down_double.png',
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Widget buildTimmi(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    Annoucer annoucerController = Get.put(Annoucer());

    setMessageHead();
    return Stack(
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 10.0.w, vertical: 10.h),
          child: Container(
            height: 55.h,
            width: screenWidth,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                  color: Theme.of(context).colorScheme.surfaceBright,
                  width: 1.w),
              color: Theme.of(context).colorScheme.surfaceBright,
            ),
          ),
        ),
        InkWell(
          onTap: () async {
            annoucerController.speakAllTexts(_texts);
          },
          child: Container(
              // padding: EdgeInsets.symmetric(horizontal: 20.w),
              height: 64.h,
              width: Get.width,
              child: Row(
                children: [
                  Image.asset('assets/logo/Timmii_Animated.png'),
                  Container(
                    padding: EdgeInsets.only(left: 10.w, top: 10.h),
                    width: 270.w,
                    child: Row(
                      children: [
                        Text(
                          "timmi".tr,
                          textAlign: TextAlign.left,
                          style: TextStyle(
                            // fontFamily: 'SukhumvitSet-Text',
                            fontFamily: 'SukhumvitSet-Bold',
                            fontSize: 14.sp,
                            color: Theme.of(context).colorScheme.tertiary,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        Align(
                          child: Column(
                            children: [
                              Padding(
                                padding:
                                    const EdgeInsets.only(left: 2.0, top: 15.0),
                                child: SizedBox(
                                  width: 200.w,
                                  height: 30.h,
                                  child: PageView.builder(
                                    controller: _pageController,
                                    itemCount: _texts.length,
                                    itemBuilder: (context, index) {
                                      return Container(
                                        child: Padding(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 2),
                                          child: AppWidget.overflowText(
                                              context,
                                              _texts[index],
                                              14.sp,
                                              Theme.of(context)
                                                  .colorScheme
                                                  .tertiary,
                                              FontWeight.w400),
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(top: 10.h),
                    child: IconButton(
                      icon: Icon(
                        Icons.arrow_forward_ios,
                        size: 20,
                        color: Theme.of(context).colorScheme.tertiary,
                      ),
                      onPressed: () {
                        if (_pageController.page!.toInt() < _texts.length - 1) {
                          _pageController.nextPage(
                            duration: Duration(milliseconds: 300),
                            curve: Curves.easeInOut,
                          );
                        }
                      },
                    ),
                  ),
                ],
              )),
        ),
      ],
    );
  }

  Widget buildUsedDetail() {
    return Column(
      children: [
        SizedBox(
          height: 10.h,
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AppWidget.normalText(
                context,
                profileCtr.responseMember!.full_name_th,
                16.sp,
                Theme.of(context).colorScheme.onSecondary,
                FontWeight.w400),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                AppWidget.normalText(
                    context,
                    profileCtr.responseMember!.company_management! + '.',
                    16.sp,
                    Theme.of(context).colorScheme.onSecondary,
                    FontWeight.w400),
                AppWidget.normalText(
                    context,
                    ' ${profileCtr.responseMember!.id}',
                    16.sp,
                    Theme.of(context).colorScheme.secondaryFixed,
                    FontWeight.w400),
              ],
            )
          ],
        ),
        SizedBox(
          height: 10.h,
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AppWidget.normalText(context, 'ui_purpose'.tr, 10.sp,
                Theme.of(context).colorScheme.secondaryFixed, FontWeight.w600),
            Container(
              width: MediaQuery.of(context).size.width * 0.8,
              child: AppWidget.normalText(
                  context,
                  profileCtr.responseMember!.purpose,
                  12.sp,
                  Theme.of(context).colorScheme.onSecondary,
                  FontWeight.w400),
            )
          ],
        ),
        SizedBox(
          height: 10.h,
        ),
      ],
    );
  }

  Widget builsdusedDetailPage2() {
    return Column(
      children: [
        SizedBox(
          height: 10.h,
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AppWidget.normalText(context, 'ui_OKR'.tr, 12.sp,
                Theme.of(context).colorScheme.secondaryFixed, FontWeight.w600),
            SizedBox(
              height: 10.h,
            ),
            Container(
              height: mediaQuery(context, 'h', 250),
              width: mediaQuery(context, 'w', 750),
              // margin: EdgeInsets.only(
              //   top: mediaQuery(context, "h", 150),
              // ),
              child: ListView(
                padding: EdgeInsets.zero,
                children: [
                  Container(
                    margin: EdgeInsets.only(
                      left: mediaQuery(context, "w", 50),
                    ),
                    child: Column(
                      children: [
                        AppWidget.normalText(
                            context,
                            profileCtr.campas_person,
                            12.sp,
                            Theme.of(context).colorScheme.onSecondary,
                            FontWeight.w400),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget buildSelectScreen(BuildContext context) {
    return Column(children: [
      Container(
        width: Get.width,
        height: 42.h,
        decoration: BoxDecoration(
          border: Border.all(
            width: 0.5.w,
            color: Theme.of(context).colorScheme.onSecondary.withOpacity(0.1),
          ),
          gradient: LinearGradient(
            colors: [
              Theme.of(context).colorScheme.primary.withOpacity(0),
              Theme.of(context).colorScheme.primary.withOpacity(0.1),
              Theme.of(context).colorScheme.primary.withOpacity(0),
            ],
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
          ),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            GestureDetector(
              onTap: () {
                setState(() {
                  currentIndex = 0;
                });
              },
              child: Padding(
                padding:
                    EdgeInsets.only(left: 10.0.w, top: 4.0.h, bottom: 4.0.h),
                child: Container(
                  height: 30.0.h,
                  decoration: BoxDecoration(
                    shape: BoxShape.rectangle,
                    borderRadius: BorderRadius.circular(10),
                    color: currentIndex == 0
                        ? Theme.of(context).colorScheme.surfaceBright
                        : Colors.transparent,
                    border: Border.all(
                      width: 1.w,
                      color: currentIndex == 0
                          ? Colors.transparent
                          : Theme.of(context).colorScheme.surfaceBright,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: currentIndex == 0
                            ? const Color(0x40000000)
                            : Colors.transparent,
                        offset: const Offset(0, 4),
                        blurRadius: 4,
                      ),
                    ],
                  ),
                  child: Padding(
                    padding: const EdgeInsets.only(left: 10.0, right: 10),
                    child: Align(
                      alignment: Alignment.center,
                      child: AppWidget.normalText(
                        context,
                        'ui_wealth'.tr,
                        14.sp,
                        currentIndex == 0
                            ? Theme.of(context).colorScheme.tertiary
                            : Theme.of(context).colorScheme.onBackground,
                        FontWeight.w700,
                      ),
                    ),
                  ),
                ),
              ),
            ),
            GestureDetector(
              onTap: () {
                setState(() {
                  currentIndex = 1;
                });
              },
              child: Padding(
                padding: EdgeInsets.only(left: 10.0.w, top: 4.h, bottom: 4.h),
                child: Container(
                  height: 30.0.h,
                  decoration: BoxDecoration(
                    shape: BoxShape.rectangle,
                    borderRadius: BorderRadius.circular(8),
                    color: currentIndex == 1
                        ? Theme.of(context).colorScheme.surfaceBright
                        : Colors.transparent,
                    border: Border.all(
                      width: 1.w,
                      color: currentIndex == 1
                          ? Colors.transparent
                          : Theme.of(context).colorScheme.surfaceBright,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: currentIndex == 1
                            ? const Color(0x40000000)
                            : Colors.transparent,
                        offset: const Offset(0, 4),
                        blurRadius: 4,
                      ),
                    ],
                  ),
                  child: Padding(
                    padding: const EdgeInsets.only(left: 10.0, right: 10),
                    child: Align(
                      alignment: Alignment.center,
                      child: AppWidget.normalText(
                        context,
                        'ui_health'.tr,
                        14.sp,
                        currentIndex == 1
                            ? Theme.of(context).colorScheme.tertiary
                            : Theme.of(context).colorScheme.onBackground,
                        FontWeight.w700,
                      ),
                    ),
                  ),
                ),
              ),
            ),
            GestureDetector(
              onTap: () {
                setState(() {
                  currentIndex = 2;
                });
              },
              child: Padding(
                padding: EdgeInsets.only(left: 10.0.w, top: 4.h, bottom: 4.h),
                child: Container(
                  height: 30.0.h,
                  decoration: BoxDecoration(
                    shape: BoxShape.rectangle,
                    borderRadius: BorderRadius.circular(10),
                    color: currentIndex == 2
                        ? Theme.of(context).colorScheme.surfaceBright
                        : Colors.transparent,
                    border: Border.all(
                      width: 1.w,
                      color: currentIndex == 2
                          ? Colors.transparent
                          : Theme.of(context).colorScheme.surfaceBright,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: currentIndex == 2
                            ? const Color(0x40000000)
                            : Colors.transparent,
                        offset: const Offset(0, 4),
                        blurRadius: 4,
                      ),
                    ],
                  ),
                  child: Padding(
                    padding: const EdgeInsets.only(left: 10.0, right: 10),
                    child: Align(
                      alignment: Alignment.center,
                      child: AppWidget.normalText(
                        context,
                        'ui_time'.tr,
                        14.sp,
                        currentIndex == 2
                            ? Theme.of(context).colorScheme.tertiary
                            : Theme.of(context).colorScheme.onBackground,
                        FontWeight.w700,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      Padding(
        padding: const EdgeInsets.all(18.0),
        child: Column(
          children: [
            SizedBox(
              height: mediaQuery(context, 'h', 750),
              width: mediaQuery(context, 'w', 750),
              child: IndexedStack(
                index: currentIndex,
                children: [
                  buildWealthScreen(context, isContainerVisible),
                  buildHealthScreen(context),
                  buildTimeScreen(context),
                ],
              ),
            ),
          ],
        ),
      )
    ]);
  }

  Widget buildDetail(context) {
    final List<Widget> detailPages = [
      buildUsedDetail(),
      builsdusedDetailPage2(),
      // Add more widgets as needed
    ];

    return Column(
      children: [
        SmoothPageIndicator(
          controller: detailPageController,
          count: detailPages.length,
          effect: SwapEffect(
            dotColor: const Color(0xFFFEE095).withOpacity(0.5),
            activeDotColor: const Color(0xFFFEE095),
            dotHeight: 4.h,
            dotWidth: 4.w,
            spacing: 8,
          ),
        ),
        SizedBox(
          width: 294.w,
          height: 200.h,
          child: PageView.builder(
            controller: detailPageController,
            itemCount: detailPages.length,
            itemBuilder: (context, index) {
              return Container(
                child: detailPages[index],
              );
            },
          ),
        ),
      ],
    );
  }

  Widget buildSelectMenu(context, statuts) {
    final List<Widget Function()> options = [
      () => Stack(
            children: [
              InkWell(
                child: buildMenuForm(context, 24, 24, 'Menu/Clock-2.png',
                    'btn_ta'.tr, 0, taCtr.statusButtonMorning, 1),
                onTap: () {
                  taCtr.getTime(context);

                  AppLoader.loader(context);

                  setState(() {
                    if (int.parse(convertDateTime(taCtr.datetime, "TH")) < 12 &&
                            taCtr.statusButtonMorning == 0 ||
                        taCtr.checkinta == 0) {
                      getPerson(context);

                      AppLoader.dismiss(context);
                      // Get.to(() => AlertUpdatePatchPage());
                    } else if (int.parse(
                            convertDateTime(taCtr.datetime, "TH")) >=
                        12) {
                      getPerson(context);
                      AppLoader.dismiss(context);
                    }
                    // Get.toNamed('Tascreen');
                  });
                },
              ),
              Container(
                width: 120.w,
                margin: EdgeInsets.only(
                    top: langCtr.selectedLanguageIndex == 1
                        ? mediaQuery(context, 'h', 214)
                        : mediaQuery(context, 'h', 180)),
                child: StreamBuilder(
                  stream: clock(),
                  builder: (context, snapshot) {
                    if (snapshot.hasData) {
                      return Text(
                        snapshot.data!,
                        style: TextStyle(
                          fontSize: 8.sp,
                          fontWeight: FontWeight.w700,
                          letterSpacing: 5.0,
                          color: Theme.of(context).colorScheme.scrim,
                        ),
                      );
                    } else {
                      return Text('...');
                    }
                  },
                ),
              )
            ],
          ),
      () => InkWell(
            child: buildMenuForm(
                context,
                24,
                24,
                'Menu/Calender.png',
                'btn_Leave'.tr,
                landAndCrt.dataApprove.length,
                false,
                countLaeveList),
            onTap: () {
              setState(() {
                Get.toNamed('LeaveScreen');
              });
            },
          ),
      () => InkWell(
          onTap: () {
            setState(() {
              ErrorshowAlertDialog(
                  context, 'ปิดใช้งาน', "อยู่ในระหว่างการปรับปรุง");

              // Get.toNamed('BenefitScreen');
             });
          },
          child: buildMenuForm(
              context, 24, 24, 'Menu/Love.png', 'btn_welfare'.tr, 0, false, 0)),
      () => InkWell(
          onTap: () {
            setState(() {
              // ErrorshowAlertDialog(
              //     context, 'ปิดใช้งาน', "อยู่ในระหว่างการปรับปรุง");
              Get.toNamed('FinaceScreen');
            });
          },
          child: buildMenuForm(context, 24, 24, 'Menu/Doc.png',
              'ui_NitroListhead'.tr, finaceCtr.countSignList, false, 0)),
      () => InkWell(
          onTap: () {
            setState(() {
              // ErrorshowAlertDialog(
              //     context, 'ยังไม่เปิดใช้งาน', "อยู่ในระหว่างการปรับปรุง");
              Get.toNamed('SaveCew');
            });
          },
          child: buildMenuForm(
              context, 24, 24, 'ADD/Crown.png', 'btn_cew'.tr, 0, false, 0)),
      () => InkWell(
          onTap: () {
            setState(() {
              ErrorshowAlertDialog(
                  context, 'ปิดใช้งาน', "อยู่ในระหว่างการปรับปรุง");


              // Get.toNamed('WorkIssuesScreen');
            });
          },
          child: buildMenuForm(context, 24, 24, 'ADD/file.png', 'btn_bitrix'.tr,
              bitrixCtr.countBitrix, false, 0)),
      () => InkWell(
          onTap: () {
            setState(() {
              // ErrorshowAlertDialog(
              //     context, 'ยังไม่เปิดใช้งาน', "อยู่ในระหว่างการปรับปรุง");
              Get.toNamed('CustomerFormPage');
            });
          },
          child: buildMenuForm(
              context, 24, 24, 'ADD/Crown.png', 'Mock up Verify', 0, false, 0)),
      () => InkWell(
          onTap: () {
            setState(() {
              Get.toNamed('');
            });
          },
          child: buildMenuForm(context, '', '', '', '', 0, false, 0)),
    ];

    return Column(
      children: [
        SizedBox(
          width: 320.w,
          height: 140.h,
          child: PageView.builder(
            controller: pageController,
            itemCount: (options.length / 4).ceil(),
            itemBuilder: (context, pageIndex) {
              return Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: List.generate(
                  4,
                  (index) {
                    final itemIndex = pageIndex * 4 + index;
                    if (itemIndex < options.length) {
                      return SizedBox(
                        width: 80.w,
                        height: 120.h,
                        child: options[itemIndex](),
                      );
                    } else {
                      return Container(); // Placeholder for empty space
                    }
                  },
                ),
              );
            },
          ),
        ),
        SmoothPageIndicator(
          controller: pageController,
          count: (options.length / 4).ceil(),
          effect: SwapEffect(
              dotColor: const Color(0xFFFEE095).withOpacity(0.5),
              // Change dot color
              activeDotColor: const Color(0xFFFEE095),
              // Change active dot color
              dotHeight: 4.h,
              // Change dot height
              dotWidth: 4.w,
              spacing: 8 // Change dot width
              // Change spacing between dots
              ),
        )
      ],
    );
  }

  Widget buildMenuItem(BuildContext context, String itemName) {
    return Container(
      height: 100.h,
      decoration: const BoxDecoration(
        color: Colors.red,
      ),
      child: InkWell(
        onTap: () {},
        child: Center(
          child: Text(itemName),
        ),
      ),
    );
  }

  Widget buildNotifyModify() {
    return Stack(
      children: [
        SizedBox(
            height: 35.h,
            width: 35.h,
            child: Image.asset('assets/Menu/notifications.png')),
        Positioned(
          left: 14.w,
          top: 0.h,
          child: Container(
              width: 17.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Theme.of(context).colorScheme.secondaryFixed,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AppWidget.normalText(
                    context,
                    notifyCtr.testNoti.length.toString(),
                    10.sp,
                    const Color(0xFFF6F6F6),
                    FontWeight.w500,
                  )
                ],
              )),
        ),
      ],
    );
  }

  Widget buildShowDrawer() {
    return Drawer(
      width: 200.0.w,
      child: Container(
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF1F1C2F).withOpacity(1),
              spreadRadius: 2,
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
          gradient: LinearGradient(
            colors: [
              Theme.of(context).colorScheme.primary,
              Theme.of(context).colorScheme.onPrimary,
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          height: Get.height,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                        child: Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Image.asset(
                          Get.isDarkMode
                              ? 'assets/Logo_dark.png'
                              : 'assets/Logo_light.png',
                          width: 60.w,
                        ),
                        SizedBox(
                          height: 30.h,
                        ),
                        GestureDetector(
                          child: AppWidget.normalText(
                            context,
                            'ui_profile'.tr,
                            16.sp,
                            Theme.of(context).colorScheme.onSecondary,
                            FontWeight.w600,
                          ),
                          onTap: () {
                            Get.toNamed('UserDataScreen');
                          },
                        ),
                        Liner2(context),
                        GestureDetector(
                          child: AppWidget.normalText(
                            context,
                            'ui_feedback'.tr,
                            16.sp,
                            Theme.of(context).colorScheme.onSecondary,
                            FontWeight.w600,
                          ),
                          onTap: () {
                            Get.toNamed('SuggestionsScreen');
                          },
                        ),
                        Liner2(context),
                        GestureDetector(
                          child: AppWidget.normalText(
                            context,
                            'ui_theme'.tr,
                            16.sp,
                            Theme.of(context).colorScheme.onSecondary,
                            FontWeight.w600,
                          ),
                          onTap: () {
                            Get.toNamed('ChangeThemeScreen');
                          },
                        ),
                        Liner2(context),
                        buildReset(),
                      ],
                    )),
                    SizedBox(
                      height: 8.h,
                    ),
                    SizedBox(
                      height: 8.h,
                    ),
                    Expanded(
                      child: buildEtcApp(),
                    ),
                  ],
                ),
              ),
              Column(
                children: [
                  buildLogout(),
                  SizedBox(
                    height: 30.h,
                  ),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget buildEtcApp() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        GestureDetector(
          child: AppWidget.normalText(context, 'ui_supporter'.tr, 16.sp,
              Theme.of(context).colorScheme.onSecondary, FontWeight.w600),
          onTap: () {},
        ),
        SizedBox(
          height: 10.h,
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            buildETCApp('assets/logo/icon_aam.png', () {
              OsCrt.openAppBasedOnOS('https://aamfinancegroup.page.link/aamapp',
                  'com.aamfinancegroup.aam', 'com.aamfinancegroup.aamapp');
            }),
            SizedBox(
              width: 12.w,
            ),
            buildETCApp('assets/logo/icon_rafco.png', () {
              OsCrt.openAppBasedOnOS('https://rafco.page.link/start',
                  'com.rptn.rafco', 'com.rptn.rafco');
            }),
            SizedBox(
              width: 12.w,
            ),
            buildETCApp('assets/logo/icon_rplc.png', () {
              OsCrt.openAppBasedOnOS(
                  'https://rplc.page.link/RtQw',
                  'com.ruampattanaleasing.rplc_app',
                  'com.ruampattanaleasing.rplcapp');
            })
          ],
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            buildETCApp('assets/logo/pms.png', () {
              OsCrt.openAppBasedOnOS('https://prachakij.page.link/mapp_app',
                  "com.prachakij.pms_app", "com.prachakij.pms");
            }),
          ],
        ),
      ],
    );
  }

  // สร้าง UI สำหรับการเลือกภาษา

  Widget buildReset() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        AppWidget.normalText(context, 'ui_settingchange'.tr, 15.sp,
            Theme.of(context).colorScheme.onSecondary, FontWeight.w600),
        SizedBox(
          height: 10.h,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            GestureDetector(
              onTap: () {
                languageController.selectLanguage(1); // หรือ 0
                languageController.updateLanguage();
                languageController.update(); // 🟢 force rebuild
              },
              child: Text(
                'TH',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                  color: languageController.selectedLanguageIndex == 1
                      ? Color(0xFF7295FF)
                      : Theme.of(context).colorScheme.onSecondary,
                ),
              ),
            ),
            const SizedBox(width: 8.0),
            AppWidget.normalText(context, '|', 15.sp,
                Theme.of(context).colorScheme.onSecondary, FontWeight.w600),
            const SizedBox(width: 8.0),
            GestureDetector(
              onTap: () {
                languageController.selectLanguage(0); // หรือ 0
                languageController.updateLanguage();
                languageController.update(); // 🟢 force rebuild
              },
              child: Text(
                'EN',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                  color: languageController.selectedLanguageIndex == 0
                      ? Color(0xFF7295FF)
                      : Theme.of(context).colorScheme.onSecondary,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget buildChangeLau() {
    return Padding(
      padding: EdgeInsets.only(top: 20.0.h),
      child: IconButton(
        onPressed: () {
          showDialog(
            context: context,
            builder: (context) {
              return AlertDialog(
                title: const Text('ui_selectlanguages'),
                content: Column(
                  children: [
                    for (var i = 0; i < listOfLanguages.length; i++)
                      RadioListTile(
                        title: Text(listOfLanguages[i].title),
                        value: i,
                        groupValue: languageController.selectedLanguageIndex,
                        onChanged: (value) async {
                          languageController.selectLanguage(value!);
                          await languageController.updateLanguage();
                          Navigator.pop(context); // Close the dialog
                        },
                      ),
                  ],
                ),
              );
            },
          );
        },
        icon: const Icon(Icons.settings),
      ),
    );
  }

  Widget buildLogout() {
    return Stack(
      children: <Widget>[
        ClipRect(
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 1.97, sigmaY: 1.97),
            child: Container(
              decoration: const BoxDecoration(
                color: Color(0x00bebebe),
              ),
            ),
          ),
        ),
        Column(
          mainAxisAlignment: MainAxisAlignment.end,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            InkWell(
              onTap: () async {
                await centerCon.logout();
                setState(() {
                  Get.reloadAll();
                });
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Image.asset('assets/On_button.png',
                      width: 20.w, height: 20.h),
                  SizedBox(
                    width: 5.w,
                  ),
                  GestureDetector(
                    child: AppWidget.normalText(
                        context,
                        'ui_logout'.tr,
                        12,
                        Theme.of(context).colorScheme.onSecondary,
                        FontWeight.w400),
                    onTap: () {
                      LogoutAlertDialog(context, 'ui_logoutalert'.tr.toString(),
                          "ui_logoutCF".tr.toString());
                    },
                  ),
                ],
              ),
            ),
            Liner2(context),
            AppWidget.normalText(context, 'Version 0.0.0', 12.sp,
                Theme.of(context).colorScheme.onSecondary, FontWeight.w400),
          ],
        ),
      ],
    );
  }

  Widget buildETCApp(String path, Function onTap) {
    return path == ''
        ? SizedBox(
            height: 40.0.h,
            width: 40.0.w,
          )
        : Padding(
            padding: const EdgeInsets.symmetric(vertical: 10.0),
            child: InkWell(
                onTap: onTap as void Function()?,
                child: Container(
                    height: 40.0.h,
                    width: 40.0.w,
                    decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        image: DecorationImage(
                            fit: BoxFit.fill,
                            image: AssetImage(path
                                //
                                ))))),
          );
  }

  Widget buildLikeWallet(BuildContext context, isContainerVisible) {
    bool isDarkTheme = Theme.of(context).brightness == Brightness.dark;
    final webviewCtl = Get.put(WebViewLikePointController());
    final profileCtl = Get.put(ProfileController());

    // กำหนดเส้นทางของรูปภาพตามธีม
    String wallet = isDarkTheme
        ? 'assets/Wallet_dark.png' // รูปภาพสำหรับธีมมืด
        : 'assets/Wallet_light.png';
    return Container(
      child: Column(
        children: [
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            physics: NeverScrollableScrollPhysics(),
            child: Row(
              children: [
                SizedBox(
                  height: 25.h,
                  width: 70.w,
                  child: AppWidget.normalText(
                    context,
                    'btn_LikeWallet'.tr,
                    14.sp,
                    Theme.of(context).colorScheme.scrim,
                    FontWeight.w400,
                  ),
                ),
                SizedBox(width: 5.w),
                Container(
                  margin: EdgeInsets.only(top: 5.h),
                  height: 0.5.h,
                  width: Get.width,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.scrim,
                    border: Border.all(
                      width: 0.5.w,
                      color: const Color(0xFFFCF6E4).withOpacity(0.2),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          AppWidget.boldText(
                            context,
                            double.parse(profileCtr.likebalance)
                                .toStringAsFixed(2)
                                .replaceAllMapped(
                                    RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                                    (Match m) => '${m[1]},'),
                            16.sp,
                            Theme.of(context).colorScheme.secondaryContainer,
                            FontWeight.w600,
                          ),
                          AppWidget.normalText(
                            context,
                            '${'ui_Balance'.tr} = ฿' +
                                (double.parse(profileCtr.likebalance) / 100)
                                    .toStringAsFixed(2)
                                    .replaceAllMapped(
                                        new RegExp(
                                            r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                                        (Match m) => '${m[1]},'),
                            12.sp,
                            const Color(0xFFA596FF),
                            FontWeight.w400,
                          ),
                          SizedBox(height: 10.sp),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
              Column(
                mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  AppWidget.normalText(
                    context,
                    double.parse(profileCtr.lockedBalance)
                        .toStringAsFixed(2)
                        .replaceAllMapped(
                            RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                            (Match m) => '${m[1]},'),
                    14.sp,
                    Theme.of(context).colorScheme.secondaryFixed,
                    FontWeight.w400,
                  ),
                  AppWidget.normalText(
                    context,
                    'ui_Locked'.tr,
                    12.sp,
                    Theme.of(context).colorScheme.secondaryFixedDim,
                    FontWeight.w500,
                  ),
                  SizedBox(height: 14.h),
                  AppWidget.normalText(
                    context,
                    double.parse(profileCtr.availableBalance)
                        .toStringAsFixed(2)
                        .replaceAllMapped(
                            RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                            (Match m) => '${m[1]},'),
                    14.sp,
                    Theme.of(context).colorScheme.secondaryContainer,
                    FontWeight.w400,
                  ),
                  AppWidget.normalText(
                    context,
                    'ui_Availble'.tr,
                    12.sp,
                    Theme.of(context).colorScheme.secondaryFixedDim,
                    FontWeight.w500,
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<String> formatPhoneNumberAsync(String? phoneNumber) async {
    if (phoneNumber == null || phoneNumber.isEmpty) {
      return '';
    }

    return '+66${phoneNumber.substring(3)}';
  }

  Widget buildWealthScreen(BuildContext context, isContainerVisible) {
    return SingleChildScrollView(
      scrollDirection: Axis.vertical,
      child: Container(
        width: 357.w,
        child: Column(
          children: [
            buildIncome(context),
            buildLikeWallet(context, isContainerVisible),
            Liner(context),
            buildGTC(context),
            Liner(context),
            buildCu(context),
            Liner(context),
            buildMETACu(context),
            Liner(context),
            buildINSURANCE(context),
          ],
        ),
      ),
    );
  }

  String? token_TG = "";

  Widget buildChat() {
    return DraggableFab(
      child: FloatingActionButton.large(
          backgroundColor: Colors.transparent,
          elevation: 0,
          onPressed: () async {
            if (profileCtr.responseMember!.telegram_token!.substring(0, 4) ==
                '-100') {
              token_TG =
                  "-" + profileCtr.responseMember!.telegram_token!.substring(4);
            } else {
              token_TG = profileCtr.responseMember!.telegram_token!!;
            }

            Get.to(() =>
                HtmlElementView(viewType: 'https://t.me/+v1o4UtqZ-C1hYWJl'));
          },
          child: Container(
            width: 64.w,
            height: 64.h,
            decoration: BoxDecoration(
              gradient: LinearGradient(colors: [
                Theme.of(context).primaryColor.withOpacity(0.8),
                Theme.of(context).hintColor.withOpacity(0.8),
              ]),
              // Adjust opacity for translucency
              shape: BoxShape.circle,
            ),
            child: InkWell(
              onTap: () async {
                await launch(profileCtr.tg_link.value);
              },
              child: Stack(
                children: [
                  Center(child: Image.asset('assets/Chat_alt_3_1.png')),
                  Padding(
                    padding: const EdgeInsets.only(top: 32.0),
                    child: Center(
                      child: AppWidget.normalText(
                        context,
                        'ui_talk'.tr,
                        16.sp,
                        Theme.of(context).colorScheme.scrim,
                        FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          )),
    );
  }

  Widget buildDrag(title, path) {
    return SingleChildScrollView(
      physics: NeverScrollableScrollPhysics(),
      child: Column(
        children: [
          SizedBox(
            height: 64.0.h,
            width: 64.0.w,
            child: ClipOval(
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 2.0, sigmaY: 2.0),
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(colors: [
                      Color(0xFF322B63).withOpacity(0.32),
                      Color(0xFF302C49).withOpacity(0.2)
                    ], begin: Alignment.topCenter, end: Alignment.bottomCenter),
                  ),
                  child: Stack(
                    children: [
                      Image.asset(
                        'assets/$path',
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top: 30.0),
                        child: Center(
                          child: AppWidget.normalText(
                            context,
                            title,
                            14.0.sp,
                            const Color(0xFFF6F6F6),
                            FontWeight.w400,
                          ),
                        ),
                      )
                    ],
                  ),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget logout() {
    return Container(
      alignment: Alignment.topCenter,
      margin: EdgeInsets.only(top: mediaQuery(context, "h", 405)),
      child: Container(
        width: mediaQuery(context, "w", 520),
        height: mediaQuery(context, "h", 600),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(mediaQuery(context, "h", 80)),
          color: Theme.of(context).colorScheme.primary,
        ),
        child: Stack(
          children: <Widget>[
            Column(
              children: [
                SvgPicture.asset(
                    "assets/images/pkg/home/<USER>",
                    height: mediaQuery(context, "h", 50)),
                AppWidget.normalText(
                  context,
                  'ui_logoutalert'.tr,
                  14.sp,
                  Theme.of(context).colorScheme.onSecondary,
                  FontWeight.w400,
                ),
                AppWidget.normalText(
                  context,
                  'ui_logoutCF'.tr,
                  12.sp,
                  Theme.of(context).colorScheme.onSecondary,
                  FontWeight.w400,
                ),
                GestureDetector(
                  onTap: () {
                    setState(() {
                      Get.back();
                    });
                  },
                  child: Container(
                    alignment: Alignment.center,
                    width: mediaQuery(context, "w", 250),
                    height: mediaQuery(context, "h", 90),
                    decoration: BoxDecoration(
                      borderRadius:
                          BorderRadius.circular(mediaQuery(context, "h", 30)),
                      color: Theme.of(context).colorScheme.tertiaryFixed,
                      border: Border.all(
                          width: 1.0, color: const Color(0xff302c49)),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0x1a000000),
                          offset: Offset(0, 1),
                          blurRadius: 2,
                        ),
                      ],
                    ),
                    child: AppWidget.normalText(
                      context,
                      'btn_cancel'.tr,
                      12.sp,
                      Theme.of(context).colorScheme.onSecondary,
                      FontWeight.w400,
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    Get.reloadAll();
                  },
                  child: Container(
                    alignment: Alignment.center,
                    width: mediaQuery(context, "w", 250),
                    height: mediaQuery(context, "h", 90),
                    decoration: BoxDecoration(
                      borderRadius:
                          BorderRadius.circular(mediaQuery(context, "h", 30)),
                      color: Theme.of(context).colorScheme.secondaryContainer,
                    ),
                    child: AppWidget.normalText(
                      context,
                      'btn_logout'.tr,
                      12.sp,
                      Theme.of(context).colorScheme.onSecondary,
                      FontWeight.w400,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  success(context, textMessage) {
    showDialog<String>(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('Success'),
        content: Text(textMessage.toString()),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.pop(context, 'OK'),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  setMessageHead() {
    setState(() {
      var i = 0;
      _texts.clear();
      if (finaceCtr.countSignList > 0) {
        MessageHead = "ui_timminoti_documents".tr.toString();
        _texts.add(MessageHead);
      }
      if (landAndCrt.dataApprove.length > 0) {
        MessageHead = "ui_timminoti".tr.toString();
        _texts.add(MessageHead);
      }
      if (bitrixCtr.countBitrix > 0) {
        MessageHead = "ui_jobmenu".tr.toString();
        _texts.add(MessageHead);
      }
      if (taCtr.statusButtonMorning > 0) {
        MessageHead = "ui_morning_y".tr.toString();
        _texts.add(MessageHead);
      }
      if (taCtr.statusButtonMorning > 0) {
        MessageHead = "ui_morning_y".tr.toString();
        _texts.add(MessageHead);
      }
    });
  }

  Future alertUpdate(context) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context)
                .viewInsets
                .bottom, // ทำให้เลื่อนขึ้นตามแป้นพิมพ์
          ),
          child: Container(
            padding: EdgeInsets.only(
              left: 20.w,
              right: 20.w,
            ),
            height: 760.h,
            width: 392.w,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).colorScheme.primary,
                  Theme.of(context).colorScheme.primary,
                  Theme.of(context).colorScheme.surfaceDim.withOpacity(0.2),
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Container(
                  margin: EdgeInsets.only(top: 50.h),
                  alignment: Alignment.center,
                  child: AppWidget.normalText(
                    context,
                    'ui_update'.tr,
                    16.sp,
                    Theme.of(context).colorScheme.onSecondary,
                    FontWeight.w400,
                  ),
                ),
                SizedBox(
                  height: 30.h,
                ),
                AppWidget.normalText(
                  context,
                  'ui_updateDetail'.tr,
                  14.sp,
                  Theme.of(context).colorScheme.onSecondary,
                  FontWeight.w400,
                ),
                InkWell(
                  onTap: () async {},
                  child: Container(
                    alignment: Alignment.center,
                    width: 1.sw,
                    height: 0.06.sh,
                    margin: EdgeInsets.only(
                      top: 0.50.sh,
                      left: 0.02.sw,
                      right: 0.02.sw,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.only(
                        topRight: Radius.circular(15),
                        topLeft: Radius.circular(15),
                        bottomRight: Radius.circular(15),
                        bottomLeft: Radius.circular(15),
                      ),
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        stops: const [0.0, 1.0],
                        colors: [
                          Theme.of(context)
                              .colorScheme
                              .secondaryContainer
                              .withOpacity(0.4),
                          Theme.of(context)
                              .colorScheme
                              .secondaryContainer
                              .withOpacity(0.6),
                        ],
                      ),
                      border: Border.all(
                          width: 1,
                          color:
                              Theme.of(context).colorScheme.secondaryContainer),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.2),
                          offset: const Offset(1, 2),
                          blurRadius: 3,
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        AppWidget.boldText(
                            context,
                            "btn_update".tr.toString(),
                            15.sp,
                            Theme.of(context).colorScheme.onSecondary,
                            FontWeight.w400),
                        // AppWidget.normalText(context, " ตอนนี้", 15.sp,
                        //     const Color(0xFFFFB100), FontWeight.w400)
                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
        );
      },
    );
  }

  Future alertUpdatePatch(context) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true, // เพื่อให้เลื่อนขึ้นตามแป้นพิมพ์
      builder: (BuildContext context) {
        return SizedBox(
          width: 1.sw,
          height: 1.sh,
          child: Column(
            // mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                margin: EdgeInsets.only(top: 0.2.sh, right: 0.05.sw),
                height: 0.1.sh,
              ),
              SizedBox(
                height: 0.08.sh,
              ),
              Obx(() => AppWidget.boldTextS(
                  context,
                  updatePatchCtl.restartApp.value == true
                      ? "อัปเดตแพทช์สำเร็จ!"
                      : "กำลังอัปเดตแพทช์...",
                  18.sp,
                  Theme.of(context).colorScheme.secondaryContainer,
                  FontWeight.w500)),
              SizedBox(
                height: 0.02.sh,
              ),
              Obx(() => updatePatchCtl.restartApp.value == true
                  ? Column(
                      children: [
                        AppWidget.boldTextS(
                            context,
                            "กดปุ่มรีสตาร์ทแอปด้านล่างและเข้าใช้งานแอปใหม่อีกครั้ง",
                            14.sp,
                            Theme.of(context).colorScheme.onSecondary,
                            FontWeight.w400)
                      ],
                    )
                  : Column(
                      children: [
                        AppWidget.boldTextS(
                            context,
                            "เพื่อเพิ่มประสิทธิภาพการใช้งาน",
                            14.sp,
                            Theme.of(context).colorScheme.onSecondary,
                            FontWeight.w400),
                        AppWidget.boldTextS(
                            context,
                            "และรองรับสำหรบฟีเจอร์ใหม่",
                            14.sp,
                            Theme.of(context).colorScheme.onSecondary,
                            FontWeight.w400)
                      ],
                    )),
              SizedBox(
                height: 0.04.sh,
              ),
              Obx(() => Container(
                    margin: EdgeInsets.only(
                      top: 0.02.sh,
                      left: 0.06.sw,
                      right: 0.06.sw,
                    ),
                    child: updatePatchCtl.restartApp.value == true
                        ? InkWell(
                            onTap: () async {
                              /// In Web Platform, Fill webOrigin only when your new origin is different than the app's origin
                              Restart.restartApp();
                            },
                            child: Container(
                              width: 1.sw,
                              height: 0.06.sh,
                              margin: EdgeInsets.only(
                                top: 0.35.sh,
                                left: 0.02.sw,
                                right: 0.02.sw,
                              ),
                              decoration: BoxDecoration(
                                borderRadius: const BorderRadius.only(
                                  topRight: Radius.circular(15),
                                  topLeft: Radius.circular(15),
                                  bottomRight: Radius.circular(15),
                                  bottomLeft: Radius.circular(15),
                                ),
                                gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  stops: const [0.0, 1.0],
                                  colors: [
                                    Theme.of(context)
                                        .colorScheme
                                        .secondaryContainer
                                        .withOpacity(0.4),
                                    Theme.of(context)
                                        .colorScheme
                                        .secondaryContainer
                                        .withOpacity(0.6),
                                  ],
                                ),
                                border: Border.all(
                                    width: 1,
                                    color: Theme.of(context)
                                        .colorScheme
                                        .secondaryContainer),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.grey.withOpacity(0.2),
                                    offset: const Offset(1, 2),
                                    blurRadius: 3,
                                  ),
                                ],
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  AppWidget.boldText(
                                      context,
                                      "รีสตาร์ทแอป",
                                      15.sp,
                                      Theme.of(context).colorScheme.onSecondary,
                                      FontWeight.w400),
                                  // AppWidget.normalText(context, " ตอนนี้", 15.sp,
                                  //     const Color(0xFFFFB100), FontWeight.w400)
                                ],
                              ),
                            ),
                          )
                        : Column(
                            children: [
                              LinearPercentIndicator(
                                barRadius: const Radius.circular(10),
                                lineHeight: 8,
                                percent: updatePatchCtl
                                        .downloadProgressNotifier.value /
                                    100,
                                backgroundColor: Theme.of(context)
                                    .colorScheme
                                    .secondaryContainer
                                    .withOpacity(0.2),
                                progressColor: Theme.of(context)
                                    .colorScheme
                                    .secondaryContainer
                                    .withOpacity(0.6),
                              ),
                              SizedBox(
                                height: 0.01.sh,
                              ),
                              AppWidget.normalText(
                                  context,
                                  "${updatePatchCtl.downloadProgressNotifier.value.round()}%",
                                  14,
                                  Theme.of(context).colorScheme.onSecondary,
                                  FontWeight.w400),
                            ],
                          ),
                  ))
            ],
          ),
        );
      },
    );
  }
}
