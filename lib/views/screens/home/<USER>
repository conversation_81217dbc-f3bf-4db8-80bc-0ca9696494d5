import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_ms24/views/screens/home/<USER>';
import 'package:microphone/microphone.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../../controllers/record_soundController.dart';
import 'dart:html' as html;
import 'package:audioplayers/audioplayers.dart';

class SoundPage extends StatefulWidget {
  @override
  State<SoundPage> createState() => _SoundPageState();
}

class _SoundPageState extends State<SoundPage> {
  final RecordController _controller = Get.put(RecordController());
  final MicrophoneRecorder _recorder = MicrophoneRecorder();
  final TextEditingController customerIdController = TextEditingController();
  final TextEditingController filenameController = TextEditingController();
  final TextEditingController memberController = TextEditingController();
  final TextEditingController topicController = TextEditingController();
  final RxBool isFormValid = false.obs;

  @override
  void initState() {
    super.initState(); // เรียก super ก่อน
    Future.microtask(() async {
      await _recorder.init();
      await _initialize();
    });

    customerIdController.addListener(_validateForm);
    topicController.addListener(_validateForm);
  }

  Future<void> _initialize() async {
    await _requestPermission();

    // await _controller.onInit();

    await _controller.clearAndRequestPermissions(
      isMicrophoneReady: _controller.isMicrophoneReady,
      isCameraReady: _controller.isCameraReady,
    );
  }

  Future<void> _requestPermission() async {
    if (kIsWeb) {
      try {
        final mediaDevices = html.window.navigator.mediaDevices;
        if (mediaDevices != null) {
          await mediaDevices.getUserMedia({'audio': true, 'video': true});
        }
      } catch (e) {
        _showErrorSnack("กรุณาอนุญาตไมโครโฟน/กล้องบนเบราว์เซอร์");
      }
    } else {
      final micStatus = await Permission.microphone.request();
      final camStatus = await Permission.camera.request();

      if (!micStatus.isGranted || !camStatus.isGranted) {
        _showErrorSnack("ต้องอนุญาตไมโครโฟนและกล้องเพื่อใช้งานฟีเจอร์นี้");
      }
    }
  }

  void _showErrorSnack(String msg) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(msg),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _validateForm() {
    isFormValid.value = customerIdController.text.trim().isNotEmpty &&
        topicController.text.trim().isNotEmpty;
  }

  void _resetForm() {
    customerIdController.clear();
    topicController.clear();
    _controller.clearSelectedTopic();
  }

  @override
  void dispose() {
    if (_controller.isRecording.value) {
      _controller.stopRecording(
        filenameController.text.trim(),
        customerIdController.text.trim(),
        _controller.selectedTopic ?? '',
      );
    }

    _recorder.dispose();
    _controller.onClose();

    customerIdController.dispose();
    topicController.dispose();
    super.dispose();
  }

  Widget _buildTextField({
    required String label,
    required TextEditingController controller,
  }) {
    return TextField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        border: const OutlineInputBorder(),
        enabledBorder: const OutlineInputBorder(
          borderSide: BorderSide(color: Colors.black),
        ),
        focusedBorder: const OutlineInputBorder(
          borderSide: BorderSide(color: Colors.black),
        ),
      ),
    );
  }

  Widget _buildDropdown() {
    return DropdownButtonFormField<String>(
      value: _controller.selectedTopic?.isEmpty == true
          ? null
          : _controller.selectedTopic,
      decoration: const InputDecoration(
        labelText: 'Topic',
        border: OutlineInputBorder(),
      ),
      items: _controller.topics.map((topic) {
        return DropdownMenuItem(value: topic, child: Text(topic));
      }).toList(),
      onChanged: (String? newValue) {
        setState(() {
          _controller.selectedTopic = newValue;
          topicController.text = newValue ?? '';
        });
      },
    );
  }

  Future<void> _requestMediaPermissionForWeb() async {
    if (kIsWeb) {
      try {
        final mediaDevices = html.window.navigator.mediaDevices;
        if (mediaDevices != null) {
          await mediaDevices.getUserMedia({'audio': true});
        } else {
          print("❌ Web: mediaDevices not available");
        }
      } catch (e) {
        print("🌐 Web permission error: $e");
      }
    } else {
      await Permission.microphone.request();
      await Permission.camera.request();
    }
  }

  Widget _buildRecordButton() {
    return Obx(() {
      final isRecording = _controller.isRecording.value;
      final isLoading = _controller.isLoading.value;

      return ElevatedButton.icon(
        onPressed: isLoading || !isFormValid.value
            ? null
            : () async {
                if (isRecording) {
                  await _controller.stopRecording(
                      filenameController.text.trim(),
                      customerIdController.text.trim(),
                      _controller.selectedTopic ?? '');
                  _resetForm(); // รีเซ็ตค่าหลังหยุดการบันทึก
                } else {
                  setState(() {});
                  _controller.initializeRecorder();
                  await _controller.startRecording();
                }
              },
        icon: isLoading
            ? const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                    strokeWidth: 2, color: Colors.white),
              )
            : Icon(isRecording ? Icons.stop : Icons.mic),
        label: Text(
            isLoading ? 'Processing...' : (isRecording ? 'Stop' : 'Start')),
        style: ElevatedButton.styleFrom(
          backgroundColor: isRecording ? Colors.red : Colors.blue,
        ),
      );
    });
  }

  Widget _buildMuteButton() {
    return Obx(() {
      final isMuted = _controller.isMuted.value;

      return ElevatedButton.icon(
        onPressed: () {
          if (_controller.isRecording.value) {
            if (isMuted) {
              _controller.unmuteForWeb();
            } else {
              _controller.muteForWeb();
            }
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('กรุณาเริ่มการบันทึกเสียงก่อน')),
            );
          }
        },
        icon: Icon(isMuted ? Icons.mic_off : Icons.mic),
        label: Text(isMuted ? 'Unmute' : 'Mute'),
        style: ElevatedButton.styleFrom(
          backgroundColor: isMuted ? Colors.orange : Colors.grey,
        ),
      );
    });
  }

  Widget _buildFilePathDisplay() {
    return Obx(() {
      final path = _controller.file_link.value;

      if (path.isNotEmpty && !_controller.isRecording.value) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Recording saved at:',
                style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Text(path),
          ],
        );
      }

      return const SizedBox.shrink();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Record Audio Beta'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            if (kIsWeb) {
              Get.offAllNamed('home'); // เปลี่ยน URL สำหรับ Web
            } else {
              Get.offAllNamed('home'); // นำทางกลับใน Mobile
            }
          },
        ),
        actions: [
          InkWell(
            child: Icon(Icons.list),
            onTap: () async {
              await _controller.GetRECHistory();
              popListHistory(context);
            },
          )
        ],
      ),
      body: _controller.isLoading.value
          ? loadingProcess()
          : SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Center(
                      child: Text('1.กด refresh 1 ครั้งก่อนใช้งาน'),
                    ),
                    Center(
                      child: Text(
                          '2.กด ปุ่ม"เช็คอุปกรณ์" ก่อนใช้งาน เพื่อขอ Permission '),
                    ),
                    Center(
                      child: Text(
                          'สถานะ \n สีแดง = ไม่พร้อมใช้งาน \n สีเขียว = พร้อมใช้งาน'),
                    ),
                    Center(
                      child: Text(
                          '3.ใส่เลขรหัสสมาชิกก่อนทุกครั้ง ไม่ว่าจะกระทำการใดๆ'),
                    ),
                    Center(
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.list),
                          Text(
                              '  มีไว้สำหรับการดูประวัติการบันทึก ตาม PKG ID '),
                        ],
                      ),
                    ),
                    Column(
                      children: [
                        Obx(() => Text(
                              "Microphone: ${_controller.isMicrophoneReady.value ? "Ready" : "Not Ready"}",
                              style: TextStyle(
                                color: _controller.isMicrophoneReady.value
                                    ? Colors.green
                                    : Colors.red,
                                fontWeight: FontWeight.bold,
                              ),
                            )),
                        Obx(() => Text(
                              "Camera: ${_controller.isCameraReady.value ? "Ready" : "Not Ready"}",
                              style: TextStyle(
                                color: _controller.isCameraReady.value
                                    ? Colors.green
                                    : Colors.red,
                                fontWeight: FontWeight.bold,
                              ),
                            )),
                      ],
                    ),
                    ElevatedButton.icon(
                      onPressed: () async {
                        await _requestPermission();
                        await _controller.clearAndRequestPermissions(
                          isMicrophoneReady: _controller.isMicrophoneReady,
                          isCameraReady: _controller.isCameraReady,
                        );
                      },
                      icon: Icon(Icons.settings),
                      label: Text("เช็คอุปกรณ์"),
                    ),
                    const SizedBox(height: 16),
                    _buildTextField(
                        label: 'ชื่อไฟล์', controller: filenameController),
                    const SizedBox(height: 16),
                    _buildTextField(
                        label: 'Customer ID', controller: customerIdController),
                    const SizedBox(height: 16),
                    _buildDropdown(),
                    const SizedBox(height: 30),
                    Obx(() {
                      return Text(
                        _controller.isRecording.value
                            ? 'Recording...'
                            : 'Press the button to start recording',
                        style: TextStyle(
                          fontSize: 18,
                          color: _controller.isRecording.value
                              ? Colors.red
                              : Colors.black,
                        ),
                      );
                    }),
                    const SizedBox(height: 20),
                    _buildRecordButton(),
                    const SizedBox(height: 20),
                    _buildMuteButton(),
                    const SizedBox(height: 30),
                    _buildFilePathDisplay(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget loadingProcess() {
    return Center(
      child: Container(
        color: Colors.black.withOpacity(0.5),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(color: Colors.white),
            const SizedBox(height: 20),
            const Text(
              'กำลังโหลด... กรุณารอสักครู่',
              style: TextStyle(color: Colors.white, fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> popListHistory(BuildContext context) async {
    final List<Map<String, dynamic>> audioHistory =
        await _controller.GetRECHistory();

    final AudioPlayer audioPlayer = AudioPlayer();

    // แสดง Popup Dialog
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('History of Recorded Files'),
          content: audioHistory.isEmpty
              ? const Text('No history available.')
              : SizedBox(
                  width: double.maxFinite,
                  height: 300,
                  child: ListView.builder(
                    itemCount: audioHistory.length,
                    itemBuilder: (context, index) {
                      final audioData = audioHistory[index];
                      final fileUrl = audioData["link"] ?? "";
                      final topic = audioData["topic"] ?? "Unknown Topic";
                      final recTo = audioData["rec_to"] ?? "Unknown Recipient";
                      final name =
                          audioData["file_name"] ?? "Unknown Recipient";

                      return ListTile(
                        title: Text(topic),
                        subtitle: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Text("From: $recTo"),
                            Text("File_name: $name"),
                          ],
                        ),
                        trailing: IconButton(
                          icon: const Icon(Icons.play_arrow),
                          onPressed: () async {
                            await audioPlayer
                                .stop(); // หยุดเสียงก่อนหน้าหากเล่นอยู่
                            await audioPlayer
                                .play(fileUrl); // เล่นไฟล์เสียงจาก URL
                          },
                        ),
                      );
                    },
                  ),
                ),
          actions: [
            TextButton(
              onPressed: () {
                audioPlayer.stop(); // หยุดเสียงเมื่อปิด Popup
                Navigator.of(context).pop();
              },
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }

  Future<void> popListHistory_error(BuildContext context) async {
    // แสดง Popup Dialog
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('History of Recorded Files'),
          content: const Text('กรุณากรอกรหัสสมาชิกก่อนใช้งาน'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }
}
