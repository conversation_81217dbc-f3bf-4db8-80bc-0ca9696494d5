import 'dart:html' as html;
import 'dart:js' as js;

class NfcService {
  Future<void> scanNfc(Function(String) onRead) async {
    if (!js.context.hasProperty('NDEFReader')) {
      throw UnsupportedError('Web NFC is not supported by this browser.');
    }

    final reader = js.JsObject(js.context['NDEFReader']);
    final promise = reader.callMethod('scan');

    promise.callMethod('then', [
          (result) {
        reader.callMethod('onreading', [
              (event) {
            final message = event['message'];
            final records = message['records'];

            if (records != null && records.length > 0) {
              final record = records[0];
              final data = record['data'] as String? ?? 'No data found';
              onRead(data);
            }
          }
        ]);
      }
    ]);

    promise.callMethod('catch', [
          (error) => print('NFC Scan Error: $error'),
    ]);
  }
}
