import 'dart:ui';

import 'package:date_picker_plus/date_picker_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';

import 'package:intl/intl.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:mapp_ms24/configs/color_system.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/controllers/utils/loader.dart';
import 'package:mapp_ms24/models/leaveModel/absenceListModel.dart';
import 'package:mapp_ms24/models/leaveModel/absenceListModel.dart';
import 'package:mapp_ms24/views/screens/home/<USER>';

import '../../../../../controllers/internal/LeaveController/LeaveApprovalController.dart';
import '../../../../../controllers/utils/widget.dart';
import '../../../Widget/Widget.dart';
import '../../../Widget/library.dart';

class LeaveScreen extends StatefulWidget {
  const LeaveScreen({super.key});

  @override
  State<LeaveScreen> createState() => _LeaveScreenState();
}

class _LeaveScreenState extends State<LeaveScreen> {
  LeaveAndAppController landACrt = Get.find<LeaveAndAppController>();
  ProfileController profileCrt = Get.find<ProfileController>();
  late FToast fToast;
  bool visible = false;
  bool checking = true;
  String _value = '0';
  String _valueApprove = '0';
  String name = "0";
  String howday = '0';

  LeaveAndAppController leaveCtr = Get.find<LeaveAndAppController>();
  AbsenceModelList? testLeaveForm = AbsenceModelList();
  final TextEditingController _ReasonController = TextEditingController();
  final TextEditingController _LeaveDetailsController = TextEditingController();

  @override
  void initState() {
    super.initState();
    clearFields();

    initializePage();
  }

  void initializePage() async {
    // taCtr.checkinternet(cont ext);
    fToast = FToast();
    fToast.init(context);
    landACrt.cheang = true.obs;
    await landACrt.resetExpandList();
    await landACrt.resetData();
    await landACrt.getTypeLeave();
    await landACrt.getApproveName();
    await landACrt.loadApprove();
    if (landACrt.datas.isNotEmpty) {
      _value = landACrt.datas[0].value;
    }

    checking = false;
    setState(() {});
  }

  void clearFields() {
    leaveCtr.sumdateController.clear();
    landACrt.description1 = '';
    landACrt.ApproveID = '';
    landACrt.datas = [
      KeyValueModel(key: "dp_selectleavecreate".tr.toString(), value: "0"),
    ];
    landACrt.datasApprove = [
      KeyValueModelApprove(key: "dp_personApprove".tr.toString(), value: "0"),
    ];
  }

  String formatDate(String date) {
    // แปลง String เป็น DateTime ก่อน
    DateTime dateTime = DateTime.parse(date);

    // แปลง DateTime เป็นรูปแบบ dd/MM/yyyy
    return DateFormat('dd/MM/yyyy').format(dateTime);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Stack(
      children: [
        Container(
          height: Get.height,
          decoration: BoxDecoration(
              gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                Theme.of(context).colorScheme.primary,
                Theme.of(context).colorScheme.onPrimary,
              ])),
          child: Stack(
            children: [
              Padding(
                padding: EdgeInsets.only(top: 100.0.h),
                child: Obx(
                  () => SingleChildScrollView(
                    child: Column(
                      children: [
                        landACrt.cheang.value
                            ? buildLeave()
                            : buildForm(context),
                      ],
                    ),
                  ),
                ),
              ),
              buildCheckAppbar(),
            ],
          ),
        ),
        checking
            ? Container(
                width: MediaQuery.of(context).size.width,
                height: MediaQuery.of(context).size.height,
                color: Colors.black12,
                child: Center(
                  child: SizedBox(
                    width: 60.w,
                    child: const LoadingIndicator(
                      indicatorType: Indicator.lineSpinFadeLoader,
                      colors: [Colors.white],
                    ),
                  ),
                ))
            : Container(),
      ],
    ));
  }

  Widget buildCheckAppbar() {
    return Padding(
        padding: const EdgeInsets.only(top: 20.0),
        child: Container(
          width: Get.width,
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Obx(() => buildAppBarWithFuntionForApp(
                      context,
                      'ui_Leave'.tr,
                      landACrt.cheang.value ? 'btn_approve'.tr : 'btn_leave'.tr,
                      'ADD/Transfer-2.png',
                      () {
                        landACrt.cheang.value = !landACrt.cheang.value;
                      },
                      landACrt.cheang.value,
                    )),
              ),
            ],
          ),
        ));
  }

  Widget buildAppBarWithFuntionForApp(
      context, title, namefun, img, funtion, bool type) {
    return Container(
      width: Get.width,
      height: 63.h,
      padding: EdgeInsets.symmetric(horizontal: 10.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary,
        boxShadow: [
          BoxShadow(
            color: const Color(0x26000000), // Shadow color with opacity
            offset: const Offset(0, 4),
            blurRadius: 10,
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          InkWell(
            onTap: () {
              // Get.offAndToNamed('home');
              Get.back();
            },
            child: Container(
              height: 34.h,
              width: 99.w,
              padding: EdgeInsets.all(5),
              alignment: Alignment.centerLeft,
              child: Container(
                height: 14.h,
                width: 9.w,
                child: Image.asset(
                  'assets/ADD/back_Vector.png',
                  color: Theme.of(context).colorScheme.onSecondary,
                  scale: 1,
                ),
              ),
            ),
          ),
          AppWidget.normalText(
            context,
            title,
            16.sp,
            Theme.of(context).colorScheme.onSecondary,
            FontWeight.w500,
          ),
          InkWell(
            onTap: () {
              setState(() {
                funtion();
              });
            },
            child: Container(
              height: 30.h,
              width: 99.w,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: const Color(0xFF302C49).withOpacity(0.5),
                  width: 1,
                ),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AppWidget.normalText(
                    context,
                    namefun,
                    12.sp,
                    Theme.of(context).colorScheme.onSecondary,
                    FontWeight.w500,
                  ),
                  SizedBox(
                    width: 5.w,
                  ),
                  SizedBox(
                    height: 16.h,
                    width: 16.w,
                    child: Image.asset('assets/$img'),
                  )
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildApprove(id, name, type, date, note, index) {
    return InkWell(
      onTap: () {
        setState(() {
          landACrt.toggleExpandState(index);
        });
      },
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.symmetric(vertical: 5.0.h),
            child: Container(
              padding: EdgeInsets.fromLTRB(10.w, 5.h, 10.w, 5.h),
              // ปรับ padding ตามที่ต้องการ
              width: 353.w,
              height: landACrt.isExpandList[index].value ? 190.h : 92.h,
              decoration: landACrt.isExpandList[index].value
                  ? BoxDecoration(
                      color: Theme.of(context).colorScheme.primary,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Theme.of(context)
                            .colorScheme
                            .onPrimaryFixed
                            .withOpacity(1),
                        width: 1,
                      ),
                    )
                  : BoxDecoration(
                      color: Theme.of(context).colorScheme.primary,
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(
                        color: Theme.of(context)
                            .colorScheme
                            .onPrimaryFixed
                            .withOpacity(1),
                        width: 1.5,
                      ),
                    ),

              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                            height: 24.h,
                            width: 24.w,
                            child: Image.asset('assets/ADD/Stick-note.png'),
                          ),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 10.w),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                AppWidget.normalTextRX(
                                  context,
                                  '$id $name'.obs,
                                  14.sp,
                                  Theme.of(context).colorScheme.onSecondary,
                                  FontWeight.w400,
                                ),
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    AppWidget.normalText(
                                      context,
                                      'ui_typeleavecreate'.tr,
                                      14.sp,
                                      Theme.of(context)
                                          .colorScheme
                                          .secondaryContainer,
                                      FontWeight.w400,
                                    ),
                                    landACrt.isExpandList[index].value
                                        ? Container(
                                            width: 170.w,
                                            child: Text(
                                              '$type',
                                              textAlign: TextAlign.left,
                                              maxLines: 2,
                                              overflow: TextOverflow.ellipsis,
                                              style: TextStyle(
                                                fontFamily: 'SukhumvitSet-Text',
                                                fontSize: 14.sp,
                                                color: Theme.of(context)
                                                    .colorScheme
                                                    .secondaryFixed,
                                                fontWeight: FontWeight.w400,
                                              ),
                                            ),
                                          )
                                        : Container(
                                            width: 170.w,
                                            child: Text(
                                              '$type',
                                              textAlign: TextAlign.left,
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                              style: TextStyle(
                                                fontFamily: 'SukhumvitSet-Text',
                                                fontSize: 14.sp,
                                                color: Theme.of(context)
                                                    .colorScheme
                                                    .secondaryFixed,
                                                fontWeight: FontWeight.w400,
                                              ),
                                            ),
                                          )
                                  ],
                                ),
                                Row(
                                  children: [
                                    AppWidget.normalTextRX(
                                      context,
                                      'ui_date_lev'.tr.obs,
                                      14.sp,
                                      Theme.of(context)
                                          .colorScheme
                                          .secondaryFixedDim,
                                      FontWeight.w400,
                                    ),
                                    AppWidget.normalTextRX(
                                      context,
                                      '$date'.obs,
                                      14.sp,
                                      Theme.of(context)
                                          .colorScheme
                                          .secondaryFixedDim,
                                      FontWeight.w400,
                                    ),
                                  ],
                                ),
                                landACrt.isExpandList[index].value
                                    ? Container(
                                        width: 250.w,
                                        child: Text(
                                          "${'tf_levae_note'.tr}$note",
                                          textAlign: TextAlign.left,
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                          style: TextStyle(
                                            fontFamily: 'SukhumvitSet-Text',
                                            fontSize: 14.sp,
                                            color: Theme.of(context)
                                                .colorScheme
                                                .onSecondary,
                                            fontWeight: FontWeight.w400,
                                          ),
                                        ),
                                      )
                                    : Container(),
                              ],
                            ),
                          )
                        ],
                      ),
                      Column(
                        children: [
                          InkWell(
                            onTap: () {
                              setState(() {
                                landACrt.toggleExpandState(index);
                              });
                            },
                            child: SizedBox(
                              height: 24.h,
                              width: 24.w,
                              child: Image.asset(
                                landACrt.isExpandList[index].value
                                    ? 'assets/ADD/Expand_up_light.png'
                                    : 'assets/ADD/Expand_down_light.png',
                                color:
                                    Theme.of(context).colorScheme.onSecondary,
                              ),
                            ),
                          ),
                        ],
                      )
                    ],
                  ),
                  Visibility(
                    visible: landACrt.isExpandList[index]
                        .value, // กำหนดให้แสดงเมื่อ expand เท่านั้น
                    child: Padding(
                      padding: const EdgeInsets.only(top: 10.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          InkWell(
                          onTap: () async {
                    await leaveCtr.approveLeave(
                    context,
                    leaveCtr.dataApprove[index]["absenceID"].toString(),
                    leaveCtr.dataApprove[index]["typeID"].toString(),
                    leaveCtr.dataApprove[index]["description"].toString(),
                    leaveCtr.dataApprove[index]["detail"].toString(),
                    leaveCtr.dataApprove[index]["cancalComment"].toString(),
                    leaveCtr.dataApprove[index]["date"].toString(),
                    leaveCtr.dataApprove[index]["startDate"].toString(),
                    leaveCtr.dataApprove[index]["endDate"].toString(),
                    leaveCtr.dataApprove[index]["sumDate"].toString(),
                    leaveCtr.dataApprove[index]["personID"].toString(),
                    leaveCtr.dataApprove[index]["uthFirstName"].toString(),
                    leaveCtr.dataApprove[index]["uthLastName"].toString(),
                    leaveCtr.dataApprove[index]["reasonID"].toString(),
                    leaveCtr.dataApprove[index]["user_add"].toString(),
                    leaveCtr.dataApprove[index]["approveID"].toString(),
                    "อนุมัติ",
                    );

                    // โหลดข้อมูลใหม่ หรืออัปเดตรายการ
                    await leaveCtr.loadApprove(); // ตัวอย่างการโหลดใหม่
                    setState(() {}); // รีเฟรชหน้า
                    },
                            child: Container(
                                height: 42.h,
                                width: 146.w,
                                decoration: BoxDecoration(
                                  borderRadius: const BorderRadius.all(
                                      Radius.circular(10)),
                                  color: Theme.of(context)
                                      .colorScheme
                                      .secondaryContainer
                                      .withOpacity(0.5),
                                  border: Border.all(
                                    width: 1.w,
                                    color: Theme.of(context)
                                        .colorScheme
                                        .secondaryContainer,
                                  ),
                                ),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    AppWidget.normalText(
                                      context,
                                      'btn_approveDetail'.tr,
                                      14.sp,
                                      Theme.of(context).colorScheme.onSecondary,
                                      FontWeight.w400,
                                    ),
                                  ],
                                )),
                          ),
                          SizedBox(width: 8.w),
                          InkWell(
                            onTap: () {
                              _ReasonController.text = '';
                              // showCupertinoModalPopup(
                              //   context: context,
                              //   builder: (BuildContext context) {
                              //     return
                              buildReason(context, index);

                              //   },
                              // );
                            },
                            child: Container(
                                height: 42.h,
                                width: 156.w,
                                decoration: BoxDecoration(
                                  borderRadius: const BorderRadius.all(
                                      Radius.circular(10)),
                                  color: Theme.of(context).colorScheme.primary,
                                  border: Border.all(
                                    width: 1.w,
                                    color: Theme.of(context)
                                        .colorScheme
                                        .secondaryContainer,
                                  ),
                                ),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    AppWidget.normalText(
                                      context,
                                      'btn_notApproveDetail'.tr,
                                      14.sp,
                                      Theme.of(context)
                                          .colorScheme
                                          .secondaryContainer
                                          .withOpacity(0.8),
                                      FontWeight.w400,
                                    ),
                                  ],
                                )),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildLeave() {
    try {
      if (landACrt.testLeaveForm!.data!.length > 0) {
        // Build the list if it is not empty
        return SizedBox(
          height: 700.h,
          child: ListView.builder(
            itemCount: landACrt.testLeaveForm!.data!.length,
            itemBuilder: (context, index) {
              return buildApprove(
                  landACrt.testLeaveForm!.data![index].user_add,
                  landACrt.testLeaveForm!.data![index].uthFirstName +
                      ' ' +
                      landACrt.testLeaveForm!.data![index].uthLastName,
                  landACrt.testLeaveForm!.data![index].description,
                  formatDate(landACrt.testLeaveForm!.data![index].startDate) +
                      ' - ' +
                      formatDate(landACrt.testLeaveForm!.data![index].endDate),
                  landACrt.testLeaveForm!.data![index].detail,
                  index);
            },
          ),
        );
      } else {
        // Build empty message if the list is empty
        return Container(
            margin: EdgeInsets.only(top: 30.h),
            child: buildEmtpy(context, 'ui_leave_emtpy'.tr));
      }
    } catch (e) {
      return Container(
          margin: EdgeInsets.only(top: 30.h),
          child: buildEmtpy(context, 'ui_leave_emtpy'.tr));
    }
  }

  Widget buildForm(context) {
    return Padding(
      padding: const EdgeInsets.only(top: 20.0),
      child: Container(
          padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
          width: double.infinity.w,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Stack(
                children: [
                  Stack(
                    children: [
                      Column(
                        children: [
                          buildSelect(context),
                          buildSTime(context),
                          buildSelectTime(context),
                          buildAccepter(context),
                          buildInputDes(context),
                          Padding(
                            padding: const EdgeInsets.only(
                                top: 15.0, left: 10, right: 10),
                            child: Liner(context),
                          ),
                          buildDetail(context),
                        ],
                      ),
                      buildSelecttime(
                          landACrt.chektime, // Pass the update method directly
                          landACrt.selectedStartTime)
                    ],
                  ),
                  buildButton(context),
                  alertSuccess()
                ],
              ),
            ],
          )),
    );
  }

  Widget buildSelecttime(
    void Function(String selectedDate) updateSelectedDate,
    RxString selectedValue,
  ) {
    return Padding(
      padding: const EdgeInsets.only(top: 120.0, left: 10, right: 10),
      child: Visibility(
        child: AnimatedContainer(
            duration: Duration(milliseconds: 500),
            padding: EdgeInsets.fromLTRB(18.0, 10.0, 10.0, 10.0),
            decoration: BoxDecoration(
              color: Theme.of(context)
                  .colorScheme
                  .primary, // Set your desired color
              borderRadius: BorderRadius.circular(8.0),
            ),
            width: Get.width,
            height: visible ? 500.0.h : 0, //
            child: SingleChildScrollView(
              child: buildCalendar(
                context,
                selectedValue.toString().obs,
                (RxString selectedDate) {
                  // Handle the selected date here
                  // You may want to close the dialog or perform other actions.
                  setState(() {
                    updateSelectedDate(selectedDate.value);
                  });
                },
              ),
            )),
      ),
    );
  }

  Widget buildSelect(context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 10.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          SizedBox(
            height: 50.h,
            width: 353.w,
            child: buildDropdownType(),
          )
        ],
      ),
    );
  }

  Widget buildSTime(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 10.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          SizedBox(
            height: 50.h,
            width: 172.w,
            child: buildDatePicker(
                'ui_timestart'.tr,
                context,
                'Select StartTime'.obs,
                landACrt
                    .updateselectedStartTime, // Pass the update method directly
                landACrt.selectedStartTime,
                0),
          ),
          SizedBox(
            height: 50.h,
            width: 172.w,
            child: buildDatePicker(
                'ui_timestop'.tr,
                context,
                'Select EndTime'.obs,
                landACrt
                    .updateselectedEndTime, // Pass the update method directly
                landACrt.selectedEndTime,
                1

                // Pass the RxString directly
                ),
          ),
        ],
      ),
    );
  }

// ... (rest of your code remains the same)

  Widget buildSelectTime(context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 10.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            margin: EdgeInsets.only(top: 5.h),
            child: Row(
              children: [
                Container(
                  height: 52.h,
                  width: 85.w,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.sp),
                    color: Theme.of(context).colorScheme.primary,
                    boxShadow: [
                      BoxShadow(
                        color: const Color(
                            0x26000000), // Shadow color with opacity
                        offset: const Offset(0, 4),
                        blurRadius: 5,
                      ),
                    ],
                  ),
                  child: Container(
                    alignment: Alignment.center,
                    margin: EdgeInsets.only(left: 17.w),
                    child: TextField(
                      cursorColor: Colors.black,

                      readOnly: true,
                      controller: leaveCtr.sumdateController.obs.value,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'[.0-9]'))
                      ],
                      style: TextStyle(
                        fontFamily: 'SukhumvitSet-Text',
                        fontSize: 14.sp,
                        color: Theme.of(context).colorScheme.onSecondary,
                        fontWeight: FontWeight.w400,
                      ),
                      decoration: InputDecoration.collapsed(
                        hintText: 'ui_sumdate'.tr,
                        hintStyle: TextStyle(
                          fontFamily: 'SukhumvitSet-Text',
                          fontSize: 12.sp,
                          color: Theme.of(context)
                              .colorScheme
                              .onSecondary
                              .withOpacity(0.7),
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      maxLines: null, // Allow multiline input
                    ),
                  ),
                ),
                Container(
                  width: 10.h,
                ),
                GestureDetector(
                  onTap: () async {
                    AppLoader.loader(context);
                    landACrt.loadcheckLeaveApp(_value, context);
                  },
                  child: Container(
                    width: 30.w,
                    height: 30.h,
                    alignment: Alignment.center,
                    color: Colors.transparent,
                    child: Image.asset('assets/ADD/Clock-2.png'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget buildAccepter(context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 10.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          SizedBox(
            height: 50.h,
            width: 353.w,
            child: buildDropdownWithIcon(),
          )
        ],
      ),
    );
  }

  Widget buildInputDes(context) {
    return Container(
      margin: EdgeInsets.only(top: 5.h, left: 10.w, right: 10.w, bottom: 5.h),
      child: Row(
        children: [
          Container(
            width: 352.w,
            height: 100.h,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: const Color(0x26000000), // Shadow color with opacity
                  offset: const Offset(0, 4),
                  blurRadius: 5,
                ),
              ],
              // border: Border.all(
              //   color: const Color(0xFF5F569B),
              //   width: 1.w,
              // ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(10.0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    height: 24.h,
                    width: 24.w,
                    child: Image.asset('assets/ADD/Message-bubble.png'),
                  ),
                  SizedBox(
                    width: 12.w,
                  ),
                  Container(
                    width: 280.w,
                    height: Get.height,
                    child: TextField(
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                          RegExp(r'[a-zA-Zก-๙0-9\s]'),
                        ),
                      ],
                      controller: _LeaveDetailsController,
                      cursorColor: Colors.black,
                      style: TextStyle(
                        fontFamily: 'SukhumvitSet-Text',
                        fontSize: 14.sp,
                        color: Theme.of(context)
                            .colorScheme
                            .onSecondary
                            .withOpacity(0.7),
                        fontWeight: FontWeight.w400,
                      ),
                      decoration: InputDecoration.collapsed(
                        hintText: 'tf_levae_Write'.tr,
                        hintStyle: TextStyle(
                          fontFamily: 'SukhumvitSet-Text',
                          fontSize: 14.sp,
                          color: Theme.of(context)
                              .colorScheme
                              .onSecondary
                              .withOpacity(0.7),
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      textInputAction: TextInputAction.done,
                      onChanged: (_) {
                        setState(() {});
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildDetail(context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 32.h,
                child: AppWidget.normalText(
                  context,
                  'ui_levae_detail'.tr,
                  14.sp,
                  Theme.of(context).colorScheme.onSecondary,
                  FontWeight.w400,
                ),
              ),
              SizedBox(
                height: 10.h,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  AppWidget.normalText(
                    context,
                    'ui_levae_type'.tr,
                    14.sp,
                    Theme.of(context).colorScheme.onSecondary.withOpacity(0.9),
                    FontWeight.w400,
                  ),
                  AppWidget.normalTextRX(
                    context,
                    landACrt.description1.obs != ''
                        ? landACrt.description1.obs
                        : '-'.obs,
                    14.sp,
                    Theme.of(context).colorScheme.onSecondary.withOpacity(0.9),
                    FontWeight.w400,
                  ),
                ],
              ),
              SizedBox(
                height: 10.h,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  AppWidget.normalText(
                    context,
                    'ui_dateleave'.tr,
                    14.sp,
                    const Color(0xFFA596FF),
                    FontWeight.w400,
                  ),
                  Row(
                    children: [
                      AppWidget.normalText(
                        context,
                        landACrt.selectedStartTime != ''.obs &&
                                landACrt.selectedEndTime != ''.obs
                            ? '${landACrt.selectedStartTime} - ${landACrt.selectedEndTime}'
                            : '-',
                        14.sp,
                        const Color(0xFFA596FF),
                        FontWeight.w400,
                      ),
                    ],
                  )
                ],
              ),
              SizedBox(
                height: 10.h,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  AppWidget.normalText(
                    context,
                    'ui_levae_time'.tr,
                    14.sp,
                    Theme.of(context).colorScheme.onSecondary.withOpacity(0.7),
                    FontWeight.w400,
                  ),
                  AppWidget.normalTextRX(
                    context,
                    landACrt.selectedTimetypemorning != ''.obs &&
                            landACrt.selectedTimetypeevening != ''.obs
                        ? '${landACrt.selectedTimetypemorning} - ${landACrt.selectedTimetypeevening}'
                            .obs
                        : '-'.obs,
                    14.sp,
                    Theme.of(context).colorScheme.onSecondary.withOpacity(0.7),
                    FontWeight.w400,
                  ),
                ],
              ),
              SizedBox(
                height: 10.h,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  AppWidget.normalText(
                    context,
                    'ui_levae_sumday'.tr,
                    14.sp,
                    Theme.of(context).colorScheme.onSecondary.withOpacity(0.7),
                    FontWeight.w400,
                  ),
                  Row(
                    children: [
                      AppWidget.normalText(
                        context,
                        leaveCtr.sumdateController.text != ''.obs ||
                                leaveCtr.sumdateController.text != null
                            ? leaveCtr.sumdateController.text.obs.value
                            : '0.0',
                        14.sp,
                        Theme.of(context)
                            .colorScheme
                            .onSecondary
                            .withOpacity(0.7),
                        FontWeight.w400,
                      ),
                      AppWidget.normalText(
                        context,
                        ' Day',
                        14.sp,
                        Theme.of(context)
                            .colorScheme
                            .onSecondary
                            .withOpacity(0.7),
                        FontWeight.w400,
                      ),
                    ],
                  )
                ],
              ),
              SizedBox(
                height: 10.h,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  AppWidget.normalText(
                    context,
                    'ui_detail_personApprove'.tr,
                    14.sp,
                    Theme.of(context).colorScheme.onSecondary.withOpacity(0.7),
                    FontWeight.w400,
                  ),
                  AppWidget.normalTextRX(
                    context,
                    leaveCtr.ApproveID != ''.obs ? _valueApprove.obs : '-'.obs,
                    14.sp,
                    Theme.of(context).colorScheme.onSecondary.withOpacity(0.7),
                    FontWeight.w400,
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  bool get isComplete {
    return landACrt.selectedStartTime.value.isNotEmpty &&
        landACrt.selectedEndTime.value.isNotEmpty &&
        leaveCtr.sumdateController.text.isNotEmpty &&
        _value != '0' &&
        _valueApprove != '0' &&
        _LeaveDetailsController.text.isNotEmpty;
  }

  Widget buildButton(BuildContext context) {
    return Obx(() {
      final bool ready = isComplete;

      final containerColor = ready
          ? Theme.of(context).colorScheme.secondaryContainer.withOpacity(0.5)
          : Theme.of(context).colorScheme.secondaryContainer.withOpacity(0.2);

      final borderColor = ready
          ? Theme.of(context).colorScheme.secondaryContainer.withOpacity(1)
          : Theme.of(context).colorScheme.secondaryContainer.withOpacity(0.2);

      final textColor = ready
          ? Theme.of(context).colorScheme.onSecondary.withOpacity(1)
          : Theme.of(context).colorScheme.onSecondary.withOpacity(0.2);

      return Container(
        height: 700.h,
        alignment: Alignment.bottomCenter,
        padding: const EdgeInsets.symmetric(horizontal: 20.0),
        child: InkWell(
          splashColor: Colors.white.withOpacity(0.1),
          highlightColor: Colors.transparent,
          onTap: () {
            if (!ready) {
              Get.snackbar('ui_error'.tr, 'msg_fill_all'.tr);
            } else {
              showCupertinoModalPopup(
                context: context,
                builder: (_) =>
                    buildShowLA(context, _LeaveDetailsController.text),
              );
            }
          },
          child: Container(
            height: 52.h,
            decoration: BoxDecoration(
              color: containerColor,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: borderColor, width: 1.5.w),
            ),
            child: Center(
              child: AppWidget.normalText(
                context,
                'btn_leave'.tr,
                16.sp,
                textColor,
                FontWeight.w900,
              ),
            ),
          ),
        ),
      );
    });
  }

  Widget buildDropdownWithIcon() {
    return Container(
      height: Get.height,
      width: Get.width,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary,
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: const [
          BoxShadow(
            color: Color(0x40000000),
            offset: Offset(0, 4),
            blurRadius: 4,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
        child: Stack(
          children: [
            Container(
              alignment: Alignment.centerLeft,
              margin: EdgeInsets.only(
                  left: mediaQuery(context, "w", 10),
                  right: mediaQuery(context, "w", 10)),
              child: DropdownButton<String>(
                icon: SizedBox(
                  width: 24.w,
                  height: 24.h,
                  child: Image.asset(
                    'assets/ADD/Expand_down_light.png',
                    color: Theme.of(context).colorScheme.onSecondary,
                  ),
                ),
                underline: Container(),
                dropdownColor:
                    Theme.of(context).colorScheme.primary.withOpacity(0.9),
                items: landACrt.datasApprove
                    .map((data) => DropdownMenuItem<String>(
                          child: Row(
                            children: [
                              Image.asset(
                                'assets/ADD/User-Verified.png', // แทรกรูปภาพที่นี่
                                width: 24,
                                height: 24,
                              ),
                              SizedBox(
                                width: 15.w,
                              ),
                              AppWidget.normalTextDynamic(
                                context,
                                data.key,
                                14.sp,
                                Theme.of(context)
                                    .colorScheme
                                    .onSecondary
                                    .withOpacity(0.7),
                                FontWeight.w400,
                              )
                            ],
                          ),
                          value: data.value,
                        ))
                    .toList(),
                isExpanded: true,
                onChanged: (value) {
                  setState(() {
                    _valueApprove = value.toString();
                  });
                  leaveCtr.getIDApprove(_valueApprove, context);
                },
                value: _valueApprove,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildDropdownType() {
    return Container(
      height: Get.height,
      width: Get.width,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary,
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: const [
          BoxShadow(
            color: Color(0x40000000),
            offset: Offset(0, 4),
            blurRadius: 4,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Padding(
          padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
          child: DropdownButton<String>(
            icon: SizedBox(
              width: 24.w,
              height: 24.h,
              child: Image.asset(
                'assets/ADD/Expand_down_light.png',
                color: Theme.of(context).colorScheme.onSecondary,
              ),
            ),
            underline: Container(),
            dropdownColor: Theme.of(context).colorScheme.primary,
            items: landACrt.datas
                .map((data) => DropdownMenuItem<String>(
                      child: Row(
                        children: [
                          Image.asset(
                            'assets/ADD/Stick-note.png', // แทรกรูปภาพที่นี่
                            width: 24,
                            height: 24,
                          ),
                          SizedBox(
                            width: 15.w,
                          ),
                          AppWidget.normalTextDynamic(
                            context,
                            data.key,
                            14.sp,
                            Theme.of(context)
                                .colorScheme
                                .onSecondary
                                .withOpacity(0.6),
                            FontWeight.w400,
                          )
                        ],
                      ),
                      value: data.value,
                    ))
                .toList(),
            isExpanded: true,
            onChanged: (value) {
              setState(() {
                _value = value.toString();
                howday = "0";
                leaveCtr.sumdateController.text = '';
                landACrt.selectedStartTime = ''.obs;
                landACrt.selectedEndTime = ''.obs;
                landACrt.inputReson = 'tf_levae_Write'.tr.obs;
                _valueApprove = "0";
              });
              landACrt.getreasonIDLeave(value, context);

              landACrt.getRstID(_value);
            },
            value: _value,
          )),
    );
  }

  Widget buildDatePicker(
      String type,
      BuildContext context,
      RxString title,
      void Function(String selectedDate) updateSelectedDate,
      RxString selectedValue,
      int check) {
    DateTime selectedDate = selectedValue.isNotEmpty
        ? DateFormat('dd/MM/yyyy').parse(selectedValue.value)
        : DateTime.now();

    return Container(
      height: 50.h,
      width: 172.w,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary,
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: const [
          BoxShadow(
            color: Color(0x40000000),
            offset: Offset(0, 4),
            blurRadius: 4,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 10.0.w),
        child: Obx(() {
          return Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                width: 24.w,
                height: 24.h,
                child: Image.asset('assets/ADD/Calender.png'),
              ),
              SizedBox(
                width: 10.w,
              ),
              SizedBox(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    AppWidget.normalText(
                      context,
                      type,
                      12.sp,
                      Theme.of(context)
                          .colorScheme
                          .onSecondary
                          .withOpacity(0.6),
                      FontWeight.w400,
                    ),
                    AppWidget.normalText(
                      context,
                      selectedValue.isNotEmpty
                          ? selectedValue.value
                          : '-/-/----',
                      selectedValue.isNotEmpty ? 12.sp : 14.sp,
                      Theme.of(context).colorScheme.onSecondary,
                      FontWeight.w700,
                    ),
                  ],
                ),
              ),
              Spacer(),
              InkWell(
                onTap: () async {
                  howday = "0";
                  leaveCtr.sumdateController.text = "";

                  setState(() {
                    visible = !visible;
                  });
                  landACrt.Check(check);
                },
                child: SizedBox(
                  width: 24.w,
                  height: 24.h,
                  child: Image.asset(
                    'assets/ADD/Expand_down_light.png',
                    color: Theme.of(context).colorScheme.onSecondary,
                  ),
                ),
              ),
            ],
          );
        }),
      ),
    );
  }

  Widget buildCalendar(
    BuildContext context,
    RxString sValue,
    void Function(RxString value) updateSelectedCEWType,
  ) {
    DateTime currentDate = DateTime.now();
    DateTime maxDate = currentDate.add(const Duration(days: 365));
    return SizedBox(
      width: Get.width,
      height: 500,
      child: Column(
        children: [
          DatePicker(
            ///สีเดือนปี
            leadingDateTextStyle: TextStyle(
              fontFamily: 'SukhumvitSet-Text',
              fontSize: 14.sp,
              color: Theme.of(context).colorScheme.secondaryContainer,
              fontWeight: FontWeight.w700,
            ),
            initialPickerType: PickerType.days,

            ///สีวัน
            daysNameTextStyle: TextStyle(
              fontFamily: 'SukhumvitSet-Text',
              fontSize: 12.sp,
              color: const Color(0xFFA596FF),
              fontWeight: FontWeight.w600,
            ),

            ///สีตัวเลข
            enabledCellTextStyle: TextStyle(
              fontFamily: 'SukhumvitSet-Text',
              fontSize: 14.sp,
              color: Theme.of(context).colorScheme.onSecondary,
              fontWeight: FontWeight.w600,
            ),

            ///สีตัวเลขตอนเลือก
            selectedCellTextStyle: TextStyle(
              fontFamily: 'SukhumvitSet-Text',
              fontSize: 14.sp,
              color: Theme.of(context).colorScheme.onSecondary,
              fontWeight: FontWeight.w600,
            ),

            ///สีเครื่องหมายเลื่อนเดือน
            slidersColor: Theme.of(context).colorScheme.onSecondary,

            ///สีวันที่เลือก
            selectedCellDecoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(6.0.r)),
              color: Theme.of(context)
                  .colorScheme
                  .secondaryContainer
                  .withOpacity(0.6),
              border: Border.all(
                color: Theme.of(context)
                    .colorScheme
                    .secondaryContainer
                    .withOpacity(1),
                width: 1.0.w,
              ),
            ),

            ///สีวันที่ปัจจุบันและวันที่เลือก
            currentDateTextStyle: TextStyle(
              fontFamily: 'SukhumvitSet-Text',
              fontSize: 14.sp,
              color: Theme.of(context).colorScheme.onSecondary,
              fontWeight: FontWeight.w600,
            ),
            currentDateDecoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(6.0.r)),
              color: Colors.transparent,
              border: Border.all(
                color: Colors.transparent,
                width: 1.0.w,
              ),
            ),

            ///สีวันที่ปัจจุบันและวันที่เลือก

            splashColor: Theme.of(context).colorScheme.onSecondary,
            initialDate: DateTime.now(),
            minDate: DateTime(2021, 1, 1),
            maxDate: maxDate,
            slidersSize: 14,
            onDateChanged: (value) {
              updateSelectedCEWType(DateFormat('dd/MM/yyyy').format(value).obs);
            },
          ),
          InkWell(
            onTap: () {
              setState(() {
                visible = false;
              });
            },
            child: Container(
              height: 50.h,
              width: 317.w,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.all(Radius.circular(20)),
                color: Theme.of(context)
                    .colorScheme
                    .secondaryContainer
                    .withOpacity(0.6),
                border: Border.all(
                  width: 1.w,
                  color: Theme.of(context).colorScheme.secondaryContainer,
                ),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AppWidget.normalText(
                    context,
                    'ui_NitroSelect'.tr,
                    14.sp,
                    Theme.of(context).colorScheme.onSecondary,
                    FontWeight.w400,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildShowLA(BuildContext context, LeaveDetails) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 20.w),
      height: 396.h,
      width: 392.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(10.r),
          topRight: Radius.circular(10.r),
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF000000)
                .withOpacity(0.85), // เปลี่ยนค่า opacity ตามต้องการ
            offset: const Offset(0, 4),
            blurRadius: 10,
            spreadRadius: 0,
          ),
        ],
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.onPrimary,
          ],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            width: Get.width.w,
            padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: 32.h,
                  child: AppWidget.normalText(
                    context,
                    'ui_levae_detail'.tr,
                    14.sp,
                    Theme.of(context).colorScheme.onSecondary,
                    FontWeight.w500,
                  ),
                ),
                SizedBox(
                  height: 10.h,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    AppWidget.normalText(
                      context,
                      'ui_levae_type'.tr,
                      14.sp,
                      Theme.of(context).colorScheme.onSecondary,
                      FontWeight.w400,
                    ),
                    AppWidget.normalTextRX(
                      context,
                      landACrt.description1 != ''.obs
                          ? landACrt.description1.obs
                          : '-'.obs,
                      14.sp,
                      Theme.of(context).colorScheme.onSecondary,
                      FontWeight.w400,
                    ),
                  ],
                ),
                SizedBox(
                  height: 10.h,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    AppWidget.normalText(
                      context,
                      'ui_dateleave'.tr,
                      14.sp,
                      const Color(0xFFA596FF),
                      FontWeight.w400,
                    ),
                    Row(
                      children: [
                        AppWidget.normalText(
                          context,
                          landACrt.selectedStartTime != ''.obs &&
                                  landACrt.selectedEndTime != ''.obs
                              ? landACrt.selectedStartTime ==
                                      landACrt.selectedEndTime
                                  ? '${landACrt.selectedStartTime}'
                                  : '${landACrt.selectedStartTime} - ${landACrt.selectedEndTime}'
                              : '-',
                          14.sp,
                          const Color(0xFFA596FF),
                          FontWeight.w400,
                        ),
                      ],
                    )
                  ],
                ),
                SizedBox(
                  height: 10.h,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    AppWidget.normalText(
                      context,
                      'ui_levae_time'.tr,
                      14.sp,
                      Theme.of(context).colorScheme.onSecondary,
                      FontWeight.w400,
                    ),
                    AppWidget.normalTextRX(
                      context,
                      landACrt.selectedTimetypemorning != ''.obs
                          ? landACrt.selectedTimetypemorning
                          : '-'.obs,
                      14.sp,
                      Theme.of(context).colorScheme.onSecondary,
                      FontWeight.w400,
                    ),
                  ],
                ),
                SizedBox(
                  height: 10.h,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    AppWidget.normalText(
                      context,
                      'ui_levae_sumday'.tr,
                      14.sp,
                      Theme.of(context).colorScheme.onSecondary,
                      FontWeight.w400,
                    ),
                    Row(
                      children: [
                        AppWidget.normalText(
                          context,
                          leaveCtr.sumdateController.text != ''.obs
                              ? leaveCtr.sumdateController.text.obs.value
                              : '0.0',
                          14.sp,
                          Theme.of(context).colorScheme.onSecondary,
                          FontWeight.w400,
                        ),
                        AppWidget.normalText(
                          context,
                          'วัน',
                          14.sp,
                          Theme.of(context).colorScheme.onSecondary,
                          FontWeight.w400,
                        ),
                      ],
                    )
                  ],
                ),
                SizedBox(
                  height: 10.h,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    AppWidget.normalText(
                      context,
                      'ui_detail_personApprove'.tr,
                      14.sp,
                      Theme.of(context).colorScheme.onSecondary,
                      FontWeight.w400,
                    ),
                    AppWidget.normalTextRX(
                      context,
                      leaveCtr.ApproveID != '' || leaveCtr.ApproveID != '0'
                          ? _valueApprove.obs
                          : '-'.obs,
                      14.sp,
                      Theme.of(context).colorScheme.onSecondary,
                      FontWeight.w400,
                    ),
                  ],
                ),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(vertical: 10.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Center(
                  child: InkWell(
                    onTap: () {
                      landACrt.createLeave(
                          context,
                          profileCrt.responseMember!.id,
                          _value,
                          "1",
                          profileCrt.responseMember!.telNumber,
                          LeaveDetails,
                          landACrt.selectedStartTime.toString(),
                          landACrt.selectedEndTime.toString(),
                          leaveCtr.sumdateController.text.obs.value,
                          "-",
                          landACrt.ApproveID,
                          landACrt.selectedTimetypemorning,
                          landACrt.selectedTimetypeevening,
                          "ตกลง",
                          _LeaveDetailsController);

                      // ✅ เคลียร์ค่าหลังจากสร้างใบลา
                      landACrt.clearForm();

                      // ✅ กลับไปหน้าก่อนหน้า
                      Get.back();
                    },
                    child: Container(
                        height: 52.h,
                        width: 353.w,
                        decoration: BoxDecoration(
                          borderRadius:
                              const BorderRadius.all(Radius.circular(10)),
                          color: Theme.of(context)
                              .colorScheme
                              .secondaryContainer
                              .withOpacity(0.5),
                          border: Border.all(
                            width: 1.w,
                            color: Theme.of(context)
                                .colorScheme
                                .secondaryContainer,
                          ),
                        ),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            AppWidget.normalText(
                              context,
                              'btn_comfirm'.tr,
                              16.sp,
                              Theme.of(context).colorScheme.onSecondary,
                              FontWeight.w900,
                            ),
                          ],
                        )),
                  ),
                ),
                SizedBox(
                  height: 20.h,
                ),
                InkWell(
                    onTap: () {
                      setState(() {
                        Navigator.pushReplacement(
                            context,
                            MaterialPageRoute(
                                builder: (context) => HomeScreen()));
                      });
                    },
                    child: SizedBox(
                        height: 30.h,
                        width: 71.w,
                        child: Center(
                          child: AppWidget.normalText(
                            context,
                            'btn_cancelcreate'.tr,
                            16.sp,
                            Theme.of(context).colorScheme.onSecondary,
                            FontWeight.w400,
                          ),
                        )))
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget alertSuccess() {
    if (leaveCtr.statusAlertSuccess == 1) {
      return ClipRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 1.97, sigmaY: 1.97),
          child: Container(
            alignment: Alignment.topCenter,
            decoration: BoxDecoration(
              color: const Color(0x00bebebe),
            ),
            child: Container(
              width: mediaQuery(context, "w", 520),
              height: mediaQuery(context, "h", 550),
              margin: EdgeInsets.only(top: mediaQuery(context, "h", 405)),
              decoration: BoxDecoration(
                borderRadius:
                    BorderRadius.circular(mediaQuery(context, "w", 80)),
                color: const Color(0xfffcf6e4),
              ),
              child: Stack(
                children: [
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 82)),
                    child: SvgPicture.string(
                      '<svg viewBox="163.0 0.0 50.0 50.0" ><path transform="translate(160.43, -1.93)" d="M 27.57070350646973 6.61553430557251 C 22.14501762390137 6.61553430557251 17.04413795471191 8.728425025939941 13.20761585235596 12.56494903564453 C 9.371090888977051 16.40147399902344 7.25820255279541 21.50235176086426 7.25820255279541 26.92803764343262 C 7.25820255279541 32.35371780395508 9.371092796325684 37.4546012878418 13.20761680603027 41.2911262512207 C 17.04413986206055 45.12765121459961 22.14501953125 47.24053955078125 27.57070350646973 47.24053955078125 C 32.99638748168945 47.24053955078125 38.09726333618164 45.12765121459961 41.93379211425781 41.2911262512207 C 45.77031326293945 37.4546012878418 47.88320541381836 32.35372161865234 47.88320541381836 26.92803764343262 C 47.88320541381836 21.50235176086426 45.77031326293945 16.40147399902344 41.933837890625 12.56494903564453 C 38.09736251831055 8.728425025939941 32.99638748168945 6.61553430557251 27.57070350646973 6.61553430557251 Z M 27.57070350646973 1.928033590316772 C 41.37783050537109 1.928033590316772 52.57070159912109 13.12090492248535 52.57070159912109 26.92803764343262 C 52.57070159912109 40.73516845703125 41.37783050537109 51.92803955078125 27.57070350646973 51.92803955078125 C 13.76357555389404 51.92803955078125 2.570701122283936 40.73516845703125 2.570701122283936 26.92803955078125 C 2.570701122283936 13.12090969085693 13.76357173919678 1.928035974502563 27.57070350646973 1.928035974502563 Z M 24.44570159912109 36.30303955078125 L 30.69570350646973 36.30303955078125 L 30.69570350646973 42.55304336547852 L 24.44570159912109 42.55304336547852 L 24.44570159912109 36.30303955078125 Z M 24.44570159912109 11.30303478240967 L 30.69570350646973 11.30303478240967 L 30.69570350646973 30.05303573608398 L 24.44570159912109 30.05303573608398 L 24.44570159912109 11.30303478240967 Z" fill="#934d0f" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                      allowDrawingOutsideViewBox: true,
                      height: mediaQuery(context, "h", 50),
                    ),
                  ),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 155)),
                    child: Text(
                      "ui_createLeaveSuccess".tr.toString(),
                      style: TextStyle(
                        fontFamily: 'SukhumvitSet-Bold',
                        fontSize: mediaQuery(context, "h", 33),
                        color: const Color(0xff302c49),
                        letterSpacing: mediaQuery(context, "h", 1.32),
                        fontWeight: FontWeight.w700,
                        height: 0.9393939393939394,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 210)),
                    child: Text(
                      "ui_createdetail".tr.toString(),
                      style: TextStyle(
                        fontFamily: 'SukhumvitSet-Medium',
                        fontSize: mediaQuery(context, "h", 28),
                        color: const Color(0xff302c49),
                        letterSpacing: mediaQuery(context, "h", 1.12),
                        fontWeight: FontWeight.w500,
                        height: 1.5357142857142858,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 379)),
                    child: GestureDetector(
                      onTap: () {
                        leaveCtr.statusAlertSuccess = 0;
                        Navigator.of(context).pop();
                        Navigator.pushReplacement(
                            context,
                            MaterialPageRoute(
                                builder: (context) => LeaveScreen()));
                      },
                      child: Container(
                        alignment: Alignment.center,
                        width: mediaQuery(context, "w", 250),
                        height: mediaQuery(context, "h", 90),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(
                              mediaQuery(context, "h", 30)),
                          color: const Color(0xff7f420c),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0x1a000000),
                              offset: Offset(0, 1),
                              blurRadius: 2,
                            ),
                          ],
                        ),
                        child: Text(
                          "ui_approveok".tr.toString(),
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Bold',
                            fontSize: mediaQuery(context, "h", 30),
                            color: const Color(0xfffcf6e4),
                            letterSpacing: mediaQuery(context, "h", 1.2),
                            fontWeight: FontWeight.w700,
                            // height: 1.0333333333333334,
                            shadows: [
                              Shadow(
                                color: const Color(0x26000000),
                                offset: Offset(0, 1),
                                blurRadius: 1,
                              )
                            ],
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    } else {
      return Container();
    }
  }

  buildReason(BuildContext context, index) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true, // เพื่อให้เลื่อนขึ้นตามแป้นพิมพ์
      builder: (BuildContext context) {
        return Padding(
            padding: EdgeInsets.only(
              // ปรับแต่งขนาดป๊อปอัปเมื่อแป้นพิมพ์ปรากฏ
              bottom: MediaQuery.of(context).viewInsets.bottom,
            ),
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 20.w),
              height: 396.h,
              width: 392.w,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(10.r),
                  topRight: Radius.circular(10.r),
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF000000)
                        .withOpacity(0.85), // เปลี่ยนค่า opacity ตามต้องการ
                    offset: const Offset(0, 4),
                    blurRadius: 10,
                    spreadRadius: 0,
                  ),
                ],
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Theme.of(context).colorScheme.primary,
                    Theme.of(context).colorScheme.onPrimary,
                  ],
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    width: Get.width.w,
                    padding:
                        EdgeInsets.only(top: 20.h, left: 10.w, right: 10.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SizedBox(
                          height: 32.h,
                          child: AppWidget.normalText(
                            context,
                            'ui_Not_approved'.tr,
                            18.sp,
                            Theme.of(context).colorScheme.onSecondary,
                            FontWeight.w500,
                          ),
                        ),
                        SizedBox(
                          height: 10.h,
                        ),
                        AppWidget.normalText(
                          context,
                          'ui_Not_approved2'.tr,
                          18.sp,
                          Theme.of(context).colorScheme.onSecondary,
                          FontWeight.w400,
                        ),
                        SizedBox(
                          height: 30.h,
                        ),
                        TextField(
                          cursorColor: Colors.black,

                          controller: _ReasonController,
                          decoration: InputDecoration(
                            enabledBorder: UnderlineInputBorder(
                              borderSide: BorderSide(
                                  color: Theme.of(context)
                                      .colorScheme
                                      .onBackground),
                            ),
                            focusedBorder: UnderlineInputBorder(
                              borderSide: BorderSide(
                                  color: Theme.of(context)
                                      .colorScheme
                                      .onBackground),
                            ),
                            hintText: 'ใส่เหตุผล'.tr,
                            hintStyle: TextStyle(
                              fontFamily: 'SukhumvitSet-Text',
                              fontSize: 16.sp,
                              color: Theme.of(context).colorScheme.scrim,
                              fontWeight: FontWeight.w400,
                            ),
                            contentPadding: EdgeInsets.symmetric(
                                vertical: 16.h), // Adjust as needed
                            alignLabelWithHint:
                                true, // Align the label (hintText) with the input text
                          ),
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Text',
                            fontSize: 16.sp,
                            color: Theme.of(context).colorScheme.scrim,
                            fontWeight: FontWeight.w400,
                          ),
                          textAlign: TextAlign.center, // Center the input text
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding:
                        EdgeInsets.only(top: 50.h, left: 10.w, right: 10.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Center(
                          child: InkWell(
                            onTap: () {
                              leaveCtr.approveLeave(
                                  context,
                                  leaveCtr.dataApprove[index]["absenceID"]
                                      .toString(),
                                  leaveCtr.dataApprove[index]["typeID"]
                                      .toString(),
                                  leaveCtr.dataApprove[index]["description"]
                                      .toString(),
                                  leaveCtr.dataApprove[index]["detail"]
                                      .toString(),
                                  leaveCtr.dataApprove[index]["cancalComment"]
                                      .toString(),
                                  leaveCtr.dataApprove[index]["date"]
                                      .toString(),
                                  leaveCtr.dataApprove[index]["startDate"]
                                      .toString(),
                                  leaveCtr.dataApprove[index]["endDate"]
                                      .toString(),
                                  leaveCtr.dataApprove[index]["sumDate"]
                                      .toString(),
                                  leaveCtr.dataApprove[index]["personID"]
                                      .toString(),
                                  leaveCtr.dataApprove[index]["uthFirstName"]
                                      .toString(),
                                  leaveCtr.dataApprove[index]["uthLastName"]
                                      .toString(),
                                  leaveCtr.dataApprove[index]["reasonID"]
                                      .toString(),
                                  leaveCtr.dataApprove[index]["user_add"]
                                      .toString(),
                                  leaveCtr.dataApprove[index]["approveID"]
                                      .toString(),
                                  "ไม่อนุมัติ");
                              setState(() {});
                            },
                            child: Container(
                                height: 52.h,
                                width: 250.w,
                                decoration: BoxDecoration(
                                  borderRadius: const BorderRadius.all(
                                      Radius.circular(20)),
                                  color: Theme.of(context)
                                      .colorScheme
                                      .secondaryContainer
                                      .withOpacity(0.5),
                                  border: Border.all(
                                    width: 1.w,
                                    color: Theme.of(context)
                                        .colorScheme
                                        .secondaryContainer,
                                  ),
                                ),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    AppWidget.normalText(
                                      context,
                                      'btn_comfirm'.tr,
                                      16.sp,
                                      Theme.of(context).colorScheme.onSecondary,
                                      FontWeight.w400,
                                    ),
                                  ],
                                )),
                          ),
                        ),
                      ],
                    ),
                  )
                ],
              ),
            ));
      },
    );
  }
}
