import 'dart:async';
import 'dart:convert';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/controllers/internal/Tacontroller/Tacontroller.dart';
import 'package:mapp_ms24/controllers/internal/yubikey/QrScanController.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:permission_handler_web/permission_handler_web.dart';
import 'dart:html' as html;

import 'package:mapp_ms24/views/screens/Widget/Widget.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:permission_handler_web/permission_handler_web.dart';

class QrScanScreen extends StatefulWidget {
  const QrScanScreen({super.key});

  @override
  State<QrScanScreen> createState() => _QrScanScreenState();
}

class _QrScanScreenState extends State<QrScanScreen>
    with WidgetsBindingObserver {
  late FToast fToast;
  final MobileScannerController controller = MobileScannerController();
  QrScanController QrScan = Get.put(QrScanController());
  ProfileController profile = Get.put(ProfileController());
  Tacontroller tacontroller = Get.find<Tacontroller>();
  bool _hasCameraPermission = false;
  bool _isCameraRunning = false;
  var isLoading = false.obs;
  late html.VideoElement _videoElement;

  @override
  void initState() {
    super.initState();
    requestCameraPermission();
    WidgetsBinding.instance.addObserver(this);
    restartCamera(); // Start camera safely
    QrScan.determinePosition();
  }

  Future<void> requestCameraPermission() async {
    if (kIsWeb) {
      try {
        var status = await Permission.camera.request();
        if (status.isGranted) {
          print("Camera permission granted");
        } else if (status.isDenied) {
          print("Camera permission denied");
        } else if (status.isPermanentlyDenied) {
          print("Camera permission permanently denied");
        }
      } catch (e) {
        print("Error requesting camera permission on mobile: $e");
      }
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      restartCamera();
    } else if (state == AppLifecycleState.paused) {
      controller.stop();
      _isCameraRunning = false;
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    controller.dispose();
    _isCameraRunning = false;
    super.dispose();
  }

  Future<void> restartCamera() async {
    if (_isCameraRunning) {
      await controller.stop();
      await Future.delayed(const Duration(milliseconds: 300));
    }

    await controller.start();
    _isCameraRunning = true;
  }

  Future<void> pickAndAnalyzeImage(BuildContext context) async {
    Uint8List? fileBytes;
    String? filePath;
    String? webFileUrl;

    if (kIsWeb) {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        withData: true,
      );
      if (result != null && result.files.single.bytes != null) {
        fileBytes = result.files.single.bytes;

        final blob = html.Blob([fileBytes]);
        webFileUrl = html.Url.createObjectUrlFromBlob(blob);
      }
    } else {
      var image =
          await ImagePicker.platform.pickImage(source: ImageSource.gallery);
      if (image != null) {
        filePath = image.path;
      }
    }

    if (fileBytes == null && filePath == null && webFileUrl == null) return;

    BarcodeCapture? result;
    if (kIsWeb) {
      if (webFileUrl != null) {
        result = await controller.analyzeImage(webFileUrl!);
      }
    } else {
      if (filePath != null) {
        result = await controller.analyzeImage(filePath);
      }
    }

    if (result != null) {
      await QrScan.processQR(
        context,
        result.toString(),
        profile.responseMember!.id.toString(),
        profile.responseMember!.email.toString(),
        profile.responseMember!.phone_like.toString(),
        profile.responseMember!.name_th.toString(),
        profile.responseMember!.surname_th.toString(),
        profile.responseMember!.company_management.toString(),
      );

      QrScan.authen(
          context, result.toString(), profile.responseMember!.id.toString());
      QrScan.authenNew(
        context,
        result.toString(),
        profile.responseMember!.id.toString(),
        profile.responseMember!.email.toString(),
      );
    }

    // ลบ Blob URL หลังจากใช้งานเสร็จ
    if (kIsWeb && webFileUrl != null) {
      html.Url.revokeObjectUrl(webFileUrl); // ✅ clear blob memory
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
            gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
              Theme.of(context).colorScheme.primary,
              Theme.of(context).colorScheme.onPrimary,
            ])),
        width: double.infinity.w,
        child: Stack(
          children: [
            Container(width: Get.width, child: builbuttom(context)),
            Column(
              children: [
                buildAppBar(context, 'ui_QRscanhead'.tr),
              ],
            )
          ],
        ),
      ),
    );
  }

  Future<void> _onBarcodeDetected(BarcodeCapture capture) async {
    for (final barcode in capture.barcodes) {
      final value = barcode.rawValue;
      if (value == null || value.isEmpty) continue;

      try {
        final decoded = jsonDecode(value);
        if (decoded is Map<String, dynamic>) {
          final isValid1 = decoded.containsKey("case_id") &&
              decoded.containsKey("selectedService") &&
              decoded.containsKey("message_thread_id") &&
              decoded.containsKey("phone") &&
              decoded.containsKey("emp_code") &&
              decoded.containsKey("latitude") &&
              decoded.containsKey("longitude");

          final isValid2 = decoded.containsKey("case_id") &&
              decoded.containsKey("message_thread_id") &&
              decoded.containsKey("my_lat") &&
              decoded.containsKey("my_lng") &&
              decoded.containsKey("emp_code");

          if (isValid1) {
            QrScan.handleBarcodeScan(capture);
            await controller.stop();
            _isCameraRunning = false;
            Get.offAllNamed('/home');
            return;
          }

          if (isValid2) {
            await QrScan.verifyCustomer(capture);
            await controller.stop();
            _isCameraRunning = false;
            Get.offAllNamed('/home');
            return;
          }
        }
      } catch (e) {
        debugPrint("Invalid JSON: $e");
      }

      try {
        if (await QrScan.checkData(value)) {
          await QrScan.authen(context, value, profile.responseMember!.id.toString());
          await QrScan.authenNew(
              context, value, profile.responseMember!.id.toString(), profile.responseMember!.email.toString());
        } else {
          await QrScan.processQR(
              context,
              value,
              profile.responseMember!.id.toString(),
              profile.responseMember!.email.toString(),
              profile.responseMember!.phone_like.toString(),
              profile.responseMember!.name_th.toString(),
              profile.responseMember!.surname_th.toString(),
              profile.responseMember!.company_management.toString());
        }
        await controller.stop();
        _isCameraRunning = false;
      } catch (e) {
        debugPrint("Error processing QR: $e");
      }
    }
  }

  Widget builbuttom(context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        GetBuilder<ProfileController>(builder: (profile) {
          return Stack(
            alignment: Alignment.center,
            children: [
              Container(
                width: 300.w,
                height: 300.h,
                child:MobileScanner(
                  controller: controller,
                  onDetect: _onBarcodeDetected, // 👈 ใช้ชื่อฟังก์ชันที่แยกไว้
                ),
              ),
              Container(
                alignment: Alignment.bottomRight,
                padding: EdgeInsets.only(bottom: 150.h, right: 50.w),
                child: GestureDetector(
                  onTap: () async {
                    try {
                      controller.stop();
                      pickAndAnalyzeImage(context);
                    } catch (e) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('Error scanning QR Code: $e')),
                      );
                    }
                  },
                  child: Image.asset(
                    "assets/logo/gallery.png",
                    height: 30.h,
                    fit: BoxFit.fitHeight,
                  ),
                ),
              ),
            ],
          );
        }),
      ],
    );
  }
}
