import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/controllers/utils/color.dart';
import 'package:mapp_ms24/controllers/utils/widget.dart';
import 'package:mapp_ms24/views/screens/Widget/Widget.dart';

class UserDataScreen extends StatefulWidget {
  const UserDataScreen({super.key});

  @override
  State<UserDataScreen> createState() => _UserDataScreenState();
}

class _UserDataScreenState extends State<UserDataScreen> {
  ProfileController profileCrt = Get.find<ProfileController>();

  @override
  void initState() {
    super.initState();
    profileCrt.WorkExperience();
  }

  String convertBirthDay(input) {
    DateTime inputDate = DateTime.parse(input);

    String formattedDate = DateFormat("dd-MM-yyyy").format(inputDate);
    return formattedDate;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            width: double.infinity,
            height: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).colorScheme.primary,
                  Theme.of(context).colorScheme.onPrimary,
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
          ),
          SingleChildScrollView(
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(child: buildShowProfile()),
                  ],
                ),
                Row(
                  children: [
                    Expanded(
                      child: Container(
                          padding: EdgeInsets.symmetric(horizontal: 20.0.w),
                          child: buildDetail()),
                    )
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget buildShowProfile() {
    return SizedBox(
      child: Padding(
        padding: EdgeInsets.only(top: 40.0.h),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              padding: EdgeInsets.symmetric(horizontal: 10.w),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  IconButton(
                    icon: const Icon(
                      Icons.arrow_back_ios_new,
                    ),
                    color: Theme.of(context).colorScheme.onSecondary,
                    onPressed: () {
                      Get.back();
                    },
                  ),
                  AppWidget.normalText(
                      context,
                      'ui_profile'.tr,
                      14.sp,
                      Theme.of(context).colorScheme.onSecondary,
                      FontWeight.w400),
                  IconButton(
                    icon: const Icon(Icons.edit),
                    color: Theme.of(context).colorScheme.onSecondary,
                    onPressed: () {
                      // Get.toNamed('EditUserScreen');
                    },
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 20.0),
              child: Container(
                height: 90.h,
                width: 90.w,
                decoration: BoxDecoration(
                    border: Border.all(
                      color: Theme.of(context).colorScheme.secondaryContainer,
                      width: 1.0,
                    ),
                    color: Colors.red,
                    shape: BoxShape.circle),
                child: CircleAvatar(
                  backgroundColor: Colors.white,
                  backgroundImage: NetworkImage(
                      "https://s3-ap-southeast-1.amazonaws.com/storage-pkg/icon/icon1548914956default.png"),
                  foregroundImage:                                 NetworkImage(profileCrt.imageProfile,),

                ),
              ),
            ),
            AppWidget.normalText(
                context,
                profileCrt.responseMember!.full_name_th,
                14.sp,
                Theme.of(context).colorScheme.onSecondary,
                FontWeight.w400),
            AppWidget.normalText(context, profileCrt.responseMember!.role,
                12.sp, AppColors.BlueSky, FontWeight.w400),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.0.w, vertical: 20.h),
              child: SizedBox(
                height: 60.h,
                child: Column(
                  children: [
                    Image(
                      image: AssetImage('assets/ADD/Line 523.png'),
                    ),
                    SizedBox(
                      height: 17.h,
                    ),
                    AppWidget.normalText(
                        context,
                        profileCrt.servicelife.toString(),
                        12.sp,
                        Theme.of(context).colorScheme.onSecondary,
                        FontWeight.w400),
                    AppWidget.normalText(context, 'ui_life'.tr, 12.sp,
                        AppColors.lavenderPurple, FontWeight.w400),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildDetail() {
    return Stack(
      children: [
        Column(
          children: [
            userInfoRow(
                'ui_birthday'.tr,
                convertBirthDay(
                    profileCrt.responseMember!.birthDay.toString())),
            userInfoRow('ui_IDcard'.tr,
                profileCrt.responseMember!.personalID.toString()),
            userInfoRow('ui_number'.tr,
                profileCrt.responseMember!.telNumber.toString()),
            userInfoRow('ui_numberlikewallet'.tr,
                profileCrt.responseMember!.phone_like.toString()),
            userInfoRow(
                'ui_email'.tr, profileCrt.responseMember!.email.toString()),
            SizedBox(
              height: 15.h,
            ),
            InkWell(
              onTap: () {
                Get.toNamed('SafetyScreen');
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Image(
                        image: AssetImage(
                          Get.isDarkMode
                              ? 'assets/ADD/people-safe-D.png'
                              : 'assets/ADD/people-safe-B.png',
                        ),
                        width: 20.w,
                        height: 20.h,
                      ),
                      SizedBox(
                        width: 10.w,
                      ),
                      AppWidget.normalText(
                          context,
                          'ui_security_p'.tr,
                          13.sp,
                          Theme.of(context).colorScheme.onSecondary,
                          FontWeight.w400),
                    ],
                  ),
                  InkWell(
                    onTap: () {
                      Get.toNamed('SafetyScreen');
                    },
                    child: Image(
                      image: AssetImage('assets/ADD/See_detail.png'),
                      color: Theme.of(context).colorScheme.onSecondary,
                      width: 22.w,
                      height: 22.h,
                    ),
                  )
                ],
              ),
            ),
            LinerGray(context),
            InkWell(
              onTap: () {
                Get.to(() => HtmlElementView(
                    viewType: 'https://www.agilesoftgroup.com/ms.html'));
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Image(
                        image: AssetImage(
                          Get.isDarkMode
                              ? 'assets/ADD/message-security-D.png'
                              : 'assets/ADD/message-security-B.png',
                        ),
                        width: 20.w,
                        height: 20.h,
                      ),
                      SizedBox(
                        width: 10.w,
                      ),
                      AppWidget.normalText(
                          context,
                          'privacy_policy'.tr,
                          13.sp,
                          Theme.of(context).colorScheme.onSecondary,
                          FontWeight.w400),
                    ],
                  ),
                  InkWell(
                    onTap: () {
                      Get.to(() => HtmlElementView(
                          viewType: 'https://www.agilesoftgroup.com/ms.html'));
                    },
                    child: Image(
                      image: AssetImage('assets/ADD/See_detail.png'),
                      color: Theme.of(context).colorScheme.onSecondary,
                      width: 22.w,
                      height: 22.h,
                    ),
                  )
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget userInfoRow(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: 10.h,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            AppWidget.normalText(context, label, 12.sp,
                Theme.of(context).colorScheme.onSecondary, FontWeight.w400),
            AppWidget.normalText(context, value, 12.sp,
                Theme.of(context).colorScheme.tertiary, FontWeight.w400),
          ],
        ),
        Divider(
            color: Theme.of(context)
                .colorScheme
                .onSecondaryContainer
                .withOpacity(1)),
      ],
    );
  }
}
