import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mapp_ms24/views/screens/Widget/Widget.dart';

import '../../../../../Models/language.dart';
import '../../../../../controllers/utils/language_selection_controller.dart';
import '../../../../../controllers/utils/widget.dart';
class ChangelanguageScreen extends StatefulWidget {


  const ChangelanguageScreen({super.key});

  @override
  State<ChangelanguageScreen> createState() => _ChangelanguageScreenState();
}

class _ChangelanguageScreenState extends State<ChangelanguageScreen> {
  final LanguageSelectionController languageController =
  Get.put(LanguageSelectionController());
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Color(0xFF232034), // เพิ่มสีแรกที่นี่
            Color(0xFF232034), // เพิ่มสีแร
          ],
          begin: Alignment.topCenter, // เริ่มต้น Gradient ที่มุมบนซ้าย
          end: Alignment.bottomCenter,
        ),
      ),
      child: Column(
        children: [buildApp(), buildReset()],
      ),
    );
  }

  Widget buildApp() {
    return Padding(
      padding: EdgeInsets.only(top: 40.0.h),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 10.w),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                IconButton(
                  icon: const Icon(
                    null,
                  ),
                  color: Colors.white,
                  onPressed: () {},
                ),
                AppWidget.normalText(
                    context, '', 14.sp, const Color(0xFFF6F6F6), FontWeight.w400),
                IconButton(
                  icon: const Icon(Icons.home_filled),
                  color: Colors.white,
                  onPressed: () {
                    Get.offAllNamed('home');
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget buildReset() {
    return Column(
      children: [
        SizedBox(
          height: 80.h,
          width: 120.w,
          child: Image.asset('assets/logo/MS24_Logo.png'),
        ),
        SizedBox(
          height: 10.h,
        ),
        AppWidget.normalText(context, 'ui_settingchange'.tr, 16.sp,
            const Color(0xFFF6F6F6), FontWeight.w600),
        SizedBox(
          height: 20.h,
        ),
        Container(
          padding: EdgeInsets.symmetric(vertical: 20.h, horizontal: 100.w),
          child: Column(
            children: [
              InkWell(
                child: AppWidget.normalText(context, 'ui_selecteng'.tr, 16.sp,
                    const Color(0xFFF6F6F6), FontWeight.w400),
                onTap: () {
                  languageController.selectLanguage(0); // Change to English
                  languageController.updateLanguage();
                },
              ),
              Liner(context),
              InkWell(
                child: AppWidget.normalText(context, 'ui_selectth'.tr, 16.sp,
                    const Color(0xFFF6F6F6), FontWeight.w400),
                onTap: () {
                  languageController.selectLanguage(1); // Change to Thai
                  languageController.updateLanguage();
                },
              )
            ],
          ),
        )
      ],
    );
  }
  Widget buildChangeLau(){
    return Padding(
      padding:  EdgeInsets.only(top: 20.0.h),
      child: IconButton(
        onPressed: (){
          showDialog(
            context: context,
            builder: (context) {
              return AlertDialog(
                title: const Text('ui_selectlanguages'),
                content: Column(
                  children: [
                    for (var i = 0; i < listOfLanguages.length; i++)
                      RadioListTile(
                        title: Text(listOfLanguages[i].title),
                        value: i,
                        groupValue: languageController.selectedLanguageIndex,
                        onChanged: (value) async {
                          languageController.selectLanguage(value!);
                         await languageController.updateLanguage();
                          Navigator.pop(context); // Close the dialog
                        },
                      ),
                  ],
                ),
              );
            },
          );
        }, icon: const Icon(Icons.settings),
      ),
    );
  }
}
