import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mapp_ms24/controllers/utils/widget.dart';
import 'package:mapp_ms24/models/dashedborder.dart';

class regisEsign extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: Get.width,
        height: Get.height,
        decoration: BoxDecoration(
            gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Theme.of(context).colorScheme.primary,
                  Theme.of(context).colorScheme.onPrimary,
                ])),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(height: 24),
              AppBar(
                leading: IconButton(
                  icon: Icon(Icons.arrow_back, color: Theme.of(context).colorScheme.secondary),
                  onPressed: () => Navigator.of(context).pop(),
                ),
                title:  AppWidget.normalText(
                    context,
                    'ui_register_Esign'.tr,
                    15.sp,
                    Theme.of(context).colorScheme.secondary,
                    FontWeight.w400),
                backgroundColor: Colors.transparent,
                elevation: 0,
              ),
              SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildImageOption(context, 'assets/ADD/Timmi_Face_verify2.png', 'รูปถ่ายใบหน้า'),
                  _buildImageOption(context, 'assets/ADD/Timmi_Face_verify.png', 'รูปถ่ายลายเซ็น'),
                ],
              ),
              SizedBox(height: 24),
              _buildInstructionsSection(context),
              SizedBox(height: 16),
              _buildStepsSection(context),
              Spacer(),
              _buildSubmitButton(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImageOption(BuildContext context,String icon, String label) {
    return Column(
      children: [
        Image.asset(icon, width: 30, height: 30),
        SizedBox(height: 8),
        GestureDetector(
          onTap: () {
            // เพิ่มฟังก์ชันสำหรับอัพโหลดรูปภาพ
          },
          child: Container(
            width: 100,
            height: 130,
            child: CustomPaint(
              painter: DashedBorderPainter(
                color: Theme.of(context).colorScheme.secondaryContainer, // สีของเส้นปะ
                strokeWidth: 2.0, // ความหนาของเส้นปะ
                dashWidth: 5.0, // ความยาวของเส้นปะ
                dashSpace: 3.0, // ช่องว่างระหว่างเส้นปะ
                radius: 10.0, // ความโค้งของขอบ
              ),
              child: Center(
                child: Icon(
                  Icons.add,
                  size: 40,
                  color: Theme.of(context).colorScheme.secondary.withOpacity(0.4),
                ),
              ),
            ),
          )

        ),
        SizedBox(height: 8),
        Text(label, style: TextStyle(color: Theme.of(context).colorScheme.secondary)),
      ],
    );
  }

  Widget _buildInstructionsSection(context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'สิ่งที่ต้องเตรียมก่อนลงทะเบียน',
          style: TextStyle(color: Theme.of(context).colorScheme.secondary, fontSize: 16, fontWeight: FontWeight.bold),
        ),
        SizedBox(height: 8),
        Text(
          '• กรุณาเปิดการตั้งค่าการเข้าถึงกล้อง\n• เตรียมลายเซ็น ที่เซ็นด้วยปากกาสีน้ำเงินบนกระดาษพื้นสีขาว',
          style: TextStyle(color: Theme.of(context).colorScheme.secondary),
        ),
      ],
    );
  }

  Widget _buildStepsSection(context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'ขั้นตอนการลงทะเบียน',
          style: TextStyle(color: Theme.of(context).colorScheme.secondary, fontSize: 16, fontWeight: FontWeight.bold),
        ),
        SizedBox(height: 8),
        Text(
          '• ถ่ายรูปใบหน้าเพื่อยืนยันตัวตน\n• ถ่ายรูปลายเซ็น ที่เซ็นด้วยปากกาสีน้ำเงินบนกระดาษพื้นสีขาว\n• กดบันทึกข้อมูล',
          style: TextStyle(color:Theme.of(context).colorScheme.secondary),
        ),
      ],
    );
  }

  Widget _buildSubmitButton(BuildContext context) {
    return ElevatedButton(
      onPressed: () {
        // ฟังก์ชันสำหรับบันทึกข้อมูล
      },
      style: ElevatedButton.styleFrom(
        foregroundColor: Theme.of(context).colorScheme.secondary, backgroundColor: Theme.of(context).colorScheme.secondary.withOpacity(0.4),
        minimumSize: Size(double.infinity, 50),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: Text('บันทึกข้อมูล'),
    );
  }
}
