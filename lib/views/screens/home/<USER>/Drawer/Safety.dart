import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mapp_ms24/views/screens/Widget/Widget.dart';
import '../../../../../controllers/utils/widget.dart';
class SafetyScreen extends StatefulWidget {
  const SafetyScreen({super.key});

  @override
  State<SafetyScreen> createState() => _SafetyScreenState();
}

class _SafetyScreenState extends State<SafetyScreen> {
  @override
  Widget build(BuildContext context) {
    return Container(

      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.onPrimary// เพิ่มสีที่สองที่นี่
          ],
          begin: Alignment.topCenter, // เริ่มต้น Gradient ที่มุมบนซ้าย
          end: Alignment.bottomCenter,
        ),
      ),
      child: Column(

        children: [buildApp(),buildOption()
        ],
      ),
    );
  }
  Widget buildApp() {
    return  Padding(
      padding: EdgeInsets.only(top: 40.0.h),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 10.w),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                IconButton(
                  icon: const Icon(
                    Icons.arrow_back_ios_new,
                  ),
                  color: Theme.of(context).colorScheme.onSecondary,
                  iconSize: 15,
                  onPressed: () {
                    Get.back();
                  },
                ),
                AppWidget.normalText(context, 'ui_security_p'.tr, 14.sp,
                    Theme.of(context).colorScheme.onSecondary, FontWeight.w400),
                IconButton(
                  icon: const Icon(null),
                  color: Theme.of(context).colorScheme.onSecondary,
                  onPressed: () {

                  },
                ),
              ],
            ),
          ),

        ],
      ),
    );

  }
  Widget buildOption(){
    return Container(
      padding: EdgeInsets.only(left: 30.w, right: 5.w, top: 10.h),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Image(
                    image: AssetImage(
                      Get.isDarkMode
                          ? 'assets/ADD/ubic_D.png' // รูปสำหรับธีมมืด
                          : 'assets/ADD/ubic_B.png', // รูปสำหรับธีมสว่าง
                    ),
                    width: 30.w,
                    height: 30.h,
                  ),
                   SizedBox(
                    width: 10.w,
                  ),
                  InkWell(
                    onTap: () {
                      Get.toNamed('TwoFAscreem');
                    },
                    child: AppWidget.normalText(context, 'ui_register_twoFA'.tr, 12.sp,
                        Theme.of(context).colorScheme.onSecondary, FontWeight.w400),
                  ),

                ],
              ),
              IconButton(
                  onPressed: () {
                    Get.toNamed('TwoFAscreem');
                  },
                  icon: Icon(
                    Icons.arrow_forward_ios,
                    color: Theme.of(context).colorScheme.onSecondary,
                    size: 15,
                  ))
            ],
          ),
          LinerGray2(context),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Image(
                    image: AssetImage(
                      Get.isDarkMode
                          ? 'assets/ADD/Lock_D.png' // รูปสำหรับธีมมืด
                          : 'assets/ADD/Lock_B.png', // รูปสำหรับธีมสว่าง
                    ),
                    width: 30.w,
                    height: 30.h,
                  ),
                  SizedBox(
                    width: 10.w,
                  ),
                  InkWell(
                    onTap: () {
                      Get.toNamed('PinlockScreen');
                    },
                    child: AppWidget.normalText(context, 'ui_passcode'.tr, 12.sp,
                      Theme.of(context).colorScheme.onSecondary, FontWeight.w400)),
                ],
              ),
              IconButton(
                  onPressed: () {
                    Get.toNamed('ResetPassword');
                  },
                  icon: Icon(
                    Icons.arrow_forward_ios,
                    color: Theme.of(context).colorScheme.onSecondary,
                    size: 15,
                  ))
            ],
          ),
          LinerGray2(context),
        ],
      ),
    );
  }
}
