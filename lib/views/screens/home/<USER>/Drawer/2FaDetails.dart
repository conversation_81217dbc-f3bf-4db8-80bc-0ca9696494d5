import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/yubikey.dart';
import '../../../../../controllers/utils/widget.dart';
class TwoFAscreem extends StatefulWidget {
  const TwoFAscreem({super.key});

  @override
  State<TwoFAscreem> createState() => _TwoFAscreemState();
}

class _TwoFAscreemState extends State<TwoFAscreem> {
  @override
  Widget build(BuildContext context) {
    return Container(

      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary, // เพิ่มสีแรกที่นี่
            Theme.of(context).colorScheme.onPrimary, // เพิ่มสีที่สองที่นี่
          ],
          begin: Alignment.topCenter, // เริ่มต้น Gradient ที่มุมบนซ้าย
          end: Alignment.bottomCenter,
        ),
      ),
      child: Column(
mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(children: [
            buildApp(),
            AppWidget.normalText(context, 'ui_register_twoFA_header'.tr, 15.sp,
                Theme.of(context).colorScheme.onSecondary, FontWeight.w600),
            buildDetails()
          ],),
          buildButton()
        ],
      ),
    );
  }
  Widget buildApp() {
    return  Padding(
      padding: EdgeInsets.only(top: 40.0.h),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 10.w),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.arrow_back_ios_new,
                    size: 20.sp,
                  ),
                  color: Theme.of(context).colorScheme.onSecondary,
                  onPressed: () {
                    setState(() {
                      Get.back();

                    });

                  },
                ),Row(children: [
                  Icon(Icons.person,color: Theme.of(context).colorScheme.onSecondary,size: 20.sp,),
                  SizedBox(width: 8.w,),
                  AppWidget.normalText(context, 'ui_register_twoFA'.tr, 14.sp,
                      Theme.of(context).colorScheme.onSecondary, FontWeight.w400),
                ],),

                IconButton(
                  icon: const Icon(null),
                  color: Theme.of(context).colorScheme.onSecondary,
                  onPressed: () {

                  },
                ),
              ],
            ),
          ),

        ],
      ),
    );

  }
  Widget buildDetails(){
    return Padding(
      padding: const EdgeInsets.only(top: 16.0),
      child: Container(
        padding: EdgeInsets.only(left: 30.w, right: 5.w, top: 10.h),
        child: Column(
        children: [
          AppWidget.normalText(context, '• ในปัจจุบันมีการโจมตีทาง Cyber เกิดขึ้นตลอดเวลา PKG จึงต้องมีการป้องกันที่ดีมากยิ่งขึ้นในระบบต่างๆ ที่มีิอยู่เพราะใน PKG มีการเก็บรวบรวมข้อมูลสำคัญ', 13.sp,
              Theme.of(context).colorScheme.onSecondary, FontWeight.w400),
          SizedBox(height: 20.h,),
          AppWidget.normalText(context, '• รวมถึงข้อมูลส่วนตัวของลูกค้า ซึ่งสมาชิกของ PKG สามารถเข้าถึงได้ หากบัญชีหรืออุปกรณ์ใดๆที่สมาชิกใช้งานถูกโจมตีหรือถูกแฮคจะทำให้เกิดผลเสียหายอย่างมากทั้งต่อ PKG และสมาชิกเองหนึ่งในระบบป้องกัน คือ', 13.sp,
              Theme.of(context).colorScheme.onSecondary, FontWeight.w400),
          SizedBox(height: 20.h,),
          AppWidget.normalText(context, '• การใช้คีย์ความปลอดภัยที่เรียกว่า YubiKey ทำหน้าที่ยืนยัน 2 ขั้นตอน (2FA) ในการเข้าถึงบริการหรือระบบต่างๆ ที่ทีม Cyber Security กำหนด เพื่อเป็นการยกระดับความปลอดภัยของ PKG', 13.sp,
              Theme.of(context).colorScheme.onSecondary, FontWeight.w400),
          SizedBox(height: 20.h,),
          AppWidget.normalText(context, '• ขอความร่วมมือสมาชิกลงทะเบียน 2FA สำหรับการใช้งาน MS24', 14.sp,
              Theme.of(context).colorScheme.onSecondary, FontWeight.w700),
        ],
      ),),
    );
  }
  Widget buildButton(){
    return Padding(
      padding:  EdgeInsets.symmetric(horizontal: 20.0.w,vertical: 20.h),
      child: InkWell(
        onTap: (){
          Yubikey.registerYubi(context);
        },
        child: Container(
          height: 50.0.h,
          width: Get.width,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.secondaryContainer.withOpacity(0.6),
            borderRadius:
            BorderRadius.circular(10.0), // Add border radius
            border: Border.all(
              color: Theme.of(context).colorScheme.secondaryContainer.withOpacity(1),
              width: 1.0,
            ),
            // Add border radius
          ),
          child: Padding(
              padding:
              const EdgeInsets.all(10.0), // Add padding of 10
              child: Center(child: AppWidget.normalText(context, 'btn_register_twoFA'.tr, 14.sp,
                  Theme.of(context).colorScheme.onSecondary, FontWeight.w400),)
          ),
        ),
      ),
    );
  }
}
