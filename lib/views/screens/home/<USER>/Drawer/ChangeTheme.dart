import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../controllers/utils/theme_models.dart';
import '../../../../../controllers/utils/widget.dart';
import '../../../Widget/Widget.dart';

class ChangethemeScreen extends StatefulWidget {
  const ChangethemeScreen({super.key});

  @override
  State<ChangethemeScreen> createState() => _changethemeScreen();
}

class _changethemeScreen extends State<ChangethemeScreen> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  ThemeController themeController = Get.find<ThemeController>();

  @override
  Widget build(BuildContext context) {
    return Container(
      height: Get.height,
      width: Get.width,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.onPrimary,
          ],
          begin: Alignment.topCenter, // เริ่มต้น Gradient ที่มุมบนซ้าย
          end: Alignment.bottomCenter,
        ),
      ),
      child: Padding(
        padding: EdgeInsets.only(top: 20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              children: [
                buildAppBar(
                  context,
                  'ui_theme'.tr,
                ),
                SizedBox(
                  height: 20.h,
                ),
                buildTheme(),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget buildTheme() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.0.w, vertical: 20.0.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          GestureDetector(
            onTap: () {
              themeController.selectedTheme.value = 0;
              themeController.selectTheme();
            },
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Container(
                  width: 100.w,
                  height: 150.h,
                  decoration: BoxDecoration(
                    border: Border.all(
                        color: themeController.selectedTheme.value == 0
                            ? const Color.fromRGBO(255, 136, 193, 1)
                            : Colors.transparent,
                        width: 3.w),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Image.asset(
                      'assets/Theme/Device_default_theme.png',
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                SizedBox(
                  height: 12.h,
                ),
                Container(
                  width: 18.w,
                  height: 18.h,
                  decoration: BoxDecoration(
                    border: Border.all(
                        color: themeController.selectedTheme.value == 0
                            ? const Color.fromRGBO(255, 136, 193, 1)
                            : Colors.transparent,
                        width: 5.w),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Image.asset(
                    'assets/Theme/Theme_button.png',
                    width: 18.w,
                    height: 18.h,
                    fit: BoxFit.contain,
                  ),
                ),
                SizedBox(
                  height: 12.h,
                ),
                SizedBox(
                  width: 110.w,
                  height: 32.h, // height to fit two lines of text comfortably
                  child: Text(
                    "ui_theme_0".tr,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    softWrap: true,
                    style: TextStyle(
                      fontFamily: 'Sukhumvit Set',
                      fontSize: 12.sp,
                      color: Theme.of(context).colorScheme.onSecondary,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
              ],
            ),
          ),
          GestureDetector(
            onTap: () {
              themeController.selectedTheme.value = 1;
              themeController.selectTheme();
            },
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Container(
                  width: 100.w,
                  height: 150.h,
                  decoration: BoxDecoration(
                    border: Border.all(
                        color: themeController.selectedTheme.value == 1
                            ? const Color.fromRGBO(255, 136, 193, 1)
                            : Colors.transparent,
                        width: 3.w),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Image.asset(
                      'assets/Theme/Dark_theme.png',
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                SizedBox(
                  height: 12.h,
                ),
                Container(
                  width: 18.w,
                  height: 18.h,
                  decoration: BoxDecoration(
                    border: Border.all(
                        color: themeController.selectedTheme.value == 1
                            ? const Color.fromRGBO(255, 136, 193, 1)
                            : Colors.transparent,
                        width: 5.w),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Image.asset(
                    'assets/Theme/Theme_button.png',
                    width: 18.w,
                    height: 18.h,
                    fit: BoxFit.contain,
                  ),
                ),
                SizedBox(
                  height: 12.h,
                ),
                SizedBox(
                  width: 110.w,
                  height: 32.h, // height to fit two lines of text comfortably
                  child: Text(
                    "ui_theme_1".tr,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    softWrap: true,
                    style: TextStyle(
                      fontFamily: 'Sukhumvit Set',
                      fontSize: 12.sp,
                      color: Theme.of(context).colorScheme.onSecondary,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
              ],
            ),
          ),
          GestureDetector(
            onTap: () {
              themeController.selectedTheme.value = 2;
              themeController.selectTheme();
            },
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Container(
                  width: 100.w,
                  height: 150.h,
                  decoration: BoxDecoration(
                    border: Border.all(
                        color: themeController.selectedTheme.value == 2
                            ? const Color.fromRGBO(255, 136, 193, 1)
                            : Colors.transparent,
                        width: 3.w),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Image.asset(
                      'assets/Theme/Light_theme.png',
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                SizedBox(
                  height: 12.h,
                ),
                Container(
                  width: 18.w,
                  height: 18.h,
                  decoration: BoxDecoration(
                    border: Border.all(
                        color: themeController.selectedTheme.value == 2
                            ? const Color.fromRGBO(255, 136, 193, 1)
                            : Colors.transparent,
                        width: 5.w),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Image.asset(
                    'assets/Theme/Theme_button.png',
                    width: 18.w,
                    height: 18.h,
                    fit: BoxFit.contain,
                  ),
                ),
                SizedBox(
                  height: 12.h,
                ),
                SizedBox(
                  width: 110.w,
                  height: 32.h, // height to fit two lines of text comfortably
                  child: Text(
                    "ui_theme_2".tr,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    softWrap: true,
                    style: TextStyle(
                      fontFamily: 'Sukhumvit Set',
                      fontSize: 12.sp,
                      color: Theme.of(context).colorScheme.onSecondary,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
