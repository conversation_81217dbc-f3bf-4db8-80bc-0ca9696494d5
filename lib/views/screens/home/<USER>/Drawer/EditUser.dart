import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../../../controllers/internal/Profile/userController.dart';
import '../../../../../controllers/utils/widget.dart';

class EditUserScreen extends StatefulWidget {
  const EditUserScreen({super.key});

  @override
  State<EditUserScreen> createState() => _EditUserScreenState();
}

class _EditUserScreenState extends State<EditUserScreen> {
  UserController userCtr = Get.find<UserController>();
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Color(0xFF0D0C14), // เพิ่มสีแรกที่นี่
            Color(0xFF0D0C14), // เพิ่มสีแร
          ],
          begin: Alignment.topCenter, // เริ่มต้น Gradient ที่มุมบนซ้าย
          end: Alignment.bottomCenter,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [buildShowProfile(), buildButton()],
      ),
    );
  }

  Widget buildShowProfile() {
    return Container(
      decoration: const BoxDecoration(),
      child: Padding(
        padding: EdgeInsets.only(top: 40.0.h),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              padding: EdgeInsets.symmetric(horizontal: 10.w),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  IconButton(
                    icon: const Icon(
                      Icons.arrow_back_ios_new,
                    ),
                    color: Colors.white,
                    onPressed: () {
                      Get.back();
                    },
                  ),
                  AppWidget.normalText(context, 'ui_profile_e'.tr, 14.sp,
                      const Color(0xFFF6F6F6), FontWeight.w400),
                  IconButton(
                    icon: const Icon(null),
                    color: Colors.white,
                    onPressed: () {
                      Get.toNamed('EditUserScreen');
                    },
                  ),
                ],
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(vertical: 20.0.h),
              child: Container(
                height: 150.h,
                width: 150.w,
                decoration: BoxDecoration(
                    border: Border.all(
                      color: const Color(
                          0xFF5F569B), // Set your desired border color here
                      width: 6.0.w, // Set the border width
                    ),
                    color: Colors.red,
                    shape: BoxShape.circle),
              ),
            ),
            SizedBox(
              height: 10.h,
            ),
            buildEdit()
          ],
        ),
      ),
    );
  }

  Widget buildEdit() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      width: Get.width,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AppWidget.normalText(context, 'ui_fullname'.tr, 12.sp,
                  const Color(0xFFF6F6F6), FontWeight.w400),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Container(
                      height: 50.0.h,
                      decoration: BoxDecoration(
                        color: const Color(0xFF232034),
                        borderRadius:
                            BorderRadius.circular(6.0), // Add border radius
                        // Add border radius
                      ),
                      child: const Padding(
                        padding: EdgeInsets.only(bottom: 10.0),
                        child: TextField(
                          textAlignVertical: TextAlignVertical.center,
                          textAlign: TextAlign.center,
                          decoration: InputDecoration(
                            hintText: 'ui_name',
                            border: InputBorder.none,
                          ),
                        ),
                      ),
                    ),
                  ),

                  SizedBox(
                      width:
                          16.w), // Add some space between the text input fields
                  Expanded(
                    child: Container(
                      height: 50.0.h,
                      decoration: BoxDecoration(
                        color: const Color(0xFF232034),
                        borderRadius:
                            BorderRadius.circular(6.0), // Add border radius
                      ),
                      child: const Padding(
                        padding: EdgeInsets.only(bottom: 10.0),
                        child: TextField(
                          textAlignVertical: TextAlignVertical.center,
                          textAlign: TextAlign.center,
                          decoration: InputDecoration(
                            hintText: 'ui_lastname',
                            border: InputBorder.none,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              )
            ],
          ),
          SizedBox(
            height: 10.h,
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  AppWidget.normalText(context, 'ui_birthday'.tr, 12.sp,
                      const Color(0xFFF6F6F6), FontWeight.w400),
                  AppWidget.normalText(context, 'ui_yeartpye'.tr, 12.sp,
                      const Color(0xFFFEE095), FontWeight.w400),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Container(
                      height: 50.0.h,
                      decoration: BoxDecoration(
                        color: const Color(0xFF232034),
                        borderRadius:
                            BorderRadius.circular(6.0), // Add border radius
                        // Add border radius
                      ),
                      child: const Padding(
                        padding: EdgeInsets.only(bottom: 10.0),
                        child: TextField(
                          textAlignVertical: TextAlignVertical.center,
                          textAlign: TextAlign.center,
                          decoration: InputDecoration(
                            hintText: 'dd/mm/yyyy',
                            border: InputBorder.none,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              )
            ],
          ),
          SizedBox(
            height: 10.h,
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AppWidget.normalText(context, 'ui_IDcard'.tr, 12.sp,
                  const Color(0xFFF6F6F6), FontWeight.w400),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Container(
                      height: 50.0.h,
                      decoration: BoxDecoration(
                        color: const Color(0xFF232034),
                        borderRadius:
                            BorderRadius.circular(6.0), // Add border radius
                        // Add border radius
                      ),
                      child: const Padding(
                        padding: EdgeInsets.only(bottom: 10.0),
                        child: TextField(
                          textAlignVertical: TextAlignVertical.center,
                          textAlign: TextAlign.center,
                          decoration: InputDecoration(
                            hintText: '123',
                            border: InputBorder.none,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              )
            ],
          ),
          SizedBox(
            height: 10.h,
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AppWidget.normalText(context, 'ui_number'.tr, 12.sp,
                  const Color(0xFFF6F6F6), FontWeight.w400),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    flex: 1,
                    child: Container(
                      height: 50.0.h,
                      decoration: BoxDecoration(
                        color: const Color(0xFF232034),
                        borderRadius:
                            BorderRadius.circular(6.0), // Add border radius
                        // Add border radius
                      ),
                      child: Center(
                        child: DropdownButton<String>(
                          items: userCtr.phoneCodes
                              .map<DropdownMenuItem<String>>(
                                  (Map<String, String> country) {
                            return DropdownMenuItem<String>(
                              value: country['code'],
                              child: AppWidget.normalText(
                                  context,
                                  '${country["name"]}'.tr,
                                  14.sp,
                                  const Color(0xFFF6F6F6).withOpacity(0.6),
                                  FontWeight.w400),
                            );
                          }).toList(),
                          dropdownColor: Color(0xFF232034),
                          alignment: Alignment.center,
                          value: userCtr.selectedCode,
                          onChanged: (String? newValue) {
                            setState(() {
                              userCtr.selectedCode = newValue!;
                            });
                          },
                          underline: Container(), // Remove the underline
                          selectedItemBuilder: (BuildContext context) {
                            return userCtr.phoneCodes
                                .map<Widget>((Map<String, String> country) {
                              return Center(
                                child: AppWidget.normalText(
                                    context,
                                    '${country["code"]}'.tr,
                                    14.sp,
                                    const Color(0xFFF6F6F6).withOpacity(0.6),
                                    FontWeight.w400),
                              );
                            }).toList();
                          },
                        ),
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 10.w,
                  ),
                  Expanded(
                    flex: 3,
                    child: Center(
                      child: Container(
                        height: 50.0.h,
                        decoration: BoxDecoration(
                          color: const Color(0xFF232034),
                          borderRadius:
                              BorderRadius.circular(6.0), // Add border radius
                        ),
                        child: Padding(
                          padding: const EdgeInsets.only(bottom: 10.0),
                          child: TextField(
                            textAlignVertical: TextAlignVertical.center,
                            textAlign: TextAlign.center,
                            decoration: InputDecoration(
                              hintText: '1232131',
                              border: InputBorder.none,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(
                height: 10.h,
              ),
              Row(
                children: [
                  AppWidget.normalText(context, 'ui_numberlikewallet'.tr, 12.sp,
                      const Color(0xFFF6F6F6), FontWeight.w400),
                  AppWidget.normalText(context, 'ui_LikeWallet'.tr, 12.sp,
                      const Color(0xFFFEE095), FontWeight.w400),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    flex: 1,
                    child: Container(
                      height: 50.0.h,
                      decoration: BoxDecoration(
                        color: const Color(0xFF232034),
                        borderRadius:
                            BorderRadius.circular(6.0), // Add border radius
                        // Add border radius
                      ),
                      child: Center(
                        child: DropdownButton<String>(
                          items: userCtr.phoneCodes
                              .map<DropdownMenuItem<String>>(
                                  (Map<String, String> country) {
                            return DropdownMenuItem<String>(
                              value: country['code'],
                              child: AppWidget.normalText(
                                  context,
                                  '${country["name"]}'.tr,
                                  14.sp,
                                  const Color(0xFFF6F6F6).withOpacity(0.6),
                                  FontWeight.w400),
                            );
                          }).toList(),
                          dropdownColor: Color(0xFF232034),
                          alignment: Alignment.center,
                          value: userCtr.selectedCodeWithlike,
                          onChanged: (String? newValue) {
                            setState(() {
                              userCtr.selectedCodeWithlike = newValue!;
                            });
                          },
                          underline: Container(), // Remove the underline
                          selectedItemBuilder: (BuildContext context) {
                            return userCtr.phoneCodes
                                .map<Widget>((Map<String, String> country) {
                              return Center(
                                child: AppWidget.normalText(
                                    context,
                                    '${country["code"]}'.tr,
                                    14.sp,
                                    const Color(0xFFF6F6F6).withOpacity(0.6),
                                    FontWeight.w400),
                              );
                            }).toList();
                          },
                        ),
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 10.w,
                  ),
                  Expanded(
                    flex: 3,
                    child: Container(
                      height: 50.0.h,
                      decoration: BoxDecoration(
                        color: const Color(0xFF232034),
                        borderRadius:
                            BorderRadius.circular(6.0), // Add border radius
                        // Add border radius
                      ),
                      child: const Padding(
                        padding: EdgeInsets.only(bottom: 10.0),
                        child: TextField(
                          textAlignVertical: TextAlignVertical.center,
                          textAlign: TextAlign.center,
                          decoration: InputDecoration(
                            hintText: '9393828',
                            border: InputBorder.none,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              )
            ],
          ),
        ],
      ),
    );
  }

  Widget buildButton() {
    return InkWell(
      onTap: () {},
      child: Container(
        height: 50.0.h,
        width: 200.w,
        decoration: BoxDecoration(
          color: const Color(0xFF232034),
          borderRadius: BorderRadius.circular(16.0), // Add border radius
          // Add border radius
        ),
        child: Padding(
            padding: const EdgeInsets.all(10.0), // Add padding of 10
            child: Center(
              child: AppWidget.normalText(context, 'btn_save'.tr, 16.sp,
                  const Color(0xFFF6F6F6), FontWeight.w400),
            )),
      ),
    );
  }
}
