import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mapp_ms24/controllers/utils/widget.dart';
import 'package:mapp_ms24/views/screens/Widget/Widget.dart';

import '../../../../../controllers/internal/notification_controller.dart';

class SuggestionsScreen extends StatefulWidget {
  const SuggestionsScreen({super.key});

  @override
  State<SuggestionsScreen> createState() => _SuggestionsScreenState();
}

class _SuggestionsScreenState extends State<SuggestionsScreen> {
  NotifyController notfiCtr = Get.find<NotifyController>();

  @override
  Widget build(BuildContext context) {
    return Container(
      height: Get.height,
      width: Get.width,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.onPrimary,
          ],
          begin: Alignment.topCenter, // เริ่มต้น Gradient ที่มุมบนซ้าย
          end: Alignment.bottomCenter,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.only(top: 20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              children: [
                buildAppBar(context, 'ui_feedback'.tr),
                buildSuggest()
              ],
            ),
            Padding(
              padding: EdgeInsets.symmetric(vertical: 20.0.h),
              child: buttonSave(context, 'btn_saveFeedback'.tr, () async {
                if (notfiCtr.subjectController.text.isEmpty ||
                    notfiCtr.detailController.text.isEmpty) {
                  Get.snackbar(
                      "กรุณากรอกข้อมูล", "หัวข้อและรายละเอียดต้องไม่ว่าง");
                  return;
                }

                Get.dialog(Center(child: CircularProgressIndicator()),
                    barrierDismissible: false);

                await notfiCtr.sendFeedbackToN8N();

                // ✅ ล้างค่าหลังส่ง
                notfiCtr.subjectController.clear();
                notfiCtr.detailController.clear();
                notfiCtr.uploadedImageUrl = '';
                notfiCtr.selectedImage = null;

                // ✅ ไปหน้า Home
                Get.offAllNamed('home');
              }),
            )
          ],
        ),
      ),
    );
  }

  Widget buildSuggest() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.0.w, vertical: 20.0.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          AppWidget.normalText(context, 'ui_subject'.tr, 12.sp,
              Theme.of(context).colorScheme.secondary, FontWeight.w400),
          SizedBox(
            height: 20.h,
          ),
          Container(
            height: 60.h,
            width: Get.width,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.secondary.withOpacity(0.1),
              borderRadius: const BorderRadius.all(Radius.circular(10.0)),
            ),
            child: TextField(
              cursorColor: Theme.of(context).colorScheme.secondary,
              controller: notfiCtr.subjectController,
              decoration: InputDecoration(
                hintText: 'ui_input'.tr,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.all(16.0),
              ),
            ),
          ),
          SizedBox(
            height: 20.h,
          ),
          AppWidget.normalText(context, 'ui_detailFeedback'.tr, 12.sp,
              Theme.of(context).colorScheme.secondary, FontWeight.w400),
          SizedBox(
            height: 20.h,
          ),
          Container(
            height: 200.h,
            width: Get.width,
            decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.secondary.withOpacity(0.1),
                borderRadius: const BorderRadius.all(Radius.circular(10.0))),
            child: TextField(
              cursorColor: Theme.of(context).colorScheme.secondary,
              controller: notfiCtr.detailController,
              decoration: InputDecoration(
                hintText: 'ui_input'.tr,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.all(16.0),
              ),
            ),
          ),
          SizedBox(
            height: 20.h,
          ),
          SizedBox(
            height: 20.h,
          ),
          Container(
            height: 60.h,
            width: Get.width,
            decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.secondary.withOpacity(0.1),
                borderRadius: const BorderRadius.all(Radius.circular(10.0))),
            child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 10.0.w),
                child: InkWell(
                  onTap: () async {
                    try {
                      final pickedList = await ImagePicker()
                          .pickMultiImage(); // เลือกรูปหลายใบ

                      if (pickedList == null || pickedList.isEmpty) {
                        return;
                      }

                      Get.dialog(
                        const Center(child: CircularProgressIndicator()),
                        barrierDismissible: false,
                      );

                      notfiCtr.uploadedImageUrls.clear(); // เคลียร์ของเก่า

                      for (final picked in pickedList) {
                        try {
                          final bytes = await picked
                              .readAsBytes(); // รองรับทั้ง Web/Mobile
                          final base64 = base64Encode(bytes);

                          final url = await notfiCtr.uploadtoS3(base64);

                          if (url.isNotEmpty) {
                            notfiCtr.uploadedImageUrls.add(url);
                          } else {
                            print("❌ Upload failed for ${picked.name}");
                          }
                        } catch (e) {
                          print("❌ Error on one image: $e");
                        }
                      }

                      Get.back(); // ปิด loading
                      setState(() {}); // รีเฟรช UI
                    } catch (e, stack) {
                      Get.back();
                      Get.snackbar("อัปโหลดล้มเหลว", e.toString());
                    }
                  },
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          notfiCtr.uploadedImageUrls.isNotEmpty
                              ? 'อัปโหลดแล้ว ${notfiCtr.uploadedImageUrls.length} รูป'
                              : 'กดเลือกรูปเพื่ออัปโหลด',
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                          softWrap: false,
                          style: TextStyle(
                            fontFamily: 'Sukhumvit Set',
                            fontSize: 12.sp,
                            color: Theme.of(context).colorScheme.secondary,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      const Icon(Icons.upload),
                    ],
                  ),
                )),
          ),
        ],
      ),
    );
  }
}
