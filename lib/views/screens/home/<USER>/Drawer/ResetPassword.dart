import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:mapp_ms24/views/screens/Widget/Widget.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
// import 'package:pin_code_fields/pin_code_fields.dart';

import '../../../../../controllers/utils/widget.dart';
import '../../../../../models/LocalStorageModel.dart';

class ResetPassword extends StatefulWidget {
  const ResetPassword({super.key});

  @override
  State<ResetPassword> createState() => _ResetPasswordState();
}

class _ResetPasswordState extends State<ResetPassword> {
  late TextEditingController? _textController;
  bool check = false;
  // TextEditingController _pinCodeController;
  final box = GetStorage();
  String? savedPin;
  String? newPin;
  @override
  void initState() {
    super.initState();
    _textController = TextEditingController();
    _textController?.clear();
    _textController?.text.isEmpty;
    savedPin = box.read('pinCode');
    if (savedPin != null) {
      _textController?.text = savedPin!;
    }
  }

  void _savePin(enteredPin) {
    box.write(LocalStorage.pinCode, enteredPin);
    newPin = box.read('pinCode');
  }

  @override
  void dispose() {
    _textController?.dispose();
    _textController = null;

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Color(0xFF1F1C2F), // เพิ่มสีแรกที่นี่
            Color(0xFF0D0C14), // เพิ่มสีที่สองที่นี่
          ],
          begin: Alignment.topCenter, // เริ่มต้น Gradient ที่มุมบนซ้าย
          end: Alignment.bottomCenter,
        ),
      ),
      child: Column(
        children: [buildApp(), buildReset()],
      ),
    );
  }

  Widget buildApp() {
    return Padding(
      padding: EdgeInsets.only(top: 40.0.h),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 10.w),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                IconButton(
                  icon: const Icon(
                    null,
                  ),
                  color: Colors.white,
                  onPressed: () {},
                ),
                AppWidget.normalText(context, '', 14.sp,
                    const Color(0xFFF6F6F6), FontWeight.w400),
                IconButton(
                  icon: const Icon(Icons.home_filled),
                  color: Colors.white,
                  onPressed: () {
                    Get.offAllNamed('UserDataScreen');
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget buildReset() {
    return Column(
      children: [
        SizedBox(
          height: 80.h,
          width: 120.w,
          child: Image.asset('assets/logo/MS24_Logo.png'),
        ),
        SizedBox(
          height: 10.h,
        ),
        AppWidget.normalText(context, 'ui_setting'.tr, 16.sp,
            const Color(0xFFF6F6F6), FontWeight.w600),
        SizedBox(
          height: 20.h,
        ),
        InkWell(
          onTap: () {
            setState(() {
              check = !check;
              if (!check) {
                _textController?.text = '';
              }
            });
          },
          child: Container(
            width: 300.w,
            padding: EdgeInsets.symmetric(vertical: 20.h),
            child: Column(
              children: [
                AppWidget.normalText(context, 'ui_resetpasscode'.tr, 12.sp,
                    const Color(0xFFF6F6F6), FontWeight.w400),
                // check == false
                //     ? Padding(
                //   padding: const EdgeInsets.only(top: 10.0, bottom: 10),
                //   child: Container(
                //     height: 0.5,
                //     decoration: BoxDecoration(
                //       color: Colors.orange,
                //       border: Border.all(
                //         width: 0.5,
                //         color: const Color(0xFFFCF6E4).withOpacity(0.2),
                //       ),
                //     ),
                //   ),
                // )
                //     :
                PinCodeTextField(
                  cursorColor: Colors.black,
                  textStyle: const TextStyle(
                    decorationColor: Colors.white,
                    color: Color(0xFFF6F6F6),
                    fontWeight: FontWeight.bold,
                  ),
                  appContext: context,
                  pastedTextStyle: const TextStyle(
                    fontFamily: 'SukhumvitSet-Text',
                    decorationColor: Colors.white,
                    color: Color(0xFFF6F6F6),
                    fontWeight: FontWeight.bold,
                  ),
                  length: 6,
                  obscureText: true,
                  obscuringCharacter: '*',
                  blinkWhenObscuring: true,
                  animationType: AnimationType.fade,
                  validator: (v) {
                    if (v!.length < 6) {
                      return "Enter 6 digits";
                    } else {
                      return null;
                    }
                  },
                  pinTheme: PinTheme(
                    inactiveColor: Colors.orange,
                    shape: PinCodeFieldShape.underline,
                    activeColor: Colors.orange,
                    fieldHeight: 50.0,
                    fieldWidth: 40.0,
                    selectedColor: Colors.green,
                    selectedFillColor: Colors.purpleAccent,
                  ),
                  animationDuration: const Duration(milliseconds: 300),
                  controller: _textController,
                  keyboardType: TextInputType.number,
                  boxShadows: const [
                    BoxShadow(
                      offset: Offset(0, 1),
                      color: Colors.black12,
                      blurRadius: 10,
                    )
                  ],
                  onCompleted: (v) {
                    _savePin(_textController?.text);
                    _textController?.clear();
                    _textController?.text.isEmpty;
                    _showCompletionDialog();
                    setState(() {});
                  },
                  onChanged: (value) {
                    setState(() {});
                  },
                  beforeTextPaste: (text) {
                    return true;
                  },
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _showCompletionDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          insetPadding: EdgeInsets.zero,
          contentPadding: EdgeInsets.zero,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.0),
          ),
          content: Container(
            height: 180,
            // width: Get.width * 0.40,
            decoration: BoxDecoration(
              color: Colors.amber[200],
              borderRadius: BorderRadius.circular(16.0),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                AppWidget.normalText(
                  context,
                  'ui_alertpass'.tr,
                  14.sp,
                  Colors.black,
                  FontWeight.w800,
                ),
                SizedBox(height: 8),
                Align(
                  alignment: Alignment.center,
                  child: AppWidget.normalText(
                    context,
                    'ui_alertdata'.tr,
                    12.sp,
                    Colors.black,
                    FontWeight.w600,
                  ),
                ),
                SizedBox(height: 16),
                InkWell(
                  onTap: () {
                    Navigator.of(context).pop();
                    Get.offAllNamed('home');
                  },
                  child: Container(
                    width: Get.width * 0.30,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Color(0xFF934D0F),
                      borderRadius: BorderRadius.circular(16.0),
                    ),
                    alignment: Alignment.center,
                    child: AppWidget.normalText(
                      context,
                      'btn_ok'.tr,
                      14.sp,
                      Colors.black,
                      FontWeight.w600,
                    ),
                  ),
                ),
                SizedBox(height: 8),
              ],
            ),
          ),
        );
      },
    );
  }
}
