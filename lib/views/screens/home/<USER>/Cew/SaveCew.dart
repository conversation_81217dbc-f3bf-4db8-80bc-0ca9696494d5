import 'dart:core';

import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:mapp_ms24/configs/color_system.dart';
import 'package:mapp_ms24/controllers/internal/LeaveController/LeaveApprovalController.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/models/cewModel/CewModel.dart';

import '../../../../../controllers/internal/CewController/CewController.dart';
import '../../../../../controllers/utils/widget.dart';
import '../../../Widget/Widget.dart';

class SaveCew extends StatefulWidget {
  const SaveCew({super.key});

  @override
  State<SaveCew> createState() => _SaveCewState();
}

class _SaveCewState extends State<SaveCew> {
  final CewController cewCtr = Get.find<CewController>();
  final ProfileController profileCtr = Get.find<ProfileController>();
  TextEditingController searchController = TextEditingController();
  TextEditingController _numLikeController = TextEditingController();
  FocusNode _focusNode = FocusNode();
  late FToast fToast;

  String _valueTeamRec = '0';
  String _valueCategory = '0';
  String _valueCounty = '0';
  String _valueType = '0';
  int statusshowAutoComplet = 0;
  int statusshow = 1;

  bool checkselect = false;
  var keyword = '';

  TextEditingController _idPersonRecController = TextEditingController();
  TextEditingController _detailController = TextEditingController();

  GlobalKey<ScaffoldState> key = GlobalKey<ScaffoldState>();

  LeaveAndAppController landACrt = Get.find<LeaveAndAppController>();
  ProfileController profileCrt = Get.find<ProfileController>();
  bool checking = true;

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(() {
      setState(() {}); // Update UI when focus changes
    });
    initializePage();
  }

  void initializePage() async {
    await cewCtr.teamCewModel;
    await cewCtr.loadTypescew();
    await cewCtr.getTeamCew();
    await cewCtr.loadCounty();
    await cewCtr.loadCategory();
    await cewCtr.getInfoRecCewAll();
    checking = false;
    setState(() {});
    _numLikeController.text =
        cewCtr.datasTypescew.any((item) => item.toString() == "บรรยากาศ")
            ? '0'
            : cewCtr.datasTypescew.any((item) => item.toString() == "ทั่วไป")
                ? '1000'
                : '';
  }

  @override
  void dispose() {
    fToast.removeCustomToast();
    cewCtr.resetData();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            height: Get.height,
            decoration: BoxDecoration(
                gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                  const Color(0xFF1F1C2F).withOpacity(1),
                  const Color(0xFF0D0C14).withOpacity(1),
                ])),
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.only(top: 20.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    buildAppBar(context, 'ui_cewhead'.tr),
                    Stack(
                      children: [
                        Column(
                          children: [
                            buildForm(context),
                          ],
                        ),
                        Column(
                          children: [
                            Padding(
                              padding:
                                  const EdgeInsets.only(left: 120.0, top: 110),
                              child: buildShowResult(),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
          checking
              ? Container(
                  width: MediaQuery.of(context).size.width,
                  height: MediaQuery.of(context).size.height,
                  color: Colors.black12,
                  child: Center(
                    child: SizedBox(
                      width: 60.w,
                      child: const LoadingIndicator(
                        indicatorType: Indicator.lineSpinFadeLoader,
                        colors: [Colors.white],
                      ),
                    ),
                  ))
              : Container(),
        ],
      ),
    );
  }

  Widget buildForm(context) {
    return Container(
        padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
        width: double.infinity.w,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Obx(
              () => Column(
                children: [
                  builType(context),
                  buildRecive(context, key),
                  buildCewType(context),
                  buildInputDes(context),
                  buildUpload(context),
                  buildGiveLike(context),
                  buildDetail(context),
                  buildButton(context),
                ],
              ),
            ),
          ],
        ));
  }

  Widget builType(context) {
    return Obx(
      () => Container(
        padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 10.w),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                AppWidget.normalText(context, 'dp_cewtype'.tr, 14.sp,
                    const Color(0xFFF6F6F6), FontWeight.w400)
              ],
            ),
            Padding(
              padding: const EdgeInsets.only(left: 14.0),
              child: InkWell(
                onTap: () {
                  setState(() {
                    cewCtr.toggleSelection(true);
                    _idPersonRecController.text = "";
                    cewCtr.buRec = "";
                    // cewCtr.detailController.text = "";
                    _numLikeController.text = "";
                    _valueCategory = "0";
                    _valueCounty = "0";
                    cewCtr.idRec = "";
                    cewCtr.fullnameRec = "";
                    cewCtr.nicknameRec = "";
                    statusshow = 1;
                  });
                },
                child: Container(
                  height: 34.h,
                  width: 125.w,
                  decoration: BoxDecoration(
                    color: cewCtr.isPersonSelected == true
                        ? const Color(0xFF302C49)
                        : const Color(0xFF302C49).withOpacity(0.5),
                    borderRadius: BorderRadius.circular(8.r),
                    border: Border.all(
                      color: cewCtr.isPersonSelected == true
                          ? const Color(0xFFA596FF)
                          : const Color(0xFFA596FF).withOpacity(0.5),
                      width: 1.w,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      AppWidget.normalText(
                        context,
                        'btn_cewPerson'.tr,
                        14.sp,
                        cewCtr.isPersonSelected == true
                            ? const Color(0xFFFEE095)
                            : const Color(0xFFF6F6F6).withOpacity(0.5),
                        cewCtr.isPersonSelected == true
                            ? FontWeight.w600
                            : FontWeight.w400,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            InkWell(
              onTap: () {
                setState(() {
                  cewCtr.toggleSelection(false);
                  statusshow = 2;
                });
              },
              child: Container(
                height: 34.h,
                width: 125.w,
                decoration: BoxDecoration(
                  color: cewCtr.isTeamSelected == true
                      ? const Color(0xFF302C49)
                      : const Color(0xFF302C49).withOpacity(0.5),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: cewCtr.isTeamSelected == true
                        ? const Color(0xFFA596FF)
                        : const Color(0xFFA596FF).withOpacity(0.5),
                    width: 1.w,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    AppWidget.normalText(
                      context,
                      'ui_cewTeam'.tr,
                      14.sp,
                      cewCtr.isTeamSelected == true
                          ? const Color(0xFFFEE095)
                          : const Color(0xFFF6F6F6).withOpacity(0.5),
                      cewCtr.isTeamSelected == true
                          ? FontWeight.w600
                          : FontWeight.w400,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildRecive(context, key) {
    return Stack(
      children: [
        Container(
          height: _focusNode.hasFocus ? 300.h : null,
          padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 10.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    height: _focusNode.hasFocus ? 240.h : 30.h,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        cewCtr.isPersonSelected == true
                            ? AppWidget.normalText(
                                context,
                                'ui_cewPersonRec'.tr,
                                14.sp,
                                const Color(0xFFF6F6F6),
                                FontWeight.w400,
                              )
                            : AppWidget.normalText(
                                context,
                                'ui_cewTeamRec'.tr,
                                14.sp,
                                const Color(0xFFF6F6F6),
                                FontWeight.w400,
                              ),
                      ],
                    ),
                  ),
                  cewCtr.isPersonSelected == true
                      ? Stack(
                          children: [
                            buildFindMember(),
                          ],
                        )
                      : Container(
                          height: 42.h,
                          width: 260.w,
                          decoration: BoxDecoration(
                            color: const Color(0xFF302C49),
                            borderRadius: BorderRadius.circular(8.r),
                            boxShadow: const [
                              BoxShadow(
                                color: Color(0x40000000),
                                offset: Offset(0, 4),
                                blurRadius: 4,
                                spreadRadius: 0,
                              ),
                            ],
                          ),
                          child: Padding(
                            padding: EdgeInsets.only(
                                top: 5.h, bottom: 5.h, left: 20.w, right: 10.w),
                            child: DropdownButton<String>(
                              icon: Container(
                                child: SvgPicture.string(
                                  '<svg viewBox="727.7 984.7 22.5 14.5" ><defs><linearGradient id="gradient" x1="0.5" y1="0.0" x2="0.5" y2="1.0"><stop offset="0.0" stop-color="#fffdd163"  /><stop offset="1.0" stop-color="#fffee095"  /></linearGradient></defs><path transform="matrix(0.0, -1.0, 1.0, 0.0, 721.0, 1001.0)" d="M 2.235063552856445 19.1953125 L 11.79756355285645 28.7578125 C 12.45850086212158 29.41875076293945 13.52725124359131 29.41875076293945 14.18115711212158 28.7578125 L 15.77021980285645 27.16875076293945 C 16.4311580657959 26.5078125 16.4311580657959 25.43906402587891 15.77021980285645 24.78515625 L 8.992094993591309 18.00703048706055 L 15.77021980285645 11.22890567779541 C 16.4311580657959 10.56796836853027 16.4311580657959 9.499217987060547 15.77021980285645 8.845312118530273 L 14.18818855285645 7.2421875 C 13.52725028991699 6.581250190734863 12.45850086212158 6.581250190734863 11.80459403991699 7.2421875 L 2.242094039916992 16.8046875 C 1.574125289916992 17.46562576293945 1.574125289916992 18.53437423706055 2.235062599182129 19.1953125 Z" fill="url(#gradient)" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                                  allowDrawingOutsideViewBox: true,
                                  fit: BoxFit.fitHeight,
                                  height: 10.h,
                                ),
                              ),
                              underline: Container(),
                              dropdownColor: Color(0xff1f1c2f),
                              items: cewCtr.datasTeamRec
                                  .map((data) => DropdownMenuItem<String>(
                                        child: Container(
                                          alignment: Alignment.center,
                                          child: AppWidget.normalTextDynamic(
                                            context,
                                            data.key,
                                            14.sp,
                                            const Color(0xFFF6F6F6),
                                            FontWeight.w400,
                                          ),
                                        ),
                                        value: data.value,
                                      ))
                                  .toList(),
                              isExpanded: true,
                              onChanged: (value) {
                                setState(() {
                                  _valueTeamRec = value.toString();
                                  _idPersonRecController.text =
                                      value.toString();
                                });
                                cewCtr.getInfoRecCew(value);
                              },
                              value: _valueTeamRec,
                            ),
                          ),
                        ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget buildCewType(context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 10.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          SizedBox(
            width: 92.w,
            height: 42.h,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                AppWidget.normalText(
                  context,
                  'ui_cew_selcet'.tr,
                  14.sp,
                  const Color(0xFFF6F6F6),
                  FontWeight.w400,
                ),
              ],
            ),
          ),
          Container(
            height: 42.h,
            width: 260.w,
            decoration: BoxDecoration(
              color: const Color(0xFF302C49),
              borderRadius: BorderRadius.circular(8),
              boxShadow: const [
                BoxShadow(
                  color: Color(0x40000000),
                  offset: Offset(0, 4),
                  blurRadius: 4,
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Padding(
              padding: EdgeInsets.only(
                  top: 5.h, bottom: 5.h, left: 20.w, right: 10.w),
              child: DropdownButton<String>(
                icon: Container(
                  child: SvgPicture.string(
                    '<svg viewBox="727.7 984.7 22.5 14.5" ><defs><linearGradient id="gradient" x1="0.5" y1="0.0" x2="0.5" y2="1.0"><stop offset="0.0" stop-color="#fffdd163"  /><stop offset="1.0" stop-color="#fffee095"  /></linearGradient></defs><path transform="matrix(0.0, -1.0, 1.0, 0.0, 721.0, 1001.0)" d="M 2.235063552856445 19.1953125 L 11.79756355285645 28.7578125 C 12.45850086212158 29.41875076293945 13.52725124359131 29.41875076293945 14.18115711212158 28.7578125 L 15.77021980285645 27.16875076293945 C 16.4311580657959 26.5078125 16.4311580657959 25.43906402587891 15.77021980285645 24.78515625 L 8.992094993591309 18.00703048706055 L 15.77021980285645 11.22890567779541 C 16.4311580657959 10.56796836853027 16.4311580657959 9.499217987060547 15.77021980285645 8.845312118530273 L 14.18818855285645 7.2421875 C 13.52725028991699 6.581250190734863 12.45850086212158 6.581250190734863 11.80459403991699 7.2421875 L 2.242094039916992 16.8046875 C 1.574125289916992 17.46562576293945 1.574125289916992 18.53437423706055 2.235062599182129 19.1953125 Z" fill="url(#gradient)" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                    allowDrawingOutsideViewBox: true,
                    fit: BoxFit.fitHeight,
                    height: 10.h,
                  ),
                ),
                underline: Container(),
                dropdownColor: Color(0xff1f1c2f),
                items: cewCtr.datasTypescew
                    .map((data) => DropdownMenuItem<String>(
                          child: Container(
                            alignment: Alignment.center,
                            child: AppWidget.normalTextDynamic(
                              context,
                              data.key,
                              14.sp,
                              const Color(0xFFF6F6F6),
                              FontWeight.w400,
                            ),
                          ),
                          value: data.value,
                        ))
                    .toList(),
                isExpanded: true,
                onChanged: (value) {
                  cewCtr.updateCew(value, _numLikeController.text);

                  setState(() {
                    _valueType = value.toString();
                    cewCtr.updateSelectedCEWType(_valueType);
                    cewCtr.selectedCEWType.value == _valueType;
                  });
                },
                value: _valueType,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildInputDes(context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
      child: Row(
        children: [
          Expanded(
            child: Container(
              height: 100.h,
              decoration: BoxDecoration(
                color: const Color(0xFF1F1C2F),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: const Color(0xFF5F569B),
                  width: 1.w,
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(10.0),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: 24.h,
                      width: 24.w,
                      child: Image.asset('assets/ADD/Message-bubble.png'),
                    ),
                    SizedBox(
                      width: 12.w,
                    ),
                    Expanded(
                      child: Container(
                        width: Get.width,
                        height: Get.height,
                        child: TextField(
                          cursorColor: Colors.black,
                          onChanged: (value) {
                            setState(() {
                              _detailController.text = value;
                            });
                          },
                          onEditingComplete: () {},
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Medium',
                            color: const Color(0xFFF6F6F6),
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w400,
                          ),
                          decoration: InputDecoration.collapsed(
                            hintText: 'ui_cewinput'.tr,
                            hintStyle: TextStyle(
                              fontFamily: 'SukhumvitSet-Medium',
                              fontSize: 14.sp,
                              fontWeight: FontWeight.w400,
                              color: const Color(0xFFF6F6F6).withOpacity(0.6),
                            ),
                          ),
                          maxLines: null,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildUpload(context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 5.h),
      child: Row(
        children: [
          Container(
              padding: EdgeInsets.only(
                  top: 5.h, bottom: 5.h, left: 20.w, right: 15.w),
              height: 50.h,
              width: 353.w,
              decoration: BoxDecoration(
                color: const Color(0xFF302C49),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  AppWidget.normalText(
                    context,
                    'ui_imgUpload'.tr,
                    14.sp,
                    const Color(0xFFF6F6F6).withOpacity(0.6),
                    FontWeight.w400,
                  ),
                  SizedBox(
                    height: 18.h,
                    width: 18.w,
                    child: Image.asset('assets/ADD/Image.png'),
                  )
                ],
              )),
        ],
      ),
    );
  }

  Widget buildGiveLike(context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 5.h),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                height: 52.h,
                width: 172.w,
                decoration: BoxDecoration(
                  color: const Color(0xFF302C49),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Column(
                  children: [
                    TextField(
                      cursorColor: Colors.black,
                      readOnly: cewCtr.selectedCEWType.value != 'แผนงาน',
                      textAlign: TextAlign.center,
                      keyboardType: TextInputType.number,
                      controller: _numLikeController,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'[.0-9]'))
                      ],
                      onChanged: (value) {
                        setState(() {});

                        if (cewCtr.selectedCEWType.value == 'แผนงาน') {
                          cewCtr.inputCEW.value = value;
                          cewCtr.inputLike.value = value;
                        } else {
                          _numLikeController.text = '';
                          _numLikeController.text = cewCtr.inputCEW.value;
                        }
                      },
                      style: TextStyle(
                        fontFamily: 'SukhumvitSet-Text',
                        fontSize: 12.sp,
                        color: const Color(0xb2fcf6e4),
                        letterSpacing: 0.70.w,
                      ),
                      decoration: InputDecoration(
                        counterText: '',
                        hintText: cewCtr.selectedCEWType.value == 'แผนงาน' ||
                                cewCtr.inputCEW.value == ''
                            ? "tf_cewNumLike".tr.toString()
                            : cewCtr.inputCEW.value,
                        hintStyle: TextStyle(
                          fontFamily: 'SukhumvitSet-Text',
                          fontSize: 12.sp,
                          color: const Color(0xb2fcf6e4),
                          letterSpacing: 0.70.w,
                        ),
                        enabledBorder: const UnderlineInputBorder(
                          borderSide:
                              const BorderSide(color: Colors.transparent),
                        ),
                        focusedBorder: const UnderlineInputBorder(
                          borderSide:
                              const BorderSide(color: Colors.transparent),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                height: 52.h,
                width: 172.w,
                decoration: BoxDecoration(
                  color: const Color(0xFF302C49),
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: const [
                    BoxShadow(
                      color: Color(0x40000000),
                      offset: Offset(0, 4),
                      blurRadius: 4,
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: Padding(
                  padding: EdgeInsets.only(
                      top: 5.h, bottom: 5.h, left: 20.w, right: 10.w),
                  child: DropdownButton<String>(
                    icon: Container(
                      child: SvgPicture.string(
                        '<svg viewBox="727.7 984.7 22.5 14.5" ><defs><linearGradient id="gradient" x1="0.5" y1="0.0" x2="0.5" y2="1.0"><stop offset="0.0" stop-color="#fffdd163"  /><stop offset="1.0" stop-color="#fffee095"  /></linearGradient></defs><path transform="matrix(0.0, -1.0, 1.0, 0.0, 721.0, 1001.0)" d="M 2.235063552856445 19.1953125 L 11.79756355285645 28.7578125 C 12.45850086212158 29.41875076293945 13.52725124359131 29.41875076293945 14.18115711212158 28.7578125 L 15.77021980285645 27.16875076293945 C 16.4311580657959 26.5078125 16.4311580657959 25.43906402587891 15.77021980285645 24.78515625 L 8.992094993591309 18.00703048706055 L 15.77021980285645 11.22890567779541 C 16.4311580657959 10.56796836853027 16.4311580657959 9.499217987060547 15.77021980285645 8.845312118530273 L 14.18818855285645 7.2421875 C 13.52725028991699 6.581250190734863 12.45850086212158 6.581250190734863 11.80459403991699 7.2421875 L 2.242094039916992 16.8046875 C 1.574125289916992 17.46562576293945 1.574125289916992 18.53437423706055 2.235062599182129 19.1953125 Z" fill="url(#gradient)" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                        allowDrawingOutsideViewBox: true,
                        fit: BoxFit.fitHeight,
                        height: 10.h,
                      ),
                    ),
                    underline: Container(),
                    dropdownColor: Color(0xff1f1c2f),
                    items: cewCtr.datasCategory
                        .map((data) => DropdownMenuItem<String>(
                              child: Container(
                                alignment: Alignment.center,
                                child: AppWidget.normalTextDynamic(
                                  context,
                                  data.key,
                                  14.sp,
                                  const Color(0xFFF6F6F6),
                                  FontWeight.w400,
                                ),
                              ),
                              value: data.value,
                            ))
                        .toList(),
                    isExpanded: true,
                    onChanged: (value) {
                      setState(() {
                        _valueCategory = value.toString();
                      });
                    },
                    value: _valueCategory,
                  ),
                ),
              ),
            ],
          ),
          Container(
            padding: EdgeInsets.symmetric(vertical: 10.h),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                SizedBox(
                  width: 172.w,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      AppWidget.normalText(
                        context,
                        'ui_cewDetailAAM'.tr,
                        14.sp,
                        const Color(0xFFFEE095),
                        FontWeight.w500,
                      ),
                      AppWidget.normalText(
                        context,
                        'ui_cewDetailAAM2'.tr,
                        12.sp,
                        const Color(0xFFA596FF),
                        FontWeight.w400,
                      ),
                    ],
                  ),
                ),
                Container(
                  height: 42.h,
                  width: 172.w,
                  decoration: BoxDecoration(
                    color: const Color(0xFF302C49),
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: const [
                      BoxShadow(
                        color: Color(0x40000000),
                        offset: Offset(0, 4),
                        blurRadius: 4,
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: Padding(
                    padding: EdgeInsets.only(
                        top: 5.h, bottom: 5.h, left: 20.w, right: 10.w),
                    child: DropdownButton<String>(
                      icon: Container(
                        child: SvgPicture.string(
                          '<svg viewBox="727.7 984.7 22.5 14.5" ><defs><linearGradient id="gradient" x1="0.5" y1="0.0" x2="0.5" y2="1.0"><stop offset="0.0" stop-color="#fffdd163"  /><stop offset="1.0" stop-color="#fffee095"  /></linearGradient></defs><path transform="matrix(0.0, -1.0, 1.0, 0.0, 721.0, 1001.0)" d="M 2.235063552856445 19.1953125 L 11.79756355285645 28.7578125 C 12.45850086212158 29.41875076293945 13.52725124359131 29.41875076293945 14.18115711212158 28.7578125 L 15.77021980285645 27.16875076293945 C 16.4311580657959 26.5078125 16.4311580657959 25.43906402587891 15.77021980285645 24.78515625 L 8.992094993591309 18.00703048706055 L 15.77021980285645 11.22890567779541 C 16.4311580657959 10.56796836853027 16.4311580657959 9.499217987060547 15.77021980285645 8.845312118530273 L 14.18818855285645 7.2421875 C 13.52725028991699 6.581250190734863 12.45850086212158 6.581250190734863 11.80459403991699 7.2421875 L 2.242094039916992 16.8046875 C 1.574125289916992 17.46562576293945 1.574125289916992 18.53437423706055 2.235062599182129 19.1953125 Z" fill="url(#gradient)" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                          allowDrawingOutsideViewBox: true,
                          fit: BoxFit.fitHeight,
                          height: 10.h,
                        ),
                      ),
                      underline: Container(),
                      dropdownColor: Color(0xff1f1c2f),
                      items: cewCtr.datasCounty
                          .map((data) => DropdownMenuItem<String>(
                                child: Container(
                                  alignment: Alignment.center,
                                  child: AppWidget.normalTextDynamic(
                                    context,
                                    data.key,
                                    14.sp,
                                    const Color(0xFFF6F6F6),
                                    FontWeight.w400,
                                  ),
                                ),
                                value: data.value,
                              ))
                          .toList(),
                      isExpanded: true,
                      onChanged: (value) {
                        setState(() {
                          _valueCounty = value.toString();
                        });
                      },
                      value: _valueCounty,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Container(
            height: 0.5.h,
            decoration: BoxDecoration(
              color: const Color(0xFFFCF6E4).withOpacity(0.2),
              border: Border.all(
                width: 0.5.w,
                color: const Color(0xFFFCF6E4).withOpacity(0.2),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildDetail(context) {
    String formattedDate = DateFormat('dd-MM-yyyy').format(DateTime.now());
    return Container(
      padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppWidget.normalText(
            context,
            'ui_cewDetailCew'.tr,
            14.sp,
            const Color(0xFF8D6500),
            FontWeight.w600,
          ),
          SizedBox(
            height: 20.h,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              AppWidget.normalText(
                context,
                'ui_cewPersonRec2'.tr,
                14.sp,
                const Color(0xFFF6F6F6),
                FontWeight.w400,
              ),
              cewCtr.isTeamSelected == true
                  ? AppWidget.normalText(
                      context,
                      cewCtr.idRec.toString() == ""
                          ? '-'
                          : cewCtr.idRec.toString() +
                              " " +
                              cewCtr.fullnameRec.toString() +
                              " (" +
                              cewCtr.nicknameRec.toString() +
                              ")",
                      14.sp,
                      const Color(0xFFF6F6F6),
                      FontWeight.w400,
                    )
                  : AppWidget.normalText(
                      context,
                      cewCtr.idRec.toString() == ""
                          ? '-'
                          : cewCtr.idRec.toString() +
                              " " +
                              cewCtr.fullnameRec.toString() +
                              " (" +
                              cewCtr.nicknameRec.toString() +
                              ")",
                      14.sp,
                      const Color(0xFFF6F6F6),
                      FontWeight.w400,
                    ),
            ],
          ),
          SizedBox(
            height: 5.h,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              AppWidget.normalText(
                context,
                'ui_cewNumLike'.tr,
                14.sp,
                const Color(0xFFA596FF),
                FontWeight.w400,
              ),
              Row(
                children: [
                  AppWidget.normalTextRX(
                    context,
                    cewCtr.inputLike.value.obs.isNotEmpty
                        ? cewCtr.inputLike.value.obs
                        : '0'.obs,
                    14.sp,
                    const Color(0xFFFEE095),
                    FontWeight.w400,
                  ),
                  SizedBox(
                    width: 5.w,
                  ),
                  AppWidget.normalText(
                    context,
                    'ui_cewUnitLike'.tr,
                    14.sp,
                    const Color(0xFFF6F6F6).withOpacity(0.6),
                    FontWeight.w400,
                  ),
                ],
              )
            ],
          ),
          SizedBox(
            height: 5.h,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              AppWidget.normalText(
                context,
                'ui_cewTypeCategory'.tr,
                14.sp,
                const Color(0xFFF6F6F6),
                FontWeight.w400,
              ),
              AppWidget.normalText(
                context,
                _valueCategory == "" ? "-" : _valueCategory,
                14.sp,
                const Color(0xFFF6F6F6).withOpacity(0.6),
                FontWeight.w400,
              ),
            ],
          ),
          SizedBox(
            height: 5.h,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              AppWidget.normalText(
                context,
                'ui_cewGiveCew'.tr,
                14.sp,
                const Color(0xFFF6F6F6).withOpacity(0.6),
                FontWeight.w400,
              ),
              AppWidget.normalText(
                context,
                profileCtr.responseMember!.id! +
                    " " +
                    profileCtr.responseMember!.full_name_th!,
                14.sp,
                const Color(0xFFF6F6F6).withOpacity(0.6),
                FontWeight.w400,
              ),
            ],
          ),
          SizedBox(
            height: 5.h,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              AppWidget.normalText(
                context,
                'ui_cewDate'.tr,
                14.sp,
                const Color(0xFFF6F6F6).withOpacity(0.6),
                FontWeight.w400,
              ),
              AppWidget.normalText(
                context,
                '${formattedDate}',
                14.sp,
                const Color(0xFFA596FF),
                FontWeight.w400,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget buildButton(BuildContext context) {
    return Container(
      child: Align(
        alignment: Alignment.bottomCenter,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          width: double.infinity.w,
          child: InkWell(
            onTap: () {
              showCupertinoModalPopup(
                context: context,
                builder: (BuildContext context) {
                  return buildShowSheet(context);
                },
              );
            },
            child: Container(
              width: 353.w,
              height: 50.h,
              decoration: BoxDecoration(
                color: const Color(0xFF302C49),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: const Color(0xFFA596FF),
                  width: 1.w,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AppWidget.normalText(
                    context,
                    'btn_continew'.tr,
                    16.sp,
                    const Color(0xFFF6F6F6),
                    FontWeight.w900,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget buildFindMember() {
    return Stack(
      children: [
        Container(
          alignment: Alignment.center,
          height: 50.0.h,
          width: 260.0.w,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.h),
            color: const Color(0xff1f1c2f),
            boxShadow: [
              BoxShadow(
                color: const Color(0x4d000000),
                offset: Offset(0, 2),
                blurRadius: 20,
              ),
            ],
          ),
          child: TextField(
            cursorColor: Colors.black,
            focusNode: _focusNode,
            textAlign: TextAlign.center,
            onChanged: (value) {
              cewCtr.searchFuncRecCew(value);
              cewCtr.toggleSelection(true);
            },
            controller: _idPersonRecController,
            style: TextStyle(
              fontFamily: 'SukhumvitSet-Text',
              fontSize: 14.h,
              color: const Color(0xb2fcf6e4),
              letterSpacing: 0.70.w,
            ),
            decoration: InputDecoration(
              counterText: '',
              hintText: "tf_cewIdPerson".tr.toString(),
              hintStyle: TextStyle(
                fontFamily: 'SukhumvitSet-Text',
                fontSize: 14.h,
                color: const Color(0xb2fcf6e4),
                letterSpacing: 0.70.w,
              ),
              enabledBorder: const UnderlineInputBorder(
                borderSide: BorderSide(color: Colors.transparent),
              ),
              focusedBorder: const UnderlineInputBorder(
                borderSide: BorderSide(color: Colors.transparent),
              ),
            ),
          ),
        ),
        if (_focusNode.hasFocus)
          GestureDetector(
            onTap: () {},
            child: Container(
              margin: EdgeInsets.only(top: 60.h),
              width: 260.w,
              height: 225.h,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20.0),
                color: const Color(0xff1f1c2f),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0x4d000000),
                    offset: Offset(0, 2),
                    blurRadius: 20,
                  ),
                ],
              ),
              child: ListView(
                padding: EdgeInsets.only(top: 0),
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    // children: cewCtr.autoCompletRecCew(
                    //     keyword),
                    children: List.generate(
                        10,
                        (index) => InkWell(
                              onTap: () {},
                              child: Container(
                                color: Colors.transparent,
                                padding: EdgeInsets.only(
                                    top: 5.h, bottom: 5.h, left: 10.w),
                                child: Text(
                                  cewCtr.showNames[index],
                                  style: TextStyle(
                                    fontFamily: 'SukhumvitSet-Text',
                                    fontSize: 14.h,
                                    color: const Color(0xb2fcf6e4),
                                    letterSpacing: 1.h,
                                  ),
                                  textAlign: TextAlign.left,
                                ),
                              ),
                            )),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Widget buildShowResult() {
    // Filter items based on whether they match the search text
    RxList<Map<String, String>> matchingItems = cewCtr.sampleData
        .where((item) => item["id"]!
            .toLowerCase()
            .contains(cewCtr.searchController.text.toLowerCase()))
        .toList()
        .obs; // This line is not needed

    // Calculate the height based on the number of matching items
    double containerHeight = cewCtr.searchController.text == ''
        ? 0.0
        : matchingItems.length * 50.0.h;

    return cewCtr.expandCew
        ? Container()
        : AnimatedContainer(
            duration: Duration(milliseconds: 300),
            height: 100,
            width: 260.0.w,
            decoration: BoxDecoration(
              color: const Color(0xFF302C49),
              borderRadius: BorderRadius.circular(8),
              boxShadow: const [
                BoxShadow(
                  color: Color(0x40000000),
                  offset: Offset(0, 4),
                  blurRadius: 4,
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Obx(() => ListView.builder(
                  itemCount: matchingItems.length,
                  itemBuilder: (context, index) {
                    return InkWell(
                      onTap: () {
                        setState(() {
                          cewCtr.updatesearchController(
                              "${matchingItems[index]["id"]} ${matchingItems[index]["name"]} ${matchingItems[index]["lastname"]}");
                          cewCtr.expandCew = true;
                        });
                      },
                      child: ListTile(
                        title: AppWidget.normalText(
                            context,
                            'ID: ${matchingItems[index]["id"]} - ${matchingItems[index]["name"]} ${matchingItems[index]["lastname"]}',
                            14.sp,
                            whiteColor,
                            FontWeight.w400),
                        // You can add more content or customize the ListTile as needed
                      ),
                    );
                  },
                )),
          );
  }

  Widget buildShowSheet(BuildContext context) {
    String formattedDate = DateFormat('dd-MM-yyyy').format(DateTime.now());

    return Container(
      padding: EdgeInsets.symmetric(
        vertical: 20.h,
      ),
      height: 396.h,
      width: 392.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(10.r),
          topRight: Radius.circular(10.r),
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF000000)
                .withOpacity(0.85), // เปลี่ยนค่า opacity ตามต้องการ
            offset: const Offset(0, 4),
            blurRadius: 10,
            spreadRadius: 0,
          ),
        ],
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            const Color(0xFF302C49),
            const Color(0xFF302C49),
            const Color(0xFF1F1C2F).withOpacity(0.6),
          ],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            width: Get.width.w,
            padding: EdgeInsets.symmetric(vertical: 30.h, horizontal: 20.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: EdgeInsets.only(bottom: 15.h),
                  child: AppWidget.normalText(
                    context,
                    'ui_cewDetailCew'.tr,
                    14.sp,
                    const Color(0xFFF6F6F6),
                    FontWeight.w600,
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    AppWidget.normalText(
                      context,
                      'ui_cewPersonRec2'.tr,
                      14.sp,
                      const Color(0xFFF6F6F6),
                      FontWeight.w400,
                    ),
                    cewCtr.isTeamSelected == true
                        ? AppWidget.normalText(
                            context,
                            cewCtr.idRec.toString() == ""
                                ? '-'
                                : cewCtr.idRec.toString() +
                                    " " +
                                    cewCtr.fullnameRec.toString() +
                                    " (" +
                                    cewCtr.nicknameRec.toString() +
                                    ")",
                            14.sp,
                            const Color(0xFFF6F6F6),
                            FontWeight.w400,
                          )
                        : AppWidget.normalText(
                            context,
                            cewCtr.idRec.toString() == ""
                                ? '-'
                                : cewCtr.idRec.toString() +
                                    " " +
                                    cewCtr.fullnameRec.toString() +
                                    " (" +
                                    cewCtr.nicknameRec.toString() +
                                    ")",
                            14.sp,
                            const Color(0xFFF6F6F6),
                            FontWeight.w400,
                          ),
                  ],
                ),
                SizedBox(
                  height: 5.h,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    AppWidget.normalText(
                      context,
                      'ui_cewNumLike'.tr,
                      14.sp,
                      const Color(0xFFA596FF),
                      FontWeight.w400,
                    ),
                    Row(
                      children: [
                        AppWidget.normalTextRX(
                          context,
                          cewCtr.inputLike.value.obs.isNotEmpty
                              ? cewCtr.inputLike.value.obs
                              : '0'.obs,
                          14.sp,
                          const Color(0xFFFEE095),
                          FontWeight.w400,
                        ),
                        SizedBox(
                          width: 5.w,
                        ),
                        AppWidget.normalText(
                          context,
                          'ui_cewUnitLike'.tr,
                          14.sp,
                          const Color(0xFFF6F6F6).withOpacity(0.6),
                          FontWeight.w400,
                        ),
                      ],
                    )
                  ],
                ),
                SizedBox(
                  height: 5.h,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    AppWidget.normalText(
                      context,
                      'ui_cewTypeCategory'.tr,
                      14.sp,
                      const Color(0xFFF6F6F6),
                      FontWeight.w400,
                    ),
                    AppWidget.normalText(
                      context,
                      _valueCategory == "" ? "-" : _valueCategory,
                      14.sp,
                      const Color(0xFFF6F6F6).withOpacity(0.6),
                      FontWeight.w400,
                    ),
                  ],
                ),
                SizedBox(
                  height: 5.h,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    AppWidget.normalText(
                      context,
                      'ui_cewGiveCew'.tr,
                      14.sp,
                      const Color(0xFFF6F6F6).withOpacity(0.6),
                      FontWeight.w400,
                    ),
                    AppWidget.normalText(
                      context,
                      profileCtr.responseMember!.id! +
                          " " +
                          profileCtr.responseMember!.full_name_th!,
                      14.sp,
                      const Color(0xFFF6F6F6).withOpacity(0.6),
                      FontWeight.w400,
                    ),
                  ],
                ),
                SizedBox(
                  height: 5.h,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    AppWidget.normalText(
                      context,
                      'ui_cewDate'.tr,
                      14.sp,
                      const Color(0xFFF6F6F6).withOpacity(0.6),
                      FontWeight.w400,
                    ),
                    AppWidget.normalText(
                      context,
                      '${formattedDate}',
                      14.sp,
                      const Color(0xFFA596FF).withOpacity(0.6),
                      FontWeight.w400,
                    ),
                  ],
                ),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(vertical: 10.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                InkWell(
                  onTap: () {},
                  child: Container(
                      width: 353.w,
                      height: 50.h,
                      decoration: BoxDecoration(
                        borderRadius:
                            const BorderRadius.all(Radius.circular(10)),
                        color: const Color(0xFF8D6500),
                        border: Border.all(
                          width: 1.w,
                          color: const Color(0xFFFEE095),
                        ),
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          AppWidget.normalText(
                            context,
                            'ui_cewConfirm'.tr,
                            16.sp,
                            const Color(0xFFF6F6F6),
                            FontWeight.w900,
                          ),
                        ],
                      )),
                ),
                SizedBox(
                  height: 20.h,
                ),
                InkWell(
                    onTap: () {
                      setState(() {
                        Get.back();
                      });
                    },
                    child: SizedBox(
                        height: 30.h,
                        width: 71.w,
                        child: Center(
                          child: AppWidget.normalText(
                            context,
                            'btn_cancel'.tr,
                            16.sp,
                            const Color(0xFFF6F6F6),
                            FontWeight.w400,
                          ),
                        )))
              ],
            ),
          )
        ],
      ),
    );
  }
}
