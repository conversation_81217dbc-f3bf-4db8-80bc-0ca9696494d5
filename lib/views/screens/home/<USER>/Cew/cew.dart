import 'dart:core';

import 'dart:ui';

import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:mapp_ms24/configs/color_system.dart';
import 'package:mapp_ms24/controllers/internal/LeaveController/LeaveApprovalController.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/controllers/utils/color.dart';
import 'package:mapp_ms24/models/cewModel/CewModel.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';
import 'package:path/path.dart' as path;
import '../../../../../controllers/internal/CewController/CewController.dart';
import '../../../../../controllers/utils/widget.dart';
import '../../../Widget/Widget.dart';

class SaveCew2 extends StatefulWidget {
  const SaveCew2({super.key});

  @override
  State<SaveCew2> createState() => _SaveCew2State();
}

class _SaveCew2State extends State<SaveCew2>  with WidgetsBindingObserver {
  final CewController cewCtr = Get.find<CewController>();
  final ProfileController profileCtr = Get.find<ProfileController>();
  TextEditingController _numLikeController = TextEditingController();
  FocusNode _focusNode = FocusNode();
  late FToast fToast;
  DateTime? _backgroundTime;
  String _valueTeamRec = '0';
  String _valueCategory = '0';
  String _valueCounty = '0';
  String _valueType = '0';
  int statusshowAutoComplet2 = 0;
  int statusshow = 1;
  bool checkselect = false;
  var keyword = '';
  var dataName = "";
  var idRec = "";
  var lnameRec = "";
  var fullnameRec = "";
  var nicknameRec = "";
  var tokenLineRec = "";
  var buRec = "";
  var typeRoleGive = "";

  GlobalKey<ScaffoldState> key = GlobalKey<ScaffoldState>();

  ProfileController profileCrt = Get.find<ProfileController>();
  bool checking = true;

  @override
  void initState() {
    super.initState();
    cewCtr.idPersonRecController.text = "";
cewCtr.ClearData();
    _focusNode.addListener(() {
      setState(() {}); // Update UI when focus changes
      fToast = FToast(); // Initialize fToast in initState
      fToast.init(context);
    });
    initializePage();

  }

  void initializePage() async {
    WidgetsBinding.instance.addObserver(this);
    //  fToast = FToast();
    // await fToast.init(context); // Initialize fToast before using it
    await cewCtr.teamCewModel;
    await cewCtr.loadTypescew();
    await cewCtr.getTeamCew();
    await cewCtr.loadCounty();
    await cewCtr.loadCategory();
    await cewCtr.getInfoRecCewAll();
    checking = false;
    print("checking $checking");
    setState(() {});
    _numLikeController.text =
        cewCtr.datasTypescew.any((item) => item.toString() == "บรรยากาศ")
            ? '0'
            : cewCtr.datasTypescew.any((item) => item.toString() == "ทั่วไป")
                ? '1000'
                : '';
  }

  @override
  void dispose() {
    cewCtr.resetData();
    _focusNode.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    print('didChangeAppLifecycleState');

    if (state == AppLifecycleState.paused) {
      print('Background');
      // App is in the background
      if (_backgroundTime != null) {
        final timeInBackground = DateTime.now().difference(_backgroundTime!);
        if (timeInBackground.inSeconds > 60) {
          print('timeInBackground : $timeInBackground');
          Get.offAllNamed('PinlockScreen');
        }
      }
    } else if (state == AppLifecycleState.resumed) {
      // App is in the foreground

      _backgroundTime = DateTime.now();
    }
  }


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            height: Get.height,
            decoration: BoxDecoration(
                gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Theme.of(context).colorScheme.primary.withOpacity(1),
                      Theme.of(context).colorScheme.onPrimary.withOpacity(1),
                ])),
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.only(top: 20.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    buildAppBar(context, 'ui_cewhead'.tr),
                    Stack(
                      children: [
                        Column(
                          children: [
                            buildForm(context),
                          ],
                        ),
                        Column(
                          children: [
                            Padding(
                              padding:
                                  const EdgeInsets.only(left: 120.0, top: 110),
                              child: buildShowResult(),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
          checking
              ? Container(
                  width: MediaQuery.of(context).size.width,
                  height: MediaQuery.of(context).size.height,
                  color: Colors.black12,
                  child: Center(
                    child: SizedBox(
                      width: 60.w,
                      child: const LoadingIndicator(
                        indicatorType: Indicator.lineSpinFadeLoader,
                        colors: [Colors.white],
                      ),
                    ),
                  ))
              : Container(),
        ],
      ),
    );
  }

  Widget buildForm(context) {
    return Container(
        padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
        width: double.infinity.w,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Obx(
              () => Column(
                children: [
                  builType(context),
                  buildRecive(context, key),
                  buildCewType(context),
                  buildInputDes(context),
                  buildUpload(context),
                  buildGiveLike(context),
                  buildDetail(context),
                  buildButton(context),
                ],
              ),
            ),
          ],
        ));
  }

  Widget builType(context) {
    return Obx(
      () => Container(
        padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 10.w),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                AppWidget.normalText(context, 'dp_cewtype'.tr, 14.sp,
                    Theme.of(context).colorScheme.onSecondary, FontWeight.w400)
              ],
            ),
            Padding(
              padding: const EdgeInsets.only(left: 14.0),
              child: InkWell(
                onTap: () {
                  setState(() {

                    cewCtr.toggleSelection(true);
                    statusshow = 1;
                    print("statusshow: $statusshow");
                    cewCtr.idPersonRecController.text = "";
                    cewCtr.buRec = "";
                    // cewCtr.detailController.text = "";
                    _numLikeController.text = "";
                    _valueCategory = "0";
                    _valueCounty = "0";
                    _valueType = "0";
                    cewCtr.idRec = "";
                    cewCtr.fullnameRec = "";
                    cewCtr.nicknameRec = "";
                  });
                },
                child: Container(
                  height: 34.h,
                  width: 125.w,
                  decoration: BoxDecoration(
                    color: cewCtr.isPersonSelected == true
                        ? Theme.of(context).colorScheme.secondaryContainer.withOpacity(0.6)
                        : Theme.of(context).colorScheme.primary,
                    borderRadius: BorderRadius.circular(8.r),
                    border: Border.all(
                      color: cewCtr.isPersonSelected == true
                          ? Theme.of(context).colorScheme.secondaryContainer.withOpacity(0.2)
                          : Theme.of(context).colorScheme.secondaryContainer.withOpacity(0.4),
                      width: 1.w,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      AppWidget.normalText(
                        context,
                        'btn_cewPerson'.tr,
                        14.sp,
                        cewCtr.isPersonSelected == true
                            ? Theme.of(context).colorScheme.onSecondary
                            : Theme.of(context).colorScheme.secondaryContainer,
                        cewCtr.isPersonSelected == true
                            ? FontWeight.w600
                            : FontWeight.w400,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            InkWell(
              onTap: () {
                setState(() {

                  // ErrorshowAlertDialog(
                  //     context, 'ยังไม่เปิดใช้งาน', "อยู่ในระหว่างการปรับปรุง");
                  //
                  cewCtr.toggleSelection(false);
                  statusshow = 2;
                  print("statusshow: $statusshow");
                  cewCtr.idPersonRecController.text = "";
                  cewCtr.buRec = "";
                  // cewCtr.detailController.text = "";
                  _numLikeController.text = "";
                  _valueCategory = "0";
                  _valueCounty = "0";
                  _valueType = "0";
                  cewCtr.idRec = "";
                  cewCtr.fullnameRec = "";
                  cewCtr.nicknameRec = "";
                });
              },
              child: Container(
                height: 34.h,
                width: 125.w,
                decoration: BoxDecoration(
                  color: cewCtr.isTeamSelected == true
                      ? Theme.of(context).colorScheme.secondaryContainer.withOpacity(0.6)
                      : Theme.of(context).colorScheme.primary,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: cewCtr.isTeamSelected == true
                        ? Theme.of(context).colorScheme.secondaryContainer.withOpacity(0.4)
                        : Theme.of(context).colorScheme.secondaryContainer.withOpacity(0.5),
                    width: 1.w,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    AppWidget.normalText(
                      context,
                      'ui_cewTeam'.tr,
                      14.sp,
                      cewCtr.isTeamSelected == true
                          ? Theme.of(context).colorScheme.onSecondary
                          : Theme.of(context).colorScheme.secondaryContainer,
                      cewCtr.isTeamSelected == true
                          ? FontWeight.w600
                          : FontWeight.w400,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildRecive(context, key) {
    return Stack(
      children: [
        Container(
          height: _focusNode.hasFocus ? 300.h : null,
          padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 10.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: EdgeInsets.only(top: 10.h),
                    height: _focusNode.hasFocus ? 240.h : 30.h,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        cewCtr.isPersonSelected == true
                            ? AppWidget.normalText(
                                context,
                                'ui_cewPersonRec'.tr,
                                14.sp,
                          Theme.of(context).colorScheme.onSecondary,
                                FontWeight.w400,
                              )
                            : AppWidget.normalText(
                                context,
                                'ui_cewTeamRec'.tr,
                                14.sp,
                          Theme.of(context).colorScheme.onSecondary,
                                FontWeight.w400,
                              ),
                      ],
                    ),
                  ),
                  cewCtr.isPersonSelected == true
                      ? Stack(
                          children: [
                            buildFindMember(),
                            cewCtr.statusshowAutoComplet == 1
                                ? buildAutoComplet()
                                : Container(),
                          ],
                        )
                      : Stack(
                    children: [
                      buildFindTeam(),
                      cewCtr.statusshowAutoComplet == 1
                          ? buildAutoCompletTeam()
                          : Container(),
                    ],
                  )

                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget buildCewType(context) {
    final seen = <String>{};
    final uniqueData = cewCtr.datasTypescew
        .where((e) => seen.add(e.value))
        .toList();

    if (!uniqueData.any((e) => e.value == _valueType)) {
      _valueType = uniqueData.isNotEmpty ? uniqueData.first.value : "0";
    }
    return Container(
      padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 10.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          SizedBox(
            width: 92.w,
            height: 42.h,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                AppWidget.normalText(
                  context,
                  'ui_cew_selcet'.tr,
                  14.sp,
                  Theme.of(context).colorScheme.onSecondary,
                  FontWeight.w400,
                ),
              ],
            ),
          ),
          Container(
            height: 42.h,
            width: 260.w,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Theme.of(context).colorScheme.onSecondaryContainer.withOpacity(1),
                width: 1.w,
              ),
            ),
            child: Padding(
              padding: EdgeInsets.only(
                  top: 5.h, bottom: 5.h, left: 20.w, right: 10.w),
              child: DropdownButton<String>(
                icon: Container(
                  child: SvgPicture.string(
                    '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M18 9L12 15L6 9" stroke="#5F569B"/></svg>',
                    allowDrawingOutsideViewBox: true,
                    fit: BoxFit.fitHeight,
                    color: Theme.of(context).colorScheme.onSecondary,
                    height: 25.h,
                  ),
                ),
                underline: Container(),
                dropdownColor: Theme.of(context).colorScheme.primary,
                items: uniqueData
                    .map((data) => DropdownMenuItem<String>(
                  value: data.value,
                  child: Container(
                    alignment: Alignment.center,
                    child: AppWidget.normalTextDynamic(
                      context,
                      data.key,
                      14.sp,
                      Theme.of(context).colorScheme.onSecondary,
                      FontWeight.w400,
                    ),
                  ),
                ))
                    .toList(),
                isExpanded: true,
                onChanged: (value) {
                  print(value);

                  cewCtr.updateCew(value, _numLikeController.text);
                  print(_numLikeController.text);

                  setState(() {
                    _valueType = value!;
                    cewCtr.updateSelectedCEWType(_valueType);
                    cewCtr.selectedCEWType.value == _valueType;
                  });

                  print(_valueType);
                  print(cewCtr.inputCEW.value);

                  print(cewCtr.selectedCEWType);
                },
                value: _valueType,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildInputDes(context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
      child: Row(
        children: [
          Expanded(
            child: Container(
              height: 100.h,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Theme.of(context).colorScheme.onSecondaryContainer.withOpacity(1),
                  width: 1.w,
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(10.0),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: 24.h,
                      width: 24.w,
                      child: Image.asset('assets/ADD/Message-bubble.png'),
                    ),
                    SizedBox(
                      width: 12.w,
                    ),
                    Expanded(
                      child: Container(
                        width: Get.width,
                        height: Get.height,
                        child: TextField(
                          cursorColor: Colors.black,
                          onChanged: (value) {
                            setState(() {
                              cewCtr.inputDes.text = value;
                            });
                          },
                          onEditingComplete: () {
                            // Save the entered text when editing is complete
                            // _saveText();
                          },
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Medium',
                            color: Theme.of(context).colorScheme.onSecondary,
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w400,
                          ),
                          decoration: InputDecoration.collapsed(
                            hintText: 'ui_cewinput'.tr,
                            hintStyle: TextStyle(
                              fontFamily: 'SukhumvitSet-Medium',
                              fontSize: 14.sp,
                              fontWeight: FontWeight.w400,
                              color: Theme.of(context).colorScheme.onSecondary,
                            ),
                          ),
                          maxLines: null, // Allow multiline input
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildUpload(context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 5.h),
      child: Row(
        children: [
          GestureDetector(
            onTap: (){
              AppWidget.selectImage(context);
            },
              child: Container(
                  padding: EdgeInsets.only(
                      top: 5.h, bottom: 5.h, left: 20.w, right: 15.w),
                  height: 50.h,
                  width: 353.w,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary,
                    borderRadius: BorderRadius.circular(8.r),
                    border: Border.all(
                      color: Theme.of(context).colorScheme.onSecondaryContainer.withOpacity(1),
                      width: 1.w,
                    ),
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      cewCtr.uploadedUrls.isEmpty
                          ? AppWidget.normalText(
                        context,
                        'ui_imgUpload'.tr,
                        14.sp,
                        Theme.of(context).colorScheme.onSecondary,
                        FontWeight.w400,
                      )
                          : ContainImg(),
                      SizedBox(
                        height: 18.h,
                        width: 18.w,
                        child: Image.asset('assets/ADD/Image.png'),
                      )
                    ],
                  )),
          ),
        ],
      ),
    );
  }

   // เพิ่มตรงนี้ด้านบน

  Widget ContainImg() {
    String? firstImgName;
    if (cewCtr.uploadedUrls.isNotEmpty) {
      firstImgName = path.basename(cewCtr.uploadedUrls.last); // แสดงชื่อภาพล่าสุด
    }

    return Container(
      width: 103.0.w,
      height: 28.0.h,
      padding: EdgeInsets.fromLTRB(10, 4, 4, 4),
      decoration: BoxDecoration(
        color: Color(0xFFFFDD77),
        borderRadius: BorderRadius.circular(20.0),
      ),
      child: Center(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            SizedBox(
              width: 63.w,
              height: 18.h,
              child: Text(
                firstImgName ?? "ยังไม่มีรูป", // แสดงชื่อภาพ หรือข้อความ fallback
                style: TextStyle(color: Colors.black),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
            SizedBox(
              width: 20.w,
              height: 20.h,
              child: InkWell(
                onTap: () {
                  cewCtr.cleardataIMG();
                  print(cewCtr.ImgName);
                },
                child: Image.asset('assets/ADD/X_button.png'),
              ),
            ),
          ],
        ),
      ),
    );
  }


  Widget buildGiveLike(context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 5.h),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                height: 55.h,
                width: 172.w,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.onSecondaryContainer.withOpacity(1),
                    width: 1.w,
                  ),
                ),
                child: Column(
                  children: [
                    TextField(
                      cursorColor: Colors.black,

                      readOnly: cewCtr.selectedCEWType.value != 'แผนงาน',
                      textAlign: TextAlign.center,
                      keyboardType: TextInputType.number,
                      controller: _numLikeController,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'[.0-9]'))
                      ],
                      onChanged: (value) {
                        print(value);
                        setState(() {});

                        if (cewCtr.selectedCEWType.value == 'แผนงาน') {
                          cewCtr.inputCEW.value = value;
                          cewCtr.inputLike.value = value;
                        } else {
                          _numLikeController.text = '';
                          _numLikeController.text = cewCtr.inputCEW.value;
                        }
                        print("_numLikeController.text");

                        print(_numLikeController.text);
                      },
                      style: TextStyle(
                        fontFamily: 'SukhumvitSet-Text',
                        fontSize: 12.sp,
                        color: Theme.of(context).colorScheme.onSecondary,
                        letterSpacing: 0.70.w,
                      ),
                      decoration: InputDecoration(
                        counterText: '',
                        hintText: cewCtr.selectedCEWType.value == 'แผนงาน' ||
                                cewCtr.inputCEW.value == ''
                            ? "tf_cewNumLike".tr.toString()
                            : cewCtr.inputCEW.value,
                        hintStyle: TextStyle(
                          fontFamily: 'SukhumvitSet-Text',
                          fontSize: 12.sp,
                          color: Theme.of(context).colorScheme.onSecondary,
                          letterSpacing: 0.70.w,
                        ),
                        enabledBorder: const UnderlineInputBorder(
                          borderSide:
                              const BorderSide(color: Colors.transparent),
                        ),
                        focusedBorder: const UnderlineInputBorder(
                          borderSide:
                              const BorderSide(color: Colors.transparent),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                height: 52.h,
                width: 172.w,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.onSecondaryContainer.withOpacity(1),
                    width: 1.w,
                  ),
                ),
                child: Padding(
                  padding: EdgeInsets.only(
                      top: 5.h, bottom: 5.h, left: 20.w, right: 10.w),
                  child: DropdownButton<String>(
                    icon: Container(
                      child: SvgPicture.string(
                        '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M18 9L12 15L6 9" stroke="#FBFBFB"/></svg>',
                        allowDrawingOutsideViewBox: true,
                        fit: BoxFit.fitHeight,
                        height: 25.h,
                        color: Theme.of(context).colorScheme.onSecondary,
                      ),
                    ),
                    underline: Container(),
                    dropdownColor: Theme.of(context).colorScheme.primary,
                    items: cewCtr.datasCategory
                        .map((data) => DropdownMenuItem<String>(
                      value: data.value,
                      child: Container(
                        alignment: Alignment.center,
                        child: AppWidget.normalTextDynamic(
                          context,
                          data.key,
                          14.sp,
                          Theme.of(context).colorScheme.onSecondary,
                          FontWeight.w400,
                        ),
                      ),
                    ))
                        .toList(),
                    isExpanded: true,
                    onChanged: (value) {
                      print("เลือกหมวดหมู่: $value");
                      setState(() {
                        _valueCategory = value ?? "0"; // fallback ถ้า null
                      });
                    },
                    value: cewCtr.datasCategory.any((e) => e.value == _valueCategory)
                        ? _valueCategory
                        : null, // fallback กลับไป default ถ้าไม่ match
                  ),
                ),
              ),
            ],
          ),
          Container(
            padding: EdgeInsets.symmetric(vertical: 10.h),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                SizedBox(
                  width: 172.w,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      AppWidget.normalText(
                        context,
                        'ui_cewDetailAAM'.tr,
                        14.sp,
                        Theme.of(context).colorScheme.secondaryContainer,
                        FontWeight.w500,
                      ),
                      AppWidget.normalText(
                        context,
                        'ui_cewDetailAAM2'.tr,
                        12.sp,
                        Theme.of(context).colorScheme.secondaryFixedDim,
                        FontWeight.w400,
                      ),
                    ],
                  ),
                ),
                Container(
                  height: 47.h,
                  width: 172.w,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Theme.of(context).colorScheme.onSecondaryContainer.withOpacity(1),
                      width: 1.w,
                    ),
                  ),
                  child: Padding(
                    padding: EdgeInsets.only(
                        top: 5.h, bottom: 5.h, left: 20.w, right: 10.w),
                    child: DropdownButton<String>(
                      icon: Container(
                        child: SvgPicture.string(
                          '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M18 9L12 15L6 9" stroke="#FBFBFB"/></svg>',
                          allowDrawingOutsideViewBox: true,
                          fit: BoxFit.fitHeight,
                          height: 25.h,
                        ),
                      ),
                      underline: Container(),
                      dropdownColor: Theme.of(context).colorScheme.primary,
                      items: cewCtr.datasCounty
                          .map((data) => DropdownMenuItem<String>(
                                child: Container(
                                  alignment: Alignment.center,
                                  child: AppWidget.normalTextDynamic(
                                    context,
                                    data.key,
                                    14.sp,
                                    Theme.of(context).colorScheme.onSecondary,
                                    FontWeight.w400,
                                  ),
                                ),
                                value: data.value,
                              ))
                          .toList(),
                      isExpanded: true,
                      onChanged: (value) {
                        print(value);
                        setState(() {
                          _valueCounty = value ?? "0";
                        });
                      },
                      value: cewCtr.datasCounty.any((e) => e.value == _valueCounty)
                          ? _valueCounty
                          : null,

                    ),
                  ),
                ),
              ],
            ),
          ),
          Container(
            height: 0.5.h,
            decoration: BoxDecoration(
              color: const Color(0xFFFCF6E4).withOpacity(0.2),
              border: Border.all(
                width: 0.5.w,
                color: const Color(0xFFFCF6E4).withOpacity(0.2),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildDetail(context) {
    String formattedDate = DateFormat('dd-MM-yyyy').format(DateTime.now());
    return Container(
      padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppWidget.normalText(
            context,
            'ui_cewDetailCew'.tr,
            14.sp,
            Theme.of(context).colorScheme.onSecondary,
            FontWeight.w600,
          ),
          SizedBox(
            height: 20.h,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              AppWidget.normalText(
                context,
                'ui_cewPersonRec2'.tr,
                14.sp,
                Theme.of(context).colorScheme.onSecondary,
                FontWeight.w400,
              ),
              cewCtr.isTeamSelected == true
                  ? AppWidget.normalText(
                      context,
                _valueTeamRec.toString() == "0"
                          ? '-'
                          : _valueTeamRec.toString(),
                      14.sp,
                      Theme.of(context).colorScheme.onSecondary,
                      FontWeight.w400,
                    )
                  : AppWidget.normalText(
                      context,
                      cewCtr.idPersonRecController.text == ""
                          ? '-'
                          : idRec.toString() +
                              " " +
                              fullnameRec.toString()
                          +
                          " " +
                          " (" +
                  cewCtr.nicknameRec.toString() +
                  ")",
                      14.sp,
                Theme.of(context).colorScheme.onSecondary,
                      FontWeight.w400,
                    ),
            ],
          ),
          SizedBox(
            height: 5.h,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              AppWidget.normalText(
                context,
                'ui_cewNumLike'.tr,
                14.sp,
                const Color(0xFF7295ff),
                FontWeight.w400,
              ),
              Row(
                children: [
                  AppWidget.normalTextRX(
                    context,
                    cewCtr.inputLike.value.obs.isNotEmpty
                        ? cewCtr.inputLike.value.obs
                        : '0'.obs,
                    14.sp,
                    const Color(0xFF7295ff),
                    FontWeight.w400,
                  ),
                  SizedBox(
                    width: 5.w,
                  ),
                  AppWidget.normalText(
                    context,
                    'ui_cewUnitLike'.tr,
                    14.sp,
                    Theme.of(context).colorScheme.onSecondary.withOpacity(0.6),
                    FontWeight.w400,
                  ),
                ],
              )
            ],
          ),
          SizedBox(
            height: 5.h,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              AppWidget.normalText(
                context,
                'ui_cewTypeCategory'.tr,
                14.sp,
                Theme.of(context).colorScheme.onSecondary.withOpacity(0.6),
                FontWeight.w400,
              ),
              AppWidget.normalText(
                context,
                _valueCategory == "" ? "-" : _valueCategory,
                14.sp,
                Theme.of(context).colorScheme.onSecondary.withOpacity(0.6),
                FontWeight.w400,
              ),
            ],
          ),
          SizedBox(
            height: 5.h,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              AppWidget.normalText(
                context,
                'ui_cewGiveCew'.tr,
                14.sp,
                Theme.of(context).colorScheme.onSecondary.withOpacity(0.6),
                FontWeight.w400,
              ),
              AppWidget.normalText(
                context,
                profileCtr.responseMember!.id! +
                    " " +
                    profileCtr.responseMember!.full_name_th!,
                14.sp,
                Theme.of(context).colorScheme.onSecondary.withOpacity(0.6),
                FontWeight.w400,
              ),
            ],
          ),
          SizedBox(
            height: 5.h,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              AppWidget.normalText(
                context,
                'ui_cewDate'.tr,
                14.sp,
                Theme.of(context).colorScheme.onSecondary.withOpacity(0.6),
                FontWeight.w400,
              ),
              AppWidget.normalText(
                context,
                '${formattedDate}',
                14.sp,
                Theme.of(context).colorScheme.onSecondary.withOpacity(0.6),
                FontWeight.w400,
              ),
            ],
          ),
        ],
      ),
    );
  }


  bool isCewInputValid() {
    if ((cewCtr.isPersonSelected == true && cewCtr.idPersonRecController.text.isEmpty)
        || (cewCtr.isPersonSelected != true && (_valueTeamRec == null || _valueTeamRec == "0"))
    ) {
      return false;
    }

    if (cewCtr.isPersonSelected == true && buRec.toString().isEmpty) {
      return false;
    }

    if (cewCtr.inputLike.value.isEmpty) {
      return false;
    }

    if (_valueCategory.toString().isEmpty) {
      return false;
    }

    if (_valueCounty.toString() == "0") {
      return false;
    }

    if (cewCtr.isPersonSelected == true && typeRoleGive.toString().isEmpty) {
      return false;
    }

    return true;
  }



  bool validateCewInput(BuildContext context) {
    final missingFields = <String>[];

    // เช็คเงื่อนไขทีละรายการ
    if ((cewCtr.isPersonSelected == true && cewCtr.idPersonRecController.text.isEmpty)
        || (cewCtr.isPersonSelected != true && (_valueTeamRec == null || _valueTeamRec == "0"))
      ) {
      missingFields.add("ผู้รับคะแนน");
    }

    if (cewCtr.isPersonSelected == true && buRec.toString().isEmpty) {
      missingFields.add("BU (หน่วยงาน)");
    }
    print(buRec.toString());

    if (cewCtr.inputLike.value.isEmpty) {
      missingFields.add("คะแนนที่ต้องการให้");
    }

    if (_valueCategory.toString().isEmpty) {
      missingFields.add("หมวดหมู่กิจกรรม");
    }

    if (_valueCounty.toString() == "0") {
      missingFields.add("สาขา");
    }



    if (cewCtr.isPersonSelected == true && typeRoleGive.toString().isEmpty) {
      missingFields.add("บทบาทผู้ให้");
    }


print(typeRoleGive.toString());

    if (missingFields.isNotEmpty) {
      Get.snackbar(
        "กรุณากรอกข้อมูลให้ครบถ้วน!",
        "ขาด: ${missingFields.join(", ")}",
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }

    return true;
  }


  Widget buildButton(BuildContext context) {
    final isValid = isCewInputValid(); // ✅ ใช้ซ้ำ

    return Container(
      child: Align(
        alignment: Alignment.bottomCenter,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          width: double.infinity.w,
          child: InkWell(
            onTap: () {
              print(cewCtr.idPersonRecController.text);
              print(buRec);
              print(cewCtr.inputLike.value.obs);
              print(_valueCategory);
              print(_valueCounty);
              print(profileCtr.responseMember!.id! +
                  " " +
                  profileCtr.responseMember!.full_name_th!);
              print(typeRoleGive);
              print(cewCtr.imagePath.value);
              if ((  cewCtr.isPersonSelected != true ? cewCtr.idPersonRecController.text == "" :  _valueTeamRec == "0") ||
                  cewCtr.isPersonSelected == true && buRec.toString() == "" ||
                  cewCtr.inputLike.value.obs == "" ||
                  _valueCategory.toString() == "" ||

                  _valueCounty.toString() == "0" ||
                  profileCtr.responseMember!.id! +
                          " " +
                          profileCtr.responseMember!.full_name_th! ==
                      "" ||
                  cewCtr.isPersonSelected == true &&    typeRoleGive.toString() == "") {

              }
                if (validateCewInput(context)) {
                  showCupertinoModalPopup(
                    context: context,
                    builder: (BuildContext context) {
                      return buildShowSheet(context);
                    },
                  );



              } else {
                // showCupertinoModalPopup(
                //   context: context,
                //   builder: (BuildContext context) {
                //     return buildShowSheet(context);
                //   },
                // );
              }
            },
            child: Container(
              width: 353.w,
              height: 50.h,
              decoration: BoxDecoration(
          color: isValid
                  ? Theme.of(context).colorScheme.secondaryContainer.withOpacity(0.6)
                    : Theme.of(context).colorScheme.secondaryContainer.withOpacity(0.2),

                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isValid
                      ? Theme.of(context).colorScheme.secondaryContainer
                      : Theme.of(context).colorScheme.secondaryContainer.withOpacity(0.2),
                  width: 1.w,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AppWidget.normalText(
                    context,
                    'btn_continew'.tr,
                    16.sp,
                    const Color(0xFFF6F6F6),
                    FontWeight.w900,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget buildFindMember() {
    return Stack(
      children: [
        Container(
          alignment: Alignment.center,
          width: mediaQuery(context, "w", 550),
          height: mediaQuery(context, "h", 90),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary,
            borderRadius: BorderRadius.circular(8),
            // border: Border.all(
            //   color: Theme.of(context).colorScheme.onSecondaryContainer.withOpacity(1),
            //   width: 1.w,
            // ),
          ),
          child: new TextField(
            cursorColor: Colors.black,

            textAlign: TextAlign.center,
            onChanged: (value) {
              setState(() {
                cewCtr.statusshowAutoComplet = 1;
                keyword = value;
              });
              print("keyword: $keyword");
            },
            controller: cewCtr.idPersonRecController,
            //                controller: ctrlUsername,
            style: new TextStyle(
              fontFamily: 'SukhumvitSet-Text',
              fontSize: mediaQuery(context, "h", 28),
              color: Theme.of(context).colorScheme.onSecondary.withOpacity(0.7),
              letterSpacing: mediaQuery(context, "h", 1.4000000000000001),
            ),
            decoration: new InputDecoration(
              counterText: '',
              //                errorText: _validate1 ? 'กรุณากรอกข้อมูล' : _checktext1 ? 'กรุณากรอกข้อมูลให้ครบ 4 หลัก' : null,
              //                      fillColor: Colors.red,
              hintText: "tf_cewIdPerson".tr.toString(),
              hintStyle: TextStyle(
                fontFamily: 'SukhumvitSet-Text',
                fontSize: mediaQuery(context, "h", 28),
                color: Theme.of(context).colorScheme.onSecondary.withOpacity(0.7),
              ),

              //                  icon: Image.asset(pictel),
              enabledBorder: const UnderlineInputBorder(
                borderSide: const BorderSide(color: Colors.transparent),
              ),
              focusedBorder: const UnderlineInputBorder(
                borderSide: const BorderSide(color: Colors.transparent),
              ),
            ),
          ),
        ),
      ],
    );
  }


  Widget buildFindTeam() {
    return Stack(
      children: [
        Container(
          alignment: Alignment.center,
          width: mediaQuery(context, "w", 550),
          height: mediaQuery(context, "h", 90),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary,
            borderRadius: BorderRadius.circular(8),
            // border: Border.all(
            //   color: Theme.of(context).colorScheme.onSecondaryContainer.withOpacity(1),
            //   width: 1.w,
            // ),
          ),
          child: new TextField(
            cursorColor: Colors.black,

            textAlign: TextAlign.center,
            onChanged: (value) {
              setState(() {
                cewCtr.statusshowAutoComplet = 1;
                keyword = value;
              });
              print("keyword: $keyword");
            },
            controller: cewCtr.idPersonRecController,
            //                controller: ctrlUsername,
            style: new TextStyle(
              fontFamily: 'SukhumvitSet-Text',
              fontSize: mediaQuery(context, "h", 28),
              color: Theme.of(context).colorScheme.onSecondary.withOpacity(0.7),
              letterSpacing: mediaQuery(context, "h", 1.4000000000000001),
            ),
            decoration: new InputDecoration(
              counterText: '',
              //                errorText: _validate1 ? 'กรุณากรอกข้อมูล' : _checktext1 ? 'กรุณากรอกข้อมูลให้ครบ 4 หลัก' : null,
              //                      fillColor: Colors.red,
              hintText: "dp_cewTeam".tr.toString(),
              hintStyle: TextStyle(
                fontFamily: 'SukhumvitSet-Text',
                fontSize: mediaQuery(context, "h", 28),
                color: Theme.of(context).colorScheme.onSecondary.withOpacity(0.7),
              ),

              //                  icon: Image.asset(pictel),
              enabledBorder: const UnderlineInputBorder(
                borderSide: const BorderSide(color: Colors.transparent),
              ),
              focusedBorder: const UnderlineInputBorder(
                borderSide: const BorderSide(color: Colors.transparent),
              ),
            ),
          ),
        ),
      ],
    );
  }




  Widget buildAutoCompletTeam() {
    return GestureDetector(
      onTap: () {
        setState(() {
          cewCtr.statusshowAutoComplet = 0;
        });
      },
      child: Container(
        color: Colors.transparent,
        child: Container(
          margin: EdgeInsets.only(top: mediaQuery(context, "h", 100)),
          padding: EdgeInsets.only(
            top: mediaQuery(context, "h", 20),
          ),
          width: mediaQuery(context, "w", 550),
          height: mediaQuery(context, "h", 450),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary,
            borderRadius: BorderRadius.circular(8),
            // border: Border.all(
            //   color: Theme.of(context).colorScheme.onSecondaryContainer.withOpacity(1),
            //   width: 1.w,
            // ),
          ),
          child: ListView(
            padding: EdgeInsets.only(top: 0),
            children: <Widget>[
              new Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: autoCompletRecCewTeam(),
              ),
            ],
          ),
        ),
      ),
    );
  }
  List<Widget> autoCompletRecCewTeam() {
    List<Widget> list = [];

    for (var i = 0; i < cewCtr.datasTeamRec.length; i++) {
      final dataName = cewCtr.datasTeamRec[i].key;
      final dataId = cewCtr.datasTeamRec[i].value;

      if (dataName.toLowerCase().contains(keyword.toLowerCase())) {
        list.add(GestureDetector(
          onTap: () {
            setState(() {
              cewCtr.statusshowAutoComplet = 0;
              cewCtr.idPersonRecController.text = "$dataId $dataName";
              _valueTeamRec = "$dataId $dataName";
              idRec = dataId;
              lnameRec = dataName;
              fullnameRec = dataName;
              nicknameRec = "-";
              buRec = "-";
              tokenLineRec = "-";
              typeRoleGive = "-";
            });
          },
          child: Container(
            color: Colors.transparent,
            padding: EdgeInsets.only(
              top: mediaQuery(context, "h", 15),
              bottom: mediaQuery(context, "h", 15),
              left: mediaQuery(context, "w", 61),
            ),
            child: Text(
              "$dataId $dataName",
              style: TextStyle(
                fontFamily: 'SukhumvitSet-Text',
                fontSize: mediaQuery(context, "h", 28),
                color: Theme.of(context).colorScheme.onSecondary,
                letterSpacing: mediaQuery(context, "h", 1.4),
              ),
              textAlign: TextAlign.left,
            ),
          ),
        ));
      }
    }

    return list;
  }





  Widget buildAutoComplet() {
    return GestureDetector(
      onTap: () {
        setState(() {
          cewCtr.statusshowAutoComplet = 0;
        });
      },
      child: Container(
        color: Colors.transparent,
        child: Container(
          margin: EdgeInsets.only(top: mediaQuery(context, "h", 100)),
          padding: EdgeInsets.only(
            top: mediaQuery(context, "h", 20),
          ),
          width: mediaQuery(context, "w", 550),
          height: mediaQuery(context, "h", 450),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary,
            borderRadius: BorderRadius.circular(8),
            // border: Border.all(
            //   color: Theme.of(context).colorScheme.onSecondaryContainer.withOpacity(1),
            //   width: 1.w,
            // ),
          ),
          child: ListView(
            padding: EdgeInsets.only(top: 0),
            children: <Widget>[
              new Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: autoCompletRecCew(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<Widget> autoCompletRecCew() {
    List<Widget> list = [];
    for (var i = 0; i < cewCtr.dataInfoRecCewAll.length.toInt(); i++) {
      dataName = cewCtr.dataInfoRecCewAll[i]["id"].toString() +
          " " +
          cewCtr.dataInfoRecCewAll[i]["name_th"].toString() +
          " " +
          cewCtr.dataInfoRecCewAll[i]["surname_th"].toString() +
          " " +
          cewCtr.dataInfoRecCewAll[i]["nickname"].toString();
      print(cewCtr.dataInfoRecCewAll[i]["company_management"].toString());
      print(dataName);
      print(dataName.indexOf(keyword.toString()));
      print(keyword.toString());

      if (dataName.indexOf(keyword.toString()) > -1) {
        print(dataName.indexOf(keyword.toString()));

        list.add(GestureDetector(
          onTap: () {
            setState(() {
              cewCtr.statusshowAutoComplet = 0;
              cewCtr.idPersonRecController.text =
                  cewCtr.dataInfoRecCewAll[i]["id"].toString() +
                      " " +
                      cewCtr.dataInfoRecCewAll[i]["name_th"].toString() +
                      " " +
                      cewCtr.dataInfoRecCewAll[i]["surname_th"].toString();
              idRec = cewCtr.dataInfoRecCewAll[i]["id"].toString();
              lnameRec = cewCtr.dataInfoRecCewAll[i]["name_th"].toString();
              fullnameRec = cewCtr.dataInfoRecCewAll[i]["name_th"].toString() +
                  " " +
                  cewCtr.dataInfoRecCewAll[i]["surname_th"].toString();
              nicknameRec = cewCtr.dataInfoRecCewAll[i]["nickname"].toString();
              buRec = cewCtr.dataInfoRecCewAll[i]["company_ctt"].toString();
              tokenLineRec =
                  cewCtr.dataInfoRecCewAll[i]["token_Line"].toString();
              typeRoleGive =
                  cewCtr.dataInfoRecCewAll[i]["Type_role"].toString();
            });
          },
          child: Container(
            color: Colors.transparent,
            padding: EdgeInsets.only(
                top: mediaQuery(context, "h", 15),
                bottom: mediaQuery(context, "h", 15),
                left: mediaQuery(context, "w", 61)),
            child: Text(
              cewCtr.dataInfoRecCewAll[i]["id"].toString() +
                  " " +
                  cewCtr.dataInfoRecCewAll[i]["name_th"].toString() +
                  " " +
                  cewCtr.dataInfoRecCewAll[i]["surname_th"].toString(),
              style: TextStyle(
                fontFamily: 'SukhumvitSet-Text',
                fontSize: mediaQuery(context, "h", 28),
                color: Theme.of(context).colorScheme.onSecondary,
                letterSpacing: mediaQuery(context, "h", 1.4000000000000001),
              ),
              textAlign: TextAlign.left,
            ),
          ),
        ));
      }
    }
    return list;
  }

  Widget buildShowResult() {
    // Filter items based on whether they match the search text
    RxList<Map<String, String>> matchingItems = cewCtr.sampleData
        .where((item) => item["id"]!
            .toLowerCase()
            .contains(cewCtr.searchController.text.toLowerCase()))
        .toList()
        .obs; // This line is not needed

    // Calculate the height based on the number of matching items
    double containerHeight = cewCtr.searchController.text == ''
        ? 0.0
        : matchingItems.length * 50.0.h;

    return cewCtr.expandCew
        ? Container()
        : AnimatedContainer(
            duration: Duration(milliseconds: 300),
            height: 100,
            width: 260.0.w,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.onSecondary,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Obx(() => ListView.builder(
                  itemCount: matchingItems.length,
                  itemBuilder: (context, index) {
                    return InkWell(
                      onTap: () {
                        setState(() {
                          cewCtr.updatesearchController(
                              "${matchingItems[index]["id"]} ${matchingItems[index]["name"]} ${matchingItems[index]["lastname"]}");
                          cewCtr.expandCew = true;
                        });
                      },
                      child: ListTile(
                        title: AppWidget.normalText(
                            context,
                            'ID: ${matchingItems[index]["id"]} - ${matchingItems[index]["name"]} ${matchingItems[index]["lastname"]}',
                            14.sp,
                            whiteColor,
                            FontWeight.w400),
                        // You can add more content or customize the ListTile as needed
                      ),
                    );
                  },
                )),
          );
  }

  Widget buildShowSheet(BuildContext context) {
    String formattedDate = DateFormat('dd-MM-yyyy').format(DateTime.now());
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: 20.h,
      ),
      height: 396.h,
      width: 392.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(10.r),
          topRight: Radius.circular(10.r),
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF000000)
                .withOpacity(0.85), // เปลี่ยนค่า opacity ตามต้องการ
            offset: const Offset(0, 4),
            blurRadius: 10,
            spreadRadius: 0,
          ),
        ],
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.onPrimary,
          ],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            width: Get.width.w,
            padding: EdgeInsets.symmetric(vertical: 30.h, horizontal: 20.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: EdgeInsets.only(bottom: 15.h),
                  child: AppWidget.normalText(
                    context,
                    'ui_cewDetailCew'.tr,
                    14.sp,
                    Theme.of(context).colorScheme.onSecondary,
                    FontWeight.w600,
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    AppWidget.normalText(
                      context,
                      'ui_cewPersonRec2'.tr,
                      14.sp,
                      Theme.of(context).colorScheme.onSecondary,
                      FontWeight.w400,
                    ),
                    cewCtr.isTeamSelected == true
                        ? AppWidget.normalText(
                      context,
                      _valueTeamRec.toString() == "0"
                          ? '-'
                          : _valueTeamRec.toString() +
                          " " +
                          " (" +
                          cewCtr.nicknameRec.toString() +
                          ")",
                      14.sp,
                      Theme.of(context).colorScheme.onSecondary,
                      FontWeight.w400,
                    )
                        : AppWidget.normalText(
                      context,
                      cewCtr.idPersonRecController.text == ""
                          ? '-'
                          : idRec.toString() +
                          " " +
                          fullnameRec.toString() +
                          " (" +
                          nicknameRec.toString() +
                          ")",
                      14.sp,
                      Theme.of(context).colorScheme.onSecondary,
                      FontWeight.w400,
                    ),
                  ],
                ),
                SizedBox(
                  height: 5.h,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    AppWidget.normalText(
                      context,
                      'ui_cewNumLike'.tr,
                      14.sp,
                      AppColors.BlueSky,
                      FontWeight.w400,
                    ),
                    Row(
                      children: [
                        AppWidget.normalTextRX(
                          context,
                          cewCtr.inputLike.value.obs.isNotEmpty
                              ? cewCtr.inputLike.value.obs
                              : '0'.obs,
                          14.sp,
                          AppColors.BlueSky,
                          FontWeight.w400,
                        ),
                        SizedBox(
                          width: 5.w,
                        ),
                        AppWidget.normalText(
                          context,
                          'ui_cewUnitLike'.tr,
                          14.sp,
                          Theme.of(context).colorScheme.onSecondary,
                          FontWeight.w400,
                        ),
                      ],
                    )
                  ],
                ),
                SizedBox(
                  height: 5.h,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    AppWidget.normalText(
                      context,
                      'ui_cewTypeCategory'.tr,
                      14.sp,
                      Theme.of(context).colorScheme.onSecondary,
                      FontWeight.w400,
                    ),
                    AppWidget.normalText(
                      context,
                      _valueCategory == "" ? "-" : _valueCategory,
                      14.sp,
                      Theme.of(context).colorScheme.onSecondary,
                      FontWeight.w400,
                    ),
                  ],
                ),
                SizedBox(
                  height: 5.h,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    AppWidget.normalText(
                      context,
                      'ui_cewGiveCew'.tr,
                      14.sp,
                      Theme.of(context).colorScheme.onSecondary.withOpacity(0.6),
                      FontWeight.w400,
                    ),
                    AppWidget.normalText(
                      context,
                      profileCtr.responseMember!.id! +
                          " " +
                          profileCtr.responseMember!.full_name_th!,
                      14.sp,
                      Theme.of(context).colorScheme.onSecondary.withOpacity(0.6),
                      FontWeight.w400,
                    ),
                  ],
                ),
                SizedBox(
                  height: 5.h,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    AppWidget.normalText(
                      context,
                      'ui_cewDate'.tr,
                      14.sp,
                      Theme.of(context).colorScheme.onSecondary.withOpacity(0.6),
                      FontWeight.w400,
                    ),
                    AppWidget.normalText(
                      context,
                      '${formattedDate}',
                      14.sp,
                      Theme.of(context).colorScheme.onSecondary.withOpacity(0.6),
                      FontWeight.w400,
                    ),
                  ],
                ),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(vertical: 10.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                InkWell(
                  onTap: () {
                    _numLikeController.text = cewCtr.inputLike.value;
                    // print("idRec: $idRec");
                    // print("buRec: $buRec");
                    // print("typeRoleGive: $typeRoleGive");
                    cewCtr.saveCew(
                        context,
                        statusshow.toString(),
                        _valueType,
                        idRec,
                        buRec,
                        fullnameRec,
                        typeRoleGive,
                        cewCtr.inputDes.text
                            .toString(), // If inputDes is a TextEditingController
                        _valueCategory,
                        _valueCounty,
                        _numLikeController.text // If inputLike is an RxString
                        );
                    alertSuccess();

                  },
                  child: Container(
                      width: 353.w,
                      height: 50.h,
                      decoration: BoxDecoration(
                        borderRadius:
                            const BorderRadius.all(Radius.circular(10)),
                        color: Theme.of(context).colorScheme.secondaryContainer.withOpacity(0.7),
                        border: Border.all(
                          width: 1.w,
                          color: Theme.of(context).colorScheme.onSecondaryContainer,
                        ),
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          AppWidget.normalText(
                            context,
                            'ui_cewConfirm'.tr,
                            16.sp,
                            Theme.of(context).colorScheme.onSecondary,
                            FontWeight.w900,
                          ),
                        ],
                      )),
                ),
                SizedBox(
                  height: 20.h,
                ),
                InkWell(
                    onTap: () {
                      setState(() {
                        Get.back();
                      });
                    },
                    child: SizedBox(
                        height: 30.h,
                        width: 71.w,
                        child: Center(
                          child: AppWidget.normalText(
                            context,
                            'btn_cancel'.tr,
                            16.sp,
                            Theme.of(context).colorScheme.onSecondary,
                            FontWeight.w400,
                          ),
                        )))
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget alertSuccess() {
    print(cewCtr.statusAlertSuccess);
    if (cewCtr.statusAlertSuccess.obs.toString() == "1") {
      return ClipRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 1.97, sigmaY: 1.97),
          child: Container(
            alignment: Alignment.topCenter,
            decoration: BoxDecoration(
              color: const Color(0x00bebebe),
            ),
            child: Container(
              width: mediaQuery(context, "w", 520),
              height: mediaQuery(context, "h", 550),
              margin: EdgeInsets.only(top: mediaQuery(context, "h", 300)),
              decoration: BoxDecoration(
                borderRadius:
                BorderRadius.circular(mediaQuery(context, "h", 80)),
                color: const Color(0xfffcf6e4),
              ),
              child: Stack(
                children: [
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 82)),
                    child: SvgPicture.string(
                      '<svg viewBox="163.0 0.0 50.0 50.0" ><path transform="translate(160.43, -1.93)" d="M 27.57070350646973 6.61553430557251 C 22.14501762390137 6.61553430557251 17.04413795471191 8.728425025939941 13.20761585235596 12.56494903564453 C 9.371090888977051 16.40147399902344 7.25820255279541 21.50235176086426 7.25820255279541 26.92803764343262 C 7.25820255279541 32.35371780395508 9.371092796325684 37.4546012878418 13.20761680603027 41.2911262512207 C 17.04413986206055 45.12765121459961 22.14501953125 47.24053955078125 27.57070350646973 47.24053955078125 C 32.99638748168945 47.24053955078125 38.09726333618164 45.12765121459961 41.93379211425781 41.2911262512207 C 45.77031326293945 37.4546012878418 47.88320541381836 32.35372161865234 47.88320541381836 26.92803764343262 C 47.88320541381836 21.50235176086426 45.77031326293945 16.40147399902344 41.933837890625 12.56494903564453 C 38.09736251831055 8.728425025939941 32.99638748168945 6.61553430557251 27.57070350646973 6.61553430557251 Z M 27.57070350646973 1.928033590316772 C 41.37783050537109 1.928033590316772 52.57070159912109 13.12090492248535 52.57070159912109 26.92803764343262 C 52.57070159912109 40.73516845703125 41.37783050537109 51.92803955078125 27.57070350646973 51.92803955078125 C 13.76357555389404 51.92803955078125 2.570701122283936 40.73516845703125 2.570701122283936 26.92803955078125 C 2.570701122283936 13.12090969085693 13.76357173919678 1.928035974502563 27.57070350646973 1.928035974502563 Z M 24.44570159912109 36.30303955078125 L 30.69570350646973 36.30303955078125 L 30.69570350646973 42.55304336547852 L 24.44570159912109 42.55304336547852 L 24.44570159912109 36.30303955078125 Z M 24.44570159912109 11.30303478240967 L 30.69570350646973 11.30303478240967 L 30.69570350646973 30.05303573608398 L 24.44570159912109 30.05303573608398 L 24.44570159912109 11.30303478240967 Z" fill="#934d0f" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                      allowDrawingOutsideViewBox: true,
                      fit: BoxFit.fitHeight,
                      height: mediaQuery(context, "h", 50),
                    ),
                  ),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 155)),
                    child:  AppWidget.normalText(
                      context,
                      'ui_cewSuccess'.tr,
                      12.sp,
                      const Color(0xff302c49),
                      FontWeight.w700,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 210)),
                    child: AppWidget.normalText(
                      context,
                        "ui_cewSuccessDetail".tr.toString() +
                            " " +
                            idRec.toString() +
                            " " +
                            lnameRec.toString() +
                            "ui_cewSuccessDetail2".tr.toString(),
                      12.sp,
                      const Color(0xff302c49),
                      FontWeight.w500,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 379)),
                    child: GestureDetector(
                      onTap: () {
                        Navigator.of(context).pop();
                      },
                      child: Container(
                        alignment: Alignment.center,
                        width: mediaQuery(context, "w", 250),
                        height: mediaQuery(context, "h", 90),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(
                              mediaQuery(context, "h", 30)),
                          color: const Color(0xff7f420c),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0x1a000000),
                              offset: Offset(0, 1),
                              blurRadius: 2,
                            ),
                          ],
                        ),
                        child: AppWidget.normalText(
                          context,
                          "ui_cewSuccessok".tr.toString(),
                          12.sp,
                          const Color(0xff302c49),
                          FontWeight.w700,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    } else {
      return Container();
    }
  }

}
