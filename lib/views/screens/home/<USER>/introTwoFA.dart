import 'dart:io';

import 'package:flutter/material.dart';
// import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/yubikey.dart';
import 'package:uuid/uuid.dart';

class IntroTwoFA extends StatefulWidget {
  // final ChromeSafariBrowser browser = new ChromeSafariBrowser();
  @override
  _IntroTwoFAState createState() => _IntroTwoFAState();
}

class _IntroTwoFAState extends State<IntroTwoFA> {
  @override
  var uuid = Uuid();

  void initState() {
    super.initState();
    // analytics.setCurrentScreen(screenName: "introTwoFA");
    insertOpenMenu("introTwoFA");
    // checkinternet(context);
    WidgetsFlutterBinding.ensureInitialized();
    if (Platform.isAndroid) {
      // AndroidInAppWebViewController.setWebContentsDebuggingEnabled(true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Center(
              child: Container(
                color: const Color(0xff302C49),
              )),
          // Container(
          //   width: mediaQuery(context, "w", 828),
          //   height: mediaQuery(context, "h", 290),
          //   decoration: BoxDecoration(
          //     borderRadius: BorderRadius.only(
          //       bottomRight: Radius.circular(mediaQuery(context, "h", 40)),
          //       bottomLeft: Radius.circular(mediaQuery(context, "h", 40)),
          //     ),
          //     color: const Color(0xff1f1c2f),
          //     boxShadow: const [
          //       BoxShadow(
          //         color: Color(0x4d000000),
          //         offset: Offset(0, 2),
          //         blurRadius: 20,
          //       ),
          //     ],
          //   ),
          // ),
          Container(
            margin: EdgeInsets.only(
                top: mediaQuery(context, "h", 154),
                left: mediaQuery(context, "w", 60)),
            child: GestureDetector(
              onTap: () {
                Navigator.of(context).pop();
              },
              child: Container(
                color: Colors.transparent,
                alignment: Alignment.center,
                height: mediaQuery(context, "h", 50),
                width: mediaQuery(context, "h", 50),
                child: SvgPicture.string(
                  '<svg viewBox="67.0 154.0 21.7 38.0" ><path transform="translate(55.75, 147.81)" d="M 17.79955863952637 25.18868064880371 L 32.17768859863281 10.82186222076416 C 33.24105834960938 9.758491516113281 33.24105834960938 8.038998603820801 32.17768859863281 6.986940383911133 C 31.11431694030762 5.92357063293457 29.39482688903809 5.934882164001465 28.33145332336426 6.986940383911133 L 12.0415210723877 23.26556205749512 C 11.01208686828613 24.29499435424805 10.98946189880371 25.94661521911621 11.96233177185059 27.00998306274414 L 28.32014274597168 43.40172958374023 C 28.85183143615723 43.93341445922852 29.55320167541504 44.19359970092773 30.24326133728027 44.19359970092773 C 30.93332099914551 44.19359970092773 31.63469123840332 43.93341445922852 32.1663818359375 43.40172958374023 C 33.22975158691406 42.33835601806641 33.22975158691406 40.61886596679688 32.1663818359375 39.56680679321289 L 17.79955863952637 25.18868064880371 Z" fill="#fcf6e4" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                  allowDrawingOutsideViewBox: true,
                  height: mediaQuery(context, "h", 35),
                ),
              ),
            ),
          ),
          Container(
            height: mediaQuery(context, "h", 260),
            margin: EdgeInsets.only(top: mediaQuery(context, "h", 50)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                SvgPicture.string(
                  '<svg width="23" height="20" viewBox="0 0 23 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <circle cx="8.27272" cy="5.54545" r="4.54545" fill="#F6F6F6" stroke="#302C49" stroke-linecap="round"/><path d="M14.7656 16.8024C15.1852 16.7074 15.437 16.2711 15.2509 15.8831C14.7493 14.8367 13.9249 13.9172 12.8563 13.2242C11.5413 12.3714 9.93018 11.9092 8.27273 11.9092C6.61528 11.9092 5.00414 12.3714 3.68919 13.2242C2.6206 13.9172 1.79615 14.8367 1.29451 15.8831C1.1085 16.2711 1.36021 16.7074 1.77987 16.8024C6.05432 17.7704 10.4911 17.7704 14.7656 16.8024Z" fill="#F6F6F6" stroke="#302C49" stroke-linecap="round"/><path d="M20.5197 9.30052L17.0647 7.87534C16.6739 7.71414 16.2352 7.71414 15.8444 7.87534L12.3894 9.30052C11.7201 9.57661 11.3196 10.2677 11.4129 10.9857L11.6523 12.829C11.8719 14.5201 12.7375 16.0605 14.0678 17.1275L15.4534 18.2389C16.0384 18.7081 16.8707 18.7081 17.4556 18.2389L18.8413 17.1275C20.1716 16.0605 21.0372 14.5201 21.2568 12.829L21.4962 10.9857C21.5894 10.2677 21.189 9.57661 20.5197 9.30052Z" fill="#FCF6E4" stroke="#302C49" stroke-width="1.2" stroke-linecap="round"/><path d="M14.6363 13.2727L16.1227 15.0068C16.3477 15.2693 16.7649 15.2296 16.9364 14.9294L19.1818 11" stroke="#302C49" stroke-width="1.2" stroke-linecap="round"/></svg>',
                  allowDrawingOutsideViewBox: true,
                  fit: BoxFit.fill,
                  height: mediaQuery(context, "h", 37),
                ),
                Container(
                  margin: EdgeInsets.only(right: mediaQuery(context, "w", 10)),
                ),
                Text(
                  "ui_register_twoFA_header".tr.toString(),
                  style: TextStyle(
                    fontFamily: 'SukhumvitSet-Text',
                    fontSize: mediaQuery(context, "h", 30),
                    color: const Color(0xfffcf6e4),
                    letterSpacing: mediaQuery(context, "h", 1.2),
                    fontWeight: FontWeight.w700,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          Container(
            // height: mediaQuery(context, "h", 590),
            margin: EdgeInsets.only(top: mediaQuery(context, "h", 280),
                right: mediaQuery(context, "h", 40),
                left: mediaQuery(context, "h", 30)),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: <Widget>[
                Container(
                  height: mediaQuery(context, "h", 28),
                ),
                Text(
              "ในปัจจุบันมีการโจมตีทาง Cyber เกิดขึ้นตลอดเวลา PKG จึงต้องมีการป้องกันที่ดีมากยิ่งขึ้นในระบบต่างๆ ที่มีอยู่เพราะใน PKG มีการเก็บรวบรวมข้อมูลสำคัญ\n\n"
                  "รวมถึงข้อมูลส่วนตัวของลูกค้า ซึ่งสมาชิกของ PKG สามารถเข้าถึงได้ หากบัญชีหรืออุปกรณ์ใดๆที่สมาชิกใช้งานถูกโจมตีหรือถูกแฮค"
                  "จะทำให้เกิดผลเสียหายอย่างมากทั้งต่อ PKG และสมาชิกเองหนึ่งในระบบป้องกัน คือ\n\n"
                  "การใช้คีย์ความปลอดภัยที่เรียกว่า YubiKey ทำหน้าที่ยืนยัน 2 ขั้นตอน (2FA) ในการเข้าถึงบริการหรือระบบต่างๆ"
                  "\t\tที่ทีม Cyber Security กำหนดเพื่อเป็นการยกระดับความปลอดภัยของ PKG ขอให้สมาชิกลงทะเบียน 2FA สำหรับการใช้งาน MS24".toString(),
                  style: TextStyle(
                    fontFamily: 'SukhumvitSet-Text',
                    fontSize: mediaQuery(context, "h", 25),
                    color: const Color(0xfffcf6e4),
                    letterSpacing: mediaQuery(context, "h", 1.2),
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.left,
                ),
              ],
            ),
          ),
          Container(
            alignment: Alignment.bottomCenter,
            padding: EdgeInsets.only(bottom: mediaQuery(context, "h", 150)),
            child: GestureDetector(
              onTap: () async {
                 Yubikey.registerYubi(context);
                },
              child: Container(
                alignment: Alignment.center,
                height: mediaQuery(context, "h", 101),
                width: mediaQuery(context, "w", 700),
                decoration: BoxDecoration(
                  borderRadius:
                  BorderRadius.circular(mediaQuery(context, "h", 50)),
                  gradient: LinearGradient(
                    begin: Alignment(0.0, -1.0),
                    end: Alignment(0.0, 2.0),
                    colors: const [Color(0xffFEE095), Color(0xffFFDA7C)],
                    stops: const [0.0, 1.0],
                  ),
                ),
                child: Text(
                  "btn_register_twoFA".tr.toString(),
                  style: TextStyle(
                    fontFamily: 'SukhumvitSet-Text',
                    fontSize: mediaQuery(context, "h", 28),
                    color: const Color(0xff1F1C2F),
                    letterSpacing: mediaQuery(context, "h", 1.5),
                    fontWeight: FontWeight.w700,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}