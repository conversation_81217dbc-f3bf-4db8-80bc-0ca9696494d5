import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/controllers/utils/color.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../../controllers/internal/NotiController/NotifiController.dart';
import '../../../../../controllers/utils/widget.dart';
import '../../../Widget/Widget.dart';
import 'package:get/get.dart';

class NotiScreen extends StatefulWidget {
  final statusshow;

  const NotiScreen(this.statusshow);

  @override
  State<NotiScreen> createState() => _NotiScreenState();
}

class _NotiScreenState extends State<NotiScreen> {
  NotiController notiCtr = Get.find<NotiController>();
  int currentIndex = 0;
  ProfileController profile = Get.find<ProfileController>();

  late FToast fToast;

  @override
  void initState() {
    super.initState();
    fToast = FToast();
    // if you want to use cotext from globally instead of content we need to pass navigatorKey.currentContext!
    fToast.init(context);
    notiCtr.getaddress(context);
    notiCtr.checkreadstatusall(context);
    currentIndex = 0;
    notiCtr.initnot();
    notiCtr.resetExpandList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Container(
      height: 852.h,
      width: 393.w,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.onPrimary,
          ],
          begin: Alignment.topCenter, // เริ่มต้น Gradient ที่มุมบนซ้าย
          end: Alignment.bottomCenter,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.only(top: 30),
        child: Column(
          children: [
            buildAppBarWithFunction(
                context, 'ui_notify'.tr, 'ui_delete_3'.tr, 'ADD/Trash.png',
                () async {
              showAlert(context);
            }),
            buildSelect(),
          ],
        ),
      ),
    ));
  }

  Widget buildClaim() {
    return ListView.builder(
      itemCount: notiCtr.testNoti.length,
      padding: EdgeInsets.zero,
      // กำหนดจำนวน item ตามขนาดของ RxList
      itemBuilder: (context, index) {
        return builFormnoti(
            notiCtr.testNoti[index]['time']!,
            // notiCtr.testNoti[index]['autoclaim']!,
            notiCtr.testNoti[index]['activity']!,
            notiCtr.testNoti[index]['des']!,
            index);
      },
    );
  }

  Future showAlert(BuildContext context) {
    return showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Theme.of(context).primaryColor,
          title: Center(child: Text('')),
          content: SingleChildScrollView(
            // Wrap with SingleChildScrollView
            child: Container(
              height:
                  250.h, // You can remove this or keep it based on your design
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Center(
                      child: AppWidget.normalText(
                        context,
                        'ลบข้อความทั้งหมด'.tr,
                        16.sp,
                        Theme.of(context).colorScheme.scrim,
                        FontWeight.w400,
                      ),
                    ),
                    SizedBox(height: 10.h),
                    SizedBox(
                      width: 40.w,
                      height: 40.h,
                      child: Image.asset('assets/Group.png'),
                    ),
                    SizedBox(height: 10.h),
                    Center(
                      child: AppWidget.normalText(
                        context,
                        'คุณต้องการลบข้อความทั้งหมด'.tr,
                        14.sp,
                        Theme.of(context).colorScheme.scrim,
                        FontWeight.w200,
                      ),
                    ),
                    Center(
                      child: AppWidget.normalText(
                        context,
                        'ใช่หรือไม่'.tr,
                        14.sp,
                        Theme.of(context).colorScheme.scrim,
                        FontWeight.w200,
                      ),
                    ),
                    SizedBox(height: 20.h),
                    InkWell(
                      onTap: () {
                        setState(() {
                          /// Maintain ///
                          currentIndex == 0
                              ? notiCtr.testNoti.clear()
                              : currentIndex == 1
                                  ? notiCtr.testNews.clear()
                                  : notiCtr.testCrouse.clear();
                        });
                        Get.back();
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Theme.of(context)
                                .colorScheme
                                .secondaryContainer,
                            width: 1.0.w,
                          ),
                          color: AppColors.DeepSeaGreen,
                        ),
                        width: 235.w, // Fixed width of 235px
                        height: 46.h, // Fixed height of 46px
                        child: Center(
                          child: Text(
                            'ui_delete_3'.tr,
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: 10.h), // Space between buttons
                    InkWell(
                      onTap: () {
                        Get.back();
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        width: 235.w, // Fixed width of 235px
                        height: 46.h,
                        child: Center(
                          child: AppWidget.normalText(
                            context,
                            'ui_delete_4'.tr,
                            14.sp,
                            Theme.of(context).colorScheme.scrim,
                            FontWeight.w400,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          actions: [],
        );
      },
    );
  }

  Widget buildNews() {
    return ListView.builder(
      itemCount: notiCtr.testNews.length,
      padding: EdgeInsets.zero,
      itemBuilder: (context, i) {
        return builFormNews(i);
      },
    );
  }

  Widget buildCoruse() {
    return ListView.builder(
      itemCount: notiCtr.testCrouse.length,
      padding: EdgeInsets.zero,
      itemBuilder: (context, i) {
        return builFormCoruse(i);
      },
    );
  }

  Widget builFormnoti(
      time,
      // autoclaim,
      activity,
      des,
      int index) {
    DateTime dateTime = DateTime.parse(time);

    return InkWell(
      onTap: () {
        setState(() {
          notiCtr.toggleExpandState(index);
        });
      },
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                width: Get.width,
                padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 5.h),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        SizedBox(
                          height: 18.h,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              AppWidget.normalText(
                                context,
                                DateFormat('dd/MM/yyyy hh:mm').format(dateTime),
                                12.sp,
                                Theme.of(context).colorScheme.scrim,
                                FontWeight.w400,
                              ),
                              SizedBox(
                                width: 10.w,
                              ),
                              AppWidget.normalText(
                                context,
                                'ui_auto_claim'.tr,
                                12.sp,
                                Theme.of(context).colorScheme.tertiary,
                                FontWeight.w400,
                              ),
                            ],
                          ),
                        ),
                        InkWell(
                          onTap: () {
                            setState(() {
                              notiCtr.toggleExpandState(index);
                            });
                          },
                          child: SizedBox(
                            height: 24.h,
                            width: 24.w,
                            child: Image.asset(
                              notiCtr.isExpandList[index].value
                                  ? 'assets/ADD/Expand_up_light.png'
                                  : 'assets/ADD/Expand_down_light.png',
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 5.h,
                    ),
                    Row(
                      children: [
                        Text(
                          '${'ui_act'.tr} : ',
                          textAlign: TextAlign.left,
                          maxLines: 2,
                          style: TextStyle(
                            // fontFamily: 'SukhumvitSet-Text',
                            overflow: TextOverflow.ellipsis,
                            fontFamily: 'SukhumvitSet-Bold',
                            fontSize: 14.sp,
                            color: Theme.of(context).colorScheme.scrim,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        Container(
                            width: notiCtr.isExpandList[index].value
                                ? Get.width * 0.70
                                : Get.width * 0.70,
                            child: Text(
                              '$activity',
                              textAlign: TextAlign.left,
                              maxLines: 1,
                              style: TextStyle(
                                // fontFamily: 'SukhumvitSet-Text',
                                overflow: TextOverflow.ellipsis,
                                fontFamily: 'SukhumvitSet-Bold',
                                fontSize: 14.sp,
                                color: Theme.of(context).colorScheme.scrim,
                                fontWeight: FontWeight.w400,
                              ),
                            )),
                      ],
                    ),
                    SizedBox(
                      height: 5.h,
                    ),
                    Visibility(
                      visible: notiCtr.isExpandList[index].value,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          AppWidget.normalTextRX(
                            context,
                            'details'.obs,
                            14.sp,
                            Theme.of(context).colorScheme.secondaryContainer,
                            FontWeight.w400,
                          ),
                          AppWidget.normalTextRX(
                            context,
                            '$des'.obs,
                            14.sp,
                            Theme.of(context).colorScheme.scrim,
                            FontWeight.w400,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(
            height: 5.h,
          ),
          InkWell(
            onTap: () {
              notiCtr.doreloadcheckclaim(
                  context, notiCtr.testNoti[index]["running_act"]);
              setState(() {
                notiCtr.testNoti[index]["status_recive_like"] = "Y";
              });
            },
            child: Container(
              width: 353.w,
              height: 50.h,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Theme.of(context).colorScheme.secondaryContainer,
                  width: 1.0.w,
                ),
                color: AppColors.DeepSeaGreen,
              ),
              child: Center(
                child: AppWidget.normalText(
                  context,
                  'ui_box_claim'.tr,
                  16.sp,
                  Theme.of(context).colorScheme.scrim,
                  FontWeight.w600,
                ),
              ),
            ),
          ),
          SizedBox(
            height: 20.h,
          ),
          Container(
            width: 393,
            decoration: BoxDecoration(
              border: Border.all(
                color: const Color(0xFFFCF6E4).withOpacity(0.2),
                width: 0.1,
              ),
              color: const Color(0xFFFCF6E4).withOpacity(0.2),
            ),
          ),
        ],
      ),
    );
  }

  Widget builFormNews(index) {
    DateTime dateTime = DateTime.parse(
        notiCtr.datanotificationNews[index]["create_time"].toString());

    return InkWell(
      onTap: () {
        setState(() {
          notiCtr.toggleExpandState(index);
        });
      },
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Container(
                width: Get.width,
                padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 5.h),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    notiCtr.isExpandList[index].value
                        ? Column(
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  SizedBox(
                                      width: 300.w,
                                      child: AppWidget.normalText(
                                        context,
                                        DateFormat('dd/MM/yyyy hh:mm')
                                            .format(dateTime),
                                        12.sp,
                                        Theme.of(context)
                                            .colorScheme
                                            .tertiary
                                            .withOpacity(1),
                                        FontWeight.w500,
                                      )),
                                  InkWell(
                                    onTap: () {
                                      setState(() {
                                        notiCtr.toggleExpandState(index);
                                      });
                                    },
                                    child: SizedBox(
                                      height: 24.h,
                                      width: 24.w,
                                      child: Image.asset(
                                        notiCtr.isExpandList[index].value
                                            ? 'assets/ADD/Expand_up_light.png'
                                            : 'assets/ADD/Expand_down_light.png',
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSecondary,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Container(

                                      // width: notiCtr.isExpandList[index].value
                                      //     ? Get.width * 0.70
                                      //     : Get.width * 0.70,
                                      child: AppWidget.normalTextRX(
                                    context,
                                    notiCtr.datanotificationNews[index]["title"]
                                        .toString()
                                        .obs,
                                    12.sp,
                                    Theme.of(context)
                                        .colorScheme
                                        .onSecondary
                                        .withOpacity(1),
                                    FontWeight.w400,
                                  )),
                                  AppWidget.normalText(
                                    context,
                                    notiCtr.datanotificationNews[index]
                                            ["detail"]
                                        .toString(),
                                    12.sp,
                                    Theme.of(context)
                                        .colorScheme
                                        .onSecondary
                                        .withOpacity(1),
                                    FontWeight.w500,
                                  ),
                                  Visibility(
                                    visible: notiCtr.isExpandList[index].value,
                                    child: Column(
                                      children: [
                                        (notiCtr.datanotificationNews[index]
                                                            ["link"]
                                                        .toString()
                                                        .toString() !=
                                                    "" &&
                                                notiCtr.datanotificationNews[
                                                            index]["link"]
                                                        .toString() !=
                                                    "null")
                                            ? GestureDetector(
                                                onTap: () {
                                                  _launchURL(notiCtr
                                                      .datanotificationNews[
                                                          index]["link"]
                                                      .toString());
                                                },
                                                child: Container(
                                                    width: mediaQuery(
                                                        context, "w", 634),
                                                    color: Colors.transparent,
                                                    margin: EdgeInsets.only(
                                                        top: mediaQuery(
                                                            context, "h", 5),
                                                        bottom: mediaQuery(
                                                            context, "h", 5)),
                                                    child: AppWidget.normalText(
                                                      context,
                                                      "link1 : " +
                                                          notiCtr
                                                              .datanotificationNews[
                                                                  index]["link"]
                                                              .toString(),
                                                      13.sp,
                                                      Theme.of(context)
                                                          .colorScheme
                                                          .secondaryContainer
                                                          .withOpacity(1),
                                                      FontWeight.w500,
                                                    )),
                                              )
                                            : Container(
                                                width: mediaQuery(
                                                    context, "w", 634),
                                                height: mediaQuery(
                                                    context, "h", 50),
                                              ),
                                        (notiCtr.datanotificationNews[index]
                                                            ["link2"]
                                                        .toString() !=
                                                    "" &&
                                                notiCtr.datanotificationNews[
                                                            index]["link2"]
                                                        .toString() !=
                                                    "null")
                                            ? GestureDetector(
                                                onTap: () {
                                                  _launchURL(notiCtr
                                                      .datanotificationNews[
                                                          index]["link2"]
                                                      .toString());
                                                },
                                                child: Container(
                                                    width: mediaQuery(
                                                        context, "w", 634),
                                                    color: Colors.transparent,
                                                    margin: EdgeInsets.only(
                                                        top: mediaQuery(
                                                            context, "h", 5),
                                                        bottom: mediaQuery(
                                                            context, "h", 5)),
                                                    child: AppWidget.normalText(
                                                      context,
                                                      "link2 : " +
                                                          notiCtr
                                                              .datanotificationNews[
                                                                  index]
                                                                  ["link2"]
                                                              .toString(),
                                                      13.sp,
                                                      Theme.of(context)
                                                          .colorScheme
                                                          .secondaryContainer
                                                          .withOpacity(1),
                                                      FontWeight.w500,
                                                    )),
                                              )
                                            : Container(),
                                        (notiCtr.datanotificationNews[index]
                                                            ["link3"]
                                                        .toString() !=
                                                    "" &&
                                                notiCtr.datanotificationNews[
                                                            index]["link3"]
                                                        .toString() !=
                                                    "null")
                                            ? GestureDetector(
                                                onTap: () {
                                                  _launchURL(notiCtr
                                                      .datanotificationNews[
                                                          index]["link3"]
                                                      .toString());
                                                },
                                                child: Container(
                                                    width: mediaQuery(
                                                        context, "w", 634),
                                                    color: Colors.transparent,
                                                    margin: EdgeInsets.only(
                                                        top: mediaQuery(
                                                            context, "h", 5),
                                                        bottom: mediaQuery(
                                                            context, "h", 5)),
                                                    child: AppWidget.normalText(
                                                      context,
                                                      "link3 : " +
                                                          notiCtr
                                                              .datanotificationNews[
                                                                  index]
                                                                  ["link3"]
                                                              .toString(),
                                                      13.sp,
                                                      Theme.of(context)
                                                          .colorScheme
                                                          .secondaryContainer
                                                          .withOpacity(1),
                                                      FontWeight.w500,
                                                    )),
                                              )
                                            : Container(),
                                        (notiCtr.datanotificationNews[index]
                                                            ["link4"]
                                                        .toString() !=
                                                    "" &&
                                                notiCtr.datanotificationNews[
                                                            index]["link4"]
                                                        .toString() !=
                                                    "null")
                                            ? GestureDetector(
                                                onTap: () {
                                                  _launchURL(notiCtr
                                                      .datanotificationNews[
                                                          index]["link4"]
                                                      .toString());
                                                },
                                                child: Container(
                                                    width: mediaQuery(
                                                        context, "w", 634),
                                                    color: Colors.transparent,
                                                    margin: EdgeInsets.only(
                                                        top: mediaQuery(
                                                            context, "h", 5),
                                                        bottom: mediaQuery(
                                                            context, "h", 5)),
                                                    child: AppWidget.normalText(
                                                      context,
                                                      "link1 : " +
                                                          notiCtr
                                                              .datanotificationNews[
                                                                  index]["link"]
                                                              .toString(),
                                                      13.sp,
                                                      Theme.of(context)
                                                          .colorScheme
                                                          .secondaryContainer
                                                          .withOpacity(1),
                                                      FontWeight.w500,
                                                    )),
                                              )
                                            : Container(),
                                        (notiCtr.datanotificationNews[index]
                                                            ["link5"]
                                                        .toString() !=
                                                    "" &&
                                                notiCtr.datanotificationNews[
                                                            index]["link5"]
                                                        .toString() !=
                                                    "null")
                                            ? GestureDetector(
                                                onTap: () {
                                                  _launchURL(notiCtr
                                                      .datanotificationNews[
                                                          index]["link5"]
                                                      .toString());
                                                },
                                                child: Container(
                                                    width: mediaQuery(
                                                        context, "w", 634),
                                                    color: Colors.transparent,
                                                    margin: EdgeInsets.only(
                                                        top: mediaQuery(
                                                            context, "h", 5),
                                                        bottom: mediaQuery(
                                                            context, "h", 5)),
                                                    child: AppWidget.normalText(
                                                      context,
                                                      "link5 : " +
                                                          notiCtr
                                                              .datanotificationNews[
                                                                  index]
                                                                  ["link5"]
                                                              .toString(),
                                                      13.sp,
                                                      Theme.of(context)
                                                          .colorScheme
                                                          .secondaryContainer
                                                          .withOpacity(1),
                                                      FontWeight.w500,
                                                    )),
                                              )
                                            : Container(),
                                        (notiCtr.datanotificationNews[index]
                                                            ["link6"]
                                                        .toString() !=
                                                    "" &&
                                                notiCtr.datanotificationNews[
                                                            index]["link6"]
                                                        .toString() !=
                                                    "null")
                                            ? GestureDetector(
                                                onTap: () {
                                                  _launchURL(notiCtr
                                                      .datanotificationNews[
                                                          index]["link6"]
                                                      .toString());
                                                },
                                                child: Container(
                                                    width: mediaQuery(
                                                        context, "w", 634),
                                                    color: Colors.transparent,
                                                    margin: EdgeInsets.only(
                                                        top: mediaQuery(
                                                            context, "h", 5),
                                                        bottom: mediaQuery(
                                                            context, "h", 5)),
                                                    child: AppWidget.normalText(
                                                      context,
                                                      "link6 : " +
                                                          notiCtr
                                                              .datanotificationNews[
                                                                  index]
                                                                  ["link6"]
                                                              .toString(),
                                                      13.sp,
                                                      Theme.of(context)
                                                          .colorScheme
                                                          .secondaryContainer
                                                          .withOpacity(1),
                                                      FontWeight.w500,
                                                    )),
                                              )
                                            : Container(),
                                        (notiCtr.datanotificationNews[index]
                                                            ["link7"]
                                                        .toString() !=
                                                    "" &&
                                                notiCtr.datanotificationNews[
                                                            index]["link7"]
                                                        .toString() !=
                                                    "null")
                                            ? GestureDetector(
                                                onTap: () {
                                                  _launchURL(notiCtr
                                                      .datanotificationNews[
                                                          index]["link7"]
                                                      .toString());
                                                },
                                                child: Container(
                                                    width: mediaQuery(
                                                        context, "w", 634),
                                                    color: Colors.transparent,
                                                    margin: EdgeInsets.only(
                                                        top: mediaQuery(
                                                            context, "h", 5),
                                                        bottom: mediaQuery(
                                                            context, "h", 5)),
                                                    child: AppWidget.normalText(
                                                      context,
                                                      "link7 : " +
                                                          notiCtr
                                                              .datanotificationNews[
                                                                  index]
                                                                  ["link7"]
                                                              .toString(),
                                                      13.sp,
                                                      Theme.of(context)
                                                          .colorScheme
                                                          .secondaryContainer
                                                          .withOpacity(1),
                                                      FontWeight.w500,
                                                    )),
                                              )
                                            : Container(),
                                        (notiCtr.datanotificationNews[index]
                                                            ["link8"]
                                                        .toString() !=
                                                    "" &&
                                                notiCtr.datanotificationNews[
                                                            index]["link8"]
                                                        .toString() !=
                                                    "null")
                                            ? GestureDetector(
                                                onTap: () {
                                                  _launchURL(notiCtr
                                                      .datanotificationNews[
                                                          index]["link8"]
                                                      .toString());
                                                },
                                                child: Container(
                                                    width: mediaQuery(
                                                        context, "w", 634),
                                                    color: Colors.transparent,
                                                    margin: EdgeInsets.only(
                                                        top: mediaQuery(
                                                            context, "h", 5),
                                                        bottom: mediaQuery(
                                                            context, "h", 5)),
                                                    child: AppWidget.normalText(
                                                      context,
                                                      "link8 : " +
                                                          notiCtr
                                                              .datanotificationNews[
                                                                  index]
                                                                  ["link8"]
                                                              .toString(),
                                                      13.sp,
                                                      Theme.of(context)
                                                          .colorScheme
                                                          .secondaryContainer
                                                          .withOpacity(1),
                                                      FontWeight.w500,
                                                    )),
                                              )
                                            : Container(),
                                        (notiCtr.datanotificationNews[index]
                                                            ["link9"]
                                                        .toString() !=
                                                    "" &&
                                                notiCtr.datanotificationNews[
                                                            index]["link9"]
                                                        .toString() !=
                                                    "null")
                                            ? GestureDetector(
                                                onTap: () {
                                                  _launchURL(notiCtr
                                                      .datanotificationNews[
                                                          index]["link9"]
                                                      .toString());
                                                },
                                                child: Container(
                                                    width: mediaQuery(
                                                        context, "w", 634),
                                                    color: Colors.transparent,
                                                    margin: EdgeInsets.only(
                                                        top: mediaQuery(
                                                            context, "h", 5),
                                                        bottom: mediaQuery(
                                                            context, "h", 5)),
                                                    child: AppWidget.normalText(
                                                      context,
                                                      "link9 : " +
                                                          notiCtr
                                                              .datanotificationNews[
                                                                  index]
                                                                  ["link9"]
                                                              .toString(),
                                                      13.sp,
                                                      Theme.of(context)
                                                          .colorScheme
                                                          .secondaryContainer
                                                          .withOpacity(1),
                                                      FontWeight.w500,
                                                    )),
                                              )
                                            : Container(),
                                        (notiCtr.datanotificationNews[index]
                                                            ["link10"]
                                                        .toString() !=
                                                    "" &&
                                                notiCtr.datanotificationNews[
                                                            index]["link10"]
                                                        .toString() !=
                                                    "null")
                                            ? GestureDetector(
                                                onTap: () {
                                                  _launchURL(notiCtr
                                                      .datanotificationNews[
                                                          index]["link10"]
                                                      .toString());
                                                },
                                                child: Container(
                                                    width: mediaQuery(
                                                        context, "w", 634),
                                                    color: Colors.transparent,
                                                    margin: EdgeInsets.only(
                                                        top: mediaQuery(
                                                            context, "h", 5),
                                                        bottom: mediaQuery(
                                                            context, "h", 5)),
                                                    child: AppWidget.normalText(
                                                      context,
                                                      "link10 : " +
                                                          notiCtr
                                                              .datanotificationNews[
                                                                  index]
                                                                  ["link10"]
                                                              .toString(),
                                                      13.sp,
                                                      Theme.of(context)
                                                          .colorScheme
                                                          .secondaryContainer
                                                          .withOpacity(1),
                                                      FontWeight.w500,
                                                    )),
                                              )
                                            : Container(),
                                        notiCtr.datanotificationNews[index]
                                                        ["image"]
                                                    .toString() !=
                                                ""
                                            ? Container(
                                                width: mediaQuery(
                                                    context, "w", 634),
                                                margin: EdgeInsets.only(
                                                    top: mediaQuery(
                                                        context, "h", 10),
                                                    bottom: mediaQuery(
                                                        context, "h", 90)),
                                                child: Image.network(notiCtr
                                                    .datanotificationNews[index]
                                                        ["image"]
                                                    .toString()),
                                              )
                                            : Container(),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          )
                        : Column(
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  SizedBox(
                                      width: 300.w,
                                      child: AppWidget.normalText(
                                        context,
                                        DateFormat('dd/MM/yyyy hh:mm')
                                            .format(dateTime),
                                        12.sp,
                                        Theme.of(context)
                                            .colorScheme
                                            .tertiary
                                            .withOpacity(1),
                                        FontWeight.w500,
                                      )),
                                  InkWell(
                                    onTap: () {
                                      setState(() {
                                        notiCtr.toggleExpandState(index);
                                      });
                                    },
                                    child: SizedBox(
                                      height: 24.h,
                                      width: 24.w,
                                      child: Image.asset(
                                        notiCtr.isExpandList[index].value
                                            ? 'assets/ADD/Expand_up_light.png'
                                            : 'assets/ADD/Expand_down_light.png',
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSecondary,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                      alignment: Alignment.topLeft,
                                      width: notiCtr.isExpandList[index].value
                                          ? Get.width * 0.70
                                          : Get.width,
                                      child: AppWidget.normalTextOverflow(
                                        context,
                                        notiCtr.datanotificationNews[index]
                                                ["title"]
                                            .toString()
                                            .obs,
                                        12.sp,
                                        Theme.of(context)
                                            .colorScheme
                                            .onSecondary
                                            .withOpacity(1),
                                        FontWeight.w400,
                                      )),
                                  Text(
                                    notiCtr.datanotificationNews[index]
                                            ["detail"]
                                        .toString(),
                                    textAlign: TextAlign.left,
                                    maxLines: 2,
                                    style: TextStyle(
                                      // fontFamily: 'SukhumvitSet-Text',
                                      overflow: TextOverflow.ellipsis,
                                      fontFamily: 'SukhumvitSet-Bold',
                                      fontSize: 12.sp,
                                      color: Theme.of(context)
                                          .colorScheme
                                          .onSecondary
                                          .withOpacity(1),
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          )
                  ],
                ),
              ),
            ],
          ),
          SizedBox(
            height: 20.h,
          ),
          Container(
            width: 393,
            decoration: BoxDecoration(
              border: Border.all(
                color: Theme.of(context)
                    .colorScheme
                    .onSecondaryContainer
                    .withOpacity(0.7),
                width: 0.3,
              ),
              color: Theme.of(context)
                  .colorScheme
                  .onSecondaryContainer
                  .withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
    InkWell(
        onTap: () {
          setState(() {
            notiCtr.toggleExpandState(index);
          });
        },
        child: GestureDetector(
          onTap: () {
            setState(() {
              // statusactionclick3 = "";
            });
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: Get.width,
                padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 5.h),
                child: Text.rich(
                  TextSpan(
                    style: TextStyle(
                      fontFamily: 'SukhumvitSet-SemiBold',
                      fontSize: mediaQuery(context, "h", 25),
                      color: const Color(0xfffee095),
                      letterSpacing:
                          mediaQuery(context, "h", 2.8000000000000003),
                    ),
                    children: [
                      TextSpan(
                        text: notiCtr.datanotificationNews[index]["title"]
                            .toString(),
                        style: TextStyle(
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                    ],
                  ),
                  textAlign: TextAlign.left,
                ),
              ),
              Row(
                children: <Widget>[
                  Container(
                    child: Text(
                      notiCtr.datanotificationNews[index]["create_time"]
                          .toString(),
                      style: TextStyle(
                        fontFamily: 'SukhumvitSet-Medium',
                        fontSize: mediaQuery(context, "h", 22),
                        color: const Color(0xff5F569B),
                        letterSpacing: mediaQuery(context, "h", 1),
                        fontWeight: FontWeight.w500,
                        height: 1.6,
                      ),
                      textAlign: TextAlign.left,
                    ),
                  ),
                ],
              ),
              Container(
                width: mediaQuery(context, "w", 634),
                margin: EdgeInsets.only(top: mediaQuery(context, "h", 30)),
                child: Text(
                  notiCtr.datanotificationNews[index]["detail"].toString(),
                  style: TextStyle(
                    fontFamily: 'SukhumvitSet-Medium',
                    fontSize: mediaQuery(context, "h", 25),
                    color: const Color(0xfffcf6e4),
                    letterSpacing: mediaQuery(context, "h", 1.4000000000000001),
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.left,
                ),
              ),
              (notiCtr.datanotificationNews[index]["link"]
                              .toString()
                              .toString() !=
                          "" &&
                      notiCtr.datanotificationNews[index]["link"].toString() !=
                          "null")
                  ? GestureDetector(
                      onTap: () {
                        _launchURL(notiCtr.datanotificationNews[index]["link"]
                            .toString());
                      },
                      child: Container(
                        width: mediaQuery(context, "w", 634),
                        color: Colors.transparent,
                        margin: EdgeInsets.only(
                            top: mediaQuery(context, "h", 5),
                            bottom: mediaQuery(context, "h", 5)),
                        child: Text(
                          "link1 : " +
                              notiCtr.datanotificationNews[index]["link"]
                                  .toString(),
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Medium',
                            fontSize: mediaQuery(context, "h", 25),
                            color: const Color(0xfffcf6e4),
                            letterSpacing:
                                mediaQuery(context, "h", 1.4000000000000001),
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.left,
                        ),
                      ),
                    )
                  : Container(
                      width: mediaQuery(context, "w", 634),
                      height: mediaQuery(context, "h", 50),
                    ),
              (notiCtr.datanotificationNews[index]["link2"].toString() != "" &&
                      notiCtr.datanotificationNews[index]["link2"].toString() !=
                          "null")
                  ? GestureDetector(
                      onTap: () {
                        _launchURL(notiCtr.datanotificationNews[index]["link2"]
                            .toString());
                      },
                      child: Container(
                        width: mediaQuery(context, "w", 634),
                        color: Colors.transparent,
                        margin: EdgeInsets.only(
                            top: mediaQuery(context, "h", 5),
                            bottom: mediaQuery(context, "h", 5)),
                        child: Text(
                          "link2 : " +
                              notiCtr.datanotificationNews[index]["link2"]
                                  .toString(),
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Medium',
                            fontSize: mediaQuery(context, "h", 18),
                            color: const Color(0xfffcf6e4),
                            letterSpacing:
                                mediaQuery(context, "h", 1.4000000000000001),
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.left,
                        ),
                      ),
                    )
                  : Container(),
              (notiCtr.datanotificationNews[index]["link3"].toString() != "" &&
                      notiCtr.datanotificationNews[index]["link3"].toString() !=
                          "null")
                  ? GestureDetector(
                      onTap: () {
                        _launchURL(notiCtr.datanotificationNews[index]["link3"]
                            .toString());
                      },
                      child: Container(
                        width: mediaQuery(context, "w", 634),
                        color: Colors.transparent,
                        margin: EdgeInsets.only(
                            top: mediaQuery(context, "h", 5),
                            bottom: mediaQuery(context, "h", 5)),
                        child: Text(
                          "link3 : " +
                              notiCtr.datanotificationNews[index]["link3"]
                                  .toString(),
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Medium',
                            fontSize: mediaQuery(context, "h", 18),
                            color: const Color(0xfffcf6e4),
                            letterSpacing:
                                mediaQuery(context, "h", 1.4000000000000001),
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.left,
                        ),
                      ),
                    )
                  : Container(),
              (notiCtr.datanotificationNews[index]["link4"].toString() != "" &&
                      notiCtr.datanotificationNews[index]["link4"].toString() !=
                          "null")
                  ? GestureDetector(
                      onTap: () {
                        _launchURL(notiCtr.datanotificationNews[index]["link4"]
                            .toString());
                      },
                      child: Container(
                        width: mediaQuery(context, "w", 634),
                        color: Colors.transparent,
                        margin: EdgeInsets.only(
                            top: mediaQuery(context, "h", 5),
                            bottom: mediaQuery(context, "h", 5)),
                        child: Text(
                          "link4 : " +
                              notiCtr.datanotificationNews[index]["link4"]
                                  .toString(),
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Medium',
                            fontSize: mediaQuery(context, "h", 18),
                            color: const Color(0xfffcf6e4),
                            letterSpacing:
                                mediaQuery(context, "h", 1.4000000000000001),
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.left,
                        ),
                      ),
                    )
                  : Container(),
              (notiCtr.datanotificationNews[index]["link5"].toString() != "" &&
                      notiCtr.datanotificationNews[index]["link5"].toString() !=
                          "null")
                  ? GestureDetector(
                      onTap: () {
                        _launchURL(notiCtr.datanotificationNews[index]["link5"]
                            .toString());
                      },
                      child: Container(
                        width: mediaQuery(context, "w", 634),
                        color: Colors.transparent,
                        margin: EdgeInsets.only(
                            top: mediaQuery(context, "h", 5),
                            bottom: mediaQuery(context, "h", 5)),
                        child: Text(
                          "link5 : " +
                              notiCtr.datanotificationNews[index]["link5"]
                                  .toString(),
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Medium',
                            fontSize: mediaQuery(context, "h", 18),
                            color: const Color(0xfffcf6e4),
                            letterSpacing:
                                mediaQuery(context, "h", 1.4000000000000001),
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.left,
                        ),
                      ),
                    )
                  : Container(),
              (notiCtr.datanotificationNews[index]["link6"].toString() != "" &&
                      notiCtr.datanotificationNews[index]["link6"].toString() !=
                          "null")
                  ? GestureDetector(
                      onTap: () {
                        _launchURL(notiCtr.datanotificationNews[index]["link6"]
                            .toString());
                      },
                      child: Container(
                        width: mediaQuery(context, "w", 634),
                        color: Colors.transparent,
                        margin: EdgeInsets.only(
                            top: mediaQuery(context, "h", 5),
                            bottom: mediaQuery(context, "h", 5)),
                        child: Text(
                          "link6 : " +
                              notiCtr.datanotificationNews[index]["link6"]
                                  .toString(),
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Medium',
                            fontSize: mediaQuery(context, "h", 18),
                            color: const Color(0xfffcf6e4),
                            letterSpacing:
                                mediaQuery(context, "h", 1.4000000000000001),
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.left,
                        ),
                      ),
                    )
                  : Container(),
              (notiCtr.datanotificationNews[index]["link7"].toString() != "" &&
                      notiCtr.datanotificationNews[index]["link7"].toString() !=
                          "null")
                  ? GestureDetector(
                      onTap: () {
                        _launchURL(notiCtr.datanotificationNews[index]["link7"]
                            .toString());
                      },
                      child: Container(
                        width: mediaQuery(context, "w", 634),
                        color: Colors.transparent,
                        margin: EdgeInsets.only(
                            top: mediaQuery(context, "h", 5),
                            bottom: mediaQuery(context, "h", 5)),
                        child: Text(
                          "link7 : " +
                              notiCtr.datanotificationNews[index]["link7"]
                                  .toString(),
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Medium',
                            fontSize: mediaQuery(context, "h", 18),
                            color: const Color(0xfffcf6e4),
                            letterSpacing:
                                mediaQuery(context, "h", 1.4000000000000001),
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.left,
                        ),
                      ),
                    )
                  : Container(),
              (notiCtr.datanotificationNews[index]["link8"].toString() != "" &&
                      notiCtr.datanotificationNews[index]["link8"].toString() !=
                          "null")
                  ? GestureDetector(
                      onTap: () {
                        _launchURL(notiCtr.datanotificationNews[index]["link8"]
                            .toString());
                      },
                      child: Container(
                        width: mediaQuery(context, "w", 634),
                        color: Colors.transparent,
                        margin: EdgeInsets.only(
                            top: mediaQuery(context, "h", 5),
                            bottom: mediaQuery(context, "h", 5)),
                        child: Text(
                          "link8 : " +
                              notiCtr.datanotificationNews[index]["link8"]
                                  .toString(),
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Medium',
                            fontSize: mediaQuery(context, "h", 18),
                            color: const Color(0xfffcf6e4),
                            letterSpacing:
                                mediaQuery(context, "h", 1.4000000000000001),
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.left,
                        ),
                      ),
                    )
                  : Container(),
              (notiCtr.datanotificationNews[index]["link9"].toString() != "" &&
                      notiCtr.datanotificationNews[index]["link9"].toString() !=
                          "null")
                  ? GestureDetector(
                      onTap: () {
                        _launchURL(notiCtr.datanotificationNews[index]["link9"]
                            .toString());
                      },
                      child: Container(
                        width: mediaQuery(context, "w", 634),
                        color: Colors.transparent,
                        margin: EdgeInsets.only(
                            top: mediaQuery(context, "h", 5),
                            bottom: mediaQuery(context, "h", 5)),
                        child: Text(
                          "link9 : " +
                              notiCtr.datanotificationNews[index]["link9"]
                                  .toString(),
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Medium',
                            fontSize: mediaQuery(context, "h", 18),
                            color: const Color(0xfffcf6e4),
                            letterSpacing:
                                mediaQuery(context, "h", 1.4000000000000001),
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.left,
                        ),
                      ),
                    )
                  : Container(),
              (notiCtr.datanotificationNews[index]["link10"].toString() != "" &&
                      notiCtr.datanotificationNews[index]["link10"]
                              .toString() !=
                          "null")
                  ? GestureDetector(
                      onTap: () {
                        _launchURL(notiCtr.datanotificationNews[index]["link10"]
                            .toString());
                      },
                      child: Container(
                        width: mediaQuery(context, "w", 634),
                        color: Colors.transparent,
                        margin: EdgeInsets.only(
                            top: mediaQuery(context, "h", 5),
                            bottom: mediaQuery(context, "h", 5)),
                        child: Text(
                          "link10 : " +
                              notiCtr.datanotificationNews[index]["link10"]
                                  .toString(),
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Medium',
                            fontSize: mediaQuery(context, "h", 18),
                            color: const Color(0xfffcf6e4),
                            letterSpacing:
                                mediaQuery(context, "h", 1.4000000000000001),
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.left,
                        ),
                      ),
                    )
                  : Container(),
              notiCtr.datanotificationNews[index]["image"].toString() != ""
                  ? Container(
                      width: mediaQuery(context, "w", 634),
                      margin: EdgeInsets.only(
                          top: mediaQuery(context, "h", 10),
                          bottom: mediaQuery(context, "h", 90)),
                      child: Image.network(notiCtr.datanotificationNews[index]
                              ["image"]
                          .toString()),
                    )
                  : Container(),
            ],
          ),
        ));
  }

  DateTime safeParseDate(dynamic raw) {
    if (raw is String) {
      try {
        return DateTime.parse(raw);
      } catch (_) {}
    }
    return DateTime.now(); // fallback
  }

  Widget builFormCoruse(index) {
    DateTime dateTime =
        safeParseDate(notiCtr.datanotificationCourse[index]["create_time"]);

    return InkWell(
      onTap: () {
        setState(() {
          notiCtr.toggleExpandState(index);
        });
      },
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Container(
                width: Get.width,
                padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 5.h),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    notiCtr.isExpandList[index].value
                        ? Column(
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  SizedBox(
                                      width: 300.w,
                                      child: Row(
                                        children: [
                                          AppWidget.normalText(
                                            context,
                                            // 'notiCtr.datanotificationCourse[index]["create_time"].toString()'
                                            // ,
                                            DateFormat('dd/MM/yyyy hh:mm')
                                                .format(dateTime),
                                            12.sp,
                                            Theme.of(context)
                                                .colorScheme
                                                .onSecondary
                                                .withOpacity(1),
                                            FontWeight.w500,
                                          ),
                                          SizedBox(
                                            width: 8.w,
                                          )

                                          // , AppWidget.normalText(
                                          //   context,
                                          //   notiCtr.datanotificationCourse[index]["courseissue"].toString()
                                          //   ,
                                          //
                                          //   12.sp,
                                          //   Theme.of(context)
                                          //       .colorScheme
                                          //       .tertiary
                                          //       .withOpacity(1),
                                          //   FontWeight.w500,
                                          // )
                                        ],
                                      )),
                                  InkWell(
                                    onTap: () {
                                      setState(() {
                                        notiCtr.toggleExpandState(index);
                                      });
                                    },
                                    child: SizedBox(
                                      height: 24.h,
                                      width: 24.w,
                                      child: Image.asset(
                                        notiCtr.isExpandList[index].value
                                            ? 'assets/ADD/Expand_up_light.png'
                                            : 'assets/ADD/Expand_down_light.png',
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSecondary,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Container(

                                      // width: notiCtr.isExpandList[index].value
                                      //     ? Get.width * 0.70
                                      //     : Get.width * 0.70,
                                      child: AppWidget.normalText(
                                    context,
                                    notiCtr.datanotificationCourse[index]
                                                ["notify_title"]
                                            .toString() +
                                        "\n" +
                                        notiCtr.datanotificationCourse[index]
                                                ["content_title"]
                                            .toString(),
                                    12.sp,
                                    Theme.of(context)
                                        .colorScheme
                                        .onSecondary
                                        .withOpacity(1),
                                    FontWeight.w600,
                                  )),
                                  Visibility(
                                    visible: notiCtr.isExpandList[index].value,
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          children: [],
                                        ),
                                        SizedBox(
                                          height: 20.h,
                                        ),
                                        AppWidget.normalText(
                                          context,
                                          "เนื้อหา : ",
                                          13.sp,
                                          Theme.of(context)
                                              .colorScheme
                                              .tertiary
                                              .withOpacity(1),
                                          FontWeight.w600,
                                        ),
                                        GestureDetector(
                                          onTap: () {
                                            _launchURL(notiCtr
                                                .datanotificationCourse[index]
                                                    ["content_link"]
                                                .toString());
                                          },
                                          child: Container(
                                              width:
                                                  mediaQuery(context, "w", 634),
                                              color: Colors.transparent,
                                              margin: EdgeInsets.only(
                                                  top: mediaQuery(
                                                      context, "h", 5),
                                                  bottom: mediaQuery(
                                                      context, "h", 5)),
                                              child: AppWidget.normalText(
                                                context,
                                                "ลิงค์เนื้อหา",
                                                13.sp,
                                                Theme.of(context)
                                                    .colorScheme
                                                    .secondaryContainer
                                                    .withOpacity(1),
                                                FontWeight.w600,
                                              )),
                                        ),
                                        notiCtr.datanotificationCourse[index]
                                                    ["exam_link"]!
                                                .toString()
                                                .isNotEmpty
                                            ? GestureDetector(
                                                onTap: () {
                                                  _launchURL(notiCtr
                                                      .datanotificationCourse[
                                                          index]["exam_link"]
                                                      .toString());
                                                },
                                                child: Container(
                                                  width: mediaQuery(
                                                      context, "w", 634),
                                                  color: Colors.transparent,
                                                  margin: EdgeInsets.only(
                                                    top: mediaQuery(
                                                        context, "h", 5),
                                                    bottom: mediaQuery(
                                                        context, "h", 5),
                                                  ),
                                                  child: AppWidget.normalText(
                                                    context,
                                                    "ลิงค์ข้อสอบ",
                                                    13.sp,
                                                    Theme.of(context)
                                                        .colorScheme
                                                        .secondaryContainer
                                                        .withOpacity(1),
                                                    FontWeight.w600,
                                                  ),
                                                ))
                                            : Container(
                                                width: mediaQuery(
                                                    context, "w", 634),
                                                height: mediaQuery(
                                                    context, "h", 50),
                                              ),
                                        SizedBox(
                                          height: 20.h,
                                        ),
                                        Container(
                                            width:
                                                mediaQuery(context, "w", 634),
                                            color: Colors.transparent,
                                            margin: EdgeInsets.only(
                                                top:
                                                    mediaQuery(context, "h", 5),
                                                bottom: mediaQuery(
                                                    context, "h", 5)),
                                            child: AppWidget.normalText(
                                              context,
                                              notiCtr
                                                  .datanotificationCourse[index]
                                                      ["do_before"]
                                                  .toString(),
                                              13.sp,
                                              Theme.of(context)
                                                  .colorScheme
                                                  .secondaryFixed
                                                  .withOpacity(1),
                                              FontWeight.w600,
                                            )),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          )
                        : Column(
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  SizedBox(
                                      width: 300.w,
                                      child: Row(
                                        children: [
                                          AppWidget.normalText(
                                            context,
                                            // 'notiCtr.datanotificationCourse[index]["create_time"].toString()'
                                            // ,
                                            DateFormat('dd/MM/yyyy hh:mm')
                                                .format(dateTime),
                                            12.sp,
                                            Theme.of(context)
                                                .colorScheme
                                                .onSecondary
                                                .withOpacity(1),
                                            FontWeight.w600,
                                          ),
                                          SizedBox(
                                            width: 8.w,
                                          )
                                        ],
                                      )),
                                  InkWell(
                                    onTap: () {
                                      setState(() {
                                        notiCtr.toggleExpandState(index);
                                      });
                                    },
                                    child: SizedBox(
                                      height: 24.h,
                                      width: 24.w,
                                      child: Image.asset(
                                        notiCtr.isExpandList[index].value
                                            ? 'assets/ADD/Expand_up_light.png'
                                            : 'assets/ADD/Expand_down_light.png',
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSecondary,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                      alignment: Alignment.topLeft,
                                      width: notiCtr.isExpandList[index].value
                                          ? Get.width * 0.70
                                          : Get.width,
                                      child: AppWidget.normalTextRX(
                                        context,
                                        notiCtr.datanotificationCourse[index]
                                                ["notify_title"]
                                            .toString()
                                            .obs,
                                        12.sp,
                                        Theme.of(context)
                                            .colorScheme
                                            .onSecondary
                                            .withOpacity(1),
                                        FontWeight.w600,
                                      )),
                                ],
                              ),
                            ],
                          )
                  ],
                ),
              ),
            ],
          ),
          SizedBox(
            height: 20.h,
          ),
          Container(
            width: 393,
            decoration: BoxDecoration(
              border: Border.all(
                color: Theme.of(context)
                    .colorScheme
                    .onSecondaryContainer
                    .withOpacity(0.7),
                width: 0.3,
              ),
              color: Theme.of(context)
                  .colorScheme
                  .onSecondaryContainer
                  .withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  /// notiCtr.statusactionclick3 != "statusactionclick${i}"
  Widget ListNews(index) {
    return GetBuilder<NotiController>(builder: (notiCtr) {
      return GestureDetector(
        onTap: () {
          // notiCtr.statusactionclick3 = "statusactionclick${index.toString()}";

          notiCtr.checkreadstatus(
              context,
              notiCtr.datanotificationNews[index]["running"].toString(),
              notiCtr.datanotificationNews[index]["title"].toString(),
              notiCtr.datanotificationNews[index]["detail"].toString(),
              notiCtr.datanotificationNews[index]["typenotification"]
                  .toString(),
              "3",
              index);
        },
        child: Stack(
          children: [
            notiCtr.checkread(
                        context,
                        notiCtr.datanotificationNews[index]["running"]
                            .toString()) ==
                    "false"
                ? Container(
                    padding: EdgeInsets.only(
                        left: mediaQuery(context, "w", 70),
                        right: mediaQuery(context, "w", 86),
                        top: mediaQuery(context, "h", 72)),
                    child: Container(
                      width: mediaQuery(context, "h", 10),
                      height: mediaQuery(context, "h", 10),
                      decoration: BoxDecoration(
                        borderRadius:
                            BorderRadius.all(Radius.elliptical(9999.0, 9999.0)),
                        color: Colors.red,
                      ),
                    ),
                  )
                : Container(),
            Container(
              // color: Colors.red,
              height: notiCtr.statusactionclick3 == index
                  ? null
                  : mediaQuery(context, "h", 380),
              padding: EdgeInsets.only(
                  left: mediaQuery(context, "w", 86),
                  right: mediaQuery(context, "w", 86),
                  top: mediaQuery(context, "h", 65)),
              child: Stack(
                children: <Widget>[
                  Container(
                    alignment: Alignment.topRight,
                    child: notiCtr.statusactionclick3 == index
                        ? SvgPicture.string(
                            '<svg viewBox="732.0 369.0 19.0 10.9" ><path transform="matrix(0.0, 1.0, -1.0, 0.0, 757.19, 357.75)" d="M 14.52514743804932 15.6913948059082 L 21.71438598632812 8.5078125 C 22.24608421325684 7.976113796234131 22.24608421325684 7.11634635925293 21.71438598632812 6.590304374694824 C 21.18268775939941 6.05860710144043 20.32292175292969 6.064262390136719 19.79122161865234 6.590304374694824 L 11.64605903625488 14.72981262207031 C 11.13132858276367 15.24454116821289 11.12001609802246 16.07037162780762 11.60646343231201 16.6020679473877 L 19.78556632995605 24.79813957214355 C 20.05141830444336 25.06398963928223 20.4021110534668 25.19408416748047 20.74715042114258 25.19408416748047 C 21.09218597412109 25.19408416748047 21.44288063049316 25.06398963928223 21.70873260498047 24.79813957214355 C 22.24043083190918 24.26644134521484 22.24043083190918 23.40667533874512 21.70873260498047 22.8806324005127 L 14.52514743804932 15.6913948059082 Z" fill="#fcf6e4" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                            allowDrawingOutsideViewBox: true,
                            height: mediaQuery(context, "h", 19))
                        : SvgPicture.string(
                            '<svg viewBox="732.0 369.0 19.0 10.9" ><path transform="matrix(0.0, -1.0, 1.0, 0.0, 725.81, 391.11)" d="M 14.52514743804932 15.6913948059082 L 21.71438598632812 8.5078125 C 22.24608421325684 7.976113796234131 22.24608421325684 7.11634635925293 21.71438598632812 6.590304374694824 C 21.18268775939941 6.05860710144043 20.32292175292969 6.064262390136719 19.79122161865234 6.590304374694824 L 11.64605903625488 14.72981262207031 C 11.13132858276367 15.24454116821289 11.12001609802246 16.07037162780762 11.60646343231201 16.6020679473877 L 19.78556632995605 24.79813957214355 C 20.05141830444336 25.06398963928223 20.4021110534668 25.19408416748047 20.74715042114258 25.19408416748047 C 21.09218597412109 25.19408416748047 21.44288063049316 25.06398963928223 21.70873260498047 24.79813957214355 C 22.24043083190918 24.26644134521484 22.24043083190918 23.40667533874512 21.70873260498047 22.8806324005127 L 14.52514743804932 15.6913948059082 Z" fill="#fcf6e4" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                            allowDrawingOutsideViewBox: true,
                            height: mediaQuery(context, "h", 19)),
                  ),
                  Container(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Text(
                          cuttext(
                              notiCtr.datanotificationNews[index]["title"], 20),
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-SemiBold',
                            fontSize: mediaQuery(context, "h", 25),
                            color: const Color(0xfffee095),
                            letterSpacing:
                                mediaQuery(context, "h", 2.8000000000000003),
                            fontWeight: FontWeight.w700,
                          ),
                          textAlign: TextAlign.left,
                        ),
                        Row(
                          children: <Widget>[
                            Container(
                              child: Text(
                                convertDateTime(
                                    notiCtr.datanotificationNews[index]
                                        ["create_time"],
                                    "dd/MM/YYYY mm:ss"),
                                style: TextStyle(
                                  fontFamily: 'SukhumvitSet-Medium',
                                  fontSize: mediaQuery(context, "h", 22),
                                  color: const Color(0xff5F569B),
                                  letterSpacing: mediaQuery(context, "h", 1),
                                  fontWeight: FontWeight.w500,
                                  height: 1.6,
                                ),
                                textAlign: TextAlign.left,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 100)),
                    child: Column(
                      children: [
                        Text(
                          notiCtr.statusactionclick3 == index
                              ? notiCtr.datanotificationNews[index]["detail"]
                              : cuttext(
                                  notiCtr.datanotificationNews[index]["detail"],
                                  65),
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Medium',
                            fontSize: mediaQuery(context, "h", 25),
                            color: const Color(0xfffcf6e4),
                            letterSpacing:
                                mediaQuery(context, "h", 1.4000000000000001),
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.left,
                        ),
                        notiCtr.statusactionclick3 == index
                            ? Container(
                                child: Column(
                                  children: [
                                    (notiCtr.datanotificationNews[index]["link"]
                                                    .toString() !=
                                                "" &&
                                            notiCtr.datanotificationNews[index]
                                                        ["link"]
                                                    .toString() !=
                                                "null")
                                        ? GestureDetector(
                                            onTap: () {
                                              _launchURL(notiCtr
                                                  .datanotificationNews[index]
                                                      ["link"]
                                                  .toString());
                                            },
                                            child: Container(
                                              width:
                                                  mediaQuery(context, "w", 634),
                                              color: Colors.transparent,
                                              margin: EdgeInsets.only(
                                                  top: mediaQuery(
                                                      context, "h", 5),
                                                  bottom: mediaQuery(
                                                      context, "h", 5)),
                                              child: Text(
                                                "link1 : " +
                                                    notiCtr
                                                        .datanotificationNews[
                                                            index]["link"]
                                                        .toString(),
                                                style: TextStyle(
                                                  fontFamily:
                                                      'SukhumvitSet-Medium',
                                                  fontSize: mediaQuery(
                                                      context, "h", 25),
                                                  color:
                                                      const Color(0xfffcf6e4),
                                                  letterSpacing: mediaQuery(
                                                      context,
                                                      "h",
                                                      1.4000000000000001),
                                                  fontWeight: FontWeight.w500,
                                                ),
                                                textAlign: TextAlign.left,
                                              ),
                                            ),
                                          )
                                        : Container(
                                            width:
                                                mediaQuery(context, "w", 634),
                                            height:
                                                mediaQuery(context, "h", 50),
                                          ),
                                    (notiCtr.datanotificationNews[index]
                                                        ["link2"]
                                                    .toString() !=
                                                "" &&
                                            notiCtr.datanotificationNews[index]
                                                        ["link2"]
                                                    .toString() !=
                                                "null")
                                        ? GestureDetector(
                                            onTap: () {
                                              _launchURL(notiCtr
                                                  .datanotificationNews[index]
                                                      ["link2"]
                                                  .toString());
                                            },
                                            child: Container(
                                              width:
                                                  mediaQuery(context, "w", 634),
                                              color: Colors.transparent,
                                              margin: EdgeInsets.only(
                                                  top: mediaQuery(
                                                      context, "h", 5),
                                                  bottom: mediaQuery(
                                                      context, "h", 5)),
                                              child: Text(
                                                "link2 : " +
                                                    notiCtr
                                                        .datanotificationNews[
                                                            index]["link2"]
                                                        .toString(),
                                                style: TextStyle(
                                                  fontFamily:
                                                      'SukhumvitSet-Medium',
                                                  fontSize: mediaQuery(
                                                      context, "h", 18),
                                                  color:
                                                      const Color(0xfffcf6e4),
                                                  letterSpacing: mediaQuery(
                                                      context,
                                                      "h",
                                                      1.4000000000000001),
                                                  fontWeight: FontWeight.w500,
                                                ),
                                                textAlign: TextAlign.left,
                                              ),
                                            ),
                                          )
                                        : Container(),
                                    (notiCtr.datanotificationNews[index]
                                                        ["link3"]
                                                    .toString() !=
                                                "" &&
                                            notiCtr.datanotificationNews[index]
                                                        ["link3"]
                                                    .toString() !=
                                                "null")
                                        ? GestureDetector(
                                            onTap: () {
                                              _launchURL(notiCtr
                                                  .datanotificationNews[index]
                                                      ["link3"]
                                                  .toString());
                                            },
                                            child: Container(
                                              width:
                                                  mediaQuery(context, "w", 634),
                                              color: Colors.transparent,
                                              margin: EdgeInsets.only(
                                                  top: mediaQuery(
                                                      context, "h", 5),
                                                  bottom: mediaQuery(
                                                      context, "h", 5)),
                                              child: Text(
                                                "link3 : " +
                                                    notiCtr
                                                        .datanotificationNews[
                                                            index]["link3"]
                                                        .toString(),
                                                style: TextStyle(
                                                  fontFamily:
                                                      'SukhumvitSet-Medium',
                                                  fontSize: mediaQuery(
                                                      context, "h", 18),
                                                  color:
                                                      const Color(0xfffcf6e4),
                                                  letterSpacing: mediaQuery(
                                                      context,
                                                      "h",
                                                      1.4000000000000001),
                                                  fontWeight: FontWeight.w500,
                                                ),
                                                textAlign: TextAlign.left,
                                              ),
                                            ),
                                          )
                                        : Container(),
                                    (notiCtr.datanotificationNews[index]
                                                        ["link4"]
                                                    .toString() !=
                                                "" &&
                                            notiCtr.datanotificationNews[index]
                                                        ["link4"]
                                                    .toString() !=
                                                "null")
                                        ? GestureDetector(
                                            onTap: () {
                                              _launchURL(notiCtr
                                                  .datanotificationNews[index]
                                                      ["link4"]
                                                  .toString());
                                            },
                                            child: Container(
                                              width:
                                                  mediaQuery(context, "w", 634),
                                              color: Colors.transparent,
                                              margin: EdgeInsets.only(
                                                  top: mediaQuery(
                                                      context, "h", 5),
                                                  bottom: mediaQuery(
                                                      context, "h", 5)),
                                              child: Text(
                                                "link4 : " +
                                                    notiCtr
                                                        .datanotificationNews[
                                                            index]["link4"]
                                                        .toString(),
                                                style: TextStyle(
                                                  fontFamily:
                                                      'SukhumvitSet-Medium',
                                                  fontSize: mediaQuery(
                                                      context, "h", 18),
                                                  color:
                                                      const Color(0xfffcf6e4),
                                                  letterSpacing: mediaQuery(
                                                      context,
                                                      "h",
                                                      1.4000000000000001),
                                                  fontWeight: FontWeight.w500,
                                                ),
                                                textAlign: TextAlign.left,
                                              ),
                                            ),
                                          )
                                        : Container(),
                                    (notiCtr.datanotificationNews[index]
                                                        ["link5"]
                                                    .toString() !=
                                                "" &&
                                            notiCtr.datanotificationNews[index]
                                                        ["link5"]
                                                    .toString() !=
                                                "null")
                                        ? GestureDetector(
                                            onTap: () {
                                              _launchURL(notiCtr
                                                  .datanotificationNews[index]
                                                      ["link5"]
                                                  .toString());
                                            },
                                            child: Container(
                                              width:
                                                  mediaQuery(context, "w", 634),
                                              color: Colors.transparent,
                                              margin: EdgeInsets.only(
                                                  top: mediaQuery(
                                                      context, "h", 5),
                                                  bottom: mediaQuery(
                                                      context, "h", 5)),
                                              child: Text(
                                                "link5 : " +
                                                    notiCtr
                                                        .datanotificationNews[
                                                            index]["link5"]
                                                        .toString(),
                                                style: TextStyle(
                                                  fontFamily:
                                                      'SukhumvitSet-Medium',
                                                  fontSize: mediaQuery(
                                                      context, "h", 18),
                                                  color:
                                                      const Color(0xfffcf6e4),
                                                  letterSpacing: mediaQuery(
                                                      context,
                                                      "h",
                                                      1.4000000000000001),
                                                  fontWeight: FontWeight.w500,
                                                ),
                                                textAlign: TextAlign.left,
                                              ),
                                            ),
                                          )
                                        : Container(),
                                    (notiCtr.datanotificationNews[index]
                                                        ["link6"]
                                                    .toString() !=
                                                "" &&
                                            notiCtr.datanotificationNews[index]
                                                        ["link6"]
                                                    .toString() !=
                                                "null")
                                        ? GestureDetector(
                                            onTap: () {
                                              _launchURL(notiCtr
                                                  .datanotificationNews[index]
                                                      ["link6"]
                                                  .toString());
                                            },
                                            child: Container(
                                              width:
                                                  mediaQuery(context, "w", 634),
                                              color: Colors.transparent,
                                              margin: EdgeInsets.only(
                                                  top: mediaQuery(
                                                      context, "h", 5),
                                                  bottom: mediaQuery(
                                                      context, "h", 5)),
                                              child: Text(
                                                "link6 : " +
                                                    notiCtr
                                                        .datanotificationNews[
                                                            index]["link6"]
                                                        .toString(),
                                                style: TextStyle(
                                                  fontFamily:
                                                      'SukhumvitSet-Medium',
                                                  fontSize: mediaQuery(
                                                      context, "h", 18),
                                                  color:
                                                      const Color(0xfffcf6e4),
                                                  letterSpacing: mediaQuery(
                                                      context,
                                                      "h",
                                                      1.4000000000000001),
                                                  fontWeight: FontWeight.w500,
                                                ),
                                                textAlign: TextAlign.left,
                                              ),
                                            ),
                                          )
                                        : Container(),
                                    (notiCtr.datanotificationNews[index]
                                                        ["link7"]
                                                    .toString() !=
                                                "" &&
                                            notiCtr.datanotificationNews[index]
                                                        ["link7"]
                                                    .toString() !=
                                                "null")
                                        ? GestureDetector(
                                            onTap: () {
                                              _launchURL(notiCtr
                                                  .datanotificationNews[index]
                                                      ["link7"]
                                                  .toString());
                                            },
                                            child: Container(
                                              width:
                                                  mediaQuery(context, "w", 634),
                                              color: Colors.transparent,
                                              margin: EdgeInsets.only(
                                                  top: mediaQuery(
                                                      context, "h", 5),
                                                  bottom: mediaQuery(
                                                      context, "h", 5)),
                                              child: Text(
                                                "link7 : " +
                                                    notiCtr
                                                        .datanotificationNews[
                                                            index]["link7"]
                                                        .toString(),
                                                style: TextStyle(
                                                  fontFamily:
                                                      'SukhumvitSet-Medium',
                                                  fontSize: mediaQuery(
                                                      context, "h", 18),
                                                  color:
                                                      const Color(0xfffcf6e4),
                                                  letterSpacing: mediaQuery(
                                                      context,
                                                      "h",
                                                      1.4000000000000001),
                                                  fontWeight: FontWeight.w500,
                                                ),
                                                textAlign: TextAlign.left,
                                              ),
                                            ),
                                          )
                                        : Container(),
                                    (notiCtr.datanotificationNews[index]
                                                        ["link8"]
                                                    .toString() !=
                                                "" &&
                                            notiCtr.datanotificationNews[index]
                                                        ["link8"]
                                                    .toString() !=
                                                "null")
                                        ? GestureDetector(
                                            onTap: () {
                                              _launchURL(notiCtr
                                                  .datanotificationNews[index]
                                                      ["link8"]
                                                  .toString());
                                            },
                                            child: Container(
                                              width:
                                                  mediaQuery(context, "w", 634),
                                              color: Colors.transparent,
                                              margin: EdgeInsets.only(
                                                  top: mediaQuery(
                                                      context, "h", 5),
                                                  bottom: mediaQuery(
                                                      context, "h", 5)),
                                              child: Text(
                                                "link8 : " +
                                                    notiCtr
                                                        .datanotificationNews[
                                                            index]["link8"]
                                                        .toString(),
                                                style: TextStyle(
                                                  fontFamily:
                                                      'SukhumvitSet-Medium',
                                                  fontSize: mediaQuery(
                                                      context, "h", 18),
                                                  color:
                                                      const Color(0xfffcf6e4),
                                                  letterSpacing: mediaQuery(
                                                      context,
                                                      "h",
                                                      1.4000000000000001),
                                                  fontWeight: FontWeight.w500,
                                                ),
                                                textAlign: TextAlign.left,
                                              ),
                                            ),
                                          )
                                        : Container(),
                                    (notiCtr.datanotificationNews[index]
                                                        ["link9"]
                                                    .toString() !=
                                                "" &&
                                            notiCtr.datanotificationNews[index]
                                                        ["link9"]
                                                    .toString() !=
                                                "null")
                                        ? GestureDetector(
                                            onTap: () {
                                              _launchURL(notiCtr
                                                  .datanotificationNews[index]
                                                      ["link9"]
                                                  .toString());
                                            },
                                            child: Container(
                                              width:
                                                  mediaQuery(context, "w", 634),
                                              color: Colors.transparent,
                                              margin: EdgeInsets.only(
                                                  top: mediaQuery(
                                                      context, "h", 5),
                                                  bottom: mediaQuery(
                                                      context, "h", 5)),
                                              child: Text(
                                                "link9 : " +
                                                    notiCtr
                                                        .datanotificationNews[
                                                            index]["link9"]
                                                        .toString(),
                                                style: TextStyle(
                                                  fontFamily:
                                                      'SukhumvitSet-Medium',
                                                  fontSize: mediaQuery(
                                                      context, "h", 18),
                                                  color:
                                                      const Color(0xfffcf6e4),
                                                  letterSpacing: mediaQuery(
                                                      context,
                                                      "h",
                                                      1.4000000000000001),
                                                  fontWeight: FontWeight.w500,
                                                ),
                                                textAlign: TextAlign.left,
                                              ),
                                            ),
                                          )
                                        : Container(),
                                    (notiCtr.datanotificationNews[index]
                                                        ["link10"]
                                                    .toString() !=
                                                "" &&
                                            notiCtr.datanotificationNews[index]
                                                        ["link10"]
                                                    .toString() !=
                                                "null")
                                        ? GestureDetector(
                                            onTap: () {
                                              _launchURL(notiCtr
                                                  .datanotificationNews[index]
                                                      ["link10"]
                                                  .toString());
                                            },
                                            child: Container(
                                              width:
                                                  mediaQuery(context, "w", 634),
                                              color: Colors.transparent,
                                              margin: EdgeInsets.only(
                                                  top: mediaQuery(
                                                      context, "h", 5),
                                                  bottom: mediaQuery(
                                                      context, "h", 5)),
                                              child: Text(
                                                "link10 : " +
                                                    notiCtr
                                                        .datanotificationNews[
                                                            index]["link10"]
                                                        .toString(),
                                                style: TextStyle(
                                                  fontFamily:
                                                      'SukhumvitSet-Medium',
                                                  fontSize: mediaQuery(
                                                      context, "h", 18),
                                                  color:
                                                      const Color(0xfffcf6e4),
                                                  letterSpacing: mediaQuery(
                                                      context,
                                                      "h",
                                                      1.4000000000000001),
                                                  fontWeight: FontWeight.w500,
                                                ),
                                                textAlign: TextAlign.left,
                                              ),
                                            ),
                                          )
                                        : Container(),
                                    notiCtr.datanotificationNews[index]["image"]
                                                .toString() !=
                                            ""
                                        ? Container(
                                            width:
                                                mediaQuery(context, "w", 634),
                                            margin: EdgeInsets.only(
                                                top: mediaQuery(
                                                    context, "h", 10),
                                                bottom: mediaQuery(
                                                    context, "h", 90)),
                                            child: Image.network(notiCtr
                                                    .datanotificationNews[index]
                                                ["image"]),
                                          )
                                        : Container(),
                                  ],
                                ),
                              )
                            : Container(),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    });
  }

  _launchURL(url) async {
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not launch $url';
    }
  }

  Widget buildSelect() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 10.h),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            height: 35.h,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                GestureDetector(
                  onTap: () {
                    setState(() {
                      currentIndex = 0;
                      notiCtr.resetExpandList();
                    });
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 10.w),
                    height: 30.h,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.r),
                      border: Border.all(
                        color: currentIndex == 0
                            ? Theme.of(context).colorScheme.surfaceContainer
                            : Theme.of(context).colorScheme.surfaceContainer,
                        width: 1.0.w,
                      ),
                      color: currentIndex == 0
                          ? Theme.of(context).colorScheme.surfaceBright
                          : Theme.of(context)
                              .colorScheme
                              .surface
                              .withOpacity(0.1),
                    ),
                    child: Align(
                      alignment: Alignment.center,
                      child: AppWidget.normalText(
                        context,
                        'ui_claim'.tr,
                        14.sp,
                        currentIndex == 0
                            ? Theme.of(context).colorScheme.primaryContainer
                            : Theme.of(context).colorScheme.surfaceTint,
                        FontWeight.w500,
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  width: 10.w,
                ),
                GestureDetector(
                  onTap: () {
                    setState(() {
                      currentIndex = 1;
                      notiCtr.resetExpandList();
                    });
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 10.w),
                    height: 30.h,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(
                        color: currentIndex == 1
                            ? Theme.of(context).colorScheme.surfaceContainer
                            : Theme.of(context).colorScheme.surfaceContainer,
                        width: 1.0.w,
                      ),
                      color: currentIndex == 1
                          ? Theme.of(context).colorScheme.surfaceBright
                          : Theme.of(context).colorScheme.primary,
                    ),
                    child: Align(
                      alignment: Alignment.center,
                      child: AppWidget.normalText(
                        context,
                        'ui_news'.tr,
                        14.sp,
                        currentIndex == 1
                            ? Theme.of(context).colorScheme.primaryContainer
                            : Theme.of(context).colorScheme.surfaceTint,
                        FontWeight.w500,
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  width: 10.w,
                ),
                GestureDetector(
                  onTap: () {
                    setState(() {
                      currentIndex = 2;
                      notiCtr.resetExpandList();
                    });
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 10.w),
                    height: 30.h,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(
                        color: currentIndex == 2
                            ? Theme.of(context).colorScheme.surfaceContainer
                            : Theme.of(context).colorScheme.surfaceContainer,
                        width: 1.0.w,
                      ),
                      color: currentIndex == 2
                          ? Theme.of(context).colorScheme.surfaceBright
                          : Theme.of(context).colorScheme.primary,
                    ),
                    child: Align(
                      alignment: Alignment.center,
                      child: AppWidget.normalText(
                        context,
                        'ui_course'.tr,
                        14.sp,
                        currentIndex == 2
                            ? Theme.of(context).colorScheme.primaryContainer
                            : Theme.of(context).colorScheme.surfaceTint,
                        FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 650.h,
            width: Get.width,
            child: IndexedStack(
              index: currentIndex,
              children: [
                notiCtr.testNoti.isEmpty
                    ? buildEmtpy(context, 'ui_noti_emtpy'.tr)
                    : buildClaim(),
                notiCtr.testNews.isEmpty
                    ? buildEmtpy(context, 'ui_news_emtpy'.tr)
                    : buildNews(),
                notiCtr.testCrouse.isEmpty
                    ? buildEmtpy(context, 'ui_course_emtpy'.tr)
                    : buildCoruse(),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
