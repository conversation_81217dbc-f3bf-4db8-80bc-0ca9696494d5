import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:get/get_utils/src/extensions/internacionalization.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:mapp_ms24/controllers/internal/FinaceController/FinaceController.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/controllers/utils/connect.dart';
import 'package:mapp_ms24/controllers/utils/url.dart';
import 'package:mapp_ms24/controllers/utils/widget.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/FinanceDoc/DetailDoc.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/FinanceDoc/introNitrosign.dart';

import '../../../../../controllers/utils/color.dart';
import '../../../Widget/Widget.dart';

class listSignDoc extends StatefulWidget {
  final type;

  const listSignDoc(this.type);

  @override
  State<listSignDoc> createState() => _listSignDocState(type);
}

class _listSignDocState extends State<listSignDoc> {
  final type;
  _listSignDocState(this.type);
  FinaceController finaceCtr = Get.find<FinaceController>();
  ProfileController profile = Get.find<ProfileController>();
  // List dataNitrosignUser = [];
  bool checking = true;
  final currentpage = 0;
  void initState() {
    super.initState();
    initializePage();
  }

  void initializePage() async {
    await allLoad();
    checking = false;
    setState(() {});
  }

  allLoad() async {
    await loadNitrosign();
  }

  Future<void> loadNitrosign() async {
    try {
      Map data = {"id": profile.responseMember!.id};

      final response =
          await AppApi.callAPIjwt("POST", AppUrl.searchNitrosign, data);

      if (response["status"].toString() == "200") {
        for (var i = 0; i < response["result"].length; i++) {
          if (response["result"][i]["doc_type"].toString() ==
              widget.type.toString()) {
            finaceCtr.dataNitrosignUser.add(response["result"][i]);
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print("error getCarReg =>$e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withOpacity(1),
              boxShadow: [
                BoxShadow(
                  color: const Color(0x26000000), // Shadow color with opacity
                  offset: const Offset(0, 4),
                  blurRadius: 10,
                ),
              ],
            ),
            height: 852.h,
            width: Get.width,
            child: Column(
              children: [buildBody(context)],
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 80.h),
            child: RefreshIndicator(
              onRefresh: () => allLoad(),
              child: ListView(
                padding: EdgeInsets.only(
                    top: 40.h, left: 20.h, right: 20.h, bottom: 20.h),
                children: <Widget>[
                  Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: ListviewNitrosign()),
                ],
              ),
            ),
          ),
          checking
              ? Container(
                  width: MediaQuery.of(context).size.width,
                  height: MediaQuery.of(context).size.height,
                  color: Colors.black12,
                  child: Center(
                    child: SizedBox(
                      width: 60.w,
                      child: const LoadingIndicator(
                        indicatorType: Indicator.lineSpinFadeLoader,
                        colors: [Colors.white],
                      ),
                    ),
                  ))
              : Container(),
        ],
      ),
    );
  }

  Widget buildBody(context) {
    return Padding(
      padding: const EdgeInsets.only(top: 16.0),
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary.withOpacity(1),
          boxShadow: [
            BoxShadow(
              color: const Color(0x26000000), // Shadow color with opacity
              offset: const Offset(0, 4),
              blurRadius: 10,
            ),
          ],
        ),
        height: 100.h,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            // Back Arrow Container
            Container(
              child: InkWell(
                onTap: () {
                  Get.back();
                },
                child: SizedBox(
                  height: 34.h,
                  width: 34.w,
                  child: Center(
                    child: Container(
                      height: 14.h,
                      width: 14.w,
                      child: Image.asset(
                        'assets/ADD/back_Vector.png',
                        color: Theme.of(context).colorScheme.onSecondary,
                        scale: 1,
                      ),
                    ),
                  ),
                ),
              ),
            ),
            // Add some spacing between arrow and title

            // Title Text
            AppWidget.normalText(
              context,
              "ui_NitroListhead".tr.toString(),
              16.sp,
              Theme.of(context).colorScheme.scrim,
              FontWeight.w500,
            ),
            SizedBox(
              width: 34.w,
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> ListviewNitrosign() {
    List<Widget> list = [];
    if (finaceCtr.dataNitrosignUser.length.toInt() > 0) {
      for (var i = 0; i < finaceCtr.dataNitrosignUser.length.toInt(); i++) {
        list.add(Container(
          padding: EdgeInsets.fromLTRB(25.w, 10.h, 25.w, 10.h),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(mediaQuery(context, "h", 20)),
            gradient: LinearGradient(
              begin: Alignment(0.0, -1.0),
              end: Alignment(0.0, 1.0),
              colors: [
                Theme.of(context).colorScheme.primary,
                Theme.of(context).colorScheme.onPrimary,
              ],
              stops: [0.0, 1.0],
            ),
            boxShadow: [
              BoxShadow(
                color: Color(0xff000000),
                offset: Offset(0, 2),
                blurRadius: 1,
              ),
            ],
          ),
          child: Stack(
            children: [
              RichText(
                text: TextSpan(
                  children: <TextSpan>[
                    TextSpan(
                      text: widget.type == null
                          ? "ui_Nitroreference".tr.toString() +
                              " : " +
                              finaceCtr.dataNitrosignUser[i]["refer_id"]
                                  .toString()
                          : finaceCtr.dataNitrosignUser[i]["refer_id"]
                              .toString(),
                      style: TextStyle(
                        fontFamily: 'SukhumvitSet-Text',
                        fontSize: mediaQuery(context, "h", 28),
                        color: Theme.of(context).colorScheme.scrim,
                        letterSpacing:
                            mediaQuery(context, "h", 1.4000000000000001),
                      ),
                    ),
                    TextSpan(
                      text: "\n" + "ui_Nitrostatus".tr.toString() + " : ",
                      style: TextStyle(
                        fontFamily: 'SukhumvitSet-Text',
                        fontSize: mediaQuery(context, "h", 28),
                        color: Theme.of(context).colorScheme.scrim,
                        letterSpacing:
                            mediaQuery(context, "h", 1.4000000000000001),
                      ),
                    ),
                    TextSpan(
                      text: finaceCtr.dataNitrosignUser[i]["main_status"]
                                  .toString() ==
                              "wait_user"
                          ? "pending_sign".tr.toString()
                          : finaceCtr.dataNitrosignUser[i]["main_status"]
                              .toString(),
                      style: TextStyle(
                        fontFamily: 'SukhumvitSet-Text',
                        fontSize: mediaQuery(context, "h", 28),
                        color: AppColors.DeepSeaGreen,
                        letterSpacing:
                            mediaQuery(context, "h", 1.4000000000000001),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                alignment: Alignment.centerRight,
                child: GestureDetector(
                  onTap: () {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => introNitrosign(
                                finaceCtr.dataNitrosignUser[i],
                                finaceCtr.dataNitrosignUser[i]
                                    ["file_url_modify"],
                                currentpage)));
                  },
                  child: Container(
                    alignment: Alignment.center,
                    width: mediaQuery(context, "w", 170),
                    height: mediaQuery(context, "h", 90),
                    decoration: BoxDecoration(
                      borderRadius:
                          BorderRadius.circular(mediaQuery(context, "h", 50)),
                      color: AppColors.DeepSeaGreen,
                      boxShadow: const [
                        BoxShadow(
                          color: Color(0x4d000000),
                          offset: Offset(0, 2),
                          blurRadius: 20,
                        ),
                      ],
                    ),
                    child: Text(
                      "btn_NitroSign".tr.toString(),
                      style: TextStyle(
                        fontFamily: 'SukhumvitSet-Bold',
                        fontSize: mediaQuery(context, "h", 28),
                        color: Theme.of(context).colorScheme.scrim,
                        letterSpacing:
                            mediaQuery(context, "h", 1.4000000000000001),
                        fontWeight: FontWeight.w700,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ));
        list.add(Container(
          height: mediaQuery(context, "h", 25),
        ));
      }
    } else {
      list.add(
        buildEmtpy(context, 'ui_approval_document'.tr),
      );
    }

    return list;
  }
}
