@JS()
library pdf_renderer;

import 'package:js/js.dart';
import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'pdf_renderer.dart'; // import JS interop

@JS('renderPdfPage')
external Future<String> renderPdfPage(String base64Pdf, [int pageNumber, double scale]);

@JS('renderAllPages')
external Future<List<String>> renderAllPdfPages(String base64Pdf, [double scale]);


class PdfToImagePage extends StatefulWidget {
  
  final Uint8List pdfData;
  const PdfToImagePage({required this.pdfData});

  @override
  State<PdfToImagePage> createState() => _PdfToImagePageState();
}

class _PdfToImagePageState extends State<PdfToImagePage> {
  String? imageBase64;

  @override
  void initState() {
    super.initState();
    _convert();
  }

  Future<void> _convert() async {
    String base64String = base64Encode(widget.pdfData);
    String imageDataUrl = await renderPdfPage(base64String, 1, 2.0);
    setState(() {
      imageBase64 = imageDataUrl;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: imageBase64 != null
          ? Image.memory(base64Decode(imageBase64!.split(',').last))
          : const Center(child: CircularProgressIndicator()),
    );
  }
}
