import 'dart:async';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get_utils/src/extensions/internacionalization.dart';
import 'package:mapp_ms24/controllers/internal/FinaceController/FinaceController.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/FinanceDoc/createSignatureNitrosign.dart';

class nitrosignPDF extends StatefulWidget {
  final String? path;
  final String? linkPDFUpload;
  final dataPDF;
  nitrosignPDF({Key? key, this.path, this.linkPDFUpload, this.dataPDF})
      : super(key: key);

  @override
  State<nitrosignPDF> createState() => _nitrosignPDFState();
}

class _nitrosignPDFState extends State<nitrosignPDF> {
  final Completer<PDFViewController> _controller =
      Completer<PDFViewController>();
  final TextEditingController _detailController = TextEditingController();

  int pages = 0;
  int currentPage = 0;
  bool isReady = false;
  String errorMessage = '';
  int totalPage = 0;
  int statusAlertReport = 0;
  int statusHaveSign = 0;
  int statusAgreeSign = 0;

  ProfileController profile = ProfileController();
  FinaceController finaceController = FinaceController();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: <Widget>[
          Center(
              child: Container(
            color: const Color(0xff302C49),
          )),
          PDFView(
            filePath: widget.path,
            enableSwipe: true,
            swipeHorizontal: true,
            autoSpacing: false,
            pageFling: true,
            pageSnap: true,
            defaultPage: currentPage,
            fitPolicy: FitPolicy.BOTH,
            preventLinkNavigation: false,
            onRender: (_pages) {
              setState(() {
                pages = _pages!;
                isReady = true;
              });
            },
            onError: (error) {
              setState(() {
                errorMessage = error.toString();
              });
            },
            onPageError: (page, error) {
              setState(() {
                errorMessage = '$page: ${error.toString()}';
              });
            },
            onViewCreated: (PDFViewController pdfViewController) {
              _controller.complete(pdfViewController);
            },
            onLinkHandler: (uri) {},
            onPageChanged: (page, total) {
              setState(() {
                currentPage = page!;
                totalPage = total!;
              });
            },
          ),
          errorMessage.isEmpty
              ? !isReady
                  ? const Center(
                      child: CircularProgressIndicator(),
                    )
                  : Container()
              : Center(
                  child: Text(errorMessage),
                ),
          Container(
            margin: EdgeInsets.only(
                top: mediaQuery(context, "h", 154),
                left: mediaQuery(context, "w", 67)),
            child: GestureDetector(
              onTap: () {
                Navigator.of(context).pop();
              },
              child: Container(
                color: Colors.transparent,
                alignment: Alignment.center,
                height: mediaQuery(context, "h", 70),
                width: mediaQuery(context, "h", 70),
                child: Stack(
                  children: [
                    SvgPicture.string(
                      '<svg viewBox="67.0 154.0 21.7 38.0" ><path transform="translate(55.75, 147.81)" d="M 17.79955863952637 25.18868064880371 L 32.17768859863281 10.82186222076416 C 33.24105834960938 9.758491516113281 33.24105834960938 8.038998603820801 32.17768859863281 6.986940383911133 C 31.11431694030762 5.92357063293457 29.39482688903809 5.934882164001465 28.33145332336426 6.986940383911133 L 12.0415210723877 23.26556205749512 C 11.01208686828613 24.29499435424805 10.98946189880371 25.94661521911621 11.96233177185059 27.00998306274414 L 28.32014274597168 43.40172958374023 C 28.85183143615723 43.93341445922852 29.55320167541504 44.19359970092773 30.24326133728027 44.19359970092773 C 30.93332099914551 44.19359970092773 31.63469123840332 43.93341445922852 32.1663818359375 43.40172958374023 C 33.22975158691406 42.33835601806641 33.22975158691406 40.61886596679688 32.1663818359375 39.56680679321289 L 17.79955863952637 25.18868064880371 Z" fill="#fcf6e4" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                      allowDrawingOutsideViewBox: true,
                      height: mediaQuery(context, "h", 38),
                      color: Colors.black,
                    ),
                  ],
                ),
              ),
            ),
          ),
          Container(
            margin: EdgeInsets.only(
                top: mediaQuery(context, "h", 167),
                left: mediaQuery(context, "w", 150)),
            child: Text(
              "document_detail".tr.toString() + " (${currentPage + 1}/$pages)",
              style: TextStyle(
                  fontFamily: 'SukhumvitSet-SemiBold',
                  // fontSize: mediaQuery(context, "h", 30),
                  letterSpacing: mediaQuery(context, "h", 1.2),
                  fontWeight: FontWeight.w700),
            ),
          ),
          Container(
            alignment: Alignment.bottomCenter,
            padding: EdgeInsets.only(bottom: mediaQuery(context, "h", 100)),
            child: finaceController.remotePDFpath.isNotEmpty
                ? GestureDetector(
                    onTap: () async {},
                    child: Container(
                      alignment: Alignment.center,
                      height: mediaQuery(context, "h", 101),
                      width: mediaQuery(context, "w", 273),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(51.0),
                        color: const Color(0xff7f420c),
                        boxShadow: const [
                          BoxShadow(
                            color: Color(0x4d000000),
                            offset: Offset(0, 2),
                            blurRadius: 20,
                          ),
                        ],
                      ),
                      child: Text(
                        "btn_NitroSign".tr.toString(),
                        style: TextStyle(
                          fontFamily: 'SukhumvitSet-Bold',
                          fontSize: mediaQuery(context, "h", 30),
                          color: const Color(0xfffcf6e4),
                          letterSpacing: mediaQuery(context, "h", 1.5),
                          fontWeight: FontWeight.w700,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  )
                : Container(
                    alignment: Alignment.center,
                    height: mediaQuery(context, "h", 101),
                    width: mediaQuery(context, "w", 273),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(51.0),
                      color: const Color(0xff7f420c).withOpacity(0.3),
                      boxShadow: const [
                        BoxShadow(
                          color: Color(0x4d000000),
                          offset: Offset(0, 2),
                          blurRadius: 20,
                        ),
                      ],
                    ),
                    child: Text(
                      "btn_NitroSign".tr.toString(),
                      style: TextStyle(
                        fontFamily: 'SukhumvitSet-Bold',
                        fontSize: mediaQuery(context, "h", 30),
                        color: const Color(0xfffcf6e4),
                        letterSpacing: mediaQuery(context, "h", 1.5),
                        fontWeight: FontWeight.w700,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
          ),
          Container(
              alignment: Alignment.bottomCenter,
              padding: EdgeInsets.only(bottom: mediaQuery(context, "h", 40)),
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    statusAlertReport = 1;
                  });
                },
                child: Container(
                  child: Text(
                    "report_invalid_document".tr.toString(),
                    style: TextStyle(
                      fontFamily: 'SukhumvitSet-Text',
                      fontSize: mediaQuery(context, "h", 24),
                      color: Colors.red,
                      letterSpacing: mediaQuery(context, "h", 1.5),
                      fontWeight: FontWeight.w700,
                      decoration: TextDecoration.underline,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              )),
          AlertReport(),
        ],
      ),
    );
  }

  Widget AlertReport() {
    if (statusAlertReport == 1) {
      return Stack(
        children: <Widget>[
          ClipRect(
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 1.97, sigmaY: 1.97),
              child: Container(
                width: mediaQuery(context, "w", 828),
                height: mediaQuery(context, "h", 1792),
                decoration: const BoxDecoration(
                  color: Color(0x00bebebe),
                ),
              ),
            ),
          ),
          Container(
            alignment: Alignment.topCenter,
            margin: EdgeInsets.only(top: mediaQuery(context, "h", 405)),
            child: Container(
              width: mediaQuery(context, "w", 520),
              height: mediaQuery(context, "h", 640),
              decoration: BoxDecoration(
                borderRadius:
                    BorderRadius.circular(mediaQuery(context, "h", 80)),
                color: const Color(0xfffcf6e4),
              ),
              child: Stack(
                children: <Widget>[
                  Container(
                      alignment: Alignment.topCenter,
                      margin:
                          EdgeInsets.only(top: mediaQuery(context, "h", 82)),
                      child: SvgPicture.asset(
                        "assets/images/pkg/home/<USER>",
                        height: mediaQuery(context, "h", 50),
                      )),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 155)),
                    child: Text(
                      "report_invalid_document_detail".tr.toString(),
                      style: TextStyle(
                        fontFamily: 'SukhumvitSet-Bold',
                        fontSize: mediaQuery(context, "h", 33),
                        color: const Color(0xff302c49),
                        letterSpacing: mediaQuery(context, "h", 1.32),
                        fontWeight: FontWeight.w700,
                        height: 0.9393939393939394,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Container(
                    // alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 180)),
                    child: Container(
                      height: mediaQuery(context, "h", 140),
                      margin: EdgeInsets.only(
                          left: mediaQuery(context, "w", 44),
                          right: mediaQuery(context, "w", 44)),
                      decoration: BoxDecoration(
                        borderRadius:
                            BorderRadius.circular(mediaQuery(context, "h", 20)),
                        color: const Color(0xffFCF6E4),
                      ),
                      child: TextField(
                        cursorColor: Colors.black,
                        controller: _detailController,
                        maxLines: 1,
                        style: TextStyle(
                          color: const Color(0xff302c49),
                          fontSize: mediaQuery(context, "h", 33),
                          fontFamily: "SukhumvitSet-Text",
                        ),
                        decoration: InputDecoration(
                          counterText: '',
                          labelText: "tf_detailFeedback".tr.toString(),
                          labelStyle: TextStyle(
                            color: const Color(0xff302c49),
                            fontSize: mediaQuery(context, "h", 33),
                            fontFamily: "SukhumvitSet-Text",
                          ),
//                  icon: Image.asset(pictel),
                          enabledBorder: const UnderlineInputBorder(
                            borderSide: BorderSide(color: Color(0xff302c49)),
                          ),
                          focusedBorder: const UnderlineInputBorder(
                            borderSide: BorderSide(color: Color(0xff302c49)),
                          ),
                        ),
                      ),
                    ),
                  ),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 370)),
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          statusAlertReport = 0;
                        });
                      },
                      child: Container(
                        alignment: Alignment.center,
                        width: mediaQuery(context, "w", 250),
                        height: mediaQuery(context, "h", 90),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(
                              mediaQuery(context, "h", 30)),
                          color: const Color(0xff302c49),
                          border: Border.all(
                              width: 1.0, color: const Color(0xff302c49)),
                          boxShadow: const [
                            BoxShadow(
                              color: Color(0x1a000000),
                              offset: Offset(0, 1),
                              blurRadius: 2,
                            ),
                          ],
                        ),
                        child: Text(
                          "btn_settingcancel".tr.toString(),
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Bold',
                            fontSize: mediaQuery(context, "h", 30),
                            color: const Color(0xfffcf6e4),
                            letterSpacing: mediaQuery(context, "h", 1.2),
                            fontWeight: FontWeight.w700,
                            height: 1.0333333333333334,
                            shadows: const [
                              Shadow(
                                color: Color(0x26000000),
                                offset: Offset(0, 1),
                                blurRadius: 1,
                              )
                            ],
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 475)),
                    child: GestureDetector(
                      onTap: () async {
                        if (_detailController.text != "" &&
                            _detailController.text != "null") {
                          await finaceController.reportDocument(
                              widget.dataPDF,
                              _detailController.text,
                              profile.responseMember!.email);
                        }
                      },
                      child: Container(
                        alignment: Alignment.center,
                        width: mediaQuery(context, "w", 250),
                        height: mediaQuery(context, "h", 90),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(
                              mediaQuery(context, "h", 30)),
                          color: const Color(0xfe934d0f),
                        ),
                        child: Text(
                          "btn_report_invalid_document".tr.toString(),
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Bold',
                            fontSize: mediaQuery(context, "h", 30),
                            color: const Color(0xfeffffff),
                            letterSpacing: mediaQuery(context, "h", 1.2),
                            fontWeight: FontWeight.w700,
                            height: 1,
                            shadows: const [
                              Shadow(
                                color: Color(0x26000000),
                                offset: Offset(0, 1),
                                blurRadius: 1,
                              )
                            ],
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      );
    } else {
      return Container();
    }
  }

  int statusAlertSelectSign = 0;
  Widget AlertSelectSign() {
    if (statusAlertSelectSign == 1) {
      return GestureDetector(
        onTap: () {
          setState(() {
            statusAlertSelectSign = 0;
          });
        },
        child: ClipRect(
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 1.97, sigmaY: 1.97),
            child: Container(
              alignment: Alignment.topCenter,
              decoration: const BoxDecoration(
                color: Color(0x00bebebe),
              ),
              child: Container(
                width: mediaQuery(context, "w", 520),
                height: mediaQuery(context, "h", 650),
                margin: EdgeInsets.only(top: mediaQuery(context, "h", 300)),
                decoration: BoxDecoration(
                  borderRadius:
                      BorderRadius.circular(mediaQuery(context, "h", 80)),
                  color: const Color(0xfffcf6e4),
                ),
                child: Stack(
                  children: [
                    Container(
                      alignment: Alignment.topCenter,
                      margin:
                          EdgeInsets.only(top: mediaQuery(context, "h", 82)),
                      child: SvgPicture.string(
                        '<svg viewBox="130.0 0.0 50.0 50.0" ><path transform="translate(127.43, -1.93)" d="M 27.57070350646973 6.61553430557251 C 22.14501762390137 6.61553430557251 17.04413795471191 8.728425025939941 13.20761585235596 12.56494903564453 C 9.371090888977051 16.40147399902344 7.25820255279541 21.50235176086426 7.25820255279541 26.92803764343262 C 7.25820255279541 32.35371780395508 9.371092796325684 37.4546012878418 13.20761680603027 41.2911262512207 C 17.04413986206055 45.12765121459961 22.14501953125 47.24053955078125 27.57070350646973 47.24053955078125 C 32.99638748168945 47.24053955078125 38.09726333618164 45.12765121459961 41.93379211425781 41.2911262512207 C 45.77031326293945 37.4546012878418 47.88320541381836 32.35372161865234 47.88320541381836 26.92803764343262 C 47.88320541381836 21.50235176086426 45.77031326293945 16.40147399902344 41.933837890625 12.56494903564453 C 38.09736251831055 8.728425025939941 32.99638748168945 6.61553430557251 27.57070350646973 6.61553430557251 Z M 27.57070350646973 1.928033590316772 C 41.37783050537109 1.928033590316772 52.57070159912109 13.12090492248535 52.57070159912109 26.92803764343262 C 52.57070159912109 40.73516845703125 41.37783050537109 51.92803955078125 27.57070350646973 51.92803955078125 C 13.76357555389404 51.92803955078125 2.570701122283936 40.73516845703125 2.570701122283936 26.92803955078125 C 2.570701122283936 13.12090969085693 13.76357173919678 1.928035974502563 27.57070350646973 1.928035974502563 Z M 24.44570159912109 36.30303955078125 L 30.69570350646973 36.30303955078125 L 30.69570350646973 42.55304336547852 L 24.44570159912109 42.55304336547852 L 24.44570159912109 36.30303955078125 Z M 24.44570159912109 11.30303478240967 L 30.69570350646973 11.30303478240967 L 30.69570350646973 30.05303573608398 L 24.44570159912109 30.05303573608398 L 24.44570159912109 11.30303478240967 Z" fill="#934d0f" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                        allowDrawingOutsideViewBox: true,
                        fit: BoxFit.fitHeight,
                        height: mediaQuery(context, "h", 50),
                      ),
                    ),
                    Container(
                      alignment: Alignment.topCenter,
                      margin:
                          EdgeInsets.only(top: mediaQuery(context, "h", 155)),
                      child: Text(
                        "ui_NitroSelect".tr.toString(),
                        style: TextStyle(
                          fontFamily: 'SukhumvitSet-Bold',
                          fontSize: mediaQuery(context, "h", 33),
                          color: const Color(0xff302c49),
                          letterSpacing: mediaQuery(context, "h", 1.32),
                          fontWeight: FontWeight.w700,
                          height: 0.9393939393939394,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Container(
                      alignment: Alignment.topCenter,
                      margin:
                          EdgeInsets.only(top: mediaQuery(context, "h", 212)),
                      child: Text(
                        "ui_NitroSelectDetail".tr.toString(),
                        style: TextStyle(
                          fontFamily: 'SukhumvitSet-Medium',
                          fontSize: mediaQuery(context, "h", 28),
                          color: const Color(0xff302c49),
                          letterSpacing: mediaQuery(context, "h", 1.12),
                          fontWeight: FontWeight.w500,
                          height: 1.5357142857142858,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Container(
                      alignment: Alignment.topCenter,
                      margin:
                          EdgeInsets.only(top: mediaQuery(context, "h", 385)),
                      child: statusHaveSign == 0
                          ? GestureDetector(
                              onTap: () {
                                // setState(() {
                                //   if (statusAgreeSign == 0) {
                                //     if (finaceController.linkPDFS3.isNotEmpty) {
                                //       Navigator.push(
                                //           context,
                                //           MaterialPageRoute(
                                //             builder: (context) =>
                                //                 painterPage(finaceController.linkPDFS3, finaceController.pdfName, widget.dataPDF, finaceController.remotePDFpath, currentPage),
                                //           )
                                //       );
                                //     }
                                //   } else {
                                //     statusAlertSelectSign = 0;
                                //     statusAlertAgreeSign = 1;
                                //   }
                                // });
                              },
                              child: Container(
                                alignment: Alignment.center,
                                width: mediaQuery(context, "w", 250),
                                height: mediaQuery(context, "h", 90),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(
                                      mediaQuery(context, "h", 30)),
                                  color: const Color(0xff7f420c),
                                  boxShadow: const [
                                    BoxShadow(
                                      color: Color(0x1a000000),
                                      offset: Offset(0, 1),
                                      blurRadius: 2,
                                    ),
                                  ],
                                ),
                                child: Text(
                                  "ui_NitroSelectExist".tr.toString(),
                                  style: TextStyle(
                                    fontFamily: 'SukhumvitSet-Bold',
                                    fontSize: mediaQuery(context, "h", 30),
                                    color: const Color(0xfffcf6e4),
                                    letterSpacing:
                                        mediaQuery(context, "h", 1.2),
                                    fontWeight: FontWeight.w700,
                                    shadows: const [
                                      Shadow(
                                        color: Color(0x26000000),
                                        offset: Offset(0, 1),
                                        blurRadius: 1,
                                      )
                                    ],
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            )
                          : GestureDetector(
                              onTap: () {},
                              child: Container(
                                alignment: Alignment.center,
                                width: mediaQuery(context, "w", 250),
                                height: mediaQuery(context, "h", 90),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(
                                      mediaQuery(context, "h", 30)),
                                  color:
                                      const Color(0xff7f420c).withOpacity(0.3),
                                  boxShadow: const [
                                    BoxShadow(
                                      color: Color(0x1a000000),
                                      offset: Offset(0, 1),
                                      blurRadius: 2,
                                    ),
                                  ],
                                ),
                                child: Text(
                                  "ui_NitroSelectExist".tr.toString(),
                                  style: TextStyle(
                                    fontFamily: 'SukhumvitSet-Bold',
                                    fontSize: mediaQuery(context, "h", 30),
                                    color: const Color(0xfffcf6e4),
                                    letterSpacing:
                                        mediaQuery(context, "h", 1.2),
                                    fontWeight: FontWeight.w700,
                                    shadows: const [
                                      Shadow(
                                        color: Color(0x26000000),
                                        offset: Offset(0, 1),
                                        blurRadius: 1,
                                      )
                                    ],
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                    ),
                    Container(
                      alignment: Alignment.topCenter,
                      margin:
                          EdgeInsets.only(top: mediaQuery(context, "h", 500)),
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            statusAlertSelectSign = 0;
                          });
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) =>
                                      createSignatureNitrosign(1)));
                        },
                        child: Container(
                          alignment: Alignment.center,
                          width: mediaQuery(context, "w", 250),
                          height: mediaQuery(context, "h", 90),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(
                                mediaQuery(context, "h", 30)),
                            color: const Color(0xff7f420c),
                            boxShadow: const [
                              BoxShadow(
                                color: Color(0x1a000000),
                                offset: Offset(0, 1),
                                blurRadius: 2,
                              ),
                            ],
                          ),
                          child: Text(
                            "ui_NitroSelectNew".tr.toString(),
                            style: TextStyle(
                              fontFamily: 'SukhumvitSet-Bold',
                              fontSize: mediaQuery(context, "h", 30),
                              color: const Color(0xfffcf6e4),
                              letterSpacing: mediaQuery(context, "h", 1.2),
                              fontWeight: FontWeight.w700,
                              // height: 1.0333333333333334,
                              shadows: const [
                                Shadow(
                                  color: Color(0x26000000),
                                  offset: Offset(0, 1),
                                  blurRadius: 1,
                                )
                              ],
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      );
    } else {
      return Container();
    }
  }

  int statusAlertAgreeSign = 0;
  Widget AlertAgreeSign() {
    if (statusAlertAgreeSign == 1) {
      return GestureDetector(
        onTap: () {
          setState(() {
            statusAlertAgreeSign = 0;
          });
        },
        child: ClipRect(
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 1.97, sigmaY: 1.97),
            child: Container(
              alignment: Alignment.topCenter,
              decoration: const BoxDecoration(
                color: Color(0x00bebebe),
              ),
              child: Container(
                width: mediaQuery(context, "w", 520),
                height: mediaQuery(context, "h", 650),
                margin: EdgeInsets.only(top: mediaQuery(context, "h", 300)),
                decoration: BoxDecoration(
                  borderRadius:
                      BorderRadius.circular(mediaQuery(context, "h", 80)),
                  color: const Color(0xfffcf6e4),
                ),
                child: Stack(
                  children: [
                    Container(
                      alignment: Alignment.topCenter,
                      margin:
                          EdgeInsets.only(top: mediaQuery(context, "h", 82)),
                      child: SvgPicture.string(
                        '<svg viewBox="130.0 0.0 50.0 50.0" ><path transform="translate(127.43, -1.93)" d="M 27.57070350646973 6.61553430557251 C 22.14501762390137 6.61553430557251 17.04413795471191 8.728425025939941 13.20761585235596 12.56494903564453 C 9.371090888977051 16.40147399902344 7.25820255279541 21.50235176086426 7.25820255279541 26.92803764343262 C 7.25820255279541 32.35371780395508 9.371092796325684 37.4546012878418 13.20761680603027 41.2911262512207 C 17.04413986206055 45.12765121459961 22.14501953125 47.24053955078125 27.57070350646973 47.24053955078125 C 32.99638748168945 47.24053955078125 38.09726333618164 45.12765121459961 41.93379211425781 41.2911262512207 C 45.77031326293945 37.4546012878418 47.88320541381836 32.35372161865234 47.88320541381836 26.92803764343262 C 47.88320541381836 21.50235176086426 45.77031326293945 16.40147399902344 41.933837890625 12.56494903564453 C 38.09736251831055 8.728425025939941 32.99638748168945 6.61553430557251 27.57070350646973 6.61553430557251 Z M 27.57070350646973 1.928033590316772 C 41.37783050537109 1.928033590316772 52.57070159912109 13.12090492248535 52.57070159912109 26.92803764343262 C 52.57070159912109 40.73516845703125 41.37783050537109 51.92803955078125 27.57070350646973 51.92803955078125 C 13.76357555389404 51.92803955078125 2.570701122283936 40.73516845703125 2.570701122283936 26.92803955078125 C 2.570701122283936 13.12090969085693 13.76357173919678 1.928035974502563 27.57070350646973 1.928035974502563 Z M 24.44570159912109 36.30303955078125 L 30.69570350646973 36.30303955078125 L 30.69570350646973 42.55304336547852 L 24.44570159912109 42.55304336547852 L 24.44570159912109 36.30303955078125 Z M 24.44570159912109 11.30303478240967 L 30.69570350646973 11.30303478240967 L 30.69570350646973 30.05303573608398 L 24.44570159912109 30.05303573608398 L 24.44570159912109 11.30303478240967 Z" fill="#934d0f" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                        allowDrawingOutsideViewBox: true,
                        fit: BoxFit.fitHeight,
                        height: mediaQuery(context, "h", 50),
                      ),
                    ),
                    Container(
                      alignment: Alignment.topCenter,
                      margin:
                          EdgeInsets.only(top: mediaQuery(context, "h", 155)),
                      child: Text(
                        "ui_NitroAgree".tr.toString(),
                        style: TextStyle(
                          fontFamily: 'SukhumvitSet-Bold',
                          fontSize: mediaQuery(context, "h", 33),
                          color: const Color(0xff302c49),
                          letterSpacing: mediaQuery(context, "h", 1.32),
                          fontWeight: FontWeight.w700,
                          height: 0.9393939393939394,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Container(
                      alignment: Alignment.topCenter,
                      margin:
                          EdgeInsets.only(top: mediaQuery(context, "h", 212)),
                      child: Text(
                        "ui_NitroAgreeDetail".tr.toString(),
                        style: TextStyle(
                          fontFamily: 'SukhumvitSet-Medium',
                          fontSize: mediaQuery(context, "h", 28),
                          color: const Color(0xff302c49),
                          letterSpacing: mediaQuery(context, "h", 1.12),
                          fontWeight: FontWeight.w500,
                          height: 1.5357142857142858,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Container(
                      alignment: Alignment.topCenter,
                      margin:
                          EdgeInsets.only(top: mediaQuery(context, "h", 385)),
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            statusAlertAgreeSign = 0;
                          });
                          finaceController.updateAgreeSign();
                        },
                        child: Container(
                          alignment: Alignment.center,
                          width: mediaQuery(context, "w", 250),
                          height: mediaQuery(context, "h", 90),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(
                                mediaQuery(context, "h", 30)),
                            color: const Color(0xff7f420c),
                            boxShadow: const [
                              BoxShadow(
                                color: Color(0x1a000000),
                                offset: Offset(0, 1),
                                blurRadius: 2,
                              ),
                            ],
                          ),
                          child: Text(
                            "ui_NitroAgreeOK".tr.toString(),
                            style: TextStyle(
                              fontFamily: 'SukhumvitSet-Bold',
                              fontSize: mediaQuery(context, "h", 30),
                              color: const Color(0xfffcf6e4),
                              letterSpacing: mediaQuery(context, "h", 1.2),
                              fontWeight: FontWeight.w700,
                              shadows: const [
                                Shadow(
                                  color: Color(0x26000000),
                                  offset: Offset(0, 1),
                                  blurRadius: 1,
                                )
                              ],
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      alignment: Alignment.topCenter,
                      margin:
                          EdgeInsets.only(top: mediaQuery(context, "h", 500)),
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            statusAlertAgreeSign = 0;
                          });
                          // Navigator.push(
                          //     context,
                          //     MaterialPageRoute(
                          //         builder: (context) =>
                          //             createSignatureNitrosign(1)));
                        },
                        child: Container(
                          alignment: Alignment.center,
                          width: mediaQuery(context, "w", 250),
                          height: mediaQuery(context, "h", 90),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(
                                mediaQuery(context, "h", 30)),
                            color: Colors.red,
                            boxShadow: const [
                              BoxShadow(
                                color: Color(0x1a000000),
                                offset: Offset(0, 1),
                                blurRadius: 2,
                              ),
                            ],
                          ),
                          child: Text(
                            "ui_NitroAgreeNO".tr.toString(),
                            style: TextStyle(
                              fontFamily: 'SukhumvitSet-Bold',
                              fontSize: mediaQuery(context, "h", 30),
                              color: const Color(0xfffcf6e4),
                              letterSpacing: mediaQuery(context, "h", 1.2),
                              fontWeight: FontWeight.w700,
                              // height: 1.0333333333333334,
                              shadows: const [
                                Shadow(
                                  color: Color(0x26000000),
                                  offset: Offset(0, 1),
                                  blurRadius: 1,
                                )
                              ],
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      );
    } else {
      return Container();
    }
  }
}
