import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/FinanceDoc/pdf_renderer.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../../controllers/internal/FinaceController/FinaceController.dart';
import '../../../../../controllers/internal/NotiController/NotifiController.dart';
import '../../../../../controllers/utils/color.dart';
import '../../../../../controllers/utils/widget.dart';
import '../../../Widget/Widget.dart';
import '../../../Widget/library.dart';
import 'NDAdoc/introNDA.dart';
import 'introNitrosign.dart';
import 'listSignDoc.dart';
import 'package:http/http.dart' as http;

class AllSelectDoc extends StatelessWidget {
  AllSelectDoc({super.key});

  NotiController notiCtr = Get.find<NotiController>();
  FinaceController fiiCtr = Get.find<FinaceController>();
  ProfileController profileCtr = Get.find<ProfileController>();

  Future<Uint8List> loadPdfFromUrl(String url) async {
    final response = await http.get(Uri.parse(url));
    if (response.statusCode == 200) {
      return response.bodyBytes;
    } else {
      throw Exception('Failed to load PDF');
    }
  }

  @override
  Widget build(BuildContext context) {
    fiiCtr.loadLinkNDA(0);
    fiiCtr.loadNitrosign("null");
    return Scaffold(
        body: Container(
      height: 852.h,
      width: 393.w,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.onPrimary,
          ],
          begin: Alignment.topCenter, // เริ่มต้น Gradient ที่มุมบนซ้าย
          end: Alignment.bottomCenter,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.only(top: 30),
        child: Column(
          children: [
            buildAppBarWithFunction(context, 'ui_NitroListhead'.tr, ''.tr, '',
                () async {
              showAlert(context);
            }),
            buildSelect(context),
          ],
        ),
      ),
    ));
  }

  Future showAlert(BuildContext context) {
    return showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Theme.of(context).primaryColor,
          title: Center(child: Text('')),
          content: SingleChildScrollView(
            // Wrap with SingleChildScrollView
            child: Container(
              height:
                  250.h, // You can remove this or keep it based on your design
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Center(
                      child: AppWidget.normalText(
                        context,
                        'ลบข้อความทั้งหมด'.tr,
                        16.sp,
                        Theme.of(context).colorScheme.scrim,
                        FontWeight.w400,
                      ),
                    ),
                    SizedBox(height: 10.h),
                    SizedBox(
                      width: 40.w,
                      height: 40.h,
                      child: Image.asset('assets/Group.png'),
                    ),
                    SizedBox(height: 10.h),
                    Center(
                      child: AppWidget.normalText(
                        context,
                        'คุณต้องการลบข้อความทั้งหมด'.tr,
                        14.sp,
                        Theme.of(context).colorScheme.scrim,
                        FontWeight.w200,
                      ),
                    ),
                    Center(
                      child: AppWidget.normalText(
                        context,
                        'ใช่หรือไม่'.tr,
                        14.sp,
                        Theme.of(context).colorScheme.scrim,
                        FontWeight.w200,
                      ),
                    ),
                    SizedBox(height: 20.h),
                    InkWell(
                      onTap: () {
                        fiiCtr.currentIndex == 0.obs
                            ? notiCtr.testNoti.clear()
                            : notiCtr.testNews.clear();

                        Get.back();
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Theme.of(context)
                                .colorScheme
                                .secondaryContainer,
                            width: 1.0.w,
                          ),
                          color: AppColors.DeepSeaGreen,
                        ),
                        width: 235.w, // Fixed width of 235px
                        height: 46.h, // Fixed height of 46px
                        child: Center(
                          child: Text(
                            'ui_delete_3'.tr,
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: 10.h), // Space between buttons
                    InkWell(
                      onTap: () {
                        Get.back();
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        width: 235.w, // Fixed width of 235px
                        height: 46.h,
                        child: Center(
                          child: AppWidget.normalText(
                            context,
                            'ui_delete_4'.tr,
                            14.sp,
                            Theme.of(context).colorScheme.scrim,
                            FontWeight.w400,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          actions: [],
        );
      },
    );
  }

  Widget buildSelect(context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 10.h),
      child: Column(
        children: [
          Container(
              padding: EdgeInsets.symmetric(horizontal: 20.w),
              height: 35.h,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  GestureDetector(
                    onTap: () {
                      fiiCtr.updatePage(0);
                      notiCtr.resetExpandList();
                    },
                    child: Obx(() => Container(
                          padding: EdgeInsets.symmetric(horizontal: 10.w),
                          height: 30.h,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10.r),
                            border: Border.all(
                              color: fiiCtr.currentIndex.value == 0
                                  ? Theme.of(context)
                                      .colorScheme
                                      .surfaceContainer
                                  : Theme.of(context)
                                      .colorScheme
                                      .surfaceContainer,
                              width: 1.0.w,
                            ),
                            color: fiiCtr.currentIndex.value == 0
                                ? Theme.of(context).colorScheme.surfaceBright
                                : Theme.of(context)
                                    .colorScheme
                                    .surface
                                    .withOpacity(0.1),
                          ),
                          child: Align(
                            alignment: Alignment.center,
                            child: AppWidget.normalText(
                              context,
                              'ui_finace'.tr,
                              14.sp,
                              fiiCtr.currentIndex.value == 0
                                  ? Theme.of(context)
                                      .colorScheme
                                      .primaryContainer
                                  : Theme.of(context).colorScheme.surfaceTint,
                              FontWeight.w500,
                            ),
                          ),
                        )),
                  ),
                  SizedBox(
                    width: 10.w,
                  ),
                  GestureDetector(
                    onTap: () {
                      fiiCtr.updatePage(1);
                      notiCtr.resetExpandList();
                    },
                    child: Obx(() => Container(
                          padding: EdgeInsets.symmetric(horizontal: 10.w),
                          height: 30.h,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8.r),
                            border: Border.all(
                              color: fiiCtr.currentIndex.value == 1
                                  ? Theme.of(context)
                                      .colorScheme
                                      .surfaceContainer
                                  : Theme.of(context)
                                      .colorScheme
                                      .surfaceContainer,
                              width: 1.0.w,
                            ),
                            color: fiiCtr.currentIndex.value == 1
                                ? Theme.of(context).colorScheme.surfaceBright
                                : Theme.of(context).colorScheme.primary,
                          ),
                          child: Align(
                            alignment: Alignment.center,
                            child: AppWidget.normalText(
                              context,
                              'ui_emp'.tr,
                              14.sp,
                              fiiCtr.currentIndex.value == 1
                                  ? Theme.of(context)
                                      .colorScheme
                                      .primaryContainer
                                  : Theme.of(context).colorScheme.surfaceTint,
                              FontWeight.w500,
                            ),
                          ),
                        )),
                  ),
                  SizedBox(
                    width: 10.w,
                  ),
                  GestureDetector(
                    onTap: () {
                      fiiCtr.updatePage(2);
                      notiCtr.resetExpandList();
                    },
                    child: Obx(() => Container(
                          padding: EdgeInsets.symmetric(horizontal: 10.w),
                          height: 30.h,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8.r),
                            border: Border.all(
                              color: fiiCtr.currentIndex.value == 2
                                  ? Theme.of(context)
                                      .colorScheme
                                      .surfaceContainer
                                  : Theme.of(context)
                                      .colorScheme
                                      .surfaceContainer,
                              width: 1.0.w,
                            ),
                            color: fiiCtr.currentIndex.value == 2
                                ? Theme.of(context).colorScheme.surfaceBright
                                : Theme.of(context).colorScheme.primary,
                          ),
                          child: Align(
                            alignment: Alignment.center,
                            child: AppWidget.normalText(
                              context,
                              'ui_work'.tr,
                              14.sp,
                              fiiCtr.currentIndex.value == 2
                                  ? Theme.of(context)
                                      .colorScheme
                                      .primaryContainer
                                  : Theme.of(context).colorScheme.surfaceTint,
                              FontWeight.w500,
                            ),
                          ),
                        )),
                  ),
                  SizedBox(
                    width: 10.w,
                  ),
                  GestureDetector(
                    onTap: () {
                      fiiCtr.updatePage(3);
                      notiCtr.resetExpandList();
                    },
                    child: Obx(() => Container(
                          padding: EdgeInsets.symmetric(horizontal: 10.w),
                          height: 30.h,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8.r),
                            border: Border.all(
                              color: fiiCtr.currentIndex.value == 3
                                  ? Theme.of(context)
                                      .colorScheme
                                      .surfaceContainer
                                  : Theme.of(context)
                                      .colorScheme
                                      .surfaceContainer,
                              width: 1.0.w,
                            ),
                            color: fiiCtr.currentIndex.value == 3
                                ? Theme.of(context).colorScheme.surfaceBright
                                : Theme.of(context).colorScheme.primary,
                          ),
                          child: Align(
                            alignment: Alignment.center,
                            child: AppWidget.normalText(
                              context,
                              'NDA'.tr,
                              14.sp,
                              fiiCtr.currentIndex.value == 3
                                  ? Theme.of(context)
                                      .colorScheme
                                      .primaryContainer
                                  : Theme.of(context).colorScheme.surfaceTint,
                              FontWeight.w500,
                            ),
                          ),
                        )),
                  ),
                ],
              )),
          SizedBox(
            height: 650.h,
            width: Get.width,
            child: Obx(() {
              // Check if the data is being loaded
              if (fiiCtr.isLoading.value) {
                return SizedBox(
                  height: 100.h,
                  width: 100.w,
                  child: Center(
                    child: CircularProgressIndicator(
                      color: Colors.black,
                    ),
                  ),
                ); // Show loading spinner
              }

              // If not loading, continue with the IndexedStack logic
              return IndexedStack(
                index: fiiCtr.currentIndex.value,
                children: [
                  fiiCtr.dataNitrosignUser.isEmpty
                      ? buildEmtpy(context, 'ui_doc_emtpy'.tr)
                      : buildClaim(context),
                  fiiCtr.dataNitrosignUser.isEmpty
                      ? buildEmtpy(context, 'ui_doc_emtpy'.tr)
                      : buildNews(),
                  fiiCtr.dataNitrosignUser.isEmpty
                      ? buildEmtpy(context, 'ui_doc_emtpy'.tr)
                      : buildPerson(),
                  IntroNDAScreen(),
                ],
              );
            }),
          )
        ],
      ),
    );
  }

  Widget buildClaim(BuildContext context) {
    return Obx(() {
      return fiiCtr.isLoading.value
          ? Center(
              child:
                  CircularProgressIndicator()) // Show loading when isLoading is true
          : ListView.builder(
              itemCount: fiiCtr.dataNitrosignUser.length,
              padding: EdgeInsets.zero,
              itemBuilder: (context, index) {
                return builFormnoti(
                  fiiCtr.dataNitrosignUser[index]['update_time']!,
                  fiiCtr.dataNitrosignUser[index]['refer_id']!,
                  fiiCtr.dataNitrosignUser[index]['main_status']!,
                  index,
                  context,
                );
              },
            );
    });
  }

  List<Widget> ListviewNitrosign(context) {
    List<Widget> list = [];
    var type = '';
    if (fiiCtr.dataNitrosignUser.length.toInt() > 0) {
      for (var i = 0; i < fiiCtr.dataNitrosignUser.length.toInt(); i++) {
        list.add(Container(
          padding: EdgeInsets.fromLTRB(25.w, 10.h, 25.w, 10.h),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(mediaQuery(context, "h", 20)),
            gradient: LinearGradient(
              begin: Alignment(0.0, -1.0),
              end: Alignment(0.0, 1.0),
              colors: [
                Theme.of(context).colorScheme.primary,
                Theme.of(context).colorScheme.onPrimary,
              ],
              stops: [0.0, 1.0],
            ),
            boxShadow: [
              BoxShadow(
                color: Color(0xff000000),
                offset: Offset(0, 2),
                blurRadius: 1,
              ),
            ],
          ),
          child: Stack(
            children: [
              RichText(
                text: TextSpan(
                  children: <TextSpan>[
                    TextSpan(
                      text: type == null
                          ? "ui_Nitroreference".tr.toString() +
                              " : " +
                              fiiCtr.dataNitrosignUser[i]["refer_id"].toString()
                          : fiiCtr.dataNitrosignUser[i]["refer_id"].toString(),
                      style: TextStyle(
                        fontFamily: 'SukhumvitSet-Text',
                        fontSize: mediaQuery(context, "h", 28),
                        color: Theme.of(context).colorScheme.scrim,
                        letterSpacing:
                            mediaQuery(context, "h", 1.4000000000000001),
                      ),
                    ),
                    TextSpan(
                      text: "\n" + "ui_Nitrostatus".tr.toString() + " : ",
                      style: TextStyle(
                        fontFamily: 'SukhumvitSet-Text',
                        fontSize: mediaQuery(context, "h", 28),
                        color: Theme.of(context).colorScheme.scrim,
                        letterSpacing:
                            mediaQuery(context, "h", 1.4000000000000001),
                      ),
                    ),
                    TextSpan(
                      text: fiiCtr.dataNitrosignUser[i]["main_status"]
                                  .toString() ==
                              "wait_user"
                          ? "pending_sign".tr.toString()
                          : fiiCtr.dataNitrosignUser[i]["main_status"]
                              .toString(),
                      style: TextStyle(
                        fontFamily: 'SukhumvitSet-Text',
                        fontSize: mediaQuery(context, "h", 28),
                        color: AppColors.DeepSeaGreen,
                        letterSpacing:
                            mediaQuery(context, "h", 1.4000000000000001),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                alignment: Alignment.centerRight,
                child: GestureDetector(
                  onTap: () {
                    Get.to(() => PdfToImagePage(
                        pdfData: fiiCtr.dataNitrosignUser[i]
                            ["file_url_modify"]));
                  },
                  child: Container(
                    alignment: Alignment.center,
                    width: mediaQuery(context, "w", 170),
                    height: mediaQuery(context, "h", 90),
                    decoration: BoxDecoration(
                      borderRadius:
                          BorderRadius.circular(mediaQuery(context, "h", 50)),
                      color: AppColors.DeepSeaGreen,
                      boxShadow: const [
                        BoxShadow(
                          color: Color(0x4d000000),
                          offset: Offset(0, 2),
                          blurRadius: 20,
                        ),
                      ],
                    ),
                    child: Text(
                      "btn_NitroSign".tr.toString(),
                      style: TextStyle(
                        fontFamily: 'SukhumvitSet-Bold',
                        fontSize: mediaQuery(context, "h", 28),
                        color: Theme.of(context).colorScheme.scrim,
                        letterSpacing:
                            mediaQuery(context, "h", 1.4000000000000001),
                        fontWeight: FontWeight.w700,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ));
        list.add(Container(
          height: mediaQuery(context, "h", 25),
        ));
      }
    } else {
      list.add(
        buildEmtpy(context, 'ui_approval_document'.tr),
      );
    }

    return list;
  }

  Widget builFormnoti(
      time,
      // autoclaim,
      activity,
      des,
      int index,
      context) {
    DateTime dateTime = DateTime.parse(time);

    return InkWell(
      onTap: () {
        fiiCtr.toggleExpandState(index); // ฟังก์ชันที่เปลี่ยนแปลงการแสดงผล
      },
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                width: Get.width,
                padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 5.h),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        SizedBox(
                          height: 18.h,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              AppWidget.normalText(
                                context,
                                DateFormat('dd/MM/yyyy hh:mm').format(dateTime),
                                12.sp,
                                Theme.of(context).colorScheme.scrim,
                                FontWeight.w400,
                              ),
                              SizedBox(
                                width: 10.w,
                              ),
                              AppWidget.normalText(
                                context,
                                'btn_sign'.tr,
                                12.sp,
                                Theme.of(context).colorScheme.tertiary,
                                FontWeight.w400,
                              ),
                            ],
                          ),
                        ),
                        InkWell(
                          onTap: () {
                            fiiCtr.toggleExpandState(index);
                          },
                          child: SizedBox(
                            height: 24.h,
                            width: 24.w,
                            child: Image.asset(
                              notiCtr.isExpandList[index].value
                                  ? 'assets/ADD/Expand_up_light.png'
                                  : 'assets/ADD/Expand_down_light.png',
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 5.h,
                    ),
                    Row(
                      children: [
                        Text(
                          '${'ui_act'.tr} : ',
                          textAlign: TextAlign.left,
                          maxLines: 2,
                          style: TextStyle(
                            overflow: TextOverflow.ellipsis,
                            fontFamily: 'SukhumvitSet-Bold',
                            fontSize: 14.sp,
                            color: Theme.of(context).colorScheme.scrim,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        Container(
                            width: Get.width * 0.70,
                            child: Text(
                              fiiCtr.dataNitrosignUser[index]["refer_id"]
                                  .toString(),
                              textAlign: TextAlign.left,
                              maxLines: 1,
                              style: TextStyle(
                                overflow: TextOverflow.ellipsis,
                                fontFamily: 'SukhumvitSet-Bold',
                                fontSize: 14.sp,
                                color: Theme.of(context).colorScheme.scrim,
                                fontWeight: FontWeight.w400,
                              ),
                            )),
                      ],
                    ),
                    SizedBox(
                      height: 5.h,
                    ),
                    Row(
                      children: [
                        Text(
                          '${'ui_status'.tr} : ',
                          textAlign: TextAlign.left,
                          maxLines: 2,
                          style: TextStyle(
                            overflow: TextOverflow.ellipsis,
                            fontFamily: 'SukhumvitSet-Bold',
                            fontSize: 14.sp,
                            color: Theme.of(context).colorScheme.scrim,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        Container(
                            width: Get.width * 0.70,
                            child: Text(
                              fiiCtr.dataNitrosignUser[index]["main_status"]
                                          .toString() ==
                                      "wait_user"
                                  ? "pending_sign".tr.toString()
                                  : fiiCtr.dataNitrosignUser[index]
                                          ["main_status"]
                                      .toString(),
                              textAlign: TextAlign.left,
                              maxLines: 1,
                              style: TextStyle(
                                overflow: TextOverflow.ellipsis,
                                fontFamily: 'SukhumvitSet-Bold',
                                fontSize: 14.sp,
                                color: Theme.of(context)
                                    .colorScheme
                                    .secondaryContainer,
                                fontWeight: FontWeight.w400,
                              ),
                            )),
                      ],
                    ),
                    Obx(() => Visibility(
                          visible: notiCtr.isExpandList[index].value,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              AppWidget.normalTextRX(
                                context,
                                'details'.obs,
                                14.sp,
                                Theme.of(context)
                                    .colorScheme
                                    .secondaryContainer,
                                FontWeight.w400,
                              ),
                              AppWidget.normalTextRX(
                                context,
                                '$des'.obs,
                                14.sp,
                                Theme.of(context).colorScheme.scrim,
                                FontWeight.w400,
                              ),
                            ],
                          ),
                        )),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(
            height: 5.h,
          ),
          InkWell(
            onTap: () async {
              Get.toNamed('home');
              String url;
              url = "https://signs-doc.web.app/?pdfUrl=${fiiCtr.dataNitrosignUser[index]["file_url_modify"]}&userId=${profileCtr.responseMember!.id.toString()}&running=${fiiCtr.dataNitrosignUser[index]["running"]}";
              print("url: $url");
              print("urlPDF: ${fiiCtr.dataNitrosignUser[index]["file_url_modify"]}");
              await launch(url);
            },
            child: Container(
              width: 353.w,
              height: 50.h,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Theme.of(context).colorScheme.secondaryContainer,
                  width: 1.0.w,
                ),
                color: AppColors.DeepSeaGreen,
              ),
              child: Center(
                child: AppWidget.normalText(
                  context,
                  'btn_sign'.tr,
                  16.sp,
                  Theme.of(context).colorScheme.scrim,
                  FontWeight.w600,
                ),
              ),
            ),
          ),
          SizedBox(
            height: 20.h,
          ),
          Container(
            width: 393,
            decoration: BoxDecoration(
              border: Border.all(
                color: const Color(0xFFFCF6E4).withOpacity(0.2),
                width: 0.1,
              ),
              color: const Color(0xFFFCF6E4).withOpacity(0.2),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildPerson() {
    return Obx(() {
      return fiiCtr.isLoading.value
          ? Center(
              child:
                  CircularProgressIndicator()) // Show loading when isLoading is true
          : ListView.builder(
              itemCount: fiiCtr.dataNitrosignUser.length,
              padding: EdgeInsets.zero,
              itemBuilder: (context, index) {
                return builFormnoti(
                  fiiCtr.dataNitrosignUser[index]['update_time']!,
                  fiiCtr.dataNitrosignUser[index]['refer_id']!,
                  fiiCtr.dataNitrosignUser[index]['main_status']!,
                  index,
                  context,
                );
              },
            );
    });
  }

  Widget buildNews() {
    return Obx(() {
      return fiiCtr.isLoading.value
          ? Center(
              child:
                  CircularProgressIndicator()) // Show loading when isLoading is true
          : ListView.builder(
              itemCount: fiiCtr.dataNitrosignUser.length,
              padding: EdgeInsets.zero,
              itemBuilder: (context, index) {
                return builFormnoti(
                  fiiCtr.dataNitrosignUser[index]['update_time']!,
                  fiiCtr.dataNitrosignUser[index]['refer_id']!,
                  fiiCtr.dataNitrosignUser[index]['main_status']!,
                  index,
                  context,
                );
              },
            );
    });
  }

  _launchURL(url) async {
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not launch $url';
    }
  }
}
