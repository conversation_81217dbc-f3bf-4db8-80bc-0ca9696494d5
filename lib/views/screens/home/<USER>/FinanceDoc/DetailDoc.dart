import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mapp_ms24/controllers/internal/FinaceController/FinaceController.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';
import 'package:mapp_ms24/views/screens/home/<USER>';

import '../../../../../controllers/utils/language_selection_controller.dart';
import '../../../../../controllers/utils/widget.dart';
import '../../../Widget/Widget.dart';
// import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';

class DetailDocScreen extends StatefulWidget {
  final String? path;
  final String? linkPDFUpload;
  final dataPDF;
  const DetailDocScreen({Key? key, this.path, this.linkPDFUpload, this.dataPDF})
      : super(key: key);

  @override
  State<DetailDocScreen> createState() => _DetailDocScreenState();
}

class _DetailDocScreenState extends State<DetailDocScreen> {
  final LanguageSelectionController languageController =
      Get.put(LanguageSelectionController());
  final FinaceController finaceCt = Get.put(FinaceController());
  int pages = 0;
  int currentPage = 0;
  bool isReady = false;
  String errorMessage = '';
  int totalPage = 0;

  @override
  void initState() {
    super.initState();
    // finaceCt.createFileOfPdfUrl(widget.linkPDF).then((f) async {
    //   setState(() {
    //     finaceCt.remotePDFpath = f.path;
    //   });
    // });
    WidgetsBinding.instance.addPostFrameCallback((_) {
      buildShow();
    });
  }

  var statusPath = "";
  setStatusPath() async {
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    await new Future.delayed(const Duration(seconds: 1));
    setState(() {
      statusPath = widget.path!;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Row(
        children: [
          Container(
            height: Get.height,
            color: Colors.white,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Padding(
                  padding: const EdgeInsets.only(top: 0),
                  child: buildAppBar(context, 'document_detail'.tr),
                ),
                // buildLoadPDF(),
                buildSig()
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget buildSig() {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.symmetric(vertical: 20.h),
          child: InkWell(
            onTap: () {
              Get.offAllNamed('home');
            },
            child: Container(
              height: 42.h,
              width: 328.w,
              decoration: BoxDecoration(
                color: const Color(0xFF322B63),
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(
                  color: const Color(0xFFA596FF),
                  width: 1.w,
                ),
              ),
              child: Center(
                child: AppWidget.normalText(
                  context,
                  'btn_sign_2'.tr,
                  16.sp,
                  const Color(0xFFF6F6F6),
                  FontWeight.w600,
                ),
              ),
            ),
          ),
        )
      ],
    );
  }

  Future buildShow() {
    return showCupertinoModalPopup(
      barrierColor: Colors.transparent,
      context: context,
      builder: (BuildContext context) {
        return Container(
          decoration: BoxDecoration(
            border: Border(
              top: BorderSide(
                color: const Color(0xFF000000).withOpacity(0.1),
                width: 1.0,
              ),
            ),
          ),
          height: 174.h,
          width: 393.w,
          child: Container(
            color: Colors.white,
            child: Column(
              children: [
                Container(
                  padding:
                      EdgeInsets.symmetric(vertical: 20.0.h, horizontal: 10.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      AppWidget.normalText(
                        context,
                        'ui_sign_cap1'.tr,
                        14.sp,
                        const Color(0xFF5F569B),
                        FontWeight.w600,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          AppWidget.normalText(
                            context,
                            'ui_sign_cap2'.tr,
                            14.sp,
                            const Color(0xFF5F569B),
                            FontWeight.w400,
                          ),
                          AppWidget.normalText(
                            context,
                            'ui_sign_cap3'.tr,
                            14.sp,
                            const Color(0xFF934D0F),
                            FontWeight.w400,
                          ),
                          // Check if the selected language is not "en"
                          if (languageController.selectedLanguageIndex != "en")
                            AppWidget.normalText(
                              context,
                              'ui_sign_cap4'.tr,
                              14.sp,
                              const Color(0xFF5F569B),
                              FontWeight.w400,
                            ),
                        ],
                      )
                    ],
                  ),
                ),
                InkWell(
                  onTap: () {
                    Get.back();
                  },
                  child: Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(
                            vertical: 10.h, horizontal: 10.w),
                        child: Container(
                          height: 42.h,
                          width: 328.w,
                          decoration: BoxDecoration(
                            color: const Color(0xFF322B63),
                            borderRadius: BorderRadius.circular(8.r),
                            border: Border.all(
                              color: const Color(0xFFA596FF),
                              width: 1.w,
                            ),
                          ),
                          child: Center(
                            child: AppWidget.normalText(
                              context,
                              'btn_sign_1'.tr,
                              16.sp,
                              const Color(0xFFF6F6F6),
                              FontWeight.w600,
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
