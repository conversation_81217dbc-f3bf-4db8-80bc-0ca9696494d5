import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:get/get_utils/src/extensions/internacionalization.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:mapp_ms24/controllers/utils/widget.dart';
import 'package:syncfusion_flutter_signaturepad/signaturepad.dart';
class createSignatureNitrosign extends StatefulWidget {
  final statusCreateSignature;

  const createSignatureNitrosign(this.statusCreateSignature);

  @override
  State<createSignatureNitrosign> createState() => _createSignatureNitrosignState(this.statusCreateSignature);
}

class _createSignatureNitrosignState extends State<createSignatureNitrosign> {
  final GlobalKey<SfSignaturePadState> signatureGlobalKey = GlobalKey();
  final statusCreateSignature;
  _createSignatureNitrosignState(this.statusCreateSignature);
  bool checking = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Color(0xFF1F1C2F), // เพิ่มสีแรกที่นี่
                  Color(0xFF0D0C14), // เพิ่มสีที่สองที่นี่
                ],
                begin: Alignment.topCenter, // เริ่มต้น Gradient ที่มุมบนซ้าย
                end: Alignment.bottomCenter,
              ),
            ),
            height: 852.h,
            width: Get.width,
            child: Column(
              children: [buildBody(context)],
            ),
          ),
          checking
              ? Container(
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
              color: Colors.black12,
              child: Center(
                child: SizedBox(
                  width: 60.w,
                  child: const LoadingIndicator(
                    indicatorType: Indicator.lineSpinFadeLoader,
                    colors: [Colors.black],
                  ),
                ),
              )
          )
              : Container(),
        ],
      ),
    );
  }
  Widget buildBody(context) {
    return Column(
      children: <Widget>[
        Center(
            child: Container(
              color: const Color(0xff302C49),
            )),
        Container(
          padding: EdgeInsets.only(top: 80.h),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(
                width: 18.w,
                height: 18.h,
              ),
              AppWidget.normalText(context, 'ui_register_Esign'.tr, 16.sp,
                  const Color(0xFFF6F6F6), FontWeight.w700),
              InkWell(
                onTap: () {
                  Get.offAndToNamed('home');
                },
                child: const Icon(
                  Icons.home,
                  size: 18,
                  color: Colors.white,
                ),
              )
            ],
          ),
        ),

      ],
    );
  }

}
