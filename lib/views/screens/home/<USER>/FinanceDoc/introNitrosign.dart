import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui';
import 'package:image/image.dart' as img;
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_painter/flutter_painter.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_signature_pad/flutter_signature_pad.dart';
import 'package:flutter_svg/svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_utils/src/extensions/internacionalization.dart';
import 'package:get_storage/get_storage.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';
import 'package:path_provider/path_provider.dart';

import 'dart:ui' as ui;

import 'package:phosphor_flutter/phosphor_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:syncfusion_flutter_pdf/pdf.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../../configs/color_system.dart';
import '../../../../../controllers/internal/FinaceController/FinaceController.dart';
import '../../../../../controllers/utils/color.dart';
import '../../../../../controllers/utils/language_selection_controller.dart';
import '../../../../../controllers/utils/widget.dart';
import '../../../../../models/LocalStorageModel.dart';
import '../../../Widget/Widget.dart';
import 'createSignatureNitrosign.dart';

class introNitrosign extends StatefulWidget {
  final dataPDF;
  final linkPDFUpload;
  final int currentPage;

  const introNitrosign(this.dataPDF, this.linkPDFUpload, this.currentPage);

  @override
  State<introNitrosign> createState() => _introNitrosignState();
}

class _introNitrosignState extends State<introNitrosign> {
  final LanguageSelectionController languageController =
      Get.put(LanguageSelectionController());
  // final esingController esingCt = Get.put(esingController());
  final FinaceController finaceCt = Get.put(FinaceController());
  final TextEditingController _detailController = TextEditingController();
  final _signatureKey = GlobalKey<SignatureState>();
  bool _isSigning = false;
  late PainterController controller;

  int pages = 0;
  int currentPage = 1;
  bool isReady = false;
  String errorMessage = '';
  int totalPage = 0;
  int statusAlertReport = 0;
  int statusAlertSelectSign = 0;
  int statusAlertAgreeSign = 0;
  int statusHaveSign = 0;
  int statusAgreeSign = 0;
  late String _localFilePath = '';
  static List<String> imageLinks = [];
  ui.Image? backgroundImage;
  bool loading = false;
  final box = GetStorage();
  FocusNode textFocusNode = FocusNode();

  static const Color red = Color(0xFF002AFF);

  Paint shapePaint = Paint()
    ..strokeWidth = 1
    ..color = Color(0xFF002AFF)
    ..style = PaintingStyle.stroke
    ..strokeCap = StrokeCap.round;

  @override
  void initState() {
    super.initState();
    finaceCt.createFileOfPdfUrl().then((f) {
      setState(() {
        finaceCt.remotePDFpath = f.path;
      });
    });

    imageCache.clear();
    imageCache.clearLiveImages();
    // loading = _localFilePath == '' ? true : false;
    _downloadAndSavePDF(widget.linkPDFUpload);

    WidgetsBinding.instance.addPostFrameCallback((_) {});
    controller = PainterController(
      settings: PainterSettings(
        text: TextSettings(
          focusNode: textFocusNode,
          textStyle:
              TextStyle(fontWeight: FontWeight.bold, color: red, fontSize: 18),
        ),
        freeStyle: const FreeStyleSettings(
          color: red,
          strokeWidth: 1,
        ),
        shape: ShapeSettings(
          paint: shapePaint,
        ),
        scale: const ScaleSettings(
          enabled: true,
          minScale: 1,
          maxScale: 5,
        ),
      ),
    );
    textFocusNode.addListener(onFocus);

    imageLinks.clear();
  }

  void initBackground(S3url) async {
    await loadRDSSign();
    final image = await NetworkImage(S3url).image;

    setState(() {
      backgroundImage = image;
    });
  }

  void onFocus() {
    setState(() {});
  }

  Future<void> _downloadAndSavePDF(String url) async {
    setState(() {
      loading = true;
    });

    try {
      final dir = await getApplicationDocumentsDirectory();
      final filePath = '${dir.path}/temp.pdf';

      await Dio().download(url, filePath);

      setState(() {
        _localFilePath = filePath;
        loading = false;
      });
    } catch (e) {
      setState(() {
        loading = false;
      });
    }
  }

  resetall() {
    _localFilePath = '';
    imageLinks = [];
    backgroundImage = null;
    controller.isBlank;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButton: backgroundImage != null
          ? FloatingActionButton(
              backgroundColor: Colors.grey,
              child: Icon(
                PhosphorIcons.floppyDiskBold,
              ),
              onPressed: imageLinks.length > 0
                  ? renderAndDisplayImage
                  : showAlertDialog,
            )
          : Container(),
      body: Stack(
        children: [
          Container(
            height: Get.height,
            width: Get.width,
            child: Column(
              children: [
                buildAppBarWithFuntion(context, 'document_detail'.tr, () async {
                  await resetall();
                }),

                _localFilePath == ""
                    ? Container(
                        height: Get.height * 0.80,
                        width: Get.width,
                        child: Center(
                            child: Container(
                                height: Get.height * 0.1,
                                width: Get.height * 0.1,
                                child: const CircularProgressIndicator(
                                  color: Colors.purple,
                                ))),
                      )
                    : BuildPDFViews(_localFilePath),

                // AlertSelectSign(),
              ],
            ),
          ),
          if (backgroundImage != null)
            Padding(
              padding: const EdgeInsets.only(top: 100.0),
              child: SizedBox(
                width: Get.width,
                height: Get.height,
                child: Stack(
                  children: [
                    Positioned(
                      top: 0,
                      bottom: 0,
                      right: 0,
                      left: 0,
                      child: SizedBox(
                        height: 300.h,
                        width: Get.width,
                        child: ValueListenableBuilder(
                          valueListenable: controller,
                          builder: (context, _, __) => Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Flexible(
                                child: Container(
                                  constraints: BoxConstraints(
                                    maxWidth: Get.width,
                                  ),
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 15),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.vertical(
                                      top: Radius.circular(20),
                                    ),
                                    color: Colors.white54,
                                  ),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      if (controller.freeStyleMode !=
                                          FreeStyleMode.none) ...[
                                        Divider(),
                                        Text("Free Style Settings"),
                                        // Control free style stroke width
                                        Row(
                                          children: [
                                            Expanded(
                                              flex: 1,
                                              child: Text("Stroke Width"),
                                            ),
                                            Expanded(
                                              flex: 3,
                                              child: Slider.adaptive(
                                                min: 1,
                                                max: 25,
                                                value: controller
                                                    .freeStyleStrokeWidth,
                                                onChanged:
                                                    setFreeStyleStrokeWidth,
                                              ),
                                            ),
                                          ],
                                        ),
                                        if (controller.freeStyleMode ==
                                            FreeStyleMode.draw)
                                          Row(
                                            children: [
                                              Expanded(
                                                flex: 1,
                                                child: Text("Color"),
                                              ),
                                              // Control free style color hue
                                              Expanded(
                                                flex: 3,
                                                child: Slider.adaptive(
                                                    min: 0,
                                                    max: 359.99,
                                                    value: HSVColor.fromColor(
                                                            controller
                                                                .freeStyleColor)
                                                        .hue,
                                                    activeColor: controller
                                                        .freeStyleColor,
                                                    onChanged:
                                                        setFreeStyleColor),
                                              ),
                                            ],
                                          ),
                                      ],
                                      if (textFocusNode.hasFocus) ...[
                                        Divider(),
                                        Text("Text settings"),
                                        // Control text font size
                                        Row(
                                          children: [
                                            Expanded(
                                              flex: 1,
                                              child: Text("Font Size"),
                                            ),
                                            Expanded(
                                              flex: 3,
                                              child: Slider.adaptive(
                                                  min: 8,
                                                  max: 96,
                                                  value: controller
                                                          .textStyle.fontSize ??
                                                      14,
                                                  onChanged: setTextFontSize),
                                            ),
                                          ],
                                        ),

                                        // Control text color hue
                                        Row(
                                          children: [
                                            Expanded(
                                              flex: 1,
                                              child: Text("Color"),
                                            ),
                                            Expanded(
                                              flex: 3,
                                              child: Slider.adaptive(
                                                  min: 0,
                                                  max: 359.99,
                                                  value: HSVColor.fromColor(
                                                          controller.textStyle
                                                                  .color ??
                                                              red)
                                                      .hue,
                                                  activeColor: controller
                                                      .textStyle.color,
                                                  onChanged: setTextColor),
                                            ),
                                          ],
                                        ),
                                      ],
                                      if (controller.shapeFactory != null) ...[
                                        Divider(),
                                        Text("Shape Settings"),

                                        // Control text color hue
                                        Row(
                                          children: [
                                            Expanded(
                                                flex: 1,
                                                child: Text("Stroke Width")),
                                            Expanded(
                                              flex: 3,
                                              child: Slider.adaptive(
                                                min: 2,
                                                max: 25,
                                                value: controller.shapePaint
                                                        ?.strokeWidth ??
                                                    shapePaint.strokeWidth,
                                                onChanged: (value) =>
                                                    setShapeFactoryPaint(
                                                  (controller.shapePaint ??
                                                          shapePaint)
                                                      .copyWith(
                                                    strokeWidth: value,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),

                                        // Control shape color hue
                                        Row(
                                          children: [
                                            Expanded(
                                              flex: 1,
                                              child: Text("Color"),
                                            ),
                                            Expanded(
                                              flex: 3,
                                              child: Slider.adaptive(
                                                min: 0,
                                                max: 359.99,
                                                value: HSVColor.fromColor(
                                                        (controller.shapePaint ??
                                                                shapePaint)
                                                            .color)
                                                    .hue,
                                                activeColor:
                                                    (controller.shapePaint ??
                                                            shapePaint)
                                                        .color,
                                                onChanged: (hue) =>
                                                    setShapeFactoryPaint(
                                                  (controller.shapePaint ??
                                                          shapePaint)
                                                      .copyWith(
                                                    color: HSVColor.fromAHSV(
                                                            1, hue, 1, 1)
                                                        .toColor(),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),

                                        Row(
                                          children: [
                                            Expanded(
                                                flex: 1,
                                                child: Text("Fill shape")),
                                            Expanded(
                                              flex: 3,
                                              child: Center(
                                                child: Switch(
                                                  value:
                                                      (controller.shapePaint ??
                                                                  shapePaint)
                                                              .style ==
                                                          PaintingStyle.fill,
                                                  onChanged: (value) =>
                                                      setShapeFactoryPaint(
                                                    (controller.shapePaint ??
                                                            shapePaint)
                                                        .copyWith(
                                                      style: value
                                                          ? PaintingStyle.fill
                                                          : PaintingStyle
                                                              .stroke,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ]
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: Get.height,
                      width: Get.width,
                      child: AspectRatio(
                        aspectRatio:
                            backgroundImage!.width / backgroundImage!.height,
                        child: Center(
                          child: FlutterPainter(
                            controller: controller,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            )
          // En
        ],
      ),
    );
  }

  // Widget buildSig() {
  //   return Column(
  //     children: [
  //       Padding(
  //         padding: EdgeInsets.symmetric(vertical: 20.h),
  //         child: InkWell(
  //           onTap: (){
  //             // Navigator
  //           },
  //           child: Container(
  //             height: 42.h,
  //             width: 328.w,
  //             decoration: BoxDecoration(
  //               color: const Color(0xFF322B63),
  //               borderRadius: BorderRadius.circular(8.r),
  //               border: Border.all(
  //                 color: const Color(0xFFA596FF),
  //                 width: 1.w,
  //               ),
  //             ),
  //             child: Center(
  //               child: AppWidget.normalText(
  //                 context,
  //                 'btn_sign_2'.tr,
  //                 16.sp,
  //                 const Color(0xFFF6F6F6),
  //                 FontWeight.w600,
  //               ),
  //             ),
  //           ),
  //         ),
  //       )
  //     ],
  //   );
  // }

  Widget buildShow() {
    return SizedBox(
      height: Get.height * 0.88,
      width: Get.width,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).colorScheme.primary,
                  Theme.of(context).colorScheme.onPrimary,
                ],
                begin: Alignment.topCenter, // เริ่มต้น Gradient ที่มุมบนซ้าย
                end: Alignment.bottomCenter,
              ),
              border: Border(
                top: BorderSide(
                  color: const Color(0xFF000000).withOpacity(0.1),
                  width: 1.0,
                ),
              ),
            ),
            height: Get.height * 0.20,
            width: 393.w,
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Theme.of(context).colorScheme.primary,
                    Theme.of(context).colorScheme.onPrimary,
                  ],
                  begin: Alignment.topCenter, // เริ่มต้น Gradient ที่มุมบนซ้าย
                  end: Alignment.bottomCenter,
                ),
              ),
              child: Column(
                children: [
                  Center(
                    child: InkWell(
                      onTap: () {
                        addSticker();
                      },
                      child: Column(
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Column(
                              children: [
                                Center(
                                  child: AppWidget.normalText(
                                    context,
                                    "*" + 'ui_sign_cap1'.tr,
                                    14.sp,
                                    Theme.of(context).colorScheme.scrim,
                                    FontWeight.w600,
                                  ),
                                ),
                                Center(
                                    child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    AppWidget.normalText(
                                      context,
                                      'ui_sign_cap2'.tr + " ",
                                      14.sp,
                                      Theme.of(context).colorScheme.scrim,
                                      FontWeight.w600,
                                    ),
                                    AppWidget.normalText(
                                      context,
                                      " " + 'btn_sign_1'.tr + " ",
                                      14.sp,
                                      Theme.of(context).colorScheme.tertiary,
                                      FontWeight.w600,
                                    ),
                                    AppWidget.normalText(
                                      context,
                                      'ui_sign_cap4'.tr + " ",
                                      14.sp,
                                      Theme.of(context).colorScheme.scrim,
                                      FontWeight.w600,
                                    )
                                  ],
                                ))
                              ],
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.symmetric(
                                vertical: 10.h, horizontal: 10.w),
                            child: Container(
                              height: 42.h,
                              width: 328.w,
                              decoration: BoxDecoration(
                                color: AppColors.DeepSeaGreen,
                                borderRadius: BorderRadius.circular(8.r),
                                border: Border.all(
                                  color: AppColors.DeepSeaGreen,
                                  width: 1.w,
                                ),
                              ),
                              child: Center(
                                child: AppWidget.normalText(
                                  context,
                                  'btn_sign'.tr,
                                  16.sp,
                                  Theme.of(context).colorScheme.scrim,
                                  FontWeight.w600,
                                ),
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget BuildPDFViews(String? localFilePath) {
    return Stack(
      children: [
        loading
            ? Container(
                height: Get.height * 0.80,
                width: Get.width,
                child: Center(
                    child: Container(
                        height: Get.height * 0.1,
                        width: Get.height * 0.1,
                        child: const CircularProgressIndicator(
                          color: Colors.purple,
                        ))),
              )
            : Container(
                height: Get.height * 0.80,
                width: Get.width,
                child: PDFView(
                  filePath: localFilePath,
                  enableSwipe: true,
                  swipeHorizontal: true,
                  autoSpacing: false,
                  pageFling: true,
                  onRender: (_pages) {},
                  onError: (error) {
                    print('Error rendering PDF: $error');
                  },
                  onPageError: (page, error) {
                    print('Error on page $page: $error');
                  },
                  onViewCreated: (PDFViewController pdfViewController) {},
                ),
              ),
        if (backgroundImage == null) buildShow()
      ],
    );
  }

  static IconData getShapeIcon(ShapeFactory? shapeFactory) {
    if (shapeFactory is LineFactory) return PhosphorIcons.lineSegment;
    if (shapeFactory is ArrowFactory) return PhosphorIcons.arrowUpRight;
    if (shapeFactory is DoubleArrowFactory)
      return PhosphorIcons.arrowsHorizontal;
    if (shapeFactory is RectangleFactory) return PhosphorIcons.rectangle;
    if (shapeFactory is OvalFactory) return PhosphorIcons.circle;
    return PhosphorIcons.polygon;
  }

  void undo() {
    controller.undo();
  }

  void redo() {
    controller.redo();
  }

  void toggleFreeStyleDraw() {
    controller.freeStyleMode = controller.freeStyleMode != FreeStyleMode.draw
        ? FreeStyleMode.draw
        : FreeStyleMode.none;
  }

  void toggleFreeStyleErase() {
    controller.freeStyleMode = controller.freeStyleMode != FreeStyleMode.erase
        ? FreeStyleMode.erase
        : FreeStyleMode.none;
  }

  void addText() {
    if (controller.freeStyleMode != FreeStyleMode.none)
      controller.freeStyleMode = FreeStyleMode.none;
    controller.addText();
  }

  void addSticker() async {
    await loadRDSSign();

    final imageLink = await showDialog<String>(
      context: context,
      builder: (context) => SelectStickerImageDialog(
        imagesLinks: imageLinks,
      ),
    );

    if (imageLink == null) return;

    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Center(child: CircularProgressIndicator());
      },
    );

    try {
      // Load image and add to controller

      final image = await NetworkImage(imageLink).image;
      controller.addImage(image, Size(100, 100));

      // Call setState to refresh the UI
      setState(() {
        initBackground(imageLink);
      });
    } catch (e) {
    } finally {
      // Close loading dialog
      Navigator.of(context).pop();
    }
  }

  void setFreeStyleStrokeWidth(double value) {
    controller.freeStyleStrokeWidth = value;
  }

  void setFreeStyleColor(double hue) {
    controller.freeStyleColor = HSVColor.fromAHSV(1, hue, 1, 1).toColor();
  }

  void setTextFontSize(double size) {
// Set state is just to update the current UI, the [FlutterPainter] UI updates without it
    setState(() {
      controller.textSettings = controller.textSettings.copyWith(
        textStyle: controller.textSettings.textStyle.copyWith(fontSize: size),
      );
    });
  }

  void setShapeFactoryPaint(Paint paint) {
// Set state is just to update the current UI, the [FlutterPainter] UI updates without it
    setState(() {
      controller.shapePaint = paint;
    });
  }

  void setTextColor(double hue) {
    controller.textStyle = controller.textStyle.copyWith(
      color: HSVColor.fromAHSV(1, hue, 1, 1).toColor(),
    );
  }

  void selectShape(ShapeFactory? factory) {
    controller.shapeFactory = factory;
  }

  String remotePath = "";
  void renderAndDisplayImage() {
    if (backgroundImage == null) return;
    try {
      loading = true;
      final double widthInPixels = 7200.w * 2;
      final double heightInPixels = 3600.h * 2;
      final double logicalScreenWidth = MediaQuery.of(context).size.width;

      final double widthInDP = widthInPixels /
          logicalScreenWidth *
          MediaQuery.of(context).size.width;
      final double heightInDP = heightInPixels /
          logicalScreenWidth *
          MediaQuery.of(context).size.height;

      final backgroundImageSize = Size(widthInDP, heightInDP);
      final imageFuture = controller
          .renderImage(backgroundImageSize)
          .then<Uint8List?>((ui.Image image) => image
              .toByteData(format: ui.ImageByteFormat.png)
              .then((byteData) => byteData?.buffer.asUint8List()));

      imageFuture.then((value) async {
        if (value == null) {
          print("Error: Rendered image is null");
          return;
        }

        try {
          Uint8List enhancedImage = await enhanceSignatureImage(value);

          await _convertImageToPDF(enhancedImage);
          await uploadtoS3(remotePath);
          loading = false;

          if (linkPDFS3 != '') {
          } else {
            print("Error: PDF link is empty after upload");
          }

          Navigator.of(context).pop();
        } catch (e) {
          Navigator.of(context).pop();
        }
      }).catchError((error) {
        print("Error rendering image: $error");
        Navigator.of(context).pop();
      });
    } catch (e) {
      print("Error during PDF conversion/upload/save: $e");
    }
  }

  Future<Uint8List> enhanceSignatureImage(Uint8List imageData) async {
    img.Image image = img.decodeImage(imageData)!;
    img.Image sharpenedImage =
        img.adjustColor(image, contrast: 100, brightness: 20);

    final double scaleFactor = 1.4;
    img.Image upscaledImage_done = img.copyResize(sharpenedImage,
        width: (image.width * 0.09555).toInt(),
        height: (image.height * 0.16).toInt(),
        interpolation: img.Interpolation.cubic);

    return Uint8List.fromList(img.encodePng(upscaledImage_done));
  }

  Future<void> _convertImageToPDF(Uint8List imageData) async {
    try {
      var compressImg = imageData;
      Completer<File> completer = Completer();

      PdfDocument document =
          PdfDocument(inputBytes: File(_localFilePath).readAsBytesSync());
      document.pageSettings.margins.all = 0;

      final PdfImage image = PdfBitmap(compressImg);

      for (int i = 0; i < document.pages.count; i++) {
        PdfPage page = document.pages[i];
        final pageSize = page.getClientSize();
        final double x = (pageSize.width - image.width) / 1.42;
        final double y = (pageSize.height - image.height) / 2.92;

        page.graphics.drawImage(
            image,
            Rect.fromLTWH(
                x, y, image.width.toDouble(), image.height.toDouble()));
      }

      List<int> bytes = await document.save();
      String dir = (await getApplicationDocumentsDirectory()).path;
      File file = File(
          "$dir/${DateTime.now().millisecondsSinceEpoch}_${widget.dataPDF!['refer_id']}.pdf");
      await file.writeAsBytes(bytes);
      completer.complete(file);
      setState(() {
        remotePath = file.path;
      });
    } catch (e) {
      print("Error converting image to PDF: $e");
    }
  }

  loadAgreeSign() async {
    var id = await widget.dataPDF['member_id_approve'];
    String url =
        'https://mhmo3nnbr5.execute-api.ap-southeast-1.amazonaws.com/latest';
//    String url = 'https://086e808a.ngrok.io/agslearn/us-central1/ApiAppMS24';
    Map map = {"menu": "searchAgreeSign", "id": id.toString()};

    final response = await apiRequest(url, map);
    var jsonResponse = json.decode(response);
    if (jsonResponse["statusCode"].toString() == "200") {
      setState(() {
        if (jsonResponse["result"][0]["link_NDA_Signature"].toString() != "" &&
            jsonResponse["result"][0]["link_NDA_Signature"].toString() !=
                "null" &&
            jsonResponse["result"][0]["link_NDA_Signature"].toString() !=
                null) {
          imageLinks.add(
              "https://mapp-app.s3.ap-southeast-1.amazonaws.com/MappMS/img_Signature_NDA/" +
                  id.toString() +
                  "_e_signature1.png");
        }
      });
    } else {
      print('No data!!');
    }
  }

  loadRDSSign() async {
    imageLinks.clear();
    var id = await widget.dataPDF['member_id_approve'];
    String url =
        'https://mhmo3nnbr5.execute-api.ap-southeast-1.amazonaws.com/latest';
//    String url = 'https://086e808a.ngrok.io/agslearn/us-central1/ApiAppMS24';
    Map map = {"menu": "searchSignRDS", "id": id.toString()};

    final response = await apiRequest(url, map);
    var jsonResponse = json.decode(response);
    if (jsonResponse["statusCode"].toString() == "200") {
      setState(() {
        if (jsonResponse["result"].length > 0) {
          for (var data in jsonResponse["result"]) {
            imageLinks.add(data["url"]);
          }
        }
      });
    } else {
      print('No data!!');
    }
  }

  String linkPDFS3 = '';

  Future<void> uploadtoS3(String path) async {
    try {
      final bytes = File(path).readAsBytesSync();
      String base64Image = base64Encode(bytes);

      String url =
          'https://oxphgjyvu2.execute-api.ap-southeast-1.amazonaws.com/latest/uploadPDFS3_Center';
      Map<String, dynamic> map = {
        "name": "MappMS",
        "folder": "MappMS/signPDF",
        "image": base64Image
      };

      // ส่งคำขอไปยัง API
      final response = await apiRequest(url, map);

      // ประมวลผลคำตอบจาก API
      var jsonResponse = json.decode(response);
      if (jsonResponse["statusCode"].toString() == "200") {
        setState(() {
          linkPDFS3 = (jsonResponse["result"]["url"]["Location"].toString());
        });
      } else {
        Fluttertoast.showToast(
            msg: "upload fail!",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.TOP,
            backgroundColor: Colors.red,
            textColor: Colors.black,
            fontSize: 16.0);
      }
    } catch (e) {
      print(e);
    }
  }

  void removeSelectedDrawable() {
    final selectedDrawable = controller.selectedObjectDrawable;
    if (selectedDrawable != null) controller.removeDrawable(selectedDrawable);
  }

  void showAlertDialog() {
    alertDialog(context, "ui_titleAlert3".tr.toString(),
        "ui_detailAlert4".tr.toString(), "btn_okAlert".tr.toString());
  }
}

class RenderedImageDialog extends StatelessWidget {
  final Future<Uint8List?> imageFuture;

  const RenderedImageDialog({Key? key, required this.imageFuture})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text("Rendered Image"),
      content: FutureBuilder<Uint8List?>(
        future: imageFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState != ConnectionState.done)
            return SizedBox(
              height: 50,
              child: Center(
                child: CircularProgressIndicator.adaptive(),
              ),
            );
          if (!snapshot.hasData || snapshot.data == null) return SizedBox();
          return InteractiveViewer(
            maxScale: 10,
            child: Image.memory(snapshot.data!),
          );
        },
      ),
    );
  }
}

class SelectStickerImageDialog extends StatelessWidget {
  final List<String> imagesLinks;

  const SelectStickerImageDialog({Key? key, this.imagesLinks = const []})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Colors.white,
      title: Text(
        "ui_NitroSelect".tr.toString(),
        style: TextStyle(color: Colors.black),
      ),
      content: imagesLinks.isEmpty
          ? RichText(
              text: TextSpan(children: [
                TextSpan(
                    text: 'ui_regis_signature'.tr.toString() + " ",
                    style: TextStyle(color: Colors.black)),
                TextSpan(
                    text: 'ui_here'.tr.toString(),
                    style: TextStyle(
                        color: Colors.blue,
                        decoration: TextDecoration.underline),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        launch(
                            "http://devdev.prachakij.com/paper/SIGN/login/login.php");
                      })
              ]),
            )
          : FractionallySizedBox(
              heightFactor: 0.5,
              child: SingleChildScrollView(
                child: Wrap(
                  children: [
                    for (final imageLink in imagesLinks)
                      InkWell(
                        onTap: () => Navigator.pop(context, imageLink),
                        child: FractionallySizedBox(
                          widthFactor: 1 / 4,
                          child: Image.network(
                            imageLink,
                            loadingBuilder: (BuildContext context, Widget child,
                                ImageChunkEvent? loadingProgress) {
                              if (loadingProgress == null) return child;
                              return Center(
                                child: CircularProgressIndicator(
                                  value: loadingProgress.expectedTotalBytes !=
                                          null
                                      ? loadingProgress.cumulativeBytesLoaded /
                                          (loadingProgress.expectedTotalBytes ??
                                              1)
                                      : null,
                                ),
                              );
                            },
                            errorBuilder: (BuildContext context, Object error,
                                StackTrace? stackTrace) {
                              return Center(child: Text('Error loading image'));
                            },
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
      actions: [
        TextButton(
          child: Text("btn_cancel".tr.toString()),
          onPressed: () => Navigator.pop(context),
        )
      ],
    );
  }
}
