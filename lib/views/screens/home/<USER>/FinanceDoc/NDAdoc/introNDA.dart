import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:mapp_ms24/controllers/internal/FinaceController/FinaceController.dart';
import 'package:mapp_ms24/controllers/internal/FinaceController/NDAdoc/NDAcontroller.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/controllers/utils/widget.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/FinanceDoc/NDAdoc/createSignature.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/FinanceDoc/NDAdoc/nondisclosuerPDF.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../../../controllers/utils/color.dart';
import '../../../../Widget/Widget.dart';

class IntroNDAScreen extends StatefulWidget {
  final String? urlNDA;
  const IntroNDAScreen({super.key, this.urlNDA});

  @override
  State<IntroNDAScreen> createState() => _IntroNDAScreenState();
}

class _IntroNDAScreenState extends State<IntroNDAScreen> {
  @override

  NDAcontroller nda = NDAcontroller();
  FinaceController finaceCt = FinaceController();
  ProfileController profile = Get.find<ProfileController>();
  bool checking = true;
  int statusADM = 0;
  late String? localUrl;

  void initState() {
    finaceCt.loadLinkNDA(0);

    super.initState();
    initializePage();
  }

  void initializePage() async {
    // print("checking $checking");
    localUrl = widget.urlNDA;
    checking = false;
    print(statusADM);
    setState(() {});
  }
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).colorScheme.primary,
                  Theme.of(context).colorScheme.onPrimary,
                ],
                begin: Alignment.topCenter, // เริ่มต้น Gradient ที่มุมบนซ้าย
                end: Alignment.bottomCenter,
              ),
            ),
            width: Get.width,
            child: Padding(
              padding: const EdgeInsets.only(top: 20.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    children: [
                      // buildAppBar(context, 'btn_nonAgreement_head'.tr),
                      buildBody()
                    ],
                  ),
                  Column(
                    children: [
                      statusADM == 1
                          ? Container(
                        alignment: Alignment.bottomCenter,
                        padding:
                        EdgeInsets.only(bottom: mediaQuery(context, "h", 410)),
                        child: GestureDetector(
                          onTap: () async {
                            String url;
                            url = "https://sign-nda.web.app?userId=${profile.responseMember!.id.toString()}";
                            print("url: $url");
                            print("urlPDF: ${widget.urlNDA}");
                            await launch(url);
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: mediaQuery(context, "h", 101),
                            width: mediaQuery(context, "w", 273),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(51.0),
                              color: const Color(0xff7f420c),
                              boxShadow: [
                                BoxShadow(
                                  color: const Color(0x4d000000),
                                  offset: Offset(0, 2),
                                  blurRadius: 20,
                                )],
                              ),
                            )))
                          : Container(),
                      statusADM == 1
                          ? Container(
                              alignment: Alignment.bottomCenter,
                              padding: EdgeInsets.only(
                                  bottom: mediaQuery(context, "h", 280)),
                              child: GestureDetector(
                                onTap: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => createSignature(
                                          2,
                                          profile.responseMember!
                                              .company_management
                                              .toString()),
                                    ),
                                  );
                                },
                                child: Container(
                                  alignment: Alignment.center,
                                  height: mediaQuery(context, "h", 101),
                                  width: mediaQuery(context, "w", 273),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(51.0),
                                    color: const Color(0xff7f420c),
                                    boxShadow: [
                                      BoxShadow(
                                        color: const Color(0x4d000000),
                                        offset: Offset(0, 2),
                                        blurRadius: 20,
                                      ),
                                    ],
                                  ),
                                  child: Text(
                                    "เซ็นเอกสาร P2",
                                    style: TextStyle(
                                      fontFamily: 'SukhumvitSet-Bold',
                                      fontSize: mediaQuery(context, "h", 30),
                                      color: const Color(0xfffcf6e4),
                                      letterSpacing:
                                          mediaQuery(context, "h", 1.5),
                                      fontWeight: FontWeight.w700,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),
                            )
                          : Container(),
                      Container(
                        alignment: Alignment.bottomCenter,
                        padding: EdgeInsets.only(bottom: mediaQuery(context, "h", 150)),
                        child: GestureDetector(
                          onTap: () async {
                            String url;
                            url = "https://sign-nda.web.app/?userId=${profile.responseMember!.id.toString()}";
                            print("url: $url");
                            print("urlPDF: ${localUrl}");
                            await launch(url);
                              // ErrorshowAlertDialog(
                              //     context, 'btn_dev'.tr, "btn_dev_title".tr);

                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: mediaQuery(context, "h", 101),
                            width: mediaQuery(context, "w", 273),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(51.0),
                              color: AppColors.DeepSeaGreen,
                              boxShadow: [
                                BoxShadow(
                                  color: const Color(0x4d000000),
                                  offset: Offset(0, 2),
                                  blurRadius: 20,
                                )],

                              ),
                            child:             AppWidget.normalText(context, 'btn_sign'.tr, 12,
                                Theme.of(context).colorScheme.scrim, FontWeight.w400),
                            ),
                          )),
                    ],
                  )
                ],
              ),
            ),
          ),
          checking
              ? Container(
                  width: MediaQuery.of(context).size.width,
                  height: MediaQuery.of(context).size.height,
                  color: Colors.black12,
                  child: Center(
                    child: SizedBox(
                      width: 60.w,
                      child: const LoadingIndicator(
                        indicatorType: Indicator.lineSpinFadeLoader,
                        colors: [Colors.white],
                      ),
                    ),
                  ))
              : Container(),
        ],
      ),
    );
  }

  Widget buildBody() {
    return Padding(
      padding: EdgeInsets.only(top: 20.0.h),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 10.h),
        child: Column(
          children: [
            AppWidget.normalText(context, 'ui_NDAtitle'.tr, 12,
                Theme.of(context).colorScheme.scrim, FontWeight.w400),
            Padding(
              padding: EdgeInsets.symmetric(vertical: 20.0.h, horizontal: 20.w),
              child: Column(
                children: [
                  AppWidget.normalText(context, 'ui_NDAdetail'.tr, 12,
                      Theme.of(context).colorScheme.scrim, FontWeight.w400),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
