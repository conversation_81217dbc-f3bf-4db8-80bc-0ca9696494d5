import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';
import 'package:mapp_ms24/views/screens/home/<USER>';
import 'package:path_provider/path_provider.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';

class nondisclosuerPDFsuccess extends StatefulWidget {
  final String? path;

  nondisclosuerPDFsuccess({Key? key, this.path}) : super(key: key);

  @override
  _nondisclosuerPDFsuccessState createState() =>
      _nondisclosuerPDFsuccessState();
}

class _nondisclosuerPDFsuccessState extends State<nondisclosuerPDFsuccess>
    with WidgetsBindingObserver {
  final Completer<PDFViewController> _controller =
      Completer<PDFViewController>();

  int pages = 0;
  int currentPage = 0;
  bool isReady = false;
  String errorMessage = '';
  int totalPage = 0;

  @override
  void initState() {
    super.initState();

    setStatusPath();
  }

  var statusPath = "";
  setStatusPath() async {
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    await new Future.delayed(const Duration(seconds: 1));
    setState(() {
      statusPath = widget.path!;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: <Widget>[
          Center(
              child: Container(
            color: Color(0xff302C49),
          )),
          statusPath != ""
              ? PDFView(
                  filePath: widget.path,
                  enableSwipe: true,
                  swipeHorizontal: true,
                  autoSpacing: false,
                  pageFling: true,
                  pageSnap: true,
                  defaultPage: currentPage,
                  fitPolicy: FitPolicy.BOTH,
                  preventLinkNavigation:
                      false, // if set to true the link is handled in flutter
                  onRender: (_pages) {
                    setState(() {
                      pages = _pages!;
                      isReady = true;
                    });
                  },
                  onError: (error) {
                    setState(() {
                      errorMessage = error.toString();
                    });
                  },
                  onPageError: (page, error) {
                    setState(() {
                      errorMessage = '$page: ${error.toString()}';
                    });
                  },
                  onViewCreated: (PDFViewController pdfViewController) {
                    _controller.complete(pdfViewController);
                  },
                  onLinkHandler: (uri) {},
                  onPageChanged: (page, total) {
                    setState(() {
                      currentPage = page!;
                      totalPage = total!;
                    });
                  },
                )
              : Container(),
          errorMessage.isEmpty
              ? !isReady
                  ? Center(
                      child: CircularProgressIndicator(),
                    )
                  : Container()
              : Center(
                  child: Text(errorMessage),
                ),
          Container(
            alignment: Alignment.bottomCenter,
            padding: EdgeInsets.only(bottom: mediaQuery(context, "h", 100)),
            child: GestureDetector(
              onTap: () {
                Navigator.of(context).pushAndRemoveUntil(
                    MaterialPageRoute(builder: (context) => HomeScreen()),
                    (Route<dynamic> route) => false);
              },
              child: Container(
                alignment: Alignment.center,
                height: mediaQuery(context, "h", 101),
                width: mediaQuery(context, "w", 273),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(51.0),
                  color: const Color(0xff7f420c),
                  boxShadow: const [
                    BoxShadow(
                      color: Color(0x4d000000),
                      offset: Offset(0, 2),
                      blurRadius: 20,
                    ),
                  ],
                ),
                child: Text(
                  "กลับหน้าหลัก",
                  style: TextStyle(
                    fontFamily: 'SukhumvitSet-Bold',
                    fontSize: mediaQuery(context, "h", 30),
                    color: const Color(0xfffcf6e4),
                    letterSpacing: mediaQuery(context, "h", 1.5),
                    fontWeight: FontWeight.w700,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          )
          // : Container(),
        ],
      ),
    );
  }
}
