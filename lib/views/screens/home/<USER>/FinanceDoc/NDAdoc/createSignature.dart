import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:mapp_ms24/controllers/internal/FinaceController/FinaceController.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/FinanceDoc/NDAdoc/nondisclosurePDFsuccess.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:syncfusion_flutter_signaturepad/signaturepad.dart';

class createSignature extends StatefulWidget {
  final statusCreateSignature;
  final company;

  createSignature(this.statusCreateSignature, this.company);
  @override
  _createSignatureState createState() =>
      _createSignatureState(this.statusCreateSignature, this.company);
}

class _createSignatureState extends State<createSignature> {
  final GlobalKey<SfSignaturePadState> signatureGlobalKey = GlobalKey();
  final statusCreateSignature;
  final company;
  _createSignatureState(this.statusCreateSignature, this.company);
  FinaceController finaceCt = FinaceController();

  File? _file;

  @override
  void initState() {
    super.initState();

    SystemChrome.setPreferredOrientations([DeviceOrientation.landscapeRight]);
  }

  @override
  void dispose() {
    super.dispose();

    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
  }

  void _handleClearButtonPressed() {
    signatureGlobalKey.currentState!.clear();
  }

  void _handleSaveButtonPressed() async {
    final data =
        await signatureGlobalKey.currentState!.toImage(pixelRatio: 3.0);
    final bytes = await data.toByteData(format: ui.ImageByteFormat.png);
    String base64Image = base64Encode(bytes!.buffer.asUint8List());
    finaceCt.uploadtoS3_Signature_NDA(
        base64Image, widget.statusCreateSignature);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Column(
              children: [
                Padding(
                    padding: EdgeInsets.all(10),
                    child: Container(
                        child: SfSignaturePad(
                            key: signatureGlobalKey,
                            backgroundColor: Colors.white,
                            strokeColor: Colors.black,
                            minimumStrokeWidth: 1.0,
                            maximumStrokeWidth: 4.0),
                        decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey)))),
                SizedBox(height: 10),
                Row(children: <Widget>[
                  GestureDetector(
                    onTap: _handleSaveButtonPressed,
                    child: Container(
                      alignment: Alignment.center,
                      height: mediaQuery(context, "h", 200),
                      width: mediaQuery(context, "w", 100),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(51.0),
                        color: const Color(0xff7f420c),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0x4d000000),
                            offset: Offset(0, 2),
                            blurRadius: 20,
                          ),
                        ],
                      ),
                      child: Text(
                        "Save",
                        style: TextStyle(
                          fontFamily: 'SukhumvitSet-Bold',
                          fontSize: mediaQuery(context, "h", 50),
                          color: const Color(0xfffcf6e4),
                          letterSpacing: mediaQuery(context, "h", 1.5),
                          fontWeight: FontWeight.w700,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                  GestureDetector(
                    onTap: _handleClearButtonPressed,
                    child: Container(
                      alignment: Alignment.center,
                      height: mediaQuery(context, "h", 200),
                      width: mediaQuery(context, "w", 100),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(51.0),
                        color: const Color(0xff7f420c),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0x4d000000),
                            offset: Offset(0, 2),
                            blurRadius: 20,
                          ),
                        ],
                      ),
                      child: Text(
                        "Clear",
                        style: TextStyle(
                          fontFamily: 'SukhumvitSet-Bold',
                          fontSize: mediaQuery(context, "h", 50),
                          color: const Color(0xfffcf6e4),
                          letterSpacing: mediaQuery(context, "h", 1.5),
                          fontWeight: FontWeight.w700,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                  finaceCt.remotePDFpath.isNotEmpty
                      ? GestureDetector(
                          onTap: () {
                            if (finaceCt.remotePDFpath.isNotEmpty) {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => nondisclosuerPDFsuccess(
                                      path: finaceCt.remotePDFpath),
                                ),
                              );
                            }
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: mediaQuery(context, "h", 200),
                            width: mediaQuery(context, "w", 100),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(51.0),
                              color: const Color(0xff7f420c),
                              boxShadow: [
                                BoxShadow(
                                  color: const Color(0x4d000000),
                                  offset: Offset(0, 2),
                                  blurRadius: 20,
                                ),
                              ],
                            ),
                            child: Text(
                              "preview",
                              style: TextStyle(
                                fontFamily: 'SukhumvitSet-Bold',
                                fontSize: mediaQuery(context, "h", 50),
                                color: const Color(0xfffcf6e4),
                                letterSpacing: mediaQuery(context, "h", 1.5),
                                fontWeight: FontWeight.w700,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        )
                      : Container(
                          alignment: Alignment.center,
                          height: mediaQuery(context, "h", 200),
                          width: mediaQuery(context, "w", 100),
                        ),
                ], mainAxisAlignment: MainAxisAlignment.spaceEvenly)
              ],
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center),
          Container(
            margin: EdgeInsets.only(
                top: mediaQuery(context, "h", 154),
                left: mediaQuery(context, "w", 67)),
            child: GestureDetector(
              onTap: () {
                Navigator.of(context).pop();
              },
              child: Container(
                color: Colors.transparent,
                alignment: Alignment.center,
                height: mediaQuery(context, "h", 70),
                width: mediaQuery(context, "h", 70),
                child: SvgPicture.string(
                  '<svg viewBox="67.0 154.0 21.7 38.0" ><path transform="translate(55.75, 147.81)" d="M 17.79955863952637 25.18868064880371 L 32.17768859863281 10.82186222076416 C 33.24105834960938 9.758491516113281 33.24105834960938 8.038998603820801 32.17768859863281 6.986940383911133 C 31.11431694030762 5.92357063293457 29.39482688903809 5.934882164001465 28.33145332336426 6.986940383911133 L 12.0415210723877 23.26556205749512 C 11.01208686828613 24.29499435424805 10.98946189880371 25.94661521911621 11.96233177185059 27.00998306274414 L 28.32014274597168 43.40172958374023 C 28.85183143615723 43.93341445922852 29.55320167541504 44.19359970092773 30.24326133728027 44.19359970092773 C 30.93332099914551 44.19359970092773 31.63469123840332 43.93341445922852 32.1663818359375 43.40172958374023 C 33.22975158691406 42.33835601806641 33.22975158691406 40.61886596679688 32.1663818359375 39.56680679321289 L 17.79955863952637 25.18868064880371 Z" fill="#fcf6e4" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                  allowDrawingOutsideViewBox: true,
                  height: mediaQuery(context, "h", 50),
                  color: Colors.black,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
