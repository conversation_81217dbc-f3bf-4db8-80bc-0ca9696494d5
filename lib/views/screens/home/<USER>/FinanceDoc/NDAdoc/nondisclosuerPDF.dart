import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/FinanceDoc/NDAdoc/createSignature.dart';

class nondisclosuerPDF extends StatefulWidget {
  final String? path;
  final String? company;

  nondisclosuerPDF({Key? key, this.path, this.company}) : super(key: key);

  _nondisclosuerPDFState createState() => _nondisclosuerPDFState();
}

class _nondisclosuerPDFState extends State<nondisclosuerPDF>
    with WidgetsBindingObserver {
  final Completer<PDFViewController> _controller =
      Completer<PDFViewController>();
  int pages = 0;
  int currentPage = 0;
  bool isReady = false;
  String errorMessage = '';
  int totalPage = 0;
  bool checking = true;

  @override
  void initState() {
    super.initState();
    initializePage();
  }

  void initializePage() async {
    checking = false;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Stack(
            children: <Widget>[
              Center(
                  child: Container(
                color: Color(0xff302C49),
              )),
              PDFView(
                filePath: widget.path,
                enableSwipe: true,
                swipeHorizontal: true,
                autoSpacing: false,
                pageFling: true,
                pageSnap: true,
                defaultPage: currentPage,
                fitPolicy: FitPolicy.BOTH,
                preventLinkNavigation: false,
                onRender: (_pages) {
                  setState(() {
                    pages = _pages!;
                    isReady = true;
                  });
                },
                onError: (error) {
                  setState(() {
                    errorMessage = error.toString();
                  });
                },
                onPageError: (page, error) {
                  setState(() {
                    errorMessage = '$page: ${error.toString()}';
                  });
                },
                onViewCreated: (PDFViewController pdfViewController) {
                  _controller.complete(pdfViewController);
                },
                onLinkHandler: (uri) {},
                onPageChanged: (page, total) {
                  setState(() {
                    currentPage = page!;
                    totalPage = total!;
                  });
                },
              ),
              errorMessage.isEmpty
                  ? !isReady
                      ? Center(
                          child: CircularProgressIndicator(),
                        )
                      : Container()
                  : Center(
                      child: Text(errorMessage),
                    ),
              Container(
                margin: EdgeInsets.only(
                    top: mediaQuery(context, "h", 154),
                    left: mediaQuery(context, "w", 67)),
                child: GestureDetector(
                  onTap: () {
                    Navigator.of(context).pop();
                  },
                  child: Container(
                    color: Colors.transparent,
                    alignment: Alignment.center,
                    height: mediaQuery(context, "h", 70),
                    width: mediaQuery(context, "h", 70),
                    child: Stack(
                      children: [
                        SvgPicture.string(
                          '<svg viewBox="67.0 154.0 21.7 38.0" ><path transform="translate(55.75, 147.81)" d="M 17.79955863952637 25.18868064880371 L 32.17768859863281 10.82186222076416 C 33.24105834960938 9.758491516113281 33.24105834960938 8.038998603820801 32.17768859863281 6.986940383911133 C 31.11431694030762 5.92357063293457 29.39482688903809 5.934882164001465 28.33145332336426 6.986940383911133 L 12.0415210723877 23.26556205749512 C 11.01208686828613 24.29499435424805 10.98946189880371 25.94661521911621 11.96233177185059 27.00998306274414 L 28.32014274597168 43.40172958374023 C 28.85183143615723 43.93341445922852 29.55320167541504 44.19359970092773 30.24326133728027 44.19359970092773 C 30.93332099914551 44.19359970092773 31.63469123840332 43.93341445922852 32.1663818359375 43.40172958374023 C 33.22975158691406 42.33835601806641 33.22975158691406 40.61886596679688 32.1663818359375 39.56680679321289 L 17.79955863952637 25.18868064880371 Z" fill="#fcf6e4" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                          allowDrawingOutsideViewBox: true,
                          height: mediaQuery(context, "h", 38),
                          color: Colors.black,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              currentPage == (totalPage - 1)
                  ? Container(
                      alignment: Alignment.bottomCenter,
                      padding: EdgeInsets.only(
                          bottom: mediaQuery(context, "h", 100)),
                      child: GestureDetector(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  createSignature(0, widget.company.toString()),
                            ),
                          );
                        },
                        child: Container(
                          alignment: Alignment.center,
                          height: mediaQuery(context, "h", 101),
                          width: mediaQuery(context, "w", 273),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(51.0),
                            color: Theme.of(context)
                                .colorScheme
                                .secondaryContainer,
                            boxShadow: [
                              BoxShadow(
                                color: const Color(0x4d000000),
                                offset: Offset(0, 2),
                                blurRadius: 20,
                              ),
                            ],
                          ),
                          child: Text(
                            "เซ็นเอกสาร",
                            style: TextStyle(
                              fontFamily: 'SukhumvitSet-Bold',
                              fontSize: mediaQuery(context, "h", 30),
                              color: Theme.of(context).colorScheme.scrim,
                              letterSpacing: mediaQuery(context, "h", 1.5),
                              fontWeight: FontWeight.w700,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    )
                  : Container(),
            ],
          ),
          checking
              ? Container(
                  width: MediaQuery.of(context).size.width,
                  height: MediaQuery.of(context).size.height,
                  color: Colors.black12,
                  child: Center(
                    child: SizedBox(
                      width: 60.w,
                      child: const LoadingIndicator(
                        indicatorType: Indicator.lineSpinFadeLoader,
                        colors: [Colors.white],
                      ),
                    ),
                  ))
              : Container(),
        ],
      ),
    );
  }
}
