import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/FinanceDoc/NDAdoc/introNDA.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/FinanceDoc/listSignDoc.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';

import '../../../../../controllers/internal/FinaceController/FinaceController.dart';
import '../../../../../controllers/utils/widget.dart';
import '../../../Widget/Widget.dart';

class FinaceScreen extends StatefulWidget {
  const FinaceScreen({super.key});

  @override
  State<FinaceScreen> createState() => _FinaceScreenState();
}

class _FinaceScreenState extends State<FinaceScreen> {
  FinaceController finCtr = Get.find<FinaceController>();
  int currentIndex = 0;
  String statusshow = "1";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        height: Get.height,
        decoration: BoxDecoration(
            gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Theme.of(context).colorScheme.primary,
                  Theme.of(context).colorScheme.onPrimary,
            ])),
        child: Padding(
          padding: const EdgeInsets.only(top: 20.0),
          child: Column(
            children: [
              buildAppBar(context, 'ui_NitroListhead'.tr),
              buildChosseScreen()
            ],
          ),
        ),
      ),
    );
  }

  Widget buildChosseScreen() {
    return Column(
      children: [
        Container(
          margin: EdgeInsets.only(top: mediaQuery(context, "h", 10)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: <Widget>[
              Financialdoc(context),
              HRdoc(context),
              PMSdoc(context),
              docNDA(context),
            ],
          ),
        ),
      ],
    );
  }

  Widget Financialdoc(context){
    return Container(
      alignment: Alignment.topRight,
      child: Container(
        alignment: Alignment.topCenter,
        width: mediaQuery(context, "h", 150),
        height: mediaQuery(context, "h", 240),
        margin: EdgeInsets.only(
            left: mediaQuery(context, "w", 50),
            top: mediaQuery(context, "h", 30)),
        child: GestureDetector(
          onTap: () {
            Navigator.push(context,
                MaterialPageRoute(builder: (context) => listSignDoc("null")));
          },
          child: Stack(
            children: [
              Container(
                alignment: Alignment.topCenter,
                child: Stack(
                  children: [
                    Container(
                      alignment: Alignment.center,
                      width: mediaQuery(context, "h", 110),
                      height: mediaQuery(context, "h", 110),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(
                            mediaQuery(context, "h", 35)),
                        gradient:  LinearGradient(
                          colors: [
                            Theme.of(context).colorScheme.primary,
                            Theme.of(context).colorScheme.onPrimary                                    ],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                        ),
                        border: Border.all(
                          color: Theme.of(context).colorScheme.secondary,
                          width: 1.w,
                        ),
                        boxShadow: const [
                          BoxShadow(
                            color: Color(0x4d000000),
                            offset: Offset(0, 2),
                            blurRadius: 20,
                          ),
                        ],
                      ),
                      child: SvgPicture.string(
                        '<svg width="25" height="28" viewBox="0 0 25 28" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="1" y="1" width="20" height="24" rx="4" stroke="#FEE095" stroke-width="2"/><ellipse cx="18" cy="20.6875" rx="5.99999" ry="6.1875" fill="#302C49" stroke="#FEE095" stroke-width="2"/><path fill-rule="evenodd" clip-rule="evenodd" d="M20.2626 20.1815L20.6998 19.7442C21.0988 19.3452 21.0988 18.6983 20.6998 18.2993C20.3008 17.9002 19.6539 17.9002 19.2549 18.2993L18.8092 18.7449C19.1369 19.3563 19.6426 19.8586 20.2626 20.1815ZM18.0779 19.4762L16.0359 21.5182C16.0187 21.5354 16.0101 21.544 16.002 21.5523C15.5569 22.0078 15.3553 22.6481 15.4593 23.2765C15.4612 23.2879 15.4633 23.2999 15.4675 23.3239L15.4689 23.3316C15.4883 23.432 15.5668 23.5105 15.6671 23.5299L15.6749 23.5313L15.6749 23.5313C15.699 23.5355 15.711 23.5377 15.7224 23.5396C16.3509 23.6436 16.9913 23.4421 17.4469 22.9969C17.4552 22.9888 17.4639 22.9802 17.4812 22.9629L19.5293 20.9148C18.9393 20.553 18.4429 20.0596 18.0779 19.4762Z" fill="#FEE095"/><path d="M6 6L12 6" stroke="#FEE095" stroke-width="1.5" stroke-linecap="round"/><path d="M6 13.5L10.5 13.5" stroke="#FEE095" stroke-width="1.5" stroke-linecap="round"/><path d="M6 9.75L15 9.75" stroke="#FEE095" stroke-width="1.5" stroke-linecap="round"/></svg>',
                        allowDrawingOutsideViewBox: true,
                        fit: BoxFit.fill,
                        height: mediaQuery(context, "h", 45),
                      ),
                    ),
                    finCtr.countFINSignList > 0
                        ? Container(
                      width: 11.w,
                      height: 11.h,
                      margin: EdgeInsets.only(
                          left: mediaQuery(context, "h", 93)),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: Colors
                              .black, //                   <--- border color
                          width: 1.3,
                        ),
                        borderRadius: BorderRadius.all(
                            Radius.elliptical(15.0, 15.0)),
                        color: const Color(0xffff3e3e),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0x33000000),
                            offset: Offset(0, 3),
                            blurRadius: 5,
                          ),
                        ],
                      ),
                    )
                        : Container(
                      width: mediaQuery(context, "h", 25),
                      height: mediaQuery(context, "h", 25),
                      margin: EdgeInsets.only(
                          left: mediaQuery(context, "h", 93)),
                    ),
                  ],
                ),
              ),
              Container(
                alignment: Alignment.topCenter,
                margin:
                EdgeInsets.only(top: mediaQuery(context, "h", 130)),
                child: Text(
                  "ui_finace_general".tr.toString(),
                  style: TextStyle(
                    fontFamily: 'SukhumvitSet-Text',
                    fontSize: mediaQuery(context, "h", 24),
                    color: Theme.of(context).colorScheme.onSecondary,
                    letterSpacing:
                    mediaQuery(context, "h", 1.4000000000000001),
                    height: 1.4285714285714286,
                  ),
                  textHeightBehavior:
                  TextHeightBehavior(applyHeightToFirstAscent: false),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget HRdoc(context){
    return Container(
      alignment: Alignment.topRight,
      child: Container(
        alignment: Alignment.topCenter,
        width: mediaQuery(context, "h", 150),
        height: mediaQuery(context, "h", 240),
        margin: EdgeInsets.only(
            left: mediaQuery(context, "w", 50),
            top: mediaQuery(context, "h", 30)),
        child: GestureDetector(
          onTap: () {
            Navigator.push(context,
                MaterialPageRoute(builder: (context) => listSignDoc("HR")));
          },
          child: Stack(
            children: [
              Container(
                alignment: Alignment.topCenter,
                child: Stack(
                  children: [
                    Container(
                      alignment: Alignment.center,
                      width: mediaQuery(context, "h", 110),
                      height: mediaQuery(context, "h", 110),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(
                            mediaQuery(context, "h", 35)),
                        gradient:  LinearGradient(
                          colors: [
                            Theme.of(context).colorScheme.primary,
                            Theme.of(context).colorScheme.onPrimary                                    ],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                        ),
                        border: Border.all(
                          color: Theme.of(context).colorScheme.secondary,
                          width: 1.w,
                        ),
                        boxShadow: const [
                          BoxShadow(
                            color: Color(0x4d000000),
                            offset: Offset(0, 2),
                            blurRadius: 20,
                          ),
                        ],
                      ),
                      child: SvgPicture.string(
                        '<svg width="25" height="28" viewBox="0 0 25 28" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="1" y="1" width="20" height="24" rx="4" stroke="#FEE095" stroke-width="2"/><ellipse cx="18" cy="20.6875" rx="5.99999" ry="6.1875" fill="#302C49" stroke="#FEE095" stroke-width="2"/><path fill-rule="evenodd" clip-rule="evenodd" d="M20.2626 20.1815L20.6998 19.7442C21.0988 19.3452 21.0988 18.6983 20.6998 18.2993C20.3008 17.9002 19.6539 17.9002 19.2549 18.2993L18.8092 18.7449C19.1369 19.3563 19.6426 19.8586 20.2626 20.1815ZM18.0779 19.4762L16.0359 21.5182C16.0187 21.5354 16.0101 21.544 16.002 21.5523C15.5569 22.0078 15.3553 22.6481 15.4593 23.2765C15.4612 23.2879 15.4633 23.2999 15.4675 23.3239L15.4689 23.3316C15.4883 23.432 15.5668 23.5105 15.6671 23.5299L15.6749 23.5313L15.6749 23.5313C15.699 23.5355 15.711 23.5377 15.7224 23.5396C16.3509 23.6436 16.9913 23.4421 17.4469 22.9969C17.4552 22.9888 17.4639 22.9802 17.4812 22.9629L19.5293 20.9148C18.9393 20.553 18.4429 20.0596 18.0779 19.4762Z" fill="#FEE095"/><path d="M6 6L12 6" stroke="#FEE095" stroke-width="1.5" stroke-linecap="round"/><path d="M6 13.5L10.5 13.5" stroke="#FEE095" stroke-width="1.5" stroke-linecap="round"/><path d="M6 9.75L15 9.75" stroke="#FEE095" stroke-width="1.5" stroke-linecap="round"/></svg>',
                        allowDrawingOutsideViewBox: true,
                        fit: BoxFit.fill,
                        height: mediaQuery(context, "h", 45),
                      ),
                    ),
                    finCtr.countHRSignList > 0
                        ? Container(
                      width: 11.w,
                      height: 11.h,
                      margin: EdgeInsets.only(
                          left: mediaQuery(context, "h", 93)),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: Colors
                              .black, //                   <--- border color
                          width: 1.3,
                        ),
                        borderRadius: BorderRadius.all(
                            Radius.elliptical(15.0, 15.0)),
                        gradient:  LinearGradient(
                          colors: [
                            Theme.of(context).colorScheme.primary,
                            Theme.of(context).colorScheme.onPrimary                                    ],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                        ),

                        boxShadow: [
                          BoxShadow(
                            color: const Color(0x33000000),
                            offset: Offset(0, 3),
                            blurRadius: 5,
                          ),
                        ],
                      ),
                    )
                        : Container(
                      width: mediaQuery(context, "h", 25),
                      height: mediaQuery(context, "h", 25),
                      margin: EdgeInsets.only(
                          left: mediaQuery(context, "h", 93)),
                    ),
                  ],
                ),
              ),
              Container(
                alignment: Alignment.topCenter,
                margin:
                EdgeInsets.only(top: mediaQuery(context, "h", 130)),
                child: Text(
                  "ui_finace_HR".tr.toString(),
                  style: TextStyle(
                    fontFamily: 'SukhumvitSet-Text',
                    fontSize: mediaQuery(context, "h", 24),
                    color: Theme.of(context).colorScheme.onSecondary,
                    letterSpacing:
                    mediaQuery(context, "h", 1.4000000000000001),
                    height: 1.4285714285714286,
                  ),
                  textHeightBehavior:
                  TextHeightBehavior(applyHeightToFirstAscent: false),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget PMSdoc(context){
    return Container(
      alignment: Alignment.topRight,
      child: Container(
        alignment: Alignment.topCenter,
        width: mediaQuery(context, "h", 150),
        height: mediaQuery(context, "h", 240),
        margin: EdgeInsets.only(
            left: mediaQuery(context, "w", 50),
            top: mediaQuery(context, "h", 30)),
        child: GestureDetector(
          onTap: () {
            Navigator.push(context,
                MaterialPageRoute(builder: (context) => listSignDoc("PMS")));
          },
          child: Stack(
            children: [
              Container(
                alignment: Alignment.topCenter,
                child: Stack(
                  children: [
                    Container(
                      alignment: Alignment.center,
                      width: mediaQuery(context, "h", 110),
                      height: mediaQuery(context, "h", 110),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(
                            mediaQuery(context, "h", 35)),
                        gradient:  LinearGradient(
                          colors: [
                            Theme.of(context).colorScheme.primary,
                            Theme.of(context).colorScheme.onPrimary                                    ],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                        ),
                        border: Border.all(
                          color: Theme.of(context).colorScheme.secondary,
                          width: 1.w,
                        ),
                        boxShadow: const [
                          BoxShadow(
                            color: Color(0x4d000000),
                            offset: Offset(0, 2),
                            blurRadius: 20,
                          ),
                        ],
                      ),
                      child: SvgPicture.string(
                        '<svg width="25" height="28" viewBox="0 0 25 28" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="1" y="1" width="20" height="24" rx="4" stroke="#FEE095" stroke-width="2"/><ellipse cx="18" cy="20.6875" rx="5.99999" ry="6.1875" fill="#302C49" stroke="#FEE095" stroke-width="2"/><path fill-rule="evenodd" clip-rule="evenodd" d="M20.2626 20.1815L20.6998 19.7442C21.0988 19.3452 21.0988 18.6983 20.6998 18.2993C20.3008 17.9002 19.6539 17.9002 19.2549 18.2993L18.8092 18.7449C19.1369 19.3563 19.6426 19.8586 20.2626 20.1815ZM18.0779 19.4762L16.0359 21.5182C16.0187 21.5354 16.0101 21.544 16.002 21.5523C15.5569 22.0078 15.3553 22.6481 15.4593 23.2765C15.4612 23.2879 15.4633 23.2999 15.4675 23.3239L15.4689 23.3316C15.4883 23.432 15.5668 23.5105 15.6671 23.5299L15.6749 23.5313L15.6749 23.5313C15.699 23.5355 15.711 23.5377 15.7224 23.5396C16.3509 23.6436 16.9913 23.4421 17.4469 22.9969C17.4552 22.9888 17.4639 22.9802 17.4812 22.9629L19.5293 20.9148C18.9393 20.553 18.4429 20.0596 18.0779 19.4762Z" fill="#FEE095"/><path d="M6 6L12 6" stroke="#FEE095" stroke-width="1.5" stroke-linecap="round"/><path d="M6 13.5L10.5 13.5" stroke="#FEE095" stroke-width="1.5" stroke-linecap="round"/><path d="M6 9.75L15 9.75" stroke="#FEE095" stroke-width="1.5" stroke-linecap="round"/></svg>',
                        allowDrawingOutsideViewBox: true,
                        fit: BoxFit.fill,
                        height: mediaQuery(context, "h", 45),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                alignment: Alignment.topCenter,
                margin:
                EdgeInsets.only(top: mediaQuery(context, "h", 130)),
                child: Text(
                  "ui_finace_PMS".tr.toString(),
                  style: TextStyle(
                    fontFamily: 'SukhumvitSet-Text',
                    fontSize: mediaQuery(context, "h", 24),
                    color: Theme.of(context).colorScheme.onSecondary,
                    letterSpacing:
                    mediaQuery(context, "h", 1.4000000000000001),
                    height: 1.4285714285714286,
                  ),
                  textHeightBehavior:
                  TextHeightBehavior(applyHeightToFirstAscent: false),
                  textAlign: TextAlign.center,
                ),
              ),
              finCtr.countPMSSignList > 0
                  ? Container(
                width: 11.w,
                height: 11.h,
                margin: EdgeInsets.only(
                    left: mediaQuery(context, "h", 93)),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Theme.of(context).colorScheme.secondary,//                   <--- border color
                    width: 1.3,
                  ),
                  borderRadius: BorderRadius.all(
                      Radius.elliptical(15.0, 15.0)),
                  gradient:  LinearGradient(
                    colors: [
                      Theme.of(context).colorScheme.primary,
                      Theme.of(context).colorScheme.onPrimary                                    ],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),

                  boxShadow: [
                    BoxShadow(
                      color: const Color(0x33000000),
                      offset: Offset(0, 3),
                      blurRadius: 5,
                    ),
                  ],
                ),
              )
                  : Container(
                width: mediaQuery(context, "h", 25),
                height: mediaQuery(context, "h", 25),
                margin: EdgeInsets.only(
                    left: mediaQuery(context, "h", 93)),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget docNDA(context){
    return Container(
      alignment: Alignment.topRight,
      child: Container(
        alignment: Alignment.topCenter,
        width: mediaQuery(context, "h", 150),
        height: mediaQuery(context, "h", 240),
        margin: EdgeInsets.only(
            left: mediaQuery(context, "w", 50),
            top: mediaQuery(context, "h", 30)),
        child: GestureDetector(
          onTap: () {

            Get.toNamed('IntroNDA');
          },
          child: Stack(
            children: [
              Container(
                alignment: Alignment.topCenter,
                child: Stack(
                  children: [
                    Container(
                      alignment: Alignment.center,
                      width: mediaQuery(context, "h", 110),
                      height: mediaQuery(context, "h", 110),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: Theme.of(context).colorScheme.secondary,//                   <--- border color
                          width: 1.3,
                        ),
                        borderRadius: BorderRadius.all(
                            Radius.elliptical(15.0, 15.0)),
                        gradient:  LinearGradient(
                          colors: [
                            Theme.of(context).colorScheme.primary,
                            Theme.of(context).colorScheme.onPrimary                                    ],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                        ),
                        boxShadow: const [
                          BoxShadow(
                            color: Color(0x4d000000),
                            offset: Offset(0, 2),
                            blurRadius: 20,
                          ),
                        ],
                      ),
                      child: Image.asset('assets/ADD/File_dock_search.png',
                          width: mediaQuery(context, "h", 55),
                          height: mediaQuery(context, "h", 55)),
                    ),
                  ],
                ),
              ),
              Container(
                alignment: Alignment.topCenter,
                margin:
                EdgeInsets.only(top: mediaQuery(context, "h", 130)),
                child: Text(
                  "ui_nondisclosure".tr.toString(),
                  style: TextStyle(
                    fontFamily: 'SukhumvitSet-Text',
                    fontSize: mediaQuery(context, "h", 24),
                    color: Theme.of(context).colorScheme.onSecondary,
                    letterSpacing:
                    mediaQuery(context, "h", 1.4000000000000001),
                    height: 1.4285714285714286,
                  ),
                  textHeightBehavior:
                  TextHeightBehavior(applyHeightToFirstAscent: false),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }


}
