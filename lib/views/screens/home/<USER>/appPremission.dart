import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';
import 'package:mapp_ms24/views/screens/home/<USER>';
import 'package:mapp_ms24/views/screens/home/<USER>/unlockTwoFA.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/yubikey.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';

class AppPermission extends StatefulWidget {
  final String? deepLinkPath;
  const AppPermission({this.deepLinkPath = 'AppPermission', Key? key});
  @override
  State<AppPermission> createState() => _AppPermissionState();
}

class _AppPermissionState extends State<AppPermission> {
  void initState() {
    super.initState();
    checkSession();
  }

  @override
  void openApp() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var id = await prefs.getString('id');
    // await generateDynamicLink();
    // ใส่ชื่อสกีมของแอปพลิเคชันที่คุณต้องการเปิด
    if (await canLaunch(
        'https://prachakij.page.link/?link=https://prachakij.page.link/pms_service?ID=$id&&apn=com.prachakij.pmsservice&isi=1598242517&ibi=com.prachakij.pmsservice')) {
      await launch(
          'https://prachakij.page.link/?link=https://prachakij.page.link/pms_service?ID=$id&&apn=com.prachakij.pmsservice&isi=1598242517&ibi=com.prachakij.pmsservice');
    } else {
      throw 'ไม่สามารถเปิดแอปพลิเคชันได้';
    }
  }


  // void fetchLinkData() async {
  //   try{
  //     var link = await FirebaseDynamicLinks.instance.getInitialLink();
  //     handleLinkData(link!);
  //
  //   } catch (e) {
  //     print(e.toString());
  //   }
  // }

  // void handleLinkData(PendingDynamicLinkData data) async {
  //   final Uri? uri = data.link;
  //   print(uri); // => http://example.page.app?data=123
  // }

  checkSession() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var token_TG = await prefs.getString('token_TG');
    if (token_TG != 'null' || token_TG != '') {
      bool sessionTwoFA = await Yubikey.Check_session();
      if (!sessionTwoFA) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => UnlockTwoFA()),
        );
      }
    }
  }

  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Center(
              child: Container(
            color: Color(0xff302C49),
          )),
          Container(
            width: mediaQuery(context, "w", 828),
            height: mediaQuery(context, "h", 220),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                bottomRight: Radius.circular(mediaQuery(context, "h", 40)),
                bottomLeft: Radius.circular(mediaQuery(context, "h", 40)),
              ),
              color: const Color(0xff1f1c2f),
              boxShadow: [
                BoxShadow(
                  color: const Color(0x4d000000),
                  offset: Offset(0, 2),
                  blurRadius: 20,
                ),
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.only(
                top: mediaQuery(context, "h", 120),
                left: mediaQuery(context, "w", 35)),
            child: GestureDetector(
              onTap: () {},
              child: Container(
                color: Colors.transparent,
                alignment: Alignment.center,
                height: mediaQuery(context, "h", 70),
                width: mediaQuery(context, "h", 70),
                child: SvgPicture.asset(
                  'assets/images/pkg/SplashScreen/Group_4885.svg',
                  height: mediaQuery(context, "h", 60),
                ),
              ),
            ),
          ),
          Container(
            alignment: Alignment.center,
            height: mediaQuery(context, "h", 220),
            margin: EdgeInsets.only(top: mediaQuery(context, "h", 50)),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Text(
                  "ui_login_app".tr.toString(),
                  style: TextStyle(
                    fontFamily: 'SukhumvitSet-SemiBold',
                    fontSize: mediaQuery(context, "h", 30),
                    color: const Color(0xfffcf6e4),
                    letterSpacing: mediaQuery(context, "h", 1.2),
                    fontWeight: FontWeight.w700,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          Container(
            alignment: Alignment.topCenter,
            margin: EdgeInsets.only(top: mediaQuery(context, "h", 300)),
            child: Text.rich(
              TextSpan(
                style: TextStyle(
                  fontFamily: 'Sarabun-Regular',
                  fontSize: mediaQuery(context, "h", 28),
                  color: const Color(0xfffcf6e4),
                  letterSpacing: mediaQuery(context, "h", 2),
                ),
                children: [
                  TextSpan(
                    text: 'คุณได้เข้าสู่ระบบ ด้วย MS24 ไว้ก่อนหน้านี้\n',
                  ),
                  TextSpan(
                    text: 'คุณต้องการดำเนินการต่อหรือไม่',
                    style: TextStyle(
                      color: const Color(0xfffcf6e4),
                    ),
                  ),
                ],
              ),
              textAlign: TextAlign.left,
            ),
          ),
          Column(
            children: [
              Container(
                alignment: Alignment.topCenter,
                margin: EdgeInsets.only(top: mediaQuery(context, "h", 500)),
                child: GestureDetector(
                  onTap: () {
                    openApp();
                  },
                  child: Container(
                    width: mediaQuery(context, "w", 700),
                    height: mediaQuery(context, "h", 100),
                    child: Container(
                      alignment: Alignment.center,
                      width: mediaQuery(context, "w", 273),
                      height: mediaQuery(context, "h", 101),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15.0),
                        gradient: LinearGradient(
                          begin: Alignment(0.0, -1.0),
                          end: Alignment(0.0, 1.0),
                          colors: [
                            const Color(0xfffee095),
                            const Color(0xfffdd163)
                          ],
                          stops: [0.0, 1.0],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0x4d000000),
                            offset: Offset(0, 2),
                            blurRadius: 20,
                          ),
                        ],
                      ),
                      child: Text(
                        "btn_continew".tr.toString(),
                        style: TextStyle(
                          fontFamily: 'Sarabun-Bold',
                          fontSize: mediaQuery(context, "h", 30),
                          color: const Color(0xff1f1c2f),
                          letterSpacing: 1.5,
                          fontWeight: FontWeight.w700,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ),
              ),
              Container(
                alignment: Alignment.topCenter,
                margin: EdgeInsets.only(top: mediaQuery(context, "h", 50)),
                child: GestureDetector(
                  onTap: () {
                    Navigator.pushReplacement(
                      context,
                      MaterialPageRoute(builder: (context) => HomeScreen()),
                    );
                  },
                  child: Container(
                    width: mediaQuery(context, "w", 700),
                    height: mediaQuery(context, "h", 100),
                    child: Container(
                      alignment: Alignment.center,
                      width: mediaQuery(context, "w", 273),
                      height: mediaQuery(context, "h", 101),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15.0),
                        gradient: LinearGradient(
                          begin: Alignment(0.0, -1.0),
                          end: Alignment(0.0, 1.0),
                          colors: [
                            const Color(0xffFFFFF0),
                            const Color(0xffffffff)
                          ],
                          stops: [0.0, 1.0],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0x4d000000),
                            offset: Offset(0, 2),
                            blurRadius: 20,
                          ),
                        ],
                      ),
                      child: Text(
                        "btn_settingcancel".tr.toString(),
                        style: TextStyle(
                          fontFamily: 'Sarabun-Bold',
                          fontSize: mediaQuery(context, "h", 30),
                          color: const Color(0xff1f1c2f),
                          letterSpacing: 1.5,
                          fontWeight: FontWeight.w700,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
