import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';

import '../../../../controllers/verifycontroller.dart';
class genQRVerify extends StatefulWidget {
  const genQRVerify({super.key});

  @override
  State<genQRVerify> createState() => _genQRVerifyState();
}

class _genQRVerifyState extends State<genQRVerify> {
  final verify controller = Get.put(verify());

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.fetchService(); // ✅ โหลดข้อมูลหลังจากที่ UI สร้างเสร็จ
    });
  }

  void showQrPopup(String qrUrl) {
    Get.dialog(
      AlertDialog(
        title: const Text("🔍 QR Code"),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            qrUrl.isNotEmpty
                ? Image.network(qrUrl) // ✅ แสดง QR Code
                : const Text("⚠️ ไม่พบ QR Code"),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text("ปิด"),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Verify for CUSTOMER')),
      body: Container(
        height: Get.height,
        width: Get.width,
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // ✅ รายการที่โหลดมา
            Expanded(
              child: Obx(() {
                if (controller.isLoading.value) {
                  return const Center(child: CircularProgressIndicator()); // แสดง Loading
                }
                if (controller.serviceList.isEmpty) {
                  return const Center(child: Text("ไม่พบข้อมูล")); // กรณีไม่มีข้อมูล
                }
                return ListView.builder(
                  itemCount: controller.serviceList.length,
                  itemBuilder: (context, index) {
                    final service = controller.serviceList[index];
                    return Card(
                      elevation: 2,
                      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                      child: ListTile(
                        title: Text("Case ID: ${service['case_id']}"),
                        subtitle: Text("ชื่อลูกค้า: ${service['customer_name']}"),
                        trailing: Text("หน่วยงาน: ${service['bu']}"),
                        onTap: () {
                          Get.snackbar("รายละเอียด", "คุณเลือกเคส ${service['case_id']} ${service['message_thread_id']}");
                          controller.verifyEmpolyee(service['case_id'], service['message_thread_id']);
                        },
                      ),
                    );
                  },
                );
              }),
            ),
          ],
        ),
      ),
    );
  }
}
