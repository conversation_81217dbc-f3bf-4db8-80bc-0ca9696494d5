import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:get/get_utils/get_utils.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:mapp_ms24/controllers/internal/BitrixController/bitrixController.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/controllers/utils/widget.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/Workissues/WorkissuesScreen.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/camera/playVideo.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/camera/showimage.dart';
import 'package:url_launcher/url_launcher.dart';

class bitrixActionFinish extends StatefulWidget {
  final idIssue;
  final idBitrix;
  final createdate;
  final createbyname;
  final createbylastname;
  final responsiblename;
  final responsiblelastname;
  final groupname;
  final title;
  final deadline;
  final realsStatus;

  bitrixActionFinish(
      this.idIssue,
      this.idBitrix,
      this.createdate,
      this.createbyname,
      this.createbylastname,
      this.responsiblename,
      this.responsiblelastname,
      this.groupname,
      this.title,
      this.deadline,
      this.realsStatus);

  @override
  _bitrixActionFinishState createState() => _bitrixActionFinishState(
      this.idIssue,
      this.idBitrix,
      this.createdate,
      this.createbyname,
      this.createbylastname,
      this.responsiblename,
      this.responsiblelastname,
      this.groupname,
      this.title,
      this.deadline,
      this.realsStatus);
}

class _bitrixActionFinishState extends State<bitrixActionFinish> {
  final idIssue;
  final idBitrix;
  final createdate;
  final createbyname;
  final createbylastname;
  final responsiblename;
  final responsiblelastname;
  final groupname;
  final title;
  var deadline;
  var realsStatus;

  _bitrixActionFinishState(
      this.idIssue,
      this.idBitrix,
      this.createdate,
      this.createbyname,
      this.createbylastname,
      this.responsiblename,
      this.responsiblelastname,
      this.groupname,
      this.title,
      this.deadline,
      this.realsStatus);

  BitrixController bitrixController = Get.put(BitrixController());
  ProfileController profileController = Get.put(ProfileController());
  TextEditingController _commentController = TextEditingController();
  TextEditingController _commentEditController = TextEditingController();
  int statusShowComment = 1;
  int statusShowTextfild = 0;
  int positionEdit = -1;
  @override
  bool checking = true;
  void initState() {
    super.initState();
    initializePage();
  }

  void initializePage() async {
    await bitrixController.checkDeadLine(deadline);
    await bitrixController.loadBitrixComment(context, idIssue);
    checking = false;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () => bitrixController.allload(context, idIssue),
        child: Stack(
          children: [
            Center(
              child: Container(
                color: Color(0xff302C49),
              ),
            ),
            Container(
              margin: EdgeInsets.only(
                  top: mediaQuery(context, "h", 154),
                  left: mediaQuery(context, "w", 67)),
              child: GestureDetector(
                onTap: () {
                  Navigator.of(context).pop();
                },
                child: Container(
                  color: Colors.transparent,
                  alignment: Alignment.topCenter,
                  height: mediaQuery(context, "h", 45),
                  width: mediaQuery(context, "w", 45),
                  child: Stack(
                    children: [
                      SvgPicture.string(
                        '<svg viewBox="67.0 154.0 21.7 38.0" ><path transform="translate(55.75, 147.81)" d="M 17.79955863952637 25.18868064880371 L 32.17768859863281 10.82186222076416 C 33.24105834960938 9.758491516113281 33.24105834960938 8.038998603820801 32.17768859863281 6.986940383911133 C 31.11431694030762 5.92357063293457 29.39482688903809 5.934882164001465 28.33145332336426 6.986940383911133 L 12.0415210723877 23.26556205749512 C 11.01208686828613 24.29499435424805 10.98946189880371 25.94661521911621 11.96233177185059 27.00998306274414 L 28.32014274597168 43.40172958374023 C 28.85183143615723 43.93341445922852 29.55320167541504 44.19359970092773 30.24326133728027 44.19359970092773 C 30.93332099914551 44.19359970092773 31.63469123840332 43.93341445922852 32.1663818359375 43.40172958374023 C 33.22975158691406 42.33835601806641 33.22975158691406 40.61886596679688 32.1663818359375 39.56680679321289 L 17.79955863952637 25.18868064880371 Z" fill="#fcf6e4" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                        allowDrawingOutsideViewBox: true,
                        height: mediaQuery(context, "h", 38),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Container(
              alignment: Alignment.topCenter,
              margin: EdgeInsets.only(top: mediaQuery(context, "h", 150)),
              child: AppWidget.normalText(
                context,
                "ui_mainBitrixhead".tr.toString(),
                16.sp,
                const Color(0xffffffff),
                FontWeight.w500,
              ),
            ),
            Container(
              margin: EdgeInsets.only(top: mediaQuery(context, "h", 251)),
              child: Column(
                children: [
                  Stack(
                    children: [
                      Container(
                        margin: EdgeInsets.only(
                            left: mediaQuery(context, "w", 86),
                            top: mediaQuery(context, "h", 5)),
                        child: SvgPicture.asset(
                          "assets/bitrix/Group4936.svg",
                          fit: BoxFit.fitHeight,
                          height: mediaQuery(context, "h", 33),
                        ),
                      ),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Container(
                            width: mediaQuery(context, "w", 828),
                            padding: EdgeInsets.only(
                                left: mediaQuery(context, "w", 144),
                                right: mediaQuery(context, "w", 50)),
                            child: new RichText(
                              text: new TextSpan(
                                children: <TextSpan>[
                                  new TextSpan(
                                    text: createdate +
                                        " " +
                                        "Created by " +
                                        createbyname.toString() +
                                        " " +
                                        createbylastname.toString(),
                                    style: TextStyle(
                                      fontFamily: 'SukhumvitSet-Text',
                                      fontSize: mediaQuery(context, "h", 28),
                                      color: const Color(0xfffee095),
                                      letterSpacing: mediaQuery(
                                          context, "h", 1.4000000000000001),
                                    ),
                                  ),
                                  new TextSpan(
                                    text: " : " + groupname.toString() + ".",
                                    style: TextStyle(
                                      fontFamily: 'SukhumvitSet-Text',
                                      fontSize: mediaQuery(context, "h", 30),
                                      color: const Color(0xffa596ff),
                                      letterSpacing: mediaQuery(
                                          context, "h", 1.4000000000000001),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          Container(
                            height: mediaQuery(context, "h", 19),
                          ),
                          Container(
                            width: mediaQuery(context, "w", 828),
                            padding: EdgeInsets.only(
                                left: mediaQuery(context, "w", 86),
                                right: mediaQuery(context, "w", 30)),
                            child: new RichText(
                              text: new TextSpan(
                                children: <TextSpan>[
                                  new TextSpan(
                                    text: title.toString(),
                                    style: TextStyle(
                                      fontFamily: 'SukhumvitSet-Text',
                                      fontSize: mediaQuery(context, "h", 30),
                                      color: const Color(0xfffcf6e4),
                                      letterSpacing: mediaQuery(
                                          context, "h", 1.4000000000000001),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  Container(
                    height: mediaQuery(context, "h", 64),
                  ),
                  Stack(
                    children: [
                      Container(
                        alignment: Alignment.topLeft,
                        width: mediaQuery(context, "w", 828),
                        padding: EdgeInsets.only(
                            top: mediaQuery(context, "h", 29),
                            left: mediaQuery(context, "w", 86),
                            right: mediaQuery(context, "w", 86)),
                        child: SvgPicture.string(
                          '<svg viewBox="86.0 539.0 29.7 33.0" ><path transform="translate(79.75, 536.92)" d="M 28.57451248168945 18.68234252929688 L 26.82551193237305 16.9333438873291 L 18.77350425720215 24.9853458404541 L 15.2755069732666 21.48734283447266 L 13.5265064239502 23.23634719848633 L 18.77350425720215 28.48335266113281 L 28.57451248168945 18.68234252929688 Z M 32.6500129699707 5.383335113525391 L 31.00001525878906 5.383335113525391 L 31.00001525878906 2.083333253860474 L 27.70001029968262 2.083333253860474 L 27.70001029968262 5.383335113525391 L 14.5000057220459 5.383335113525391 L 14.5000057220459 2.083333253860474 L 11.20000457763672 2.083333253860474 L 11.20000457763672 5.383335113525391 L 9.550003051757812 5.383335113525391 C 7.71850061416626 5.383335113525391 6.266499519348145 6.868335723876953 6.266499519348145 8.683337211608887 L 6.25 31.78335189819336 C 6.25 33.59835433959961 7.71850061416626 35.08335494995117 9.550002098083496 35.08335494995117 L 32.6500129699707 35.08335494995117 C 34.46501922607422 35.08335494995117 35.95001983642578 33.59835433959961 35.95001983642578 31.78335189819336 L 35.95001983642578 8.683338165283203 C 35.95001983642578 6.868335723876953 34.46501922607422 5.383335113525391 32.6500129699707 5.383335113525391 Z M 32.6500129699707 31.78335189819336 L 9.550003051757812 31.78335189819336 L 9.550003051757812 13.63333797454834 L 32.6500129699707 13.63333797454834 L 32.6500129699707 31.78335189819336 Z" fill="#a596ff" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                          allowDrawingOutsideViewBox: true,
                          fit: BoxFit.fitHeight,
                          height: mediaQuery(context, "h", 39),
                        ),
                      ),
                      Container(
                        alignment: Alignment.topLeft,
                        width: mediaQuery(context, "w", 828),
                        padding: EdgeInsets.only(
                            top: mediaQuery(context, "h", 35),
                            left: mediaQuery(context, "w", 136),
                            right: mediaQuery(context, "w", 86)),
                        child: GestureDetector(
                          onTap: () {},
                          child: Text(
                            'Deadline ' +
                                convertDateTimeFromDB(deadline, "dd-MM-yyyy"),
                            style: TextStyle(
                              fontFamily: 'SukhumvitSet-Text',
                              fontSize: mediaQuery(context, "h", 28),
                              color: bitrixController.statusDeadLine == 1
                                  ? const Color(0xffff3e3e)
                                  : const Color(0xfffee095),
                              letterSpacing:
                                  mediaQuery(context, "h", 1.4000000000000001),
                              decoration: TextDecoration.underline,
                            ),
                            textAlign: TextAlign.left,
                          ),
                        ),
                      ),
                      Container(
                        alignment: Alignment.topLeft,
                        width: mediaQuery(context, "w", 828),
                        padding: EdgeInsets.only(
                            left: mediaQuery(context, "w", 427),
                            right: mediaQuery(context, "w", 86)),
                        child: GestureDetector(
                          onTap: () {
                            bitrixController.updateBitrixStatus(
                                context, 5, idIssue, realsStatus);
                          },
                          child: Container(
                            alignment: Alignment.center,
                            width: mediaQuery(context, "w", 170),
                            height: mediaQuery(context, "h", 90),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(
                                  mediaQuery(context, "h", 20)),
                              color: const Color(0xff5f569b),
                              boxShadow: [
                                BoxShadow(
                                  color: const Color(0x4d000000),
                                  offset: Offset(0, 2),
                                  blurRadius: 20,
                                ),
                              ],
                            ),
                            child: Text(
                              'Completed',
                              style: TextStyle(
                                fontFamily: 'SukhumvitSet-Bold',
                                fontSize: mediaQuery(context, "h", 28),
                                color: const Color(0xfffee095),
                                letterSpacing: mediaQuery(
                                    context, "h", 1.4000000000000001),
                                fontWeight: FontWeight.w700,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ),
                      Container(
                        alignment: Alignment.topLeft,
                        width: mediaQuery(context, "w", 828),
                        padding: EdgeInsets.only(
                            left: mediaQuery(context, "w", 612)),
                        child: GestureDetector(
                          onTap: () {
                            bitrixController.updateBitrixStatus(
                                context, 2, idIssue, realsStatus);
                          },
                          child: Container(
                            alignment: Alignment.center,
                            width: mediaQuery(context, "w", 170),
                            height: mediaQuery(context, "h", 90),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(
                                  mediaQuery(context, "h", 20)),
                              color: const Color(0xfffee095),
                              boxShadow: [
                                BoxShadow(
                                  color: const Color(0x4d000000),
                                  offset: Offset(0, 2),
                                  blurRadius: 20,
                                ),
                              ],
                            ),
                            child: Text(
                              'Return',
                              style: TextStyle(
                                fontFamily: 'SukhumvitSet-Bold',
                                fontSize: mediaQuery(context, "h", 28),
                                color: const Color(0xff5f569b),
                                letterSpacing: mediaQuery(
                                    context, "h", 1.4000000000000001),
                                fontWeight: FontWeight.w700,
                                height: 1.0714285714285714,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  Container(
                    height: mediaQuery(context, "h", 35),
                  ),
                  SvgPicture.string(
                    '<svg viewBox="0.0 636.0 828.0 1.0" ><path transform="translate(0.0, 636.0)" d="M 0 0 L 828 0" fill="none" fill-opacity="0.1" stroke="#fcf6e4" stroke-width="3" stroke-opacity="0.1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                    allowDrawingOutsideViewBox: true,
                  ),
                  bitrixController.dataBitrixComment.length > 0
                      ? Container(
                          height: mediaQuery(context, "h", 46),
                        )
                      : Container(),
                  Expanded(
                    child: ListView(
                      padding: EdgeInsets.only(top: 0),
                      children: <Widget>[
                        new Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: ListMyWidgets()),
                      ],
                    ),
                  )
                ],
              ),
            ),
            showeDetailFinish(),
            alertSuccessCloseIssue(),
            alertSuccessReturn(),
            checking
                ? Container(
                    width: MediaQuery.of(context).size.width,
                    height: MediaQuery.of(context).size.height,
                    color: Colors.black12,
                    child: Center(
                      child: SizedBox(
                        width: 60.w,
                        child: const LoadingIndicator(
                          indicatorType: Indicator.lineSpinFadeLoader,
                          colors: [Colors.white],
                        ),
                      ),
                    ),
                  )
                : Container(),
          ],
        ),
      ),
    );
  }

  List<Widget> ListMyWidgets() {
    List<Widget> list = [];
    for (var i = 0;
        i < bitrixController.dataBitrixComment.length.toInt();
        i++) {
      if (i == 0) {
        list.add(
          Stack(
            children: [
              statusShowComment == 0
                  ? GestureDetector(
                      onTap: () {
                        statusShowComment = 1;
                        statusShowTextfild = 1;
                      },
                      child: Stack(
                        children: [
                          Container(
                            margin: EdgeInsets.only(
                                left: mediaQuery(context, "w", 86),
                                top: mediaQuery(context, "h", 10)),
                            child: SvgPicture.string(
                              '<svg viewBox="86.0 977.0 32.7 28.6" ><path transform="translate(86.0, 974.75)" d="M 16.3304615020752 2.25 C 7.310433864593506 2.25 0 8.188930511474609 0 15.51850128173828 C 0 18.68252754211426 1.365124583244324 21.57863426208496 3.636079549789429 23.8559684753418 C 2.838693618774414 27.07102584838867 0.1722353398799896 29.93523788452148 0.1403399109840393 29.96713256835938 C 0 30.11385345458984 -0.03827453032135963 30.33073997497559 0.04465360194444656 30.5221118927002 C 0.1275817304849625 30.7134838104248 0.3061961829662323 30.82831001281738 0.5103269219398499 30.82831001281738 C 4.739661693572998 30.82831001281738 7.910067558288574 28.79976081848145 9.479323387145996 27.54945755004883 C 11.56528377532959 28.33408546447754 13.88089275360107 28.78700065612793 16.3304615020752 28.78700065612793 C 25.35049247741699 28.78700065612793 32.66092300415039 22.84807205200195 32.66092300415039 15.51850128173828 C 32.66092300415039 8.188929557800293 25.35049247741699 2.25 16.3304615020752 2.25 Z M 8.165230751037598 17.5598087310791 C 7.0361328125 17.5598087310791 6.123923301696777 16.6476001739502 6.123923301696777 15.51850128173828 C 6.123923301696777 14.38940238952637 7.0361328125 13.47719287872314 8.165230751037598 13.47719287872314 C 9.294329643249512 13.47719287872314 10.20653915405273 14.38940238952637 10.20653915405273 15.51850128173828 C 10.20653915405273 16.6476001739502 9.294329643249512 17.5598087310791 8.165230751037598 17.5598087310791 Z M 16.3304615020752 17.5598087310791 C 15.2013635635376 17.5598087310791 14.28915405273438 16.6476001739502 14.28915405273438 15.51850128173828 C 14.28915405273438 14.38940238952637 15.2013635635376 13.47719287872314 16.3304615020752 13.47719287872314 C 17.45956039428711 13.47719287872314 18.37177085876465 14.38940238952637 18.37177085876465 15.51850128173828 C 18.37177085876465 16.6476001739502 17.45956039428711 17.5598087310791 16.3304615020752 17.5598087310791 Z M 24.49569320678711 17.5598087310791 C 23.3665943145752 17.5598087310791 22.45438575744629 16.6476001739502 22.45438575744629 15.51850128173828 C 22.45438575744629 14.38940238952637 23.3665943145752 13.47719287872314 24.49569320678711 13.47719287872314 C 25.62479209899902 13.47719287872314 26.53700065612793 14.38940238952637 26.53700065612793 15.51850128173828 C 26.53700065612793 16.6476001739502 25.62479209899902 17.5598087310791 24.49569320678711 17.5598087310791 Z" fill="#fee095" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                              allowDrawingOutsideViewBox: true,
                              fit: BoxFit.fitHeight,
                              height: mediaQuery(context, "h", 28.58),
                            ),
                          ),
                          Container(
                            margin: EdgeInsets.only(
                                left: mediaQuery(context, "w", 133.2)),
                            child: Text(
                              "btn_mainBitrixAddComment".tr.toString(),
                              style: TextStyle(
                                fontFamily: 'SukhumvitSet-Bold',
                                fontSize: mediaQuery(context, "h", 28),
                                color: const Color(0xfffcf6e4),
                                letterSpacing: mediaQuery(
                                    context, "h", 1.4000000000000001),
                                fontWeight: FontWeight.w700,
                                decoration: TextDecoration.underline,
                              ),
                              textAlign: TextAlign.left,
                            ),
                          ),
                        ],
                      ),
                    )
                  : Container(),
              Container(
                alignment: Alignment.topRight,
                margin: EdgeInsets.only(right: mediaQuery(context, "w", 46)),
                child: statusShowComment == 1
                    ? GestureDetector(
                        onTap: () {
                          setState(() {
                            statusShowComment = 0;
                            statusShowTextfild = 0;
                          });
                        },
                        child: Text(
                          "btn_mainBitrixHideComment".tr.toString(),
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Medium',
                            fontSize: mediaQuery(context, "h", 26),
                            color: const Color(0xfffcf6e4),
                            letterSpacing: mediaQuery(context, "h", 1.3),
                            fontWeight: FontWeight.w500,
                            decoration: TextDecoration.underline,
                          ),
                          textAlign: TextAlign.right,
                        ),
                      )
                    : GestureDetector(
                        onTap: () {
                          setState(() {
                            statusShowComment = 1;
                          });
                        },
                        child: Text.rich(
                          TextSpan(
                            style: TextStyle(
                              fontFamily: 'SukhumvitSet-Medium',
                              fontSize: mediaQuery(context, "h", 26),
                              color: const Color(0xfffee095),
                              letterSpacing: mediaQuery(context, "h", 1.3),
                              height: 1.9230769230769231,
                            ),
                            children: [
                              TextSpan(
                                text: bitrixController.dataBitrixComment.length
                                        .toString() +
                                    ' ',
                                style: TextStyle(
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              TextSpan(
                                text: "btn_mainBitrixComment".tr.toString(),
                                style: TextStyle(
                                  color: const Color(0xfffcf6e4),
                                  fontWeight: FontWeight.w500,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ],
                          ),
                          textAlign: TextAlign.right,
                        ),
                      ),
              ),
            ],
          ),
        );
      }
      if (statusShowComment == 1) {
        list.add(
          Stack(
            children: [
              positionEdit == i
                  ? Container(
                      alignment: Alignment.center,
                      child: Stack(
                        children: [
                          Container(
                            width: mediaQuery(context, "w", 736),
                            height: mediaQuery(context, "h", 431),
                            margin: EdgeInsets.only(
                                top: mediaQuery(context, "h", 33)),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(
                                  mediaQuery(context, "h", 20)),
                              color: const Color(0xff1f1c2f),
                              boxShadow: [
                                BoxShadow(
                                  color: const Color(0x4d000000),
                                  offset: Offset(0, 2),
                                  blurRadius: 20,
                                ),
                              ],
                            ),
                            child: Stack(
                              children: [
                                Container(
                                  margin: EdgeInsets.only(
                                      top: mediaQuery(context, "h", 8),
                                      left: mediaQuery(context, "w", 135)),
                                  child: Text(
                                    bitrixController.dataBitrixComment[i]
                                                ["AUTHOR_NAME"]
                                            .toString() +
                                        " " +
                                        convertDateTimeFromDB(
                                            convertTimeToGMT(bitrixController
                                                .dataBitrixComment[i]
                                                    ["POST_DATE"]
                                                .toString()),
                                            "dd-MM-yyyy") +
                                        " " +
                                        convertDateTime(
                                            convertTimeToGMT(bitrixController
                                                .dataBitrixComment[i]
                                                    ["POST_DATE"]
                                                .toString()),
                                            "THM"),
                                    style: TextStyle(
                                      fontFamily: 'SukhumvitSet-Text',
                                      fontSize: mediaQuery(context, "h", 22),
                                      color: const Color(0xffa596ff),
                                      letterSpacing:
                                          mediaQuery(context, "h", 1.1),
                                    ),
                                    textAlign: TextAlign.left,
                                  ),
                                ),
                                Container(
                                  padding: EdgeInsets.only(
                                      top: mediaQuery(context, "h", 31),
                                      bottom: mediaQuery(context, "h", 31),
                                      left: mediaQuery(context, "w", 42),
                                      right: mediaQuery(context, "w", 42)),
                                  child: Stack(
                                    children: [
                                      new TextField(
                                        cursorColor: Colors.black,
                                        maxLines: 6,
                                        controller: _commentEditController,
                                        style: TextStyle(
                                          fontFamily: 'SukhumvitSet-Text',
                                          fontSize:
                                              mediaQuery(context, "h", 28),
                                          color: const Color(0xb2fcf6e4),
                                          letterSpacing: mediaQuery(
                                              context, "h", 1.4000000000000001),
                                        ),
                                        decoration: new InputDecoration(
                                          hintText: "btn_mainBitrixTyping"
                                              .tr
                                              .toString(),
                                          hintStyle: TextStyle(
                                            fontFamily: 'SukhumvitSet-Text',
                                            fontSize:
                                                mediaQuery(context, "h", 28),
                                            color: const Color(0xb2fcf6e4),
                                            letterSpacing: mediaQuery(context,
                                                "h", 1.4000000000000001),
                                          ),
                                          enabledBorder:
                                              const UnderlineInputBorder(
                                            borderSide: const BorderSide(
                                                color: Colors.transparent),
                                          ),
                                          focusedBorder:
                                              const UnderlineInputBorder(
                                            borderSide: const BorderSide(
                                                color: Colors.transparent),
                                          ),
                                        ),
                                      ),
                                      Container(
                                        child: Stack(
                                          children: [
                                            Container(
                                              alignment: Alignment.bottomLeft,
                                              margin: EdgeInsets.only(
                                                  bottom: mediaQuery(
                                                      context, "h", 10)),
                                              child: GestureDetector(
                                                onTap: () {
                                                  // selectimage(context);
                                                },
                                                child: SvgPicture.string(
                                                  '<svg viewBox="88.0 992.0 30.0 30.0" ><path transform="translate(85.0, 989.0)" d="M 6 9 L 3 9 L 3 30 C 3 31.64999961853027 4.349999904632568 33 6 33 L 27 33 L 27 30 L 6 30 L 6 9 Z M 30 3 L 12 3 C 10.35000038146973 3 9 4.349999904632568 9 6 L 9 24 C 9 25.64999961853027 10.35000038146973 27 12 27 L 30 27 C 31.64999961853027 27 33 25.64999961853027 33 24 L 33 6 C 33 4.349999904632568 31.64999961853027 3 30 3 Z M 28.5 16.5 L 22.5 16.5 L 22.5 22.5 L 19.5 22.5 L 19.5 16.5 L 13.5 16.5 L 13.5 13.5 L 19.5 13.5 L 19.5 7.5 L 22.5 7.5 L 22.5 13.5 L 28.5 13.5 L 28.5 16.5 Z" fill="#fee095" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                                                  allowDrawingOutsideViewBox:
                                                      true,
                                                  fit: BoxFit.fitHeight,
                                                  height: mediaQuery(
                                                      context, "h", 30),
                                                ),
                                              ),
                                            ),
                                            Container(
                                              alignment: Alignment.bottomLeft,
                                              margin: EdgeInsets.only(
                                                  left: mediaQuery(
                                                      context, "w", 45)),
                                              child: GestureDetector(
                                                onTap: () {
                                                  // selectimage(context);
                                                },
                                                child: Text(
                                                  "btn_mainBitrixUploadImg"
                                                      .tr
                                                      .toString(),
                                                  style: TextStyle(
                                                    fontFamily:
                                                        'SukhumvitSet-Text',
                                                    fontSize: mediaQuery(
                                                        context, "h", 28),
                                                    color:
                                                        const Color(0xb2a596ff),
                                                    letterSpacing: mediaQuery(
                                                        context,
                                                        "h",
                                                        1.4000000000000001),
                                                  ),
                                                  textAlign: TextAlign.left,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                              ],
                            ),
                          ),
                          Container(
                            alignment: Alignment.bottomRight,
                            width: mediaQuery(context, "w", 736),
                            height: mediaQuery(context, "h", 556),
                            margin: EdgeInsets.only(
                                top: mediaQuery(context, "h", 33)),
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  positionEdit = -1;
                                  _commentEditController.text = "";
                                  // dataLinkImage = [];
                                  // dataLinkVideo = [];
                                  // dataLinkImageEdit = [];
                                  // dataLinkVideoEdit = [];
                                });
                              },
                              child: Container(
                                alignment: Alignment.center,
                                width: mediaQuery(context, "w", 155),
                                height: mediaQuery(context, "h", 90),
                                margin: EdgeInsets.only(
                                    right: mediaQuery(context, "w", 180)),
                                child: Text(
                                  'Cancel',
                                  style: TextStyle(
                                    fontFamily: 'SukhumvitSet-Bold',
                                    fontSize: mediaQuery(context, "h", 28),
                                    color: const Color(0xfffee095),
                                    letterSpacing: mediaQuery(
                                        context, "h", 1.4000000000000001),
                                    fontWeight: FontWeight.w700,
                                    decoration: TextDecoration.underline,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          ),
                          Container(
                            alignment: Alignment.bottomRight,
                            width: mediaQuery(context, "w", 736),
                            height: mediaQuery(context, "h", 556),
                            margin: EdgeInsets.only(
                                top: mediaQuery(context, "h", 33)),
                            child: GestureDetector(
                              onTap: () {
                                bitrixController.processCommentUpdate(
                                    context,
                                    idIssue,
                                    bitrixController.dataBitrixComment[i]["ID"]
                                        .toString(),
                                    _commentEditController.text);
                              },
                              child: Container(
                                alignment: Alignment.center,
                                width: mediaQuery(context, "w", 155),
                                height: mediaQuery(context, "h", 90),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(
                                      mediaQuery(context, "h", 20)),
                                  color: const Color(0xff5f569b),
                                  boxShadow: [
                                    BoxShadow(
                                      color: const Color(0x4d000000),
                                      offset: Offset(0, 2),
                                      blurRadius: 20,
                                    ),
                                  ],
                                ),
                                child: Text(
                                  'Update',
                                  style: TextStyle(
                                    fontFamily: 'SukhumvitSet-Bold',
                                    fontSize: mediaQuery(context, "h", 28),
                                    color: const Color(0xfffcf6e4),
                                    letterSpacing: mediaQuery(
                                        context, "h", 1.4000000000000001),
                                    fontWeight: FontWeight.w700,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          )
                        ],
                      ),
                    )
                  : Container(
                      alignment: Alignment.center,
                      child: Container(
                        width: mediaQuery(context, "w", 736),
                        margin:
                            EdgeInsets.only(top: mediaQuery(context, "h", 33)),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(
                              mediaQuery(context, "h", 20)),
                          color: const Color(0xff1f1c2f),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0x4d000000),
                              offset: Offset(0, 2),
                              blurRadius: 20,
                            ),
                          ],
                        ),
                        child: Stack(
                          children: [
                            Container(
                              margin: EdgeInsets.only(
                                  top: mediaQuery(context, "h", 8),
                                  left: mediaQuery(context, "w", 135)),
                              child: Text(
                                bitrixController.dataBitrixComment[i]
                                            ["AUTHOR_NAME"]
                                        .toString() +
                                    " " +
                                    convertDateTimeFromDB(
                                        convertTimeToGMT(bitrixController
                                            .dataBitrixComment[i]["POST_DATE"]
                                            .toString()),
                                        "dd-MM-yyyy") +
                                    " " +
                                    convertDateTime(
                                        convertTimeToGMT(bitrixController
                                            .dataBitrixComment[i]["POST_DATE"]
                                            .toString()),
                                        "THM"),
                                style: TextStyle(
                                  fontFamily: 'SukhumvitSet-Text',
                                  fontSize: mediaQuery(context, "h", 22),
                                  color: const Color(0xffa596ff),
                                  letterSpacing: mediaQuery(context, "h", 1.1),
                                ),
                                textAlign: TextAlign.left,
                              ),
                            ),
                            Container(
                              margin: EdgeInsets.only(
                                  top: mediaQuery(context, "h", 63),
                                  left: mediaQuery(context, "w", 40),
                                  right: mediaQuery(context, "w", 76),
                                  bottom: mediaQuery(context, "w", 34)),
                              child: Column(
                                children: [
                                  SelectableText(
                                    bitrixController.dataBitrixComment[i]
                                            ["POST_MESSAGE"]
                                        .toString(),
                                    style: TextStyle(
                                      fontFamily: 'SukhumvitSet-Text',
                                      fontSize: mediaQuery(context, "h", 28),
                                      color: const Color(0xfffcf6e4),
                                      letterSpacing: mediaQuery(
                                          context, "h", 1.4000000000000001),
                                    ),
                                    textAlign: TextAlign.left,
                                  ),
                                  Column(
                                    children: ListLinkMyWidgets(bitrixController
                                        .dataBitrixComment[i]["POST_MESSAGE"]
                                        .toString()),
                                  ),
                                  bitrixController.processShowImg(
                                          bitrixController.dataBitrixComment[i]
                                                  ["POST_MESSAGE"]
                                              .toString())
                                      ? Container(
                                          height: mediaQuery(context, "h", 135),
                                          margin: EdgeInsets.only(
                                              top:
                                                  mediaQuery(context, "h", 23)),
                                          child: ListView(
                                            scrollDirection: Axis.horizontal,
                                            children: <Widget>[
                                              Row(
                                                children: ListIMGcomment(
                                                    bitrixController
                                                        .dataBitrixComment[i]
                                                            ["POST_MESSAGE"]
                                                        .toString()),
                                              ),
                                            ],
                                          ),
                                        )
                                      : Container(),
                                  idBitrix ==
                                          bitrixController.dataBitrixComment[i]
                                                  ["AUTHOR_ID"]
                                              .toString()
                                      ? GestureDetector(
                                          onTap: () {
                                            setState(() {
                                              statusShowTextfild = 0;
                                              positionEdit = i;
                                              bitrixController
                                                  .processDataForUpdateComment(
                                                      bitrixController
                                                          .dataBitrixComment[i]
                                                              ["POST_MESSAGE"]
                                                          .toString(),
                                                      _commentEditController
                                                          .text);
                                            });
                                          },
                                          child: Stack(
                                            children: [
                                              Container(
                                                alignment: Alignment.topRight,
                                                margin: EdgeInsets.only(
                                                    top: mediaQuery(
                                                        context, "h", 28),
                                                    bottom: mediaQuery(
                                                        context, "h", 10),
                                                    right: mediaQuery(
                                                        context, "w", 70)),
                                                child: SvgPicture.asset(
                                                  "assets/images/pkg/bitrix/Icon-edit.svg",
                                                  fit: BoxFit.fitHeight,
                                                  height: mediaQuery(
                                                      context, "h", 23.78),
                                                ),
                                              ),
                                              Container(
                                                alignment: Alignment.topRight,
                                                margin: EdgeInsets.only(
                                                  top: mediaQuery(
                                                      context, "h", 20),
                                                  bottom: mediaQuery(
                                                      context, "h", 10),
                                                ),
                                                child: Text(
                                                  "btn_mainBitrixEdit"
                                                      .tr
                                                      .toString(),
                                                  style: TextStyle(
                                                    fontFamily:
                                                        'SukhumvitSet-Medium',
                                                    fontSize: mediaQuery(
                                                        context, "h", 26),
                                                    color:
                                                        const Color(0xffa596ff),
                                                    letterSpacing: mediaQuery(
                                                        context, "h", 1.3),
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                  textAlign: TextAlign.left,
                                                ),
                                              ),
                                            ],
                                          ),
                                        )
                                      : Container(),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
              Container(
                padding: EdgeInsets.only(left: mediaQuery(context, "w", 86)),
                child: Container(
                  alignment: Alignment.center,
                  width: mediaQuery(context, "h", 75),
                  height: mediaQuery(context, "h", 75),
                  decoration: BoxDecoration(
                    borderRadius:
                        BorderRadius.all(Radius.elliptical(9999.0, 9999.0)),
                    color: const Color(0xfffcf6e4),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0x4d000000),
                        offset: Offset(0, 3),
                        blurRadius: 2,
                      ),
                    ],
                  ),
                  child: Container(
                    width: mediaQuery(context, "w", 71),
                    height: mediaQuery(context, "h", 71),
                    decoration: BoxDecoration(
                      borderRadius:
                          BorderRadius.all(Radius.elliptical(94.5, 94.5)),
                      image: DecorationImage(
                        image: (profileController.responseMember!.id == "" ||
                                idBitrix !=
                                    bitrixController.dataBitrixComment[i]
                                            ["AUTHOR_ID"]
                                        .toString())
                            ? NetworkImage(
                                "https://s3-ap-southeast-1.amazonaws.com/storage-pkg/icon/icon1548914956default.png")
                            : NetworkImage(
                                "http://devdev.prachakij.com/PPP7/uploads/emp/${profileController.responseMember!.id}.jpg"),
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      }
      list.add(
        Container(
          height: mediaQuery(context, "h", 19),
        ),
      );
    }
    list.add(
      statusShowTextfild == 1
          ? Container(
              alignment: Alignment.center,
              child: Container(
                width: mediaQuery(context, "w", 736),
                height: mediaQuery(context, "h", 431),
                padding: EdgeInsets.only(
                    top: mediaQuery(context, "h", 31),
                    bottom: mediaQuery(context, "h", 31),
                    left: mediaQuery(context, "w", 42),
                    right: mediaQuery(context, "w", 42)),
                decoration: BoxDecoration(
                  borderRadius:
                      BorderRadius.circular(mediaQuery(context, "h", 20)),
                  color: const Color(0xff1f1c2f),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0x4d000000),
                      offset: Offset(0, 2),
                      blurRadius: 20,
                    ),
                  ],
                ),
                child: Stack(
                  children: [
                    new TextField(
                      cursorColor: Colors.black,
                      maxLines: 6,
                      controller: _commentController,
                      style: TextStyle(
                        fontFamily: 'SukhumvitSet-Text',
                        fontSize: mediaQuery(context, "h", 28),
                        color: const Color(0xb2fcf6e4),
                        letterSpacing:
                            mediaQuery(context, "h", 1.4000000000000001),
                      ),
                      decoration: new InputDecoration(
                        hintText: "btn_mainBitrixTyping".tr.toString(),
                        hintStyle: TextStyle(
                          fontFamily: 'SukhumvitSet-Text',
                          fontSize: mediaQuery(context, "h", 28),
                          color: const Color(0xb2fcf6e4),
                          letterSpacing:
                              mediaQuery(context, "h", 1.4000000000000001),
                        ),
                        enabledBorder: const UnderlineInputBorder(
                          borderSide:
                              const BorderSide(color: Colors.transparent),
                        ),
                        focusedBorder: const UnderlineInputBorder(
                          borderSide:
                              const BorderSide(color: Colors.transparent),
                        ),
                      ),
                    ),
                    // Container(
                    //   alignment: Alignment.bottomLeft,
                    //   child: Container(
                    //     height: mediaQuery(context, "h", 60),
                    //     margin: EdgeInsets.only(
                    //         right: mediaQuery(context, "w", 230)),
                    //     child: ListView(
                    //       scrollDirection: Axis.horizontal,
                    //       children: <Widget>[
                    //         Row(
                    //           children: ListIMG(),
                    //         ),
                    //       ],
                    //     ),
                    //   ),
                    // ),
                    Container(
                      // margin: EdgeInsets.only(
                      //     left: mediaQuery(
                      //         context, "w", processMarginUploadImg())),
                      child: Stack(
                        children: [
                          Container(
                            alignment: Alignment.bottomLeft,
                            margin: EdgeInsets.only(
                                bottom: mediaQuery(context, "h", 10)),
                            child: GestureDetector(
                              onTap: () {
                                // selectimage(context);
                              },
                              child: SvgPicture.string(
                                '<svg viewBox="88.0 992.0 30.0 30.0" ><path transform="translate(85.0, 989.0)" d="M 6 9 L 3 9 L 3 30 C 3 31.64999961853027 4.349999904632568 33 6 33 L 27 33 L 27 30 L 6 30 L 6 9 Z M 30 3 L 12 3 C 10.35000038146973 3 9 4.349999904632568 9 6 L 9 24 C 9 25.64999961853027 10.35000038146973 27 12 27 L 30 27 C 31.64999961853027 27 33 25.64999961853027 33 24 L 33 6 C 33 4.349999904632568 31.64999961853027 3 30 3 Z M 28.5 16.5 L 22.5 16.5 L 22.5 22.5 L 19.5 22.5 L 19.5 16.5 L 13.5 16.5 L 13.5 13.5 L 19.5 13.5 L 19.5 7.5 L 22.5 7.5 L 22.5 13.5 L 28.5 13.5 L 28.5 16.5 Z" fill="#fee095" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                                allowDrawingOutsideViewBox: true,
                                fit: BoxFit.fitHeight,
                                height: mediaQuery(context, "h", 30),
                              ),
                            ),
                          ),
                          Container(
                            alignment: Alignment.bottomLeft,
                            margin: EdgeInsets.only(
                                left: mediaQuery(context, "w", 45)),
                            child: GestureDetector(
                              onTap: () {
                                // selectimage(context);
                              },
                              child: Text(
                                "btn_mainBitrixUploadImg".tr.toString(),
                                style: TextStyle(
                                  fontFamily: 'SukhumvitSet-Text',
                                  fontSize: mediaQuery(context, "h", 28),
                                  color: const Color(0xb2a596ff),
                                  letterSpacing: mediaQuery(
                                      context, "h", 1.4000000000000001),
                                ),
                                textAlign: TextAlign.left,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            )
          : statusShowComment == 1
              ? GestureDetector(
                  onTap: () {
                    setState(() {
                      statusShowComment = 1;
                      statusShowTextfild = 1;
                      positionEdit = -1;
                    });
                  },
                  child: Stack(
                    children: [
                      Container(
                        margin: EdgeInsets.only(
                            left: mediaQuery(context, "w", 86),
                            top: mediaQuery(context, "h", 10)),
                        child: SvgPicture.string(
                          '<svg viewBox="86.0 977.0 32.7 28.6" ><path transform="translate(86.0, 974.75)" d="M 16.3304615020752 2.25 C 7.310433864593506 2.25 0 8.188930511474609 0 15.51850128173828 C 0 18.68252754211426 1.365124583244324 21.57863426208496 3.636079549789429 23.8559684753418 C 2.838693618774414 27.07102584838867 0.1722353398799896 29.93523788452148 0.1403399109840393 29.96713256835938 C 0 30.11385345458984 -0.03827453032135963 30.33073997497559 0.04465360194444656 30.5221118927002 C 0.1275817304849625 30.7134838104248 0.3061961829662323 30.82831001281738 0.5103269219398499 30.82831001281738 C 4.739661693572998 30.82831001281738 7.910067558288574 28.79976081848145 9.479323387145996 27.54945755004883 C 11.56528377532959 28.33408546447754 13.88089275360107 28.78700065612793 16.3304615020752 28.78700065612793 C 25.35049247741699 28.78700065612793 32.66092300415039 22.84807205200195 32.66092300415039 15.51850128173828 C 32.66092300415039 8.188929557800293 25.35049247741699 2.25 16.3304615020752 2.25 Z M 8.165230751037598 17.5598087310791 C 7.0361328125 17.5598087310791 6.123923301696777 16.6476001739502 6.123923301696777 15.51850128173828 C 6.123923301696777 14.38940238952637 7.0361328125 13.47719287872314 8.165230751037598 13.47719287872314 C 9.294329643249512 13.47719287872314 10.20653915405273 14.38940238952637 10.20653915405273 15.51850128173828 C 10.20653915405273 16.6476001739502 9.294329643249512 17.5598087310791 8.165230751037598 17.5598087310791 Z M 16.3304615020752 17.5598087310791 C 15.2013635635376 17.5598087310791 14.28915405273438 16.6476001739502 14.28915405273438 15.51850128173828 C 14.28915405273438 14.38940238952637 15.2013635635376 13.47719287872314 16.3304615020752 13.47719287872314 C 17.45956039428711 13.47719287872314 18.37177085876465 14.38940238952637 18.37177085876465 15.51850128173828 C 18.37177085876465 16.6476001739502 17.45956039428711 17.5598087310791 16.3304615020752 17.5598087310791 Z M 24.49569320678711 17.5598087310791 C 23.3665943145752 17.5598087310791 22.45438575744629 16.6476001739502 22.45438575744629 15.51850128173828 C 22.45438575744629 14.38940238952637 23.3665943145752 13.47719287872314 24.49569320678711 13.47719287872314 C 25.62479209899902 13.47719287872314 26.53700065612793 14.38940238952637 26.53700065612793 15.51850128173828 C 26.53700065612793 16.6476001739502 25.62479209899902 17.5598087310791 24.49569320678711 17.5598087310791 Z" fill="#fee095" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                          allowDrawingOutsideViewBox: true,
                          fit: BoxFit.fitHeight,
                          height: mediaQuery(context, "h", 28.58),
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.only(
                            left: mediaQuery(context, "w", 133.2)),
                        child: Text(
                          "btn_mainBitrixAddComment".tr.toString(),
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Bold',
                            fontSize: mediaQuery(context, "h", 28),
                            color: const Color(0xfffcf6e4),
                            letterSpacing:
                                mediaQuery(context, "h", 1.4000000000000001),
                            fontWeight: FontWeight.w700,
                            decoration: TextDecoration.underline,
                          ),
                          textAlign: TextAlign.left,
                        ),
                      ),
                    ],
                  ),
                )
              : Container(),
    );
    list.add(
      statusShowTextfild == 1
          ? Container(
              height: mediaQuery(context, "h", 35),
            )
          : Container(),
    );
    list.add(
      statusShowTextfild == 1
          ? Stack(
              children: [
                Container(
                  alignment: Alignment.topRight,
                  child: GestureDetector(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: Container(
                      alignment: Alignment.center,
                      width: mediaQuery(context, "w", 155),
                      height: mediaQuery(context, "h", 90),
                      margin:
                          EdgeInsets.only(right: mediaQuery(context, "w", 201)),
                      child: Text(
                        'Cancel',
                        style: TextStyle(
                          fontFamily: 'SukhumvitSet-Bold',
                          fontSize: mediaQuery(context, "h", 28),
                          color: const Color(0xfffee095),
                          letterSpacing:
                              mediaQuery(context, "h", 1.4000000000000001),
                          fontWeight: FontWeight.w700,
                          decoration: TextDecoration.underline,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ),
                Container(
                  alignment: Alignment.topRight,
                  child: GestureDetector(
                    onTap: () {
                      // processCommentInsert();
                    },
                    child: Container(
                      alignment: Alignment.center,
                      width: mediaQuery(context, "w", 155),
                      height: mediaQuery(context, "h", 90),
                      margin:
                          EdgeInsets.only(right: mediaQuery(context, "w", 46)),
                      decoration: BoxDecoration(
                        borderRadius:
                            BorderRadius.circular(mediaQuery(context, "h", 20)),
                        color: const Color(0xff5f569b),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0x4d000000),
                            offset: Offset(0, 2),
                            blurRadius: 20,
                          ),
                        ],
                      ),
                      child: Text(
                        'Add',
                        style: TextStyle(
                          fontFamily: 'SukhumvitSet-Bold',
                          fontSize: mediaQuery(context, "h", 28),
                          color: const Color(0xfffcf6e4),
                          letterSpacing:
                              mediaQuery(context, "h", 1.4000000000000001),
                          fontWeight: FontWeight.w700,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ),
              ],
            )
          : Container(),
    );
    list.add(
      Container(
        height: mediaQuery(context, "h", 100),
      ),
    );

    return list;
  }

  List<Widget> ListIMGcomment(data) {
    var splitImgUse = [];
    var splitVdoUse = [];
    var splitcomment = data.split("&APP&TOSPLIT&");
    if (splitcomment.length > 1) {
      var splitImgVdo = splitcomment[1].split("|||");
      var splitImgCutFont = splitImgVdo[0].substring(1, splitImgVdo[0].length);
      var splitImgCutBack =
          splitImgCutFont.substring(0, splitImgCutFont.length - 1);
      splitImgUse = splitImgCutBack.split(", ");

      var splitVdoCutFont = splitImgVdo[1].substring(1, splitImgVdo[1].length);
      var splitVdoCutBack =
          splitVdoCutFont.substring(0, splitVdoCutFont.length - 1);
      splitVdoUse = splitVdoCutBack.split(", ");
    }

    List<Widget> list = [];
    if (splitImgUse.toString() != "[]") {
      for (var i = 0; i < splitImgUse.length.toInt(); i++) {
        list.add(
          GestureDetector(
            onTap: () {
              Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => showImage(splitImgUse[i])));
            },
            child: Container(
              width: mediaQuery(context, "w", 139),
              height: mediaQuery(context, "h", 135),
              decoration: BoxDecoration(
                borderRadius:
                    BorderRadius.circular(mediaQuery(context, "h", 20)),
                image: DecorationImage(
                  image: NetworkImage(splitImgUse[i]),
                  fit: BoxFit.cover,
                ),
                border: Border.all(width: 1.0, color: const Color(0xff707070)),
              ),
            ),
          ),
        );
        list.add(Container(
          height: mediaQuery(context, "h", 10),
          width: mediaQuery(context, "h", 25),
        ));
      }
    }

    if (splitVdoUse.toString() != "[]") {
      for (var i = 0; i < splitVdoUse.length.toInt(); i++) {
        list.add(
          GestureDetector(
            onTap: () {
              // Navigator.push(
              //     context,
              //     MaterialPageRoute(
              //         builder: (context) => playVideo(splitVdoUse[i])));
            },
            child: Container(
              width: mediaQuery(context, "w", 139),
              height: mediaQuery(context, "h", 135),
              decoration: BoxDecoration(
                borderRadius:
                    BorderRadius.circular(mediaQuery(context, "h", 20)),
                image: DecorationImage(
                  image: AssetImage("assets/images/pkg/bitrix/play.jpeg"),
                  fit: BoxFit.cover,
                ),
                border: Border.all(width: 1.0, color: const Color(0xff707070)),
              ),
            ),
          ),
        );
        list.add(Container(
          height: mediaQuery(context, "h", 10),
          width: mediaQuery(context, "h", 25),
        ));
      }
    }
    return list;
  }

  List<Widget> ListLinkMyWidgets(data) {
    List<Widget> list = [];
    List link = [];
// *********************************** split link from web **********************************
    var splitDataGetFromWeb = data.split("URL=");
    for (var i = 0; i < splitDataGetFromWeb.length; i++) {
      if (i != 0) {
        link.add(splitDataGetFromWeb[i].split("]https://")[0]);
      }
    }
// *********************************** split link from web **********************************
// *********************************** split link from app **********************************
    var splitCheckFromWeb = data.split("URL=");
    var splitCheckFromlinkImg = data.split("linkImg:");
    var splitCheckFromlinkVideo = data.split("linkVideo:");
    var splitDataGetFromApp = [];
    if (splitCheckFromWeb.length > 1) {
      splitDataGetFromApp = splitCheckFromWeb[0].split("http");
    } else if (splitCheckFromlinkImg.length > 1) {
      splitDataGetFromApp = splitCheckFromlinkImg[0].split("http");
    } else if (splitCheckFromlinkVideo.length > 1) {
      splitDataGetFromApp = splitCheckFromlinkVideo[0].split("http");
    } else {
      splitDataGetFromApp = data
          .split("********** สำหรับทีมพัฒนาเท่านั้น **********")[0]
          .split("http");
    }
    for (var i = 0; i < splitDataGetFromApp.length; i++) {
      if (i != 0) {
        link.add("http" + splitDataGetFromApp[i].split("\n")[0]);
      }
    }
// *********************************** split link from app **********************************
    if (link.length > 0) {
      list.add(Container(
        width: mediaQuery(context, "w", 676),
        child: Text(
          "\n\n****** link ที่ผู้ตอบประเด็นแนบมา ******",
          style: TextStyle(
            fontFamily: 'Sukhumvit Set',
            fontSize: mediaQuery(context, "h", 28),
            color: const Color(0xfffcf6e4),
            letterSpacing: mediaQuery(context, "h", 1.4000000000000001),
          ),
        ),
      ));
      for (var i = 0; i < link.length; i++) {
        list.add(
          Container(
            width: mediaQuery(context, "w", 676),
            child: GestureDetector(
              onTap: () {
                _launchURL(link[i].toString());
              },
              child: Text.rich(
                TextSpan(
                  style: TextStyle(
                    fontFamily: 'Sukhumvit Set',
                    fontSize: mediaQuery(context, "h", 28),
                    color: const Color(0xfffcf6e4),
                    letterSpacing: mediaQuery(context, "h", 1.4000000000000001),
                  ),
                  children: [
                    TextSpan(
                      text: 'Link : ',
                    ),
                    TextSpan(
                      text: link[i].toString(),
                      style: TextStyle(
                        color: const Color(0xffa596ff),
                      ),
                    ),
                  ],
                ),
                textAlign: TextAlign.left,
              ),
            ),
          ),
        );
        list.add(Container(
          height: mediaQuery(context, "h", 10),
        ));
      }
    } else {
      list.add(Container());
    }

    return list;
  }

  int statusShoweDetailFinish = 1;
  Widget showeDetailFinish() {
    if (statusShoweDetailFinish == 1) {
      return ClipRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 1.97, sigmaY: 1.97),
          child: Container(
            alignment: Alignment.topCenter,
            decoration: BoxDecoration(
              color: const Color(0x00bebebe),
            ),
            child: Container(
              width: mediaQuery(context, "w", 520),
              height: mediaQuery(context, "h", 720),
              margin: EdgeInsets.only(top: mediaQuery(context, "h", 300)),
              decoration: BoxDecoration(
                borderRadius:
                    BorderRadius.circular(mediaQuery(context, "h", 80)),
                color: const Color(0xfffcf6e4),
              ),
              child: Stack(
                children: [
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 82)),
                    child: SvgPicture.string(
                      '<svg viewBox="163.0 0.0 50.0 50.0" ><path transform="translate(160.43, -1.93)" d="M 27.57070350646973 6.61553430557251 C 22.14501762390137 6.61553430557251 17.04413795471191 8.728425025939941 13.20761585235596 12.56494903564453 C 9.371090888977051 16.40147399902344 7.25820255279541 21.50235176086426 7.25820255279541 26.92803764343262 C 7.25820255279541 32.35371780395508 9.371092796325684 37.4546012878418 13.20761680603027 41.2911262512207 C 17.04413986206055 45.12765121459961 22.14501953125 47.24053955078125 27.57070350646973 47.24053955078125 C 32.99638748168945 47.24053955078125 38.09726333618164 45.12765121459961 41.93379211425781 41.2911262512207 C 45.77031326293945 37.4546012878418 47.88320541381836 32.35372161865234 47.88320541381836 26.92803764343262 C 47.88320541381836 21.50235176086426 45.77031326293945 16.40147399902344 41.933837890625 12.56494903564453 C 38.09736251831055 8.728425025939941 32.99638748168945 6.61553430557251 27.57070350646973 6.61553430557251 Z M 27.57070350646973 1.928033590316772 C 41.37783050537109 1.928033590316772 52.57070159912109 13.12090492248535 52.57070159912109 26.92803764343262 C 52.57070159912109 40.73516845703125 41.37783050537109 51.92803955078125 27.57070350646973 51.92803955078125 C 13.76357555389404 51.92803955078125 2.570701122283936 40.73516845703125 2.570701122283936 26.92803955078125 C 2.570701122283936 13.12090969085693 13.76357173919678 1.928035974502563 27.57070350646973 1.928035974502563 Z M 24.44570159912109 36.30303955078125 L 30.69570350646973 36.30303955078125 L 30.69570350646973 42.55304336547852 L 24.44570159912109 42.55304336547852 L 24.44570159912109 36.30303955078125 Z M 24.44570159912109 11.30303478240967 L 30.69570350646973 11.30303478240967 L 30.69570350646973 30.05303573608398 L 24.44570159912109 30.05303573608398 L 24.44570159912109 11.30303478240967 Z" fill="#934d0f" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                      allowDrawingOutsideViewBox: true,
                      fit: BoxFit.fitHeight,
                      height: mediaQuery(context, "h", 50),
                    ),
                  ),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 155)),
                    child: Text(
                      "ui_mainBitrixShowDetailHead".tr.toString(),
                      style: TextStyle(
                        fontFamily: 'SukhumvitSet-Bold',
                        fontSize: mediaQuery(context, "h", 33),
                        color: const Color(0xff302c49),
                        letterSpacing: mediaQuery(context, "h", 1.32),
                        fontWeight: FontWeight.w700,
                        height: 0.9393939393939394,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 212)),
                    child: Text.rich(
                      TextSpan(
                        style: TextStyle(
                          fontFamily: 'SukhumvitSet-Medium',
                          fontSize: mediaQuery(context, "h", 28),
                          color: const Color(0xff302c49),
                          letterSpacing: mediaQuery(context, "h", 1.12),
                          height: 1.5357142857142858,
                        ),
                        children: [
                          TextSpan(
                            text: "ui_mainBitrixShowDetai".tr.toString(),
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          TextSpan(
                            text: 'Completed',
                            style: TextStyle(
                              color: const Color(0xff934d0f),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          TextSpan(
                            text: "ui_mainBitrixShowDetai2".tr.toString(),
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          TextSpan(
                            text: 'Return',
                            style: TextStyle(
                              color: const Color(0xff934d0f),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          TextSpan(
                            text: "ui_mainBitrixShowDetai3".tr.toString(),
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          TextSpan(
                            text: 'Comment \n',
                            style: TextStyle(
                              color: const Color(0xff934d0f),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          TextSpan(
                            text: "ui_mainBitrixShowDetai4".tr.toString(),
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      textAlign: TextAlign.left,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 559)),
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          statusShoweDetailFinish = 0;
                        });
                      },
                      child: Container(
                        alignment: Alignment.center,
                        width: mediaQuery(context, "w", 250),
                        height: mediaQuery(context, "h", 90),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(
                              mediaQuery(context, "h", 30)),
                          color: const Color(0xff7f420c),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0x1a000000),
                              offset: Offset(0, 1),
                              blurRadius: 2,
                            ),
                          ],
                        ),
                        child: Text(
                          "btn_mainBitrixShowDetaiContinue".tr.toString(),
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Bold',
                            fontSize: mediaQuery(context, "h", 30),
                            color: const Color(0xfffcf6e4),
                            letterSpacing: mediaQuery(context, "h", 1.2),
                            fontWeight: FontWeight.w700,
                            shadows: [
                              Shadow(
                                color: const Color(0x26000000),
                                offset: Offset(0, 1),
                                blurRadius: 1,
                              )
                            ],
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    } else {
      return Container();
    }
  }

  int statusAlertSuccessReturn = 0;
  alertSuccessReturn() {
    if (statusAlertSuccessReturn == 1) {
      return ClipRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 1.97, sigmaY: 1.97),
          child: Container(
            alignment: Alignment.topCenter,
            decoration: BoxDecoration(
              color: const Color(0x00bebebe),
            ),
            child: Container(
              width: mediaQuery(context, "w", 520),
              height: mediaQuery(context, "h", 595),
              margin: EdgeInsets.only(top: mediaQuery(context, "h", 300)),
              decoration: BoxDecoration(
                borderRadius:
                    BorderRadius.circular(mediaQuery(context, "h", 80)),
                color: const Color(0xfffcf6e4),
              ),
              child: Stack(
                children: [
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 82)),
                    child: SvgPicture.string(
                      '<svg viewBox="163.0 0.0 50.0 50.0" ><path transform="translate(160.43, -1.93)" d="M 27.57070350646973 6.61553430557251 C 22.14501762390137 6.61553430557251 17.04413795471191 8.728425025939941 13.20761585235596 12.56494903564453 C 9.371090888977051 16.40147399902344 7.25820255279541 21.50235176086426 7.25820255279541 26.92803764343262 C 7.25820255279541 32.35371780395508 9.371092796325684 37.4546012878418 13.20761680603027 41.2911262512207 C 17.04413986206055 45.12765121459961 22.14501953125 47.24053955078125 27.57070350646973 47.24053955078125 C 32.99638748168945 47.24053955078125 38.09726333618164 45.12765121459961 41.93379211425781 41.2911262512207 C 45.77031326293945 37.4546012878418 47.88320541381836 32.35372161865234 47.88320541381836 26.92803764343262 C 47.88320541381836 21.50235176086426 45.77031326293945 16.40147399902344 41.933837890625 12.56494903564453 C 38.09736251831055 8.728425025939941 32.99638748168945 6.61553430557251 27.57070350646973 6.61553430557251 Z M 27.57070350646973 1.928033590316772 C 41.37783050537109 1.928033590316772 52.57070159912109 13.12090492248535 52.57070159912109 26.92803764343262 C 52.57070159912109 40.73516845703125 41.37783050537109 51.92803955078125 27.57070350646973 51.92803955078125 C 13.76357555389404 51.92803955078125 2.570701122283936 40.73516845703125 2.570701122283936 26.92803955078125 C 2.570701122283936 13.12090969085693 13.76357173919678 1.928035974502563 27.57070350646973 1.928035974502563 Z M 24.44570159912109 36.30303955078125 L 30.69570350646973 36.30303955078125 L 30.69570350646973 42.55304336547852 L 24.44570159912109 42.55304336547852 L 24.44570159912109 36.30303955078125 Z M 24.44570159912109 11.30303478240967 L 30.69570350646973 11.30303478240967 L 30.69570350646973 30.05303573608398 L 24.44570159912109 30.05303573608398 L 24.44570159912109 11.30303478240967 Z" fill="#934d0f" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                      allowDrawingOutsideViewBox: true,
                      fit: BoxFit.fitHeight,
                      height: mediaQuery(context, "h", 50),
                    ),
                  ),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 155)),
                    child: Text(
                      "ui_mainBitrixReturnSuccess".tr.toString(),
                      style: TextStyle(
                        fontFamily: 'SukhumvitSet-Bold',
                        fontSize: mediaQuery(context, "h", 33),
                        color: const Color(0xff302c49),
                        letterSpacing: mediaQuery(context, "h", 1.32),
                        fontWeight: FontWeight.w700,
                        height: 0.9393939393939394,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 212)),
                    child: Text.rich(
                      TextSpan(
                        style: TextStyle(
                          fontFamily: 'SukhumvitSet-Medium',
                          fontSize: mediaQuery(context, "h", 28),
                          color: const Color(0xff302c49),
                          letterSpacing: mediaQuery(context, "h", 1.12),
                          height: 1.5357142857142858,
                        ),
                        children: [
                          TextSpan(
                            text: "ui_mainBitrixReturnSuccessDetail"
                                .tr
                                .toString(),
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          TextSpan(
                            text: responsiblename.toString() +
                                " " +
                                responsiblelastname.toString(),
                            style: TextStyle(
                              color: const Color(0xff934d0f),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          TextSpan(
                            text: "ui_mainBitrixReturnSuccessDetail2"
                                .tr
                                .toString(),
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 426)),
                    child: GestureDetector(
                      onTap: () {
                        Navigator.of(context).pop();
                        Navigator.pushReplacement(
                            context,
                            MaterialPageRoute(
                                builder: (context) => WorkIssuesScreen()));
                      },
                      child: Container(
                        alignment: Alignment.center,
                        width: mediaQuery(context, "w", 250),
                        height: mediaQuery(context, "h", 90),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(
                              mediaQuery(context, "h", 30)),
                          color: const Color(0xff7f420c),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0x1a000000),
                              offset: Offset(0, 1),
                              blurRadius: 2,
                            ),
                          ],
                        ),
                        child: Text(
                          'ตกลง',
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Bold',
                            fontSize: mediaQuery(context, "h", 30),
                            color: const Color(0xfffcf6e4),
                            letterSpacing: mediaQuery(context, "h", 1.2),
                            fontWeight: FontWeight.w700,
                            shadows: [
                              Shadow(
                                color: const Color(0x26000000),
                                offset: Offset(0, 1),
                                blurRadius: 1,
                              )
                            ],
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    } else {
      return Container();
    }
  }

  int statusAlertSuccessCloseIssue = 0;
  alertSuccessCloseIssue() {
    if (statusAlertSuccessCloseIssue == 1) {
      return ClipRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 1.97, sigmaY: 1.97),
          child: Container(
            alignment: Alignment.topCenter,
            decoration: BoxDecoration(
              color: const Color(0x00bebebe),
            ),
            child: Container(
              width: mediaQuery(context, "w", 520),
              height: mediaQuery(context, "h", 595),
              margin: EdgeInsets.only(top: mediaQuery(context, "h", 300)),
              decoration: BoxDecoration(
                borderRadius:
                    BorderRadius.circular(mediaQuery(context, "h", 80)),
                color: const Color(0xfffcf6e4),
              ),
              child: Stack(
                children: [
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 82)),
                    child: SvgPicture.string(
                      '<svg viewBox="163.0 0.0 50.0 50.0" ><path transform="translate(160.43, -1.93)" d="M 27.57070350646973 6.61553430557251 C 22.14501762390137 6.61553430557251 17.04413795471191 8.728425025939941 13.20761585235596 12.56494903564453 C 9.371090888977051 16.40147399902344 7.25820255279541 21.50235176086426 7.25820255279541 26.92803764343262 C 7.25820255279541 32.35371780395508 9.371092796325684 37.4546012878418 13.20761680603027 41.2911262512207 C 17.04413986206055 45.12765121459961 22.14501953125 47.24053955078125 27.57070350646973 47.24053955078125 C 32.99638748168945 47.24053955078125 38.09726333618164 45.12765121459961 41.93379211425781 41.2911262512207 C 45.77031326293945 37.4546012878418 47.88320541381836 32.35372161865234 47.88320541381836 26.92803764343262 C 47.88320541381836 21.50235176086426 45.77031326293945 16.40147399902344 41.933837890625 12.56494903564453 C 38.09736251831055 8.728425025939941 32.99638748168945 6.61553430557251 27.57070350646973 6.61553430557251 Z M 27.57070350646973 1.928033590316772 C 41.37783050537109 1.928033590316772 52.57070159912109 13.12090492248535 52.57070159912109 26.92803764343262 C 52.57070159912109 40.73516845703125 41.37783050537109 51.92803955078125 27.57070350646973 51.92803955078125 C 13.76357555389404 51.92803955078125 2.570701122283936 40.73516845703125 2.570701122283936 26.92803955078125 C 2.570701122283936 13.12090969085693 13.76357173919678 1.928035974502563 27.57070350646973 1.928035974502563 Z M 24.44570159912109 36.30303955078125 L 30.69570350646973 36.30303955078125 L 30.69570350646973 42.55304336547852 L 24.44570159912109 42.55304336547852 L 24.44570159912109 36.30303955078125 Z M 24.44570159912109 11.30303478240967 L 30.69570350646973 11.30303478240967 L 30.69570350646973 30.05303573608398 L 24.44570159912109 30.05303573608398 L 24.44570159912109 11.30303478240967 Z" fill="#934d0f" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                      allowDrawingOutsideViewBox: true,
                      fit: BoxFit.fitHeight,
                      height: mediaQuery(context, "h", 50),
                    ),
                  ),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 155)),
                    child: Text(
                      "ui_mainBitrixCloseIssueSuccess".tr.toString(),
                      style: TextStyle(
                        fontFamily: 'SukhumvitSet-Bold',
                        fontSize: mediaQuery(context, "h", 33),
                        color: const Color(0xff302c49),
                        letterSpacing: mediaQuery(context, "h", 1.32),
                        fontWeight: FontWeight.w700,
                        height: 0.9393939393939394,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 212)),
                    child: Text.rich(
                      TextSpan(
                        style: TextStyle(
                          fontFamily: 'SukhumvitSet-Medium',
                          fontSize: mediaQuery(context, "h", 28),
                          color: const Color(0xff302c49),
                          letterSpacing: mediaQuery(context, "h", 1.12),
                          height: 1.5357142857142858,
                        ),
                        children: [
                          TextSpan(
                            text: "ui_mainBitrixCloseIssueSuccessDetail"
                                .tr
                                .toString(),
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          TextSpan(
                            text: responsiblename.toString() +
                                " " +
                                responsiblelastname.toString(),
                            style: TextStyle(
                              color: const Color(0xff934d0f),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          TextSpan(
                            text: "ui_mainBitrixCloseIssueSuccessDetail2"
                                .tr
                                .toString(),
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 426)),
                    child: GestureDetector(
                      onTap: () {
                        Navigator.of(context).pop();
                        Navigator.pushReplacement(
                            context,
                            MaterialPageRoute(
                                builder: (context) => WorkIssuesScreen()));
                      },
                      child: Container(
                        alignment: Alignment.center,
                        width: mediaQuery(context, "w", 250),
                        height: mediaQuery(context, "h", 90),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(
                              mediaQuery(context, "h", 30)),
                          color: const Color(0xff7f420c),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0x1a000000),
                              offset: Offset(0, 1),
                              blurRadius: 2,
                            ),
                          ],
                        ),
                        child: Text(
                          'ตกลง',
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Bold',
                            fontSize: mediaQuery(context, "h", 30),
                            color: const Color(0xfffcf6e4),
                            letterSpacing: mediaQuery(context, "h", 1.2),
                            fontWeight: FontWeight.w700,
                            shadows: [
                              Shadow(
                                color: const Color(0x26000000),
                                offset: Offset(0, 1),
                                blurRadius: 1,
                              )
                            ],
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    } else {
      return Container();
    }
  }

  _launchURL(url) async {
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not launch $url';
    }
  }
}
