import 'dart:ui';

import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:mapp_ms24/controllers/internal/BitrixController/bitrixController.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/controllers/internal/Tacontroller/Tacontroller.dart';
import 'package:mapp_ms24/controllers/internal/WorkIssuse/WorkIssuseController.dart';
import 'package:mapp_ms24/controllers/utils/loader.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';

import '../../../../../controllers/utils/widget.dart';
import '../../../Widget/Widget.dart';

class bitrixActionScreen extends StatefulWidget {
  final idIssue;
  final idBitrix;
  final createdate;
  final createbyname;
  final createbylastname;
  final groupname;
  final title;
  final deadline;
  final realsStatus;

  const bitrixActionScreen(
      this.idIssue,
      this.idBitrix,
      this.createdate,
      this.createbyname,
      this.createbylastname,
      this.groupname,
      this.title,
      this.deadline,
      this.realsStatus);

  @override
  State<bitrixActionScreen> createState() => _bitrixActionScreenState(
      this.idIssue,
      this.idBitrix,
      this.createdate,
      this.createbyname,
      this.createbylastname,
      this.groupname,
      this.title,
      this.deadline,
      this.realsStatus);
}

class _bitrixActionScreenState extends State<bitrixActionScreen> {
  final idIssue;
  final idBitrix;
  final createdate;
  final createbyname;
  final createbylastname;
  final groupname;
  final title;
  var deadline;
  var realsStatus;

  _bitrixActionScreenState(
      this.idIssue,
      this.idBitrix,
      this.createdate,
      this.createbyname,
      this.createbylastname,
      this.groupname,
      this.title,
      this.deadline,
      this.realsStatus);

  BitrixController bitrixControlle = Get.find<BitrixController>();
  TextEditingController _commentEditController = TextEditingController();
  TextEditingController _commentController = TextEditingController();
  ProfileController profileCtr = Get.find<ProfileController>();
  Tacontroller taCtr = Get.find<Tacontroller>();

  int statusShowComment = 1;
  int statusShowTextfild = 0;
  int statusChangeComment = 0;
  int positionEdit = -1;
  int overDateComment = 0;
  bool checking = true;
  var statusDeadLine = 0;

  void initState() {
    super.initState();
    initializePage();
  }

  void initializePage() async {
    await checkDeadLine();
    await bitrixControlle.loadBitrixComment(context, idIssue);
    checking = false;
    setState(() {});
  }

  checkDeadLine() async {
    await taCtr.getTime(context);
    var diffDeadLine = (DateTime.parse(convertDateTime(deadline, "dd-MM-YYYY"))
        .difference(
            DateTime.parse(convertDateTime(taCtr.datetime, "dd-MM-YYYY2")))
        .inDays);

    if (diffDeadLine < 0) {
      statusDeadLine = 1;
    }
  }

  checkOverDateComment() async {
    var datecheck = "";
    if (bitrixControlle.dataBitrixComment.length == 0) {
      datecheck = createdate;
    } else {
      datecheck = bitrixControlle
              .dataBitrixComment[bitrixControlle.dataBitrixComment.length - 1]
          ["POST_DATE"];
    }
    var diffDate = DateTime.parse(
            convertDateTime(taCtr.datetime, "dd-MM-YYYY2"))
        .difference(DateTime.parse(convertDateTime(datecheck, "dd-MM-YYYY")))
        .inDays;
    overDateComment = diffDate;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        height: Get.height,
        width: Get.width,
        child: Stack(
          children: [
            Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Color(0xFF1F1C2F), // เพิ่มสีแรกที่นี่
                    Color(0xFF0D0C14), // เพิ่มสีที่สองที่นี่
                  ],
                  begin: Alignment.topCenter, // เริ่มต้น Gradient ที่มุมบนซ้าย
                  end: Alignment.bottomCenter,
                ),
              ),
            ),
            buildAppBar(context, 'ui_workissues'.tr),
            buildChosseScreen(),
            Container(
              margin: EdgeInsets.only(top: 330.h),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: ListMyWidgets(),
                ),
              ),
            ),
            alertFinish(),
            alertFinishError(),
            alertSuccessFinish(),
            checking
                ? Container(
                    width: MediaQuery.of(context).size.width,
                    height: MediaQuery.of(context).size.height,
                    color: Colors.black12,
                    child: Center(
                      child: SizedBox(
                        width: 60.w,
                        child: const LoadingIndicator(
                          indicatorType: Indicator.lineSpinFadeLoader,
                          colors: [Colors.white],
                        ),
                      ),
                    ))
                : Container(),
          ],
        ),
      ),
    );
  }

  Widget buildChosseScreen() {
    return Container(
      height: Get.height,
      width: Get.width,
      padding: EdgeInsets.only(top: 130.h),
      child: Column(
        children: [
          Container(
            width: Get.width,
            child: Row(
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 25),
                  child: SizedBox(
                    height: 27.h,
                    width: 27.w,
                    child: Image.asset('assets/ADD/Stick-note.png'),
                  ),
                ),
                Container(
                  width: 250.w,
                  child: new RichText(
                    text: new TextSpan(
                      children: <TextSpan>[
                        new TextSpan(
                          text: createdate +
                              " " +
                              "Created by " +
                              createbyname.toString() +
                              " " +
                              createbylastname.toString(),
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Text',
                            fontSize: mediaQuery(context, "h", 28),
                            color: const Color(0xfffee095),
                            letterSpacing:
                                mediaQuery(context, "h", 1.4000000000000001),
                          ),
                        ),
                        new TextSpan(
                          text: " : " + groupname.toString() + ".",
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Text',
                            fontSize: mediaQuery(context, "h", 30),
                            color: const Color(0xffa596ff),
                            letterSpacing:
                                mediaQuery(context, "h", 1.4000000000000001),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          Container(
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.only(top: 10, left: 60),
            child: AppWidget.normalText(context, title.toString(), 13.sp,
                const Color(0xffffffff), FontWeight.w400),
          ),
          Padding(
            padding: const EdgeInsets.only(top: 60, left: 20, right: 10),
            child: Row(
              children: [
                SizedBox(
                  height: 20.h,
                  width: 20.w,
                  child: Image.asset('assets/ADD/calendar.png'),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 10),
                  child: AppWidget.normalText(
                      context,
                      "Deadline" +
                          " " +
                          convertDateTime(deadline, "dd-MM-YYYY2").toString(),
                      12.sp,
                      statusDeadLine == 1
                          ? const Color(0xffff3e3e)
                          : const Color(0xfffee095),
                      FontWeight.w400),
                ),
                bottomStart(),
                bottomFinish(),
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 20.h),
            child: SvgPicture.string(
              '<svg viewBox="0.0 636.0 828.0 1.0" ><path transform="translate(0.0, 636.0)" d="M 0 0 L 828 0" fill="none" fill-opacity="0.1" stroke="#fcf6e4" stroke-width="3" stroke-opacity="0.1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
              allowDrawingOutsideViewBox: true,
              color: const Color(0xfffcf6e4),
            ),
          ),
        ],
      ),
    );
  }

  Widget bottomStart() {
    return Container(
      alignment: Alignment.topLeft,
      padding: EdgeInsets.only(left: 30),
      child: (realsStatus.toString() == "1" || realsStatus.toString() == "2")
          ? GestureDetector(
              onTap: () {
                bitrixControlle.updateBitrixStatus(
                    context, 3, idIssue, realsStatus);
              },
              child: Container(
                alignment: Alignment.center,
                width: 60.w,
                height: 50.h,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8.sp),
                  color: const Color(0xff5f569b),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0x4d000000),
                      offset: Offset(0, 2),
                      blurRadius: 20,
                    ),
                  ],
                ),
                child: AppWidget.normalText(context, "Start", 14.sp,
                    const Color(0xfffee095), FontWeight.w700),
              ),
            )
          : realsStatus.toString() == "4"
              ? Container(
                  alignment: Alignment.center,
                  width: 77.w,
                  height: 45.h,
                  child: AppWidget.normalText(context, 'รอปิดประเด็น', 14.sp,
                      const Color(0xfffee095), FontWeight.w500),
                )
              : GestureDetector(
                  onTap: () {
                    bitrixControlle.updateBitrixStatus(
                        context, 2, idIssue, realsStatus);
                  },
                  child: Container(
                    alignment: Alignment.center,
                    width: 80.w,
                    height: 45.h,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.sp),
                      color: const Color(0x335f569b),
                      border: Border.all(
                          width: 1.w, color: const Color(0x33fee095)),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0x0f000000),
                          offset: Offset(0, 2),
                          blurRadius: 20,
                        ),
                      ],
                    ),
                    child: AppWidget.normalText(context, 'in Progress', 14.sp,
                        const Color(0xfffee095), FontWeight.w700),
                  ),
                ),
    );
  }

  Widget bottomFinish() {
    return Container(
      alignment: Alignment.topLeft,
      padding: EdgeInsets.only(left: 10, right: 10),
      child: realsStatus.toString() == "4"
          ? Container(
              alignment: Alignment.center,
              width: 80.w,
              height: 45.h,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.sp),
                color: const Color(0xfffee095),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0x4d000000),
                    offset: Offset(0, 2),
                    blurRadius: 20,
                  ),
                ],
              ),
              child: AppWidget.normalText(context, "Completed", 14.sp,
                  const Color(0xfffee095), FontWeight.w700),
            )
          : GestureDetector(
              onTap: () {},
              child: Container(
                alignment: Alignment.center,
                width: 80.w,
                height: 45.h,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8.sp),
                  color: const Color(0xfffee095),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0x4d000000),
                      offset: Offset(0, 2),
                      blurRadius: 20,
                    ),
                  ],
                ),
                child: AppWidget.normalText(context, "Finish", 14.sp,
                    const Color(0xff5f569b), FontWeight.w700),
              ),
            ),
    );
  }

//
  List<Widget> ListMyWidgets() {
    List<Widget> list = [];
    for (var i = 0; i < bitrixControlle.dataBitrixComment.length.toInt(); i++) {
      if (i == 0) {
        list.add(
          Stack(
            children: [
              statusShowComment == 0
                  ? GestureDetector(
                      onTap: () {
                        setState(() {
                          statusShowComment = 1;
                          statusShowTextfild = 1;
                        });
                      },
                      child: Stack(
                        children: [
                          Container(
                            margin: EdgeInsets.only(
                                left: mediaQuery(context, "w", 86),
                                top: mediaQuery(context, "h", 13)),
                            child: SvgPicture.string(
                              '<svg viewBox="86.0 977.0 32.7 28.6" ><path transform="translate(86.0, 974.75)" d="M 16.3304615020752 2.25 C 7.310433864593506 2.25 0 8.188930511474609 0 15.51850128173828 C 0 18.68252754211426 1.365124583244324 21.57863426208496 3.636079549789429 23.8559684753418 C 2.838693618774414 27.07102584838867 0.1722353398799896 29.93523788452148 0.1403399109840393 29.96713256835938 C 0 30.11385345458984 -0.03827453032135963 30.33073997497559 0.04465360194444656 30.5221118927002 C 0.1275817304849625 30.7134838104248 0.3061961829662323 30.82831001281738 0.5103269219398499 30.82831001281738 C 4.739661693572998 30.82831001281738 7.910067558288574 28.79976081848145 9.479323387145996 27.54945755004883 C 11.56528377532959 28.33408546447754 13.88089275360107 28.78700065612793 16.3304615020752 28.78700065612793 C 25.35049247741699 28.78700065612793 32.66092300415039 22.84807205200195 32.66092300415039 15.51850128173828 C 32.66092300415039 8.188929557800293 25.35049247741699 2.25 16.3304615020752 2.25 Z M 8.165230751037598 17.5598087310791 C 7.0361328125 17.5598087310791 6.123923301696777 16.6476001739502 6.123923301696777 15.51850128173828 C 6.123923301696777 14.38940238952637 7.0361328125 13.47719287872314 8.165230751037598 13.47719287872314 C 9.294329643249512 13.47719287872314 10.20653915405273 14.38940238952637 10.20653915405273 15.51850128173828 C 10.20653915405273 16.6476001739502 9.294329643249512 17.5598087310791 8.165230751037598 17.5598087310791 Z M 16.3304615020752 17.5598087310791 C 15.2013635635376 17.5598087310791 14.28915405273438 16.6476001739502 14.28915405273438 15.51850128173828 C 14.28915405273438 14.38940238952637 15.2013635635376 13.47719287872314 16.3304615020752 13.47719287872314 C 17.45956039428711 13.47719287872314 18.37177085876465 14.38940238952637 18.37177085876465 15.51850128173828 C 18.37177085876465 16.6476001739502 17.45956039428711 17.5598087310791 16.3304615020752 17.5598087310791 Z M 24.49569320678711 17.5598087310791 C 23.3665943145752 17.5598087310791 22.45438575744629 16.6476001739502 22.45438575744629 15.51850128173828 C 22.45438575744629 14.38940238952637 23.3665943145752 13.47719287872314 24.49569320678711 13.47719287872314 C 25.62479209899902 13.47719287872314 26.53700065612793 14.38940238952637 26.53700065612793 15.51850128173828 C 26.53700065612793 16.6476001739502 25.62479209899902 17.5598087310791 24.49569320678711 17.5598087310791 Z" fill="#fee095" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                              allowDrawingOutsideViewBox: true,
                              fit: BoxFit.fitHeight,
                              height: mediaQuery(context, "h", 33),
                            ),
                          ),
                          Container(
                            margin: EdgeInsets.only(
                                left: mediaQuery(context, "w", 133.2),
                                top: mediaQuery(context, "h", 10)),
                            child: Text(
                              "btn_mainBitrixAddComment".tr.toString(),
                              style: TextStyle(
                                fontFamily: 'SukhumvitSet-Bold',
                                fontSize: mediaQuery(context, "h", 28),
                                color: const Color(0xfffcf6e4),
                                letterSpacing: mediaQuery(
                                    context, "h", 1.4000000000000001),
                                fontWeight: FontWeight.w700,
                                decoration: TextDecoration.underline,
                              ),
                              textAlign: TextAlign.left,
                            ),
                          ),
                        ],
                      ),
                    )
                  : Container(),
              Container(
                alignment: Alignment.topRight,
                margin: EdgeInsets.only(right: mediaQuery(context, "w", 46)),
                child: statusShowComment == 1
                    ? GestureDetector(
                        onTap: () {
                          setState(() {
                            statusShowComment = 0;
                            statusShowTextfild = 0;
                          });
                        },
                        child: Text(
                          "btn_mainBitrixHideComment".tr.toString(),
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Bold',
                            fontSize: mediaQuery(context, "h", 28),
                            color: const Color(0xfffcf6e4),
                            letterSpacing: mediaQuery(context, "h", 1.3),
                            fontWeight: FontWeight.w500,
                            decoration: TextDecoration.underline,
                          ),
                          textAlign: TextAlign.right,
                        ),
                      )
                    : GestureDetector(
                        onTap: () {
                          setState(() {
                            statusShowComment = 1;
                          });
                        },
                        child: Text.rich(
                          TextSpan(
                            style: TextStyle(
                              fontFamily: 'SukhumvitSet-Medium',
                              fontSize: mediaQuery(context, "h", 26),
                              color: const Color(0xfffee095),
                              letterSpacing: mediaQuery(context, "h", 1.3),
                              height: 1.9230769230769231,
                            ),
                            children: [
                              TextSpan(
                                text: bitrixControlle.dataBitrixComment.length
                                        .toString() +
                                    ' ',
                                style: TextStyle(
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              TextSpan(
                                text: "btn_mainBitrixComment".tr.toString(),
                                style: TextStyle(
                                  fontFamily: 'SukhumvitSet-Bold',
                                  color: const Color(0xfffcf6e4),
                                  fontSize: mediaQuery(context, "h", 28),
                                  fontWeight: FontWeight.w500,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ],
                          ),
                          textAlign: TextAlign.right,
                        ),
                      ),
              ),
            ],
          ),
        );
      }
      if (statusShowComment == 1) {
        list.add(
          Stack(
            children: [
              positionEdit == i
                  ? Container(
                      alignment: Alignment.center,
                      child: Stack(
                        children: [
                          Container(
                            width: mediaQuery(context, "w", 736),
                            height: mediaQuery(context, "h", 431),
                            margin: EdgeInsets.only(
                                top: mediaQuery(context, "h", 33)),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(
                                  mediaQuery(context, "h", 20)),
                              color: const Color(0xff1f1c2f),
                              boxShadow: [
                                BoxShadow(
                                  color: const Color(0x4d000000),
                                  offset: Offset(0, 2),
                                  blurRadius: 20,
                                ),
                              ],
                            ),
                            child: Stack(
                              children: [
                                Container(
                                  margin: EdgeInsets.only(
                                      top: mediaQuery(context, "h", 8),
                                      left: mediaQuery(context, "w", 135)),
                                  child: Text(
                                    bitrixControlle.dataBitrixComment[i]
                                                ["AUTHOR_NAME"]
                                            .toString() +
                                        " " +
                                        convertDateTimeFromDB(
                                            convertTimeToGMT(bitrixControlle
                                                .dataBitrixComment[i]
                                                    ["POST_DATE"]
                                                .toString()),
                                            "dd-MM-yyyy") +
                                        " " +
                                        convertDateTimeFromDB(
                                            convertTimeToGMT(bitrixControlle
                                                .dataBitrixComment[i]
                                                    ["POST_DATE"]
                                                .toString()),
                                            "THM"),
                                    style: TextStyle(
                                      fontFamily: 'SukhumvitSet-Text',
                                      fontSize: mediaQuery(context, "h", 22),
                                      color: const Color(0xffa596ff),
                                      letterSpacing:
                                          mediaQuery(context, "h", 1.1),
                                    ),
                                    textAlign: TextAlign.left,
                                  ),
                                ),
                                Container(
                                  padding: EdgeInsets.only(
                                      top: mediaQuery(context, "h", 31),
                                      bottom: mediaQuery(context, "h", 31),
                                      left: mediaQuery(context, "w", 42),
                                      right: mediaQuery(context, "w", 42)),
                                  child: Stack(
                                    children: [
                                      new TextField(
                                        cursorColor: Colors.black,
                                        onChanged: (text) {
                                          setState(() {
                                            statusChangeComment = 1;
                                          });
                                        },
                                        maxLines: 6,
                                        controller: _commentEditController,
                                        style: TextStyle(
                                          fontFamily: 'SukhumvitSet-Text',
                                          fontSize:
                                              mediaQuery(context, "h", 28),
                                          color: const Color(0xb2fcf6e4),
                                          letterSpacing: mediaQuery(
                                              context, "h", 1.4000000000000001),
                                        ),
                                        decoration: new InputDecoration(
                                          hintText: "btn_mainBitrixTyping"
                                              .tr
                                              .toString(),
                                          hintStyle: TextStyle(
                                            fontFamily: 'SukhumvitSet-Text',
                                            fontSize:
                                                mediaQuery(context, "h", 28),
                                            color: const Color(0xb2fcf6e4),
                                            letterSpacing: mediaQuery(context,
                                                "h", 1.4000000000000001),
                                          ),
                                          enabledBorder:
                                              const UnderlineInputBorder(
                                            borderSide: const BorderSide(
                                                color: Colors.transparent),
                                          ),
                                          focusedBorder:
                                              const UnderlineInputBorder(
                                            borderSide: const BorderSide(
                                                color: Colors.transparent),
                                          ),
                                        ),
                                      ),
                                      Container(
                                        alignment: Alignment.bottomLeft,
                                        child: Container(
                                          height: mediaQuery(context, "h", 60),
                                          margin: EdgeInsets.only(
                                              right: mediaQuery(
                                                  context, "w", 230)),
                                          child: ListView(
                                            scrollDirection: Axis.horizontal,
                                            children: <Widget>[
                                              // Row(
                                              //   children: ListIMGEdit(),
                                              // ),
                                            ],
                                          ),
                                        ),
                                      ),
                                      Container(
                                        // margin: EdgeInsets.only(
                                        // left: mediaQuery(context, "w",
                                        //     processMarginUploadImgEdit())),
                                        // margin: EdgeInsets.only(left: mediaQuery(context, "w", dataLinkImage.length == 0 ? 0:dataLinkAll.length == 1 ? 215:dataLinkAll.length >= 2 ? 435:435)),
                                        child: Stack(
                                          children: [
                                            Container(
                                              alignment: Alignment.bottomLeft,
                                              margin: EdgeInsets.only(
                                                  bottom: mediaQuery(
                                                      context, "h", 10)),
                                              child: GestureDetector(
                                                onTap: () {
                                                  // selectimage(context);
                                                },
                                                child: SvgPicture.string(
                                                  '<svg viewBox="88.0 992.0 30.0 30.0" ><path transform="translate(85.0, 989.0)" d="M 6 9 L 3 9 L 3 30 C 3 31.64999961853027 4.349999904632568 33 6 33 L 27 33 L 27 30 L 6 30 L 6 9 Z M 30 3 L 12 3 C 10.35000038146973 3 9 4.349999904632568 9 6 L 9 24 C 9 25.64999961853027 10.35000038146973 27 12 27 L 30 27 C 31.64999961853027 27 33 25.64999961853027 33 24 L 33 6 C 33 4.349999904632568 31.64999961853027 3 30 3 Z M 28.5 16.5 L 22.5 16.5 L 22.5 22.5 L 19.5 22.5 L 19.5 16.5 L 13.5 16.5 L 13.5 13.5 L 19.5 13.5 L 19.5 7.5 L 22.5 7.5 L 22.5 13.5 L 28.5 13.5 L 28.5 16.5 Z" fill="#fee095" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                                                  allowDrawingOutsideViewBox:
                                                      true,
                                                  fit: BoxFit.fitHeight,
                                                  height: mediaQuery(
                                                      context, "h", 30),
                                                ),
                                              ),
                                            ),
                                            Container(
                                              alignment: Alignment.bottomLeft,
                                              margin: EdgeInsets.only(
                                                  left: mediaQuery(
                                                      context, "w", 45)),
                                              child: GestureDetector(
                                                onTap: () {
                                                  // selectimage(context);
                                                },
                                                child: Text(
                                                  "btn_mainBitrixUploadImg"
                                                      .tr
                                                      .toString(),
                                                  style: TextStyle(
                                                    fontFamily:
                                                        'SukhumvitSet-Text',
                                                    fontSize: mediaQuery(
                                                        context, "h", 28),
                                                    color:
                                                        const Color(0xb2a596ff),
                                                    letterSpacing: mediaQuery(
                                                        context,
                                                        "h",
                                                        1.4000000000000001),
                                                  ),
                                                  textAlign: TextAlign.left,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                              ],
                            ),
                          ),
                          Container(
                            alignment: Alignment.bottomRight,
                            width: mediaQuery(context, "w", 736),
                            height: mediaQuery(context, "h", 556),
                            margin: EdgeInsets.only(
                                top: mediaQuery(context, "h", 33)),
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  positionEdit = -1;
                                  _commentEditController.text = "";
                                  // dataLinkImage = [];
                                  // dataLinkVideo = [];
                                  // dataLinkImageEdit = [];
                                  // dataLinkVideoEdit = [];
                                });
                              },
                              child: Container(
                                alignment: Alignment.center,
                                width: mediaQuery(context, "w", 155),
                                height: mediaQuery(context, "h", 90),
                                margin: EdgeInsets.only(
                                    right: mediaQuery(context, "w", 180)),
                                child: Text(
                                  'Cancel',
                                  style: TextStyle(
                                    fontFamily: 'SukhumvitSet-Bold',
                                    fontSize: mediaQuery(context, "h", 28),
                                    color: const Color(0xfffee095),
                                    letterSpacing: mediaQuery(
                                        context, "h", 1.4000000000000001),
                                    fontWeight: FontWeight.w700,
                                    decoration: TextDecoration.underline,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          ),
                          Container(
                            alignment: Alignment.bottomRight,
                            width: mediaQuery(context, "w", 736),
                            height: mediaQuery(context, "h", 556),
                            margin: EdgeInsets.only(
                                top: mediaQuery(context, "h", 33)),
                            child: GestureDetector(
                              onTap: () {
                                bitrixControlle.processCommentUpdate(
                                    context,
                                    bitrixControlle.dataBitrixComment[i]["ID"]
                                        .toString(),
                                    idIssue,
                                    _commentEditController.text);
                              },
                              child: Container(
                                alignment: Alignment.center,
                                width: mediaQuery(context, "w", 155),
                                height: mediaQuery(context, "h", 90),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(
                                      mediaQuery(context, "h", 20)),
                                  color: const Color(0xff5f569b),
                                  boxShadow: [
                                    BoxShadow(
                                      color: const Color(0x4d000000),
                                      offset: Offset(0, 2),
                                      blurRadius: 20,
                                    ),
                                  ],
                                ),
                                child: Text(
                                  'Update',
                                  style: TextStyle(
                                    fontFamily: 'SukhumvitSet-Bold',
                                    fontSize: mediaQuery(context, "h", 28),
                                    color: const Color(0xfffcf6e4),
                                    letterSpacing: mediaQuery(
                                        context, "h", 1.4000000000000001),
                                    fontWeight: FontWeight.w700,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          )
                        ],
                      ),
                    )
                  : Container(
                      alignment: Alignment.center,
                      child: Container(
                        width: mediaQuery(context, "w", 736),
                        margin:
                            EdgeInsets.only(top: mediaQuery(context, "h", 33)),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(
                              mediaQuery(context, "h", 20)),
                          color: const Color(0xff1f1c2f),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0x4d000000),
                              offset: Offset(0, 2),
                              blurRadius: 20,
                            ),
                          ],
                        ),
                        child: Stack(
                          children: [
                            Container(
                              margin: EdgeInsets.only(
                                  top: mediaQuery(context, "h", 8),
                                  left: mediaQuery(context, "w", 135)),
                              child: Text(
                                bitrixControlle.dataBitrixComment[i]
                                            ["AUTHOR_NAME"]
                                        .toString() +
                                    " " +
                                    convertDateTime(
                                        bitrixControlle.dataBitrixComment[i]
                                                ["POST_DATE"]
                                            .toString(),
                                        "dd-MM-yyyy") +
                                    " " +
                                    convertDateTime(
                                        bitrixControlle.dataBitrixComment[i]
                                                ["POST_DATE"]
                                            .toString(),
                                        "THM"),
                                style: TextStyle(
                                  fontFamily: 'SukhumvitSet-Text',
                                  fontSize: mediaQuery(context, "h", 22),
                                  color: const Color(0xffa596ff),
                                  letterSpacing: mediaQuery(context, "h", 1.1),
                                ),
                                textAlign: TextAlign.left,
                              ),
                            ),
                            Container(
                              margin: EdgeInsets.only(
                                  top: mediaQuery(context, "h", 63),
                                  left: mediaQuery(context, "w", 40),
                                  right: mediaQuery(context, "w", 76),
                                  bottom: mediaQuery(context, "w", 34)),
                              child: Column(
                                children: [
                                  SelectableText(
                                    bitrixControlle.dataBitrixComment[i]
                                            ["POST_MESSAGE"]
                                        .toString(),
                                    style: TextStyle(
                                      fontFamily: 'SukhumvitSet-Text',
                                      fontSize: mediaQuery(context, "h", 28),
                                      color: const Color(0xfffcf6e4),
                                      letterSpacing: mediaQuery(
                                          context, "h", 1.4000000000000001),
                                    ),
                                    textAlign: TextAlign.left,
                                  ),
                                  idBitrix ==
                                          bitrixControlle.dataBitrixComment[i]
                                                  ["AUTHOR_ID"]
                                              .toString()
                                      ? GestureDetector(
                                          onTap: () {
                                            // setState(() {
                                            statusShowTextfild = 0;
                                            positionEdit = i;
                                            bitrixControlle
                                                .processDataForUpdateComment(
                                                    bitrixControlle
                                                        .dataBitrixComment[i]
                                                            ["POST_MESSAGE"]
                                                        .toString(),
                                                    _commentController.text);
                                            // });
                                          },
                                          child: Stack(
                                            children: [
                                              Container(
                                                alignment: Alignment.topRight,
                                                margin: EdgeInsets.only(
                                                    top: mediaQuery(
                                                        context, "h", 28),
                                                    bottom: mediaQuery(
                                                        context, "h", 10),
                                                    right: mediaQuery(
                                                        context, "w", 70)),
                                                child: SvgPicture.asset(
                                                  "assets/bitrix/Icon-edit.svg",
                                                  fit: BoxFit.fitHeight,
                                                  height: mediaQuery(
                                                      context, "h", 23.78),
                                                ),
                                              ),
                                              Container(
                                                alignment: Alignment.topRight,
                                                margin: EdgeInsets.only(
                                                  top: mediaQuery(
                                                      context, "h", 20),
                                                  bottom: mediaQuery(
                                                      context, "h", 10),
                                                ),
                                                child: Text(
                                                  "btn_mainBitrixEdit"
                                                      .tr
                                                      .toString(),
                                                  style: TextStyle(
                                                    fontFamily:
                                                        'SukhumvitSet-Medium',
                                                    fontSize: mediaQuery(
                                                        context, "h", 26),
                                                    color:
                                                        const Color(0xffa596ff),
                                                    letterSpacing: mediaQuery(
                                                        context, "h", 1.3),
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                  textAlign: TextAlign.left,
                                                ),
                                              ),
                                            ],
                                          ),
                                        )
                                      : Container(),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
              Container(
                padding: EdgeInsets.only(left: mediaQuery(context, "w", 86)),
                child: Container(
                  alignment: Alignment.center,
                  width: mediaQuery(context, "h", 75),
                  height: mediaQuery(context, "h", 75),
                  decoration: BoxDecoration(
                    borderRadius:
                        BorderRadius.all(Radius.elliptical(9999.0, 9999.0)),
                    color: const Color(0xfffcf6e4),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0x4d000000),
                        offset: Offset(0, 3),
                        blurRadius: 2,
                      ),
                    ],
                  ),
                  child: Container(
                    width: mediaQuery(context, "w", 71),
                    height: mediaQuery(context, "h", 71),
                    decoration: BoxDecoration(
                      borderRadius:
                          BorderRadius.all(Radius.elliptical(94.5, 94.5)),
                      image: DecorationImage(
                        image: (profileCtr.responseMember!.id.toString() ==
                                    "" ||
                                idBitrix !=
                                    bitrixControlle.dataBitrixComment[i]
                                            ["AUTHOR_ID"]
                                        .toString())
                            ? NetworkImage(
                                "https://s3-ap-southeast-1.amazonaws.com/storage-pkg/icon/icon1548914956default.png")
                            : NetworkImage(
                                "http://devdev.prachakij.com/PPP7/uploads/emp/${profileCtr.responseMember!.id.toString()}.jpg"),
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      }
      list.add(
        Container(
          height: mediaQuery(context, "h", 19),
        ),
      );
    }
    list.add(
      statusShowTextfild == 1
          ? Container(
              alignment: Alignment.center,
              child: Container(
                width: 360.w,
                height: 180.h,
                padding: EdgeInsets.only(bottom: 10.h, left: 20.w, right: 20.w),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10.sp),
                  color: const Color(0xff1f1c2f),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0x4d000000),
                      offset: Offset(0, 2),
                      blurRadius: 20,
                    ),
                  ],
                ),
                child: Stack(
                  children: [
                    new TextField(
                      cursorColor: Colors.black,
                      onChanged: (text) {
                        setState(() {
                          statusChangeComment = 1;
                        });
                      },
                      maxLines: 6,
                      controller: _commentController,
                      style: TextStyle(
                        fontFamily: 'SukhumvitSet-Text',
                        fontSize: 14.sp,
                        color: const Color(0xb2fcf6e4),
                        letterSpacing:
                            mediaQuery(context, "h", 1.4000000000000001),
                      ),
                      decoration: new InputDecoration(
                        hintText: "btn_mainBitrixTyping".tr.toString(),
                        hintStyle: TextStyle(
                          fontFamily: 'SukhumvitSet-Text',
                          fontSize: 14.sp,
                          color: const Color(0xb2fcf6e4),
                          letterSpacing:
                              mediaQuery(context, "h", 1.4000000000000001),
                        ),
                        enabledBorder: const UnderlineInputBorder(
                          borderSide:
                              const BorderSide(color: Colors.transparent),
                        ),
                        focusedBorder: const UnderlineInputBorder(
                          borderSide:
                              const BorderSide(color: Colors.transparent),
                        ),
                      ),
                    ),
                    Container(
                      alignment: Alignment.bottomLeft,
                      child: Container(
                        height: 30.h,
                        margin: EdgeInsets.only(right: 115.w),
                        child: ListView(
                          scrollDirection: Axis.horizontal,
                          children: <Widget>[
                            // Row(
                            //   children: ListIMG(),
                            // ),
                          ],
                        ),
                      ),
                    ),
                    Container(
                      // margin: EdgeInsets.only(
                      //     left: mediaQuery(
                      //         context, "w", processMarginUploadImg())),
                      // // margin: EdgeInsets.only(left: mediaQuery(context, "w", dataLinkImage.length == 0 ? 0:dataLinkAll.length == 1 ? 215:dataLinkAll.length >= 2 ? 435:435)),
                      child: Stack(
                        children: [
                          Container(
                            alignment: Alignment.bottomLeft,
                            margin: EdgeInsets.only(bottom: 5.h),
                            child: GestureDetector(
                              onTap: () {
                                // selectimage(context);
                              },
                              child: SvgPicture.string(
                                '<svg viewBox="88.0 992.0 30.0 30.0" ><path transform="translate(85.0, 989.0)" d="M 6 9 L 3 9 L 3 30 C 3 31.64999961853027 4.349999904632568 33 6 33 L 27 33 L 27 30 L 6 30 L 6 9 Z M 30 3 L 12 3 C 10.35000038146973 3 9 4.349999904632568 9 6 L 9 24 C 9 25.64999961853027 10.35000038146973 27 12 27 L 30 27 C 31.64999961853027 27 33 25.64999961853027 33 24 L 33 6 C 33 4.349999904632568 31.64999961853027 3 30 3 Z M 28.5 16.5 L 22.5 16.5 L 22.5 22.5 L 19.5 22.5 L 19.5 16.5 L 13.5 16.5 L 13.5 13.5 L 19.5 13.5 L 19.5 7.5 L 22.5 7.5 L 22.5 13.5 L 28.5 13.5 L 28.5 16.5 Z" fill="#fee095" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                                allowDrawingOutsideViewBox: true,
                                fit: BoxFit.fitHeight,
                                height: 15.h,
                              ),
                            ),
                          ),
                          Container(
                            alignment: Alignment.bottomLeft,
                            margin: EdgeInsets.only(left: 23.w),
                            child: GestureDetector(
                              onTap: () {
                                // getpermissionCamera();
                              },
                              child: AppWidget.normalText(
                                  context,
                                  "btn_mainBitrixUploadImg".tr.toString(),
                                  13.sp,
                                  const Color(0xb2a596ff),
                                  FontWeight.w400),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            )
          : statusShowComment == 1
              ? GestureDetector(
                  onTap: () {
                    setState(() {
                      statusShowComment = 1;
                      statusShowTextfild = 1;
                      positionEdit = -1;
                    });
                  },
                  child: Stack(
                    children: [
                      Container(
                        margin: EdgeInsets.only(left: 23.w, top: 20.h),
                        child: SvgPicture.string(
                          '<svg viewBox="86.0 977.0 32.7 28.6" ><path transform="translate(86.0, 974.75)" d="M 16.3304615020752 2.25 C 7.310433864593506 2.25 0 8.188930511474609 0 15.51850128173828 C 0 18.68252754211426 1.365124583244324 21.57863426208496 3.636079549789429 23.8559684753418 C 2.838693618774414 27.07102584838867 0.1722353398799896 29.93523788452148 0.1403399109840393 29.96713256835938 C 0 30.11385345458984 -0.03827453032135963 30.33073997497559 0.04465360194444656 30.5221118927002 C 0.1275817304849625 30.7134838104248 0.3061961829662323 30.82831001281738 0.5103269219398499 30.82831001281738 C 4.739661693572998 30.82831001281738 7.910067558288574 28.79976081848145 9.479323387145996 27.54945755004883 C 11.56528377532959 28.33408546447754 13.88089275360107 28.78700065612793 16.3304615020752 28.78700065612793 C 25.35049247741699 28.78700065612793 32.66092300415039 22.84807205200195 32.66092300415039 15.51850128173828 C 32.66092300415039 8.188929557800293 25.35049247741699 2.25 16.3304615020752 2.25 Z M 8.165230751037598 17.5598087310791 C 7.0361328125 17.5598087310791 6.123923301696777 16.6476001739502 6.123923301696777 15.51850128173828 C 6.123923301696777 14.38940238952637 7.0361328125 13.47719287872314 8.165230751037598 13.47719287872314 C 9.294329643249512 13.47719287872314 10.20653915405273 14.38940238952637 10.20653915405273 15.51850128173828 C 10.20653915405273 16.6476001739502 9.294329643249512 17.5598087310791 8.165230751037598 17.5598087310791 Z M 16.3304615020752 17.5598087310791 C 15.2013635635376 17.5598087310791 14.28915405273438 16.6476001739502 14.28915405273438 15.51850128173828 C 14.28915405273438 14.38940238952637 15.2013635635376 13.47719287872314 16.3304615020752 13.47719287872314 C 17.45956039428711 13.47719287872314 18.37177085876465 14.38940238952637 18.37177085876465 15.51850128173828 C 18.37177085876465 16.6476001739502 17.45956039428711 17.5598087310791 16.3304615020752 17.5598087310791 Z M 24.49569320678711 17.5598087310791 C 23.3665943145752 17.5598087310791 22.45438575744629 16.6476001739502 22.45438575744629 15.51850128173828 C 22.45438575744629 14.38940238952637 23.3665943145752 13.47719287872314 24.49569320678711 13.47719287872314 C 25.62479209899902 13.47719287872314 26.53700065612793 14.38940238952637 26.53700065612793 15.51850128173828 C 26.53700065612793 16.6476001739502 25.62479209899902 17.5598087310791 24.49569320678711 17.5598087310791 Z" fill="#fee095" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                          allowDrawingOutsideViewBox: true,
                          fit: BoxFit.fitHeight,
                          height: 15.h,
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.only(left: 50.w, top: 20.h),
                        child: AppWidget.normalText(
                            context,
                            "btn_mainBitrixAddComment".tr.toString(),
                            13.sp,
                            const Color(0xffffffff),
                            FontWeight.w700),
                      ),
                    ],
                  ),
                )
              : Container(),
    );
    list.add(
      statusShowTextfild == 1
          ? Container(
              height: mediaQuery(context, "h", 35),
            )
          : Container(),
    );
    list.add(
      statusShowTextfild == 1
          ? Stack(
              children: [
                Container(
                  alignment: Alignment.topRight,
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        statusShowTextfild = 0;
                        _commentController.text = "";
                        // dataLinkImage = [];
                        // dataLinkVideo = [];
                        // dataLinkImageEdit = [];
                        // dataLinkVideoEdit = [];
                      });
                    },
                    child: Container(
                      alignment: Alignment.center,
                      width: 70.w,
                      height: 44.h,
                      margin: EdgeInsets.only(right: 90.w),
                      child: AppWidget.normalText(context, "Cancel", 14.sp,
                          const Color(0xfffee095), FontWeight.w700),
                    ),
                  ),
                ),
                Container(
                  alignment: Alignment.topRight,
                  child: GestureDetector(
                    onTap: () {
                      bitrixControlle.processCommentInsert(
                          context, idIssue, _commentController.text);
                      _commentController.text = "";
                    },
                    child: Container(
                      alignment: Alignment.center,
                      width: 70.w,
                      height: 40.h,
                      margin: EdgeInsets.only(right: 23.w),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10.sp),
                        color: const Color(0xff5f569b),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0x4d000000),
                            offset: Offset(0, 2),
                            blurRadius: 20,
                          ),
                        ],
                      ),
                      child: AppWidget.normalText(context, "Add", 14.sp,
                          const Color(0xfffcf6e4), FontWeight.w700),
                    ),
                  ),
                ),
              ],
            )
          : Container(),
    );
    list.add(
      Container(
        height: mediaQuery(context, "h", 100),
      ),
    );

    return list;
  }

  Widget alertFinish() {
    if (bitrixControlle.statusAlertFinish == 1) {
      return ClipRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 1.97, sigmaY: 1.97),
          child: Container(
            alignment: Alignment.topCenter,
            decoration: BoxDecoration(
              color: const Color(0x00bebebe),
            ),
            child: Container(
              margin: EdgeInsets.only(top: mediaQuery(context, "h", 300)),
              width: mediaQuery(context, "w", 520),
              height: mediaQuery(context, "h", 595),
              decoration: BoxDecoration(
                borderRadius:
                    BorderRadius.circular(mediaQuery(context, "h", 80)),
                color: const Color(0xfffcf6e4),
              ),
              child: Stack(
                children: [
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 82)),
                    child: SvgPicture.string(
                      '<svg viewBox="163.0 0.0 50.0 50.0" ><path transform="translate(160.43, -1.93)" d="M 27.57070350646973 6.61553430557251 C 22.14501762390137 6.61553430557251 17.04413795471191 8.728425025939941 13.20761585235596 12.56494903564453 C 9.371090888977051 16.40147399902344 7.25820255279541 21.50235176086426 7.25820255279541 26.92803764343262 C 7.25820255279541 32.35371780395508 9.371092796325684 37.4546012878418 13.20761680603027 41.2911262512207 C 17.04413986206055 45.12765121459961 22.14501953125 47.24053955078125 27.57070350646973 47.24053955078125 C 32.99638748168945 47.24053955078125 38.09726333618164 45.12765121459961 41.93379211425781 41.2911262512207 C 45.77031326293945 37.4546012878418 47.88320541381836 32.35372161865234 47.88320541381836 26.92803764343262 C 47.88320541381836 21.50235176086426 45.77031326293945 16.40147399902344 41.933837890625 12.56494903564453 C 38.09736251831055 8.728425025939941 32.99638748168945 6.61553430557251 27.57070350646973 6.61553430557251 Z M 27.57070350646973 1.928033590316772 C 41.37783050537109 1.928033590316772 52.57070159912109 13.12090492248535 52.57070159912109 26.92803764343262 C 52.57070159912109 40.73516845703125 41.37783050537109 51.92803955078125 27.57070350646973 51.92803955078125 C 13.76357555389404 51.92803955078125 2.570701122283936 40.73516845703125 2.570701122283936 26.92803955078125 C 2.570701122283936 13.12090969085693 13.76357173919678 1.928035974502563 27.57070350646973 1.928035974502563 Z M 24.44570159912109 36.30303955078125 L 30.69570350646973 36.30303955078125 L 30.69570350646973 42.55304336547852 L 24.44570159912109 42.55304336547852 L 24.44570159912109 36.30303955078125 Z M 24.44570159912109 11.30303478240967 L 30.69570350646973 11.30303478240967 L 30.69570350646973 30.05303573608398 L 24.44570159912109 30.05303573608398 L 24.44570159912109 11.30303478240967 Z" fill="#934d0f" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                      allowDrawingOutsideViewBox: true,
                      fit: BoxFit.fitHeight,
                      height: mediaQuery(context, "h", 50),
                    ),
                  ),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 155)),
                    child: Text(
                      "ui_mainBitrixNotSuccess".tr.toString(),
                      style: TextStyle(
                        fontFamily: 'SukhumvitSet-Bold',
                        fontSize: mediaQuery(context, "h", 33),
                        color: const Color(0xff302c49),
                        letterSpacing: mediaQuery(context, "h", 1.32),
                        fontWeight: FontWeight.w700,
                        height: 0.9393939393939394,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 212)),
                    child: Text.rich(
                      TextSpan(
                        style: TextStyle(
                          fontFamily: 'SukhumvitSet-Medium',
                          fontSize: mediaQuery(context, "h", 28),
                          color: const Color(0xff302c49),
                          letterSpacing: mediaQuery(context, "h", 1.12),
                          height: 1.5357142857142858,
                        ),
                        children: [
                          TextSpan(
                            text: "ui_mainBitrixNotSuccessDetail".tr.toString(),
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          TextSpan(
                            text: 'Start',
                            style: TextStyle(
                              color: const Color(0xff934d0f),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          TextSpan(
                            text:
                                "ui_mainBitrixNotSuccessDetail2".tr.toString(),
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      textAlign: TextAlign.left,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 468)),
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          bitrixControlle.statusAlertFinish = 0;
                        });
                      },
                      child: Container(
                        alignment: Alignment.center,
                        width: mediaQuery(context, "w", 250),
                        height: mediaQuery(context, "h", 90),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(
                              mediaQuery(context, "h", 30)),
                          color: const Color(0xff7f420c),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0x1a000000),
                              offset: Offset(0, 1),
                              blurRadius: 2,
                            ),
                          ],
                        ),
                        child: Text(
                          "btn_mainBitrixNotSuccessok".tr.toString(),
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Bold',
                            fontSize: mediaQuery(context, "h", 30),
                            color: const Color(0xfffcf6e4),
                            letterSpacing: mediaQuery(context, "h", 1.2),
                            fontWeight: FontWeight.w700,
                            shadows: [
                              Shadow(
                                color: const Color(0x26000000),
                                offset: Offset(0, 1),
                                blurRadius: 1,
                              )
                            ],
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    } else {
      return Container();
    }
  }

  Widget alertFinishError() {
    if (bitrixControlle.statusAlertFinishError == 1) {
      return ClipRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 1.97, sigmaY: 1.97),
          child: Container(
            alignment: Alignment.topCenter,
            decoration: BoxDecoration(
              color: const Color(0x00bebebe),
            ),
            child: Container(
              margin: EdgeInsets.only(top: mediaQuery(context, "h", 300)),
              width: mediaQuery(context, "w", 520),
              height: mediaQuery(context, "h", 595),
              decoration: BoxDecoration(
                borderRadius:
                    BorderRadius.circular(mediaQuery(context, "h", 80)),
                color: const Color(0xfffcf6e4),
              ),
              child: Stack(
                children: [
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 82)),
                    child: SvgPicture.string(
                      '<svg viewBox="163.0 0.0 50.0 50.0" ><path transform="translate(160.43, -1.93)" d="M 27.57070350646973 6.61553430557251 C 22.14501762390137 6.61553430557251 17.04413795471191 8.728425025939941 13.20761585235596 12.56494903564453 C 9.371090888977051 16.40147399902344 7.25820255279541 21.50235176086426 7.25820255279541 26.92803764343262 C 7.25820255279541 32.35371780395508 9.371092796325684 37.4546012878418 13.20761680603027 41.2911262512207 C 17.04413986206055 45.12765121459961 22.14501953125 47.24053955078125 27.57070350646973 47.24053955078125 C 32.99638748168945 47.24053955078125 38.09726333618164 45.12765121459961 41.93379211425781 41.2911262512207 C 45.77031326293945 37.4546012878418 47.88320541381836 32.35372161865234 47.88320541381836 26.92803764343262 C 47.88320541381836 21.50235176086426 45.77031326293945 16.40147399902344 41.933837890625 12.56494903564453 C 38.09736251831055 8.728425025939941 32.99638748168945 6.61553430557251 27.57070350646973 6.61553430557251 Z M 27.57070350646973 1.928033590316772 C 41.37783050537109 1.928033590316772 52.57070159912109 13.12090492248535 52.57070159912109 26.92803764343262 C 52.57070159912109 40.73516845703125 41.37783050537109 51.92803955078125 27.57070350646973 51.92803955078125 C 13.76357555389404 51.92803955078125 2.570701122283936 40.73516845703125 2.570701122283936 26.92803955078125 C 2.570701122283936 13.12090969085693 13.76357173919678 1.928035974502563 27.57070350646973 1.928035974502563 Z M 24.44570159912109 36.30303955078125 L 30.69570350646973 36.30303955078125 L 30.69570350646973 42.55304336547852 L 24.44570159912109 42.55304336547852 L 24.44570159912109 36.30303955078125 Z M 24.44570159912109 11.30303478240967 L 30.69570350646973 11.30303478240967 L 30.69570350646973 30.05303573608398 L 24.44570159912109 30.05303573608398 L 24.44570159912109 11.30303478240967 Z" fill="#934d0f" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                      allowDrawingOutsideViewBox: true,
                      fit: BoxFit.fitHeight,
                      height: mediaQuery(context, "h", 50),
                    ),
                  ),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 155)),
                    child: Text(
                      "ui_mainBitrixNotSuccess".tr.toString(),
                      style: TextStyle(
                        fontFamily: 'SukhumvitSet-Bold',
                        fontSize: mediaQuery(context, "h", 33),
                        color: const Color(0xff302c49),
                        letterSpacing: mediaQuery(context, "h", 1.32),
                        fontWeight: FontWeight.w700,
                        height: 0.9393939393939394,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 212)),
                    child: Text.rich(
                      TextSpan(
                        style: TextStyle(
                          fontFamily: 'SukhumvitSet-Medium',
                          fontSize: mediaQuery(context, "h", 28),
                          color: const Color(0xff302c49),
                          letterSpacing: mediaQuery(context, "h", 1.12),
                          height: 1.5357142857142858,
                        ),
                        children: [
                          TextSpan(
                            text: "ui_mainBitrixNotSuccessDetail".tr.toString(),
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          TextSpan(
                            text: 'Start',
                            style: TextStyle(
                              color: const Color(0xff934d0f),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          TextSpan(
                            text:
                                "ui_mainBitrixNotSuccessDetail2".tr.toString(),
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      textAlign: TextAlign.left,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 468)),
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          bitrixControlle.statusAlertFinishError = 0;
                        });
                      },
                      child: Container(
                        alignment: Alignment.center,
                        width: mediaQuery(context, "w", 250),
                        height: mediaQuery(context, "h", 90),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(
                              mediaQuery(context, "h", 30)),
                          color: const Color(0xff7f420c),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0x1a000000),
                              offset: Offset(0, 1),
                              blurRadius: 2,
                            ),
                          ],
                        ),
                        child: Text(
                          "btn_mainBitrixNotSuccessok".tr.toString(),
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Bold',
                            fontSize: mediaQuery(context, "h", 30),
                            color: const Color(0xfffcf6e4),
                            letterSpacing: mediaQuery(context, "h", 1.2),
                            fontWeight: FontWeight.w700,
                            shadows: [
                              Shadow(
                                color: const Color(0x26000000),
                                offset: Offset(0, 1),
                                blurRadius: 1,
                              )
                            ],
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    } else {
      return Container();
    }
  }

  Widget alertSuccessFinish() {
    if (bitrixControlle.alertalertSuccessFinish == 1) {
      return ClipRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 1.97, sigmaY: 1.97),
          child: Container(
            alignment: Alignment.topCenter,
            decoration: BoxDecoration(
              color: const Color(0x00bebebe),
            ),
            child: Container(
              width: mediaQuery(context, "w", 520),
              height: mediaQuery(context, "h", 555),
              margin: EdgeInsets.only(top: mediaQuery(context, "h", 300)),
              decoration: BoxDecoration(
                borderRadius:
                    BorderRadius.circular(mediaQuery(context, "h", 80)),
                color: const Color(0xfffcf6e4),
              ),
              child: Stack(
                children: [
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 82)),
                    child: SvgPicture.string(
                      '<svg viewBox="130.0 0.0 50.0 50.0" ><path transform="translate(127.43, -1.93)" d="M 27.57070350646973 6.61553430557251 C 22.14501762390137 6.61553430557251 17.04413795471191 8.728425025939941 13.20761585235596 12.56494903564453 C 9.371090888977051 16.40147399902344 7.25820255279541 21.50235176086426 7.25820255279541 26.92803764343262 C 7.25820255279541 32.35371780395508 9.371092796325684 37.4546012878418 13.20761680603027 41.2911262512207 C 17.04413986206055 45.12765121459961 22.14501953125 47.24053955078125 27.57070350646973 47.24053955078125 C 32.99638748168945 47.24053955078125 38.09726333618164 45.12765121459961 41.93379211425781 41.2911262512207 C 45.77031326293945 37.4546012878418 47.88320541381836 32.35372161865234 47.88320541381836 26.92803764343262 C 47.88320541381836 21.50235176086426 45.77031326293945 16.40147399902344 41.933837890625 12.56494903564453 C 38.09736251831055 8.728425025939941 32.99638748168945 6.61553430557251 27.57070350646973 6.61553430557251 Z M 27.57070350646973 1.928033590316772 C 41.37783050537109 1.928033590316772 52.57070159912109 13.12090492248535 52.57070159912109 26.92803764343262 C 52.57070159912109 40.73516845703125 41.37783050537109 51.92803955078125 27.57070350646973 51.92803955078125 C 13.76357555389404 51.92803955078125 2.570701122283936 40.73516845703125 2.570701122283936 26.92803955078125 C 2.570701122283936 13.12090969085693 13.76357173919678 1.928035974502563 27.57070350646973 1.928035974502563 Z M 24.44570159912109 36.30303955078125 L 30.69570350646973 36.30303955078125 L 30.69570350646973 42.55304336547852 L 24.44570159912109 42.55304336547852 L 24.44570159912109 36.30303955078125 Z M 24.44570159912109 11.30303478240967 L 30.69570350646973 11.30303478240967 L 30.69570350646973 30.05303573608398 L 24.44570159912109 30.05303573608398 L 24.44570159912109 11.30303478240967 Z" fill="#934d0f" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                      allowDrawingOutsideViewBox: true,
                      fit: BoxFit.fitHeight,
                      height: mediaQuery(context, "h", 50),
                    ),
                  ),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 155)),
                    child: Text(
                      "ui_mainBitrixSuccess".tr.toString(),
                      style: TextStyle(
                        fontFamily: 'SukhumvitSet-Bold',
                        fontSize: mediaQuery(context, "h", 33),
                        color: const Color(0xff302c49),
                        letterSpacing: mediaQuery(context, "h", 1.32),
                        fontWeight: FontWeight.w700,
                        height: 0.9393939393939394,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 212)),
                    child: Text(
                      "ui_mainBitrixSuccessDetail".tr.toString(),
                      style: TextStyle(
                        fontFamily: 'SukhumvitSet-Medium',
                        fontSize: mediaQuery(context, "h", 28),
                        color: const Color(0xff302c49),
                        letterSpacing: mediaQuery(context, "h", 1.12),
                        fontWeight: FontWeight.w500,
                        height: 1.5357142857142858,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 385)),
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          bitrixControlle.alertalertSuccessFinish = 0;
                        });
                      },
                      child: Container(
                        alignment: Alignment.center,
                        width: mediaQuery(context, "w", 250),
                        height: mediaQuery(context, "h", 90),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(
                              mediaQuery(context, "h", 30)),
                          color: const Color(0xff7f420c),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0x1a000000),
                              offset: Offset(0, 1),
                              blurRadius: 2,
                            ),
                          ],
                        ),
                        child: Text(
                          'ตกลง',
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Bold',
                            fontSize: mediaQuery(context, "h", 30),
                            color: const Color(0xfffcf6e4),
                            letterSpacing: mediaQuery(context, "h", 1.2),
                            fontWeight: FontWeight.w700,
                            shadows: [
                              Shadow(
                                color: const Color(0x26000000),
                                offset: Offset(0, 1),
                                blurRadius: 1,
                              )
                            ],
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    } else {
      return Container();
    }
  }
}
