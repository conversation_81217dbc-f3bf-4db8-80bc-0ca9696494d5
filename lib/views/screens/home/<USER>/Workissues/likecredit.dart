import 'dart:convert';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:get/get_utils/src/extensions/internacionalization.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:mapp_ms24/controllers/LikeCreditController.dart';
import 'package:mapp_ms24/controllers/internal/BenefitController%20/BenefitController.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/controllers/internal/Tacontroller/Tacontroller.dart';
import 'package:mapp_ms24/controllers/utils/widget.dart';
import 'package:mapp_ms24/views/screens/Widget/Widget.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';
import 'package:mapp_ms24/views/screens/home/<USER>';

import 'package:shared_preferences/shared_preferences.dart';

class likeCredit extends StatelessWidget {
  final LikeCreditController LikeCreCtrl = Get.put(LikeCreditController());

  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            width: Get.width,
            height: Get.height,
            decoration: BoxDecoration(
                gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                  const Color(0xFF1F1C2F).withOpacity(1),
                  const Color(0xFF0D0C14).withOpacity(1),
                ])),
            child: Padding(
              padding: const EdgeInsets.only(top: 20.0),
              child: Column(
                children: [
                  Stack(
                    children: [
                      Container(
                        margin: EdgeInsets.only(left: 20.w, top: 20.h),
                        child: IconButton(
                          icon: Icon(Icons.arrow_back_ios),
                          iconSize: 20,
                          color: Colors.white,
                          onPressed: () {
                            Get.back();
                          },
                        ),
                      ),
                      Container(
                        alignment: Alignment.topCenter,
                        margin: EdgeInsets.only(top: 32.h),
                        child: AppWidget.normalText(
                            context,
                            'ui_likeCredithead'.tr,
                            15.sp,
                            const Color(0xFFF6F6F6),
                            FontWeight.w400),
                      )
                    ],
                  ),
                  buildBody(context),
                  // buildList(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildBody(context) {
    return Obx(() {
      if (LikeCreCtrl.checking.value) {
        // Display loader while data is loading
        return Container(
            height: Get.height * 0.40,
            color: Colors.black12,
            child: Center(
              child: SizedBox(
                width: 50.w,
                child: const LoadingIndicator(
                  indicatorType: Indicator.lineSpinFadeLoader,
                  colors: [Colors.white],
                ),
              ),
            ));
      } else {
        // Display the loaded content
        return Column(
          children: [
            Container(
              margin: EdgeInsets.only(top: 20.h, left: 20.w, right: 20.w),
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(10)),
                color: Color(0xFF322B63),
              ),
              height: 70.h,
              child: Stack(
                children: [
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            margin: EdgeInsets.only(top: 5.h, left: 10.w),
                            child: SvgPicture.asset('assets/Menu/smile.svg',
                                height: 15.h),
                          ),
                          Container(
                            alignment: Alignment.topCenter,
                            margin: EdgeInsets.only(top: 10.h),
                            child: AppWidget.normalText(
                                context,
                                " " + 'ui_likeCreditPoint'.tr + " :",
                                12.sp,
                                const Color(0xFFF6F6F6),
                                FontWeight.w400),
                          ),
                          Container(
                            alignment: Alignment.topCenter,
                            margin: EdgeInsets.only(top: 10.h),
                            child: AppWidget.normalText(
                              context,
                              "  " +
                                  (LikeCreCtrl.profileCtr.likecredit != null
                                      ? LikeCreCtrl.profileCtr.likecredit!
                                          .toStringAsFixed(2)
                                          .replaceAllMapped(
                                              RegExp(
                                                  r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                                              (Match m) => '${m[1]},')
                                      : "0.00"),
                              12.sp,
                              const Color(0xFFF6F6F6),
                              FontWeight.w400,
                            ),
                          )
                        ],
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            alignment: Alignment.topCenter,
                            margin: EdgeInsets.only(top: 5.h),
                            child: AppWidget.normalText(
                                context,
                                'ui_likeCreditLastUpdate'.tr,
                                12.sp,
                                const Color(0xFFF6F6F6),
                                FontWeight.w400),
                          ),
                          Container(
                            alignment: Alignment.topCenter,
                            margin: EdgeInsets.only(top: 5.h),
                            child: AppWidget.normalText(
                                context,
                                " " +
                                    convertDateTime(LikeCreCtrl.taCrt.datetime,
                                        "dd-MM-YYYY"),
                                12.sp,
                                const Color(0xFFF6F6F6),
                                FontWeight.w400),
                          )
                        ],
                      )
                    ],
                  ),
                ],
              ),
            ),
            Column(
              children: [
                Container(
                    margin: EdgeInsets.only(top: 40.h),
                    child: Row(children: [
                      Container(
                        margin:
                            EdgeInsets.only(left: mediaQuery(context, "w", 67)),
                        child: SvgPicture.string(
                          '<svg viewBox="67.0 445.0 34.3 30.0" ><defs><linearGradient id="gradient" x1="0.5" y1="0.0" x2="0.5" y2="1.0"><stop offset="0.0" stop-color="#fffee095"  /><stop offset="1.0" stop-color="#fffdd163"  /></linearGradient></defs><path transform="translate(67.0, 442.75)" d="M 2.142857074737549 30.10714340209961 C 2.142857074737549 31.29240989685059 3.100446462631226 32.25 4.285714149475098 32.25 L 15 32.25 L 15 21.53571510314941 L 2.142857074737549 21.53571510314941 L 2.142857074737549 30.10714340209961 Z M 19.28571510314941 32.25 L 30 32.25 C 31.18526649475098 32.25 32.14285659790039 31.29240989685059 32.14285659790039 30.10714340209961 L 32.14285659790039 21.53571510314941 L 19.28571510314941 21.53571510314941 L 19.28571510314941 32.25 Z M 32.14285659790039 10.8214282989502 L 29.32366180419922 10.8214282989502 C 29.73884010314941 10.01115989685059 30 9.113839149475098 30 8.142856597900391 C 30 4.895089149475098 27.35490989685059 2.25 24.10714340209961 2.25 C 21.32143020629883 2.25 19.52009010314941 3.676339387893677 17.2098217010498 6.823660850524902 C 14.8995532989502 3.676339387893677 13.0982141494751 2.25 10.3125 2.25 C 7.064732074737549 2.25 4.419642925262451 4.895089149475098 4.419642925262451 8.142856597900391 C 4.419642925262451 9.113839149475098 4.674107074737549 10.01115989685059 5.095982551574707 10.8214282989502 L 2.142857074737549 10.8214282989502 C 0.9575892686843872 10.8214282989502 0 11.77901744842529 0 12.96428489685059 L 0 18.3214282989502 C 0 18.91071510314941 0.4821428954601288 19.39285850524902 1.071428537368774 19.39285850524902 L 33.21428680419922 19.39285850524902 C 33.80357360839844 19.39285850524902 34.28571319580078 18.91071510314941 34.28571319580078 18.3214282989502 L 34.28571319580078 12.96428489685059 C 34.28571319580078 11.77901744842529 33.328125 10.8214282989502 32.14285659790039 10.8214282989502 Z M 10.30580425262451 10.8214282989502 C 8.825893402099609 10.8214282989502 7.627233028411865 9.622767448425293 7.627233028411865 8.142856597900391 C 7.627233028411865 6.662946224212646 8.825893402099609 5.464285850524902 10.30580425262451 5.464285850524902 C 11.63839340209961 5.464285850524902 12.62276840209961 5.685267925262451 16.0714282989502 10.8214282989502 L 10.3058032989502 10.8214282989502 Z M 24.10714340209961 10.8214282989502 L 18.34151840209961 10.8214282989502 C 21.78348350524902 5.698660850524902 22.7410717010498 5.464285850524902 24.10714340209961 5.464285850524902 C 25.5870532989502 5.464285850524902 26.78571510314941 6.662946224212646 26.78571510314941 8.142856597900391 C 26.78571510314941 9.622767448425293 25.5870532989502 10.8214282989502 24.10714340209961 10.8214282989502 Z" fill="url(#gradient)" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                          allowDrawingOutsideViewBox: true,
                          fit: BoxFit.fitHeight,
                          height: mediaQuery(context, "h", 30),
                        ),
                      ),
                      Container(
                        margin:
                            EdgeInsets.only(left: mediaQuery(context, "w", 20)),
                        child: AppWidget.normalText(
                            context,
                            "ui_likeCreditChange".tr.toString(),
                            12.h,
                            const Color(0xFFF6F6F6),
                            FontWeight.w400),
                      ),
                    ])),
                LikeCreCtrl.checking.value
                    ? Container(
                        height: Get.height * 0.40,
                        color: Colors.black12,
                        child: Center(
                          child: SizedBox(
                            width: 50.w,
                            child: const LoadingIndicator(
                              indicatorType: Indicator.lineSpinFadeLoader,
                              colors: [Colors.white],
                            ),
                          ),
                        ))
                    : Container(
                        height: Get.height * 0.60,
                        width: Get.width,
                        padding: EdgeInsets.symmetric(horizontal: 16),
                        child: ListView(
                          children: <Widget>[
                            SingleChildScrollView(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.stretch,
                                children: ListMyWidgets(context),
                              ),
                            ),
                          ],
                        ),
                      ),
              ],
            )
          ],
        );
      }
    });
  }

  void showChangeSuccessPopup(BuildContext context) {
    if (LikeCreCtrl.statusAlertChangeSuccess == 1) {
      showDialog(
        context: context,
        barrierDismissible:
            false, // Prevent closing the popup by tapping outside
        builder: (BuildContext context) {
          return AlertDialog(
              backgroundColor: Colors.transparent,
              contentPadding: EdgeInsets.zero,
              content: ClipRect(
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 1.97, sigmaY: 1.97),
                  child: Container(
                    alignment: Alignment.topCenter,
                    decoration: BoxDecoration(
                      color: const Color(0x00bebebe),
                    ),
                    child: Container(
                      width: mediaQuery(context, "w", 520),
                      height: mediaQuery(context, "h", 750),
                      margin:
                          EdgeInsets.only(top: mediaQuery(context, "h", 300)),
                      decoration: BoxDecoration(
                        borderRadius:
                            BorderRadius.circular(mediaQuery(context, "h", 80)),
                        color: const Color(0xfffcf6e4),
                      ),
                      child: Stack(
                        children: [
                          Container(
                            alignment: Alignment.topCenter,
                            margin: EdgeInsets.only(
                                top: mediaQuery(context, "h", 82)),
                            child: SvgPicture.string(
                              '<svg viewBox="163.0 0.0 50.0 50.0" ><path transform="translate(160.43, -1.93)" d="M 27.57070350646973 6.61553430557251 C 22.14501762390137 6.61553430557251 17.04413795471191 8.728425025939941 13.20761585235596 12.56494903564453 C 9.371090888977051 16.40147399902344 7.25820255279541 21.50235176086426 7.25820255279541 26.92803764343262 C 7.25820255279541 32.35371780395508 9.371092796325684 37.4546012878418 13.20761680603027 41.2911262512207 C 17.04413986206055 45.12765121459961 22.14501953125 47.24053955078125 27.57070350646973 47.24053955078125 C 32.99638748168945 47.24053955078125 38.09726333618164 45.12765121459961 41.93379211425781 41.2911262512207 C 45.77031326293945 37.4546012878418 47.88320541381836 32.35372161865234 47.88320541381836 26.92803764343262 C 47.88320541381836 21.50235176086426 45.77031326293945 16.40147399902344 41.933837890625 12.56494903564453 C 38.09736251831055 8.728425025939941 32.99638748168945 6.61553430557251 27.57070350646973 6.61553430557251 Z M 27.57070350646973 1.928033590316772 C 41.37783050537109 1.928033590316772 52.57070159912109 13.12090492248535 52.57070159912109 26.92803764343262 C 52.57070159912109 40.73516845703125 41.37783050537109 51.92803955078125 27.57070350646973 51.92803955078125 C 13.76357555389404 51.92803955078125 2.570701122283936 40.73516845703125 2.570701122283936 26.92803955078125 C 2.570701122283936 13.12090969085693 13.76357173919678 1.928035974502563 27.57070350646973 1.928035974502563 Z M 24.44570159912109 36.30303955078125 L 30.69570350646973 36.30303955078125 L 30.69570350646973 42.55304336547852 L 24.44570159912109 42.55304336547852 L 24.44570159912109 36.30303955078125 Z M 24.44570159912109 11.30303478240967 L 30.69570350646973 11.30303478240967 L 30.69570350646973 30.05303573608398 L 24.44570159912109 30.05303573608398 L 24.44570159912109 11.30303478240967 Z" fill="#934d0f" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                              allowDrawingOutsideViewBox: true,
                              fit: BoxFit.fitHeight,
                              height: mediaQuery(context, "h", 50),
                            ),
                          ),
                          Container(
                            alignment: Alignment.topCenter,
                            margin: EdgeInsets.only(
                                top: mediaQuery(context, "h", 155)),
                            child: Text(
                              "ui_likeCreditConfirmChange".tr.toString(),
                              style: TextStyle(
                                fontFamily: 'SukhumvitSet-Bold',
                                fontSize: mediaQuery(context, "h", 33),
                                color: const Color(0xff302c49),
                                letterSpacing: mediaQuery(context, "h", 1.32),
                                fontWeight: FontWeight.w700,
                                height: 0.9393939393939394,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          Container(
                            alignment: Alignment.topCenter,
                            margin: EdgeInsets.only(
                                top: mediaQuery(context, "h", 212)),
                            child: Text.rich(
                              TextSpan(
                                style: TextStyle(
                                  fontFamily: 'SukhumvitSet-Medium',
                                  fontSize: mediaQuery(context, "h", 28),
                                  color: const Color(0xff302c49),
                                  letterSpacing: mediaQuery(context, "h", 1.12),
                                  height: 1.****************,
                                ),
                                children: [
                                  TextSpan(
                                    text: "ui_likeCreditConfirmChangeDetail"
                                        .tr
                                        .toString(),
                                    style: TextStyle(
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  TextSpan(
                                    text: LikeCreCtrl.namePrivilege.toString(),
                                    style: TextStyle(
                                      fontFamily: 'SukhumvitSet-Bold',
                                      color: const Color(0xff934d0f),
                                      fontWeight: FontWeight.w700,
                                    ),
                                  ),
                                  TextSpan(
                                    text: " " +
                                        "ui_likeCreditConfirmChangeDetail2"
                                            .tr
                                            .toString(),
                                    style: TextStyle(
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  TextSpan(
                                    text: "ui_likeCreditConfirmChangeDetail3"
                                            .tr
                                            .toString() +
                                        " " +
                                        LikeCreCtrl.pointChange.toString() +
                                        " " +
                                        "ui_likeCreditConfirmChangeDetail4"
                                            .tr
                                            .toString() +
                                        " " +
                                        LikeCreCtrl.detaiPrivilege.toString(),
                                    style: TextStyle(
                                      color: const Color(0xff934d0f),
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          Container(
                            alignment: Alignment.topCenter,
                            margin: EdgeInsets.only(
                                top: mediaQuery(context, "h", 473)),
                            child: GestureDetector(
                              onTap: () {
                                // CheckVacationdays();
                                for (var i = 0;
                                    i <
                                        LikeCreCtrl.dataPrivilege.length
                                            .toInt();
                                    i++) {
                                  if (LikeCreCtrl.idActivityLikeCredit ==
                                      LikeCreCtrl.dataPrivilege[i]
                                          ['idActivityLikeCredit']) {
                                    if (LikeCreCtrl.profileCtr.likecredit >=
                                        double.parse(LikeCreCtrl
                                            .dataPrivilege[i]["pointChange"]
                                            .toString())) {
                                      LikeCreCtrl.statusAlertChangeSuccess =
                                          0.obs;

                                      LikeCreCtrl.saveChangLikeCredit(
                                          LikeCreCtrl
                                              .idActivityLikeCredit.value);
                                    } else {
                                      Fluttertoast.showToast(
                                          msg: "จำนวน like Credit ไม่เพียงพอ",
                                          toastLength: Toast.LENGTH_SHORT,
                                          gravity: ToastGravity.TOP,
                                          backgroundColor: Colors.red,
                                          textColor: Colors.black,
                                          fontSize: 16.0);
                                    }
                                  }
                                }
                              },
                              child: Container(
                                alignment: Alignment.center,
                                width: mediaQuery(context, "w", 250),
                                height: mediaQuery(context, "h", 90),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(
                                      mediaQuery(context, "h", 30)),
                                  color: const Color(0xff7f420c),
                                  boxShadow: [
                                    BoxShadow(
                                      color: const Color(0x1a000000),
                                      offset: Offset(0, 1),
                                      blurRadius: 2,
                                    ),
                                  ],
                                ),
                                child: Text(
                                  "btn_likeCreditConfirmok".tr.toString(),
                                  style: TextStyle(
                                    fontFamily: 'SukhumvitSet-Bold',
                                    fontSize: mediaQuery(context, "h", 30),
                                    color: const Color(0xfffcf6e4),
                                    letterSpacing:
                                        mediaQuery(context, "h", 1.2),
                                    fontWeight: FontWeight.w700,
                                    shadows: [
                                      Shadow(
                                        color: const Color(0x26000000),
                                        offset: Offset(0, 1),
                                        blurRadius: 1,
                                      )
                                    ],
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          ),
                          Container(
                            alignment: Alignment.topCenter,
                            margin: EdgeInsets.only(
                                top: mediaQuery(context, "h", 578)),
                            child: GestureDetector(
                              onTap: () {
                                Get.back();

                                LikeCreCtrl.statusAlertChangeSuccess = 0.obs;
                              },
                              child: Container(
                                alignment: Alignment.center,
                                width: mediaQuery(context, "w", 250),
                                height: mediaQuery(context, "h", 90),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(
                                      mediaQuery(context, "h", 30)),
                                  color: const Color(0xff302c49),
                                  boxShadow: [
                                    BoxShadow(
                                      color: const Color(0x1a000000),
                                      offset: Offset(0, 1),
                                      blurRadius: 2,
                                    ),
                                  ],
                                ),
                                child: Text(
                                  "btn_likeCreditConfirmcancel".tr.toString(),
                                  style: TextStyle(
                                    fontFamily: 'SukhumvitSet-Bold',
                                    fontSize: mediaQuery(context, "h", 30),
                                    color: const Color(0xfffcf6e4),
                                    letterSpacing:
                                        mediaQuery(context, "h", 1.2),
                                    fontWeight: FontWeight.w700,
                                    shadows: [
                                      Shadow(
                                        color: const Color(0x26000000),
                                        offset: Offset(0, 1),
                                        blurRadius: 1,
                                      )
                                    ],
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ));
        },
      );
    }
  }

  List<Widget> ListMyWidgets(context) {
    List<Widget> list = [];
    for (var i = 0; i < LikeCreCtrl.dataPrivilege.length.toInt(); i++) {
      list.add(GestureDetector(
        onTap: () {
          if (LikeCreCtrl.profileCtr.likecredit >=
              double.parse(
                  LikeCreCtrl.dataPrivilege[i]["pointChange"].toString())) {
            // Check if like credit is sufficient
            if (LikeCreCtrl.resignationStatus == "Y") {
              // Check resignation status

              if (LikeCreCtrl.Press == "0") {
                // Check if already redeemed today

                // Additional condition checks
                if (LikeCreCtrl.dataPrivilege[i]["namePrivilege"].toString() ==
                    "ลาพักร้อน") {
                  if (LikeCreCtrl.sumVacation == true) {
                    // Check if vacation days are still available
                    // Set state for success and show alert
                    LikeCreCtrl.statusAlertChangeSuccess = 1.obs;
                    LikeCreCtrl.namePrivilege = LikeCreCtrl.dataPrivilege[i]
                            ["namePrivilege"]
                        .toString()
                        .obs;
                    LikeCreCtrl.detaiPrivilege = LikeCreCtrl.dataPrivilege[i]
                            ["detaiPrivilege"]
                        .toString()
                        .obs;
                    LikeCreCtrl.pointChange = LikeCreCtrl.dataPrivilege[i]
                            ["pointChange"]
                        .toString()
                        .obs;
                    LikeCreCtrl.idActivityLikeCredit = LikeCreCtrl
                        .dataPrivilege[i]["idActivityLikeCredit"]
                        .toString()
                        .obs;
                    showChangeSuccessPopup(context); // Show the alert
                  } else {
                    Fluttertoast.showToast(
                        msg: "ไม่สามารถแลกได้ วันลาพักร้อนของท่านยังไม่หมด",
                        toastLength: Toast.LENGTH_SHORT,
                        gravity: ToastGravity.TOP,
                        backgroundColor: Colors.red,
                        textColor: Colors.black,
                        fontSize: 16.0);
                  }
                } else {
                  // Handle other privileges
                  if (int.parse(LikeCreCtrl.LateDay.value) != 0 &&
                      int.parse(LikeCreCtrl.Exchange.value) <=
                          int.parse(LikeCreCtrl.LateDay.value)) {
                    LikeCreCtrl.statusAlertChangeSuccess = 1.obs;
                    LikeCreCtrl.namePrivilege = LikeCreCtrl.dataPrivilege[i]
                            ["namePrivilege"]
                        .toString()
                        .obs;
                    LikeCreCtrl.detaiPrivilege = LikeCreCtrl.dataPrivilege[i]
                            ["detaiPrivilege"]
                        .toString()
                        .obs;
                    LikeCreCtrl.pointChange = LikeCreCtrl.dataPrivilege[i]
                            ["pointChange"]
                        .toString()
                        .obs;
                    LikeCreCtrl.idActivityLikeCredit = LikeCreCtrl
                        .dataPrivilege[i]["idActivityLikeCredit"]
                        .toString()
                        .obs;
                    showChangeSuccessPopup(
                        context); // Show the alert for other privileges
                  } else if (int.parse(LikeCreCtrl.LateDay.value) == 0) {
                    Fluttertoast.showToast(
                        msg: "ไม่สามารถแลกได้ คุณไม่มีวันมาสายภายในเดือน",
                        toastLength: Toast.LENGTH_SHORT,
                        gravity: ToastGravity.TOP,
                        backgroundColor: Colors.red,
                        textColor: Colors.black,
                        fontSize: 16.0);
                  }
                }
              } else {
                Fluttertoast.showToast(
                    msg: "คุณทำการแลกไปแล้ว กรุณากดแลกใหม่พรุ่งนี้",
                    toastLength: Toast.LENGTH_SHORT,
                    gravity: ToastGravity.TOP,
                    backgroundColor: Colors.red,
                    textColor: Colors.black,
                    fontSize: 16.0);
              }
            } else {
              Fluttertoast.showToast(
                  msg: "คุณมีสถานะลาออก ไม่สามารถทำการการได้",
                  toastLength: Toast.LENGTH_SHORT,
                  gravity: ToastGravity.TOP,
                  backgroundColor: Colors.red,
                  textColor: Colors.black,
                  fontSize: 16.0);
            }
          } else {
            Fluttertoast.showToast(
                msg: "จำนวน like Credit ของคุณไม่เพียงพอ",
                toastLength: Toast.LENGTH_SHORT,
                gravity: ToastGravity.TOP,
                backgroundColor: Colors.red,
                textColor: Colors.black,
                fontSize: 16.0);
          }
        },
        child: Container(
          width: mediaQuery(context, "w", 740),
          height: mediaQuery(context, "h", 140),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(mediaQuery(context, "h", 20)),
            border: Border.all(color: Color(0xFFA596FF), width: 1),
            gradient: LinearGradient(
              begin: Alignment(0.0, -1.0),
              end: Alignment(0.0, 1.0),
              colors: [const Color(0xFF322B63), const Color(0xFF322B63)],
              stops: [0.0, 1.0],
            ),
            boxShadow: [
              BoxShadow(
                color: const Color(0x4d000000),
                offset: Offset(0, 2),
                blurRadius: 20,
              ),
            ],
          ),
          child: Stack(
            children: [
              Container(
                margin: EdgeInsets.only(
                    top: mediaQuery(context, "h", 30),
                    left: mediaQuery(context, "w", 45)),
                child: Text(
                  LikeCreCtrl.dataPrivilege[i]["namePrivilege"].toString(),
                  style: TextStyle(
                    fontFamily: 'SukhumvitSet-Medium',
                    fontSize: mediaQuery(context, "h", 28),
                    color: const Color(0xfffee095),
                    letterSpacing: mediaQuery(context, "h", 1.****************),
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.left,
                ),
              ),
              Container(
                margin: EdgeInsets.only(
                    top: mediaQuery(context, "h", 70),
                    left: mediaQuery(context, "w", 45)),
                child: Text(
                  LikeCreCtrl.dataPrivilege[i]["pointChange"].toString() +
                      " " +
                      "ui_likeCreditUnitPoint".tr.toString() +
                      " : " +
                      LikeCreCtrl.dataPrivilege[i]["detaiPrivilege"].toString(),
                  style: TextStyle(
                    fontFamily: 'SukhumvitSet-Text',
                    fontSize: mediaQuery(context, "h", 25),
                    color: const Color(0xfffcf6e4),
                    letterSpacing: mediaQuery(context, "h", 1.25),
                  ),
                  textAlign: TextAlign.left,
                ),
              ),
              Container(
                alignment: Alignment.centerRight,
                margin: EdgeInsets.only(right: mediaQuery(context, "w", 30)),
                child: Container(
                  alignment: Alignment.center,
                  width: mediaQuery(context, "w", 170),
                  height: mediaQuery(context, "h", 90),
                  decoration: BoxDecoration(
                    borderRadius:
                        BorderRadius.circular(mediaQuery(context, "h", 20)),
                    gradient: LinearGradient(
                      begin: Alignment(0.0, -1.0),
                      end: Alignment(0.0, 1.0),
                      colors: LikeCreCtrl.resignationStatus ==
                              "Y" //เช็คสถานะการทำงาน
                          ? LikeCreCtrl.profileCtr.likecredit >=
                                  double.parse(LikeCreCtrl.dataPrivilege[i][
                                          "pointChange"]
                                      .toString()) //เช็คว่า like credit มีพอไหม
                              ? LikeCreCtrl.dataPrivilege[i]
                                              ["namePrivilege"]
                                          .toString() ==
                                      "ลาพักร้อน" //เช็คว่าเป็นลาพักร้อนหรือไม่
                                  ? LikeCreCtrl.sumVacation ==
                                          true //เช็คว่าวันลาพักร้อนว่าหมดหรือยัง
                                      ? [
                                          const Color(0xff302c49),
                                          const Color(0xff232034)
                                        ]
                                      : [
                                          const Color(0xff757575),
                                          const Color(0xff616161)
                                        ]
                                  : LikeCreCtrl.Press ==
                                          "0" //เช็คว่าทำการแลกไปแล้วหรือยังในวันนี้
                                      ? LikeCreCtrl.LateDay.toString() !=
                                              "0" //วันลาภายในเดือนต้องไม่เท่ากับ0
                                          ? [
                                              const Color(0xff302c49),
                                              const Color(0xff232034)
                                            ]
                                          : [
                                              const Color(0xff757575),
                                              const Color(0xff616161)
                                            ]
                                      : [
                                          const Color(0xff757575),
                                          const Color(0xff616161)
                                        ]
                              : [
                                  const Color(0xff757575),
                                  const Color(0xff616161)
                                ]
                          : [const Color(0xff757575), const Color(0xff616161)],
                      stops: [0.0, 1.0],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0x4d000000),
                        offset: Offset(0, 2),
                        blurRadius: 10,
                      ),
                    ],
                  ),
                  child: Text(
                    "btn_likeCreditChange".tr.toString(),
                    style: TextStyle(
                      fontFamily: 'SukhumvitSet-Medium',
                      fontSize: mediaQuery(context, "h", 28),
                      color: const Color(0xfffee095),
                      letterSpacing:
                          mediaQuery(context, "h", 1.****************),
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              )
            ],
          ),
        ),
      ));
      list.add(Container(
        height: mediaQuery(context, "h", 20),
      ));
    }
    if (LikeCreCtrl.dataPrivilege.isEmpty) {
      Text('Emtyp');
    }

    return list;
  }
}
