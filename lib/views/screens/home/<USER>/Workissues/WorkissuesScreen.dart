import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:mapp_ms24/controllers/internal/BitrixController/bitrixController.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/controllers/internal/WorkIssuse/WorkIssuseController.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/Workissues/bitrixActionScreen.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/Workissues/bitrixFinish.dart';

import '../../../../../controllers/utils/widget.dart';
import '../../../Widget/Widget.dart';

class WorkIssuesScreen extends StatefulWidget {
  const WorkIssuesScreen({super.key});

  @override
  State<WorkIssuesScreen> createState() => _WorkIssuesScreenState();
}

class _WorkIssuesScreenState extends State<WorkIssuesScreen> {
  int currentIndex = 0;
  WorkIssuseController woIsCtr = Get.find<WorkIssuseController>();
  ProfileController profile = Get.find<ProfileController>();
  BitrixController bitrixControlle = Get.find<BitrixController>();

  late FToast fToast;
  bool checking = true;
  @override
  void initState() {
    super.initState();
    initializePage();
  }

  void initializePage() async {
    fToast = FToast();
    await fToast.init(context);
    await bitrixControlle.loadingAllProgress();
    checking = false;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).colorScheme.primary,
                  Theme.of(context).colorScheme.onPrimary,
                ],
                begin: Alignment.topCenter, // เริ่มต้น Gradient ที่มุมบนซ้าย
                end: Alignment.bottomCenter,
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.only(top: 20.0),
              child: SizedBox(
                child: SingleChildScrollView(
                  physics: NeverScrollableScrollPhysics(),
                  child: Column(
                    children: [
                      buildAppBar(context, 'ui_mainBitrixhead'.tr),
                      buildChosseScreen(),
                    ],
                  ),
                ),
              ),
            ),
          ),
          checking
              ? Container(
                  width: MediaQuery.of(context).size.width,
                  height: MediaQuery.of(context).size.height,
                  color: Colors.black12,
                  child: Center(
                    child: SizedBox(
                      width: 60.w,
                      child: const LoadingIndicator(
                        indicatorType: Indicator.lineSpinFadeLoader,
                        colors: [Colors.white],
                      ),
                    ),
                  ),
                )
              : Container(),
        ],
      ),
    );
  }

  Widget buildChosseScreen() {
    return Container(
      height: Get.height,
      width: Get.width,
      padding: EdgeInsets.only(top: 10.h),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            height: 42.h,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                GestureDetector(
                  onTap: () {
                    setState(() {
                      currentIndex = 0;
                    });
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 10.w),
                    height: Get.height,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(
                        color: currentIndex == 0
                            ? Theme.of(context).colorScheme.surfaceContainer
                            : Theme.of(context).colorScheme.surfaceContainer,
                        width: 1.0.w,
                      ),
                      color: currentIndex == 0
                          ? Theme.of(context).colorScheme.surfaceBright
                          : Theme.of(context).colorScheme.primary,
                    ),
                    child: Align(
                      alignment: Alignment.center,
                      child: AppWidget.normalText(
                        context,
                        'ui_work_issues'.tr,
                        14.sp,
                        currentIndex == 0
                            ? Theme.of(context).colorScheme.primaryContainer
                            : Theme.of(context).colorScheme.surfaceTint,
                        FontWeight.w500,
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  width: 10.w,
                ),
                GestureDetector(
                  onTap: () {
                    setState(() {
                      currentIndex = 1;
                    });
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 10.w),
                    height: Get.height,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(
                        color: currentIndex == 1
                            ? Theme.of(context).colorScheme.surfaceContainer
                            : Theme.of(context).colorScheme.surfaceContainer,
                        width: 1.0.w,
                      ),
                      color: currentIndex == 1
                          ? Theme.of(context).colorScheme.surfaceBright
                          : Theme.of(context).colorScheme.primary,
                    ),
                    child: Align(
                      alignment: Alignment.center,
                      child: AppWidget.normalText(
                        context,
                        'ui_work_give'.tr,
                        14.sp,
                        currentIndex == 1
                            ? Theme.of(context).colorScheme.primaryContainer
                            : Theme.of(context).colorScheme.surfaceTint,
                        FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 16, right: 16, top: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  child: Text(
                    "ui_mainBitrixNumJob".tr.toString(),
                    style: TextStyle(
                      fontFamily: 'SukhumvitSet-Text',
                      fontSize: mediaQuery(context, "h", 26),
                      color: Theme.of(context).colorScheme.scrim,
                      letterSpacing: mediaQuery(context, "h", 1.3),
                    ),
                    textAlign: TextAlign.left,
                  ),
                ),
                Container(
                  alignment: Alignment.topRight,
                  child: Text.rich(
                    TextSpan(
                      style: TextStyle(
                        fontFamily: 'SukhumvitSet-Text',
                        fontSize: mediaQuery(context, "h", 26),
                        color: Theme.of(context).colorScheme.scrim,
                        letterSpacing: mediaQuery(context, "h", 1.3),
                      ),
                      children: [
                        TextSpan(
                            text: currentIndex.toString() == "1"
                                ? bitrixControlle.dataBitrixCreate.length
                                    .toString()
                                : bitrixControlle.dataBitrix.length.toString()),
                        TextSpan(
                          text: ' ',
                          style: TextStyle(
                            color: const Color(0xffff6969),
                          ),
                        ),
                        TextSpan(
                          text: "ui_mainBitrixList".tr.toString(),
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.scrim,
                          ),
                        ),
                      ],
                    ),
                    textAlign: TextAlign.left,
                  ),
                ),
              ],
            ),
          ),
          currentIndex == 0
              ? Expanded(
                  child: Container(
                    padding: EdgeInsets.only(left: 16, right: 16),
                    child: ListView(
                      children: <Widget>[
                        SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: ListMyWidgets(),
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              : Expanded(
                  child: Container(
                    padding: EdgeInsets.only(left: 16, right: 16),
                    child: ListView(
                      children: <Widget>[
                        SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: ListMyWidgetsCreate(),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
        ],
      ),
    );
  }

  List<Widget> ListMyWidgets() {
    List<Widget> list = [];
    for (var i = 0; i < bitrixControlle.dataBitrixMe.length.toInt(); i++) {
      list.add(GestureDetector(
          onTap: () {
            Get.to(() => bitrixActionScreen(
                  bitrixControlle.bitrixlModel!.bitrixl![i].ID,
                  bitrixControlle.idBitrix,
                  convertDateTime(
                          bitrixControlle
                              .bitrixlModel!.bitrixl![i].CREATED_DATE,
                          "dd-MM-YYYY2")
                      .toString(),
                  bitrixControlle.bitrixlModel!.bitrixl![i].CREATED_BY_NAME,
                  bitrixControlle
                      .bitrixlModel!.bitrixl![i].CREATED_BY_LAST_NAME,
                  bitrixControlle
                      .nameGroupID(
                          bitrixControlle.bitrixlModel!.bitrixl![i].GROUP_ID)
                      .toString(),
                  bitrixControlle.bitrixlModel!.bitrixl![i].TITLE,
                  bitrixControlle.bitrixlModel!.bitrixl![i].DEADLINE,
                  bitrixControlle.bitrixlModel!.bitrixl![i].REAL_STATUS,
                ));
          },
          child: Container(
            padding: EdgeInsets.fromLTRB(
                mediaQuery(context, "w", 35),
                mediaQuery(context, "h", 26),
                mediaQuery(context, "w", 35),
                mediaQuery(context, "h", 26)),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(mediaQuery(context, "h", 20)),
              gradient: LinearGradient(
                begin: Alignment(0.0, -1.0),
                end: Alignment(0.0, 1.0),
                colors: [
                  Theme.of(context).colorScheme.primary,
                  Theme.of(context).colorScheme.onPrimary,
                ],
                stops: [0.0, 1.0],
              ),
              border: Border.all(
                color: Color(
                    0xFFA596FF), // The color code converted to Flutter's Color class
                width: 1.0, // Border width
              ),
              boxShadow: [
                BoxShadow(
                  color: Color(0x4d000000),
                  offset: Offset(0, 2),
                  blurRadius: 20,
                ),
              ],
            ),
            child: Row(
              children: [
                SizedBox(
                  height: 24.h,
                  width: 24.w,
                  child: Image.asset('assets/ADD/Stick-note.png'),
                ),
                SizedBox(
                  width: 8,
                ),
                SizedBox(
                  width: 250.w,
                  child: RichText(
                    text: TextSpan(
                      children: <TextSpan>[
                        TextSpan(
                          text: convertDateTime(
                                  bitrixControlle
                                      .bitrixlModel!.bitrixl![i].CREATED_DATE,
                                  "dd-MM-YYYY2") +
                              " Created by " +
                              bitrixControlle
                                  .bitrixlModel!.bitrixl![i].CREATED_BY_NAME
                                  .toString() +
                              " " +
                              bitrixControlle.bitrixlModel!.bitrixl![i]
                                  .CREATED_BY_LAST_NAME
                                  .toString() +
                              " : " +
                              bitrixControlle
                                  .nameGroupID(bitrixControlle
                                      .bitrixlModel!.bitrixl![i].GROUP_ID)
                                  .toString() +
                              ".\n",
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Text',
                            fontSize: mediaQuery(context, "h", 28),
                            color: const Color(0xfffee095),
                            overflow: TextOverflow.ellipsis,
                            letterSpacing:
                                mediaQuery(context, "h", 1.4000000000000001),
                          ),
                        ),
                        TextSpan(
                          text: cuttext(
                              bitrixControlle.dataBitrix[i]["TITLE"].toString(),
                              40),
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Text',
                            fontSize: mediaQuery(context, "h", 30),
                            color: Theme.of(context).colorScheme.scrim,
                            letterSpacing:
                                mediaQuery(context, "h", 1.4000000000000001),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          )));
      list.add(Container(
        height: mediaQuery(context, "h", 25),
      ));
    }

    return list;
  }

  List<Widget> ListMyWidgetsCreate() {
    List<Widget> list = [];
    for (var i = 0; i < bitrixControlle.dataBitrixCreate.length.toInt(); i++) {
      list.add(GestureDetector(
          onTap: () {
            Get.to(() => bitrixActionFinish(
                bitrixControlle.dataBitrixCreate[i]["ID"],
                bitrixControlle.idBitrix,
                convertDateTime(
                        bitrixControlle.dataBitrixCreate[i]["CREATED_DATE"],
                        "dd-MM-YYYY2")
                    .toString(),
                bitrixControlle.dataBitrixCreate[i]["CREATED_BY_NAME"],
                bitrixControlle.dataBitrixCreate[i]["CREATED_BY_LAST_NAME"],
                bitrixControlle.dataBitrixCreate[i]["RESPONSIBLE_NAME"],
                bitrixControlle.dataBitrixCreate[i]["RESPONSIBLE_LAST_NAME"],
                bitrixControlle
                    .nameGroupID(
                        bitrixControlle.dataBitrixCreate[i]["GROUP_ID"])
                    .toString(),
                bitrixControlle.dataBitrixCreate[i]["TITLE"],
                bitrixControlle.dataBitrixCreate[i]["DEADLINE"],
                bitrixControlle.dataBitrixCreate[i]["REAL_STATUS"]));
          },
          child: Container(
            padding: EdgeInsets.fromLTRB(
                mediaQuery(context, "w", 35),
                mediaQuery(context, "h", 26),
                mediaQuery(context, "w", 35),
                mediaQuery(context, "h", 26)),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(mediaQuery(context, "h", 20)),
              gradient: LinearGradient(
                begin: Alignment(0.0, -1.0),
                end: Alignment(0.0, 1.0),
                colors: [
                  Theme.of(context).colorScheme.primary,
                  Theme.of(context).colorScheme.onPrimary,
                ],
                stops: [0.0, 1.0],
              ),
              border: Border.all(
                color: Color(
                    0xFFA596FF), // The color code converted to Flutter's Color class
                width: 1.0, // Border width
              ),
              boxShadow: [
                BoxShadow(
                  color: Color(0x4d000000),
                  offset: Offset(0, 2),
                  blurRadius: 20,
                ),
              ],
            ),
            child: Row(
              children: [
                SizedBox(
                  height: 24.h,
                  width: 24.w,
                  child: Image.asset('assets/ADD/Stick-note.png'),
                ),
                SizedBox(
                  width: 8,
                ),
                SizedBox(
                  width: 230.w,
                  child: RichText(
                    text: TextSpan(
                      children: <TextSpan>[
                        TextSpan(
                          text: convertDateTime(
                                  bitrixControlle.dataBitrixCreate![i]
                                      ["CREATED_DATE"],
                                  "dd-MM-YYYY2") +
                              " Created by " +
                              cuttext(
                                  bitrixControlle.dataBitrixCreate![i]
                                              ["CREATED_BY_NAME"]
                                          .toString() +
                                      " " +
                                      bitrixControlle.dataBitrixCreate![i]
                                              ["CREATED_BY_LAST_NAME"]
                                          .toString(),
                                  20) +
                              " : " +
                              bitrixControlle
                                  .nameGroupID(bitrixControlle
                                      .dataBitrixCreate![i]["GROUP_ID"])
                                  .toString() +
                              ".\n",
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Text',
                            fontSize: mediaQuery(context, "h", 28),
                            color: Theme.of(context).colorScheme.scrim,
                            overflow: TextOverflow.ellipsis,
                            letterSpacing:
                                mediaQuery(context, "h", 1.4000000000000001),
                          ),
                        ),
                        TextSpan(
                          text: cuttext(
                              bitrixControlle.dataBitrixCreate![i]["TITLE"]
                                  .toString(),
                              40),
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Text',
                            fontSize: mediaQuery(context, "h", 28),
                            color: Theme.of(context)
                                .colorScheme
                                .scrim
                                .withOpacity(0.8),
                            letterSpacing:
                                mediaQuery(context, "h", 1.4000000000000001),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          )));
      list.add(Container(
        height: mediaQuery(context, "h", 25),
      ));
    }
    return list;
  }
}
