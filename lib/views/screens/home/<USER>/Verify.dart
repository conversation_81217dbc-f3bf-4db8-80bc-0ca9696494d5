import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:mobile_scanner/mobile_scanner.dart' as mobile_scanner;
import 'package:mobile_scanner/mobile_scanner.dart';


import '../../../../controllers/verifycontroller.dart';
import 'genQrVerify.dart';

// import '../controller/mvp_ms24_controller.dart';

class CustomerFormPage extends StatelessWidget {
  CustomerFormPage({super.key});
  final verify controller = Get.put(verify());
  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Customer Service Form'),
        leading: IconButton(onPressed: (){Get.back();}, icon: Icon(Icons.arrow_back))
        ,actions: [
          IconButton(onPressed: (){
            Get.to(()=> genQRVerify());

          }, icon: Icon(Icons.verified))
        ],),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Case ID
            Obx(() => Text('📌 Case ID: ${controller.scannedData.value}',
                style: TextStyle(fontSize: 16))),
            const SizedBox(height: 10),

            // Scan QR Code Button

            // BU Dropdown
            // BU Dropdown
            Obx(() {
              if (controller.isLoading.value) {
                return const Center(
                    child: CircularProgressIndicator()); // ✅ แสดงโหลดข้อมูล
              } else if (controller.Bu_list.isEmpty) {
                return const Center(
                    child: Text("ไม่พบข้อมูล BU")); // ✅ ถ้าไม่มีข้อมูล BU
              } else {
                return DropdownButtonFormField<String>(
                  decoration: const InputDecoration(labelText: 'เลือก BU'),
                  value: controller.selectedBU.value.isNotEmpty
                      ? controller.selectedBU.value
                      : null,
                  onChanged: (value) => controller.selectedBU.value = value!,
                  items: controller.Bu_list.map((bu) {
                    return DropdownMenuItem(
                      value: bu,
                      child: Text(bu),
                    );
                  }).toList(),
                );
              }
            }),

            const SizedBox(height: 10),

            // Phone Number
            TextFormField(
              decoration: const InputDecoration(labelText: 'เบอร์โทรศัพท์'),
              keyboardType: TextInputType.phone,
              onChanged: (value) => controller.phoneNumber.value = value,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'กรุณากรอกเบอร์โทรศัพท์';
                }
                return null;
              },
            ),
            const SizedBox(height: 10),

            // ค้นหาข้อมูลลูกค้า
            ElevatedButton(
              onPressed: () => controller.fetchCustomerData(
                  controller.phoneNumber.value, controller.selectedBU.value),
              child: const Text('ค้นหาข้อมูลลูกค้า...'),
            ),
            const SizedBox(height: 20),

            // Customer Form
            Obx(() => Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Customer Name
                  TextFormField(
                    controller: controller
                        .customerNameController, // ✅ ใช้ TextEditingController
                    decoration:
                    const InputDecoration(labelText: 'ชื่อลูกค้า'),
                    onChanged: (value) =>
                    controller.customerName.value = value,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'กรุณากรอกชื่อลูกค้า';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 10),

                  // Service Type Dropdown
                  DropdownButtonFormField<String>(
                    decoration:
                    const InputDecoration(labelText: 'ประเภทบริการ'),
                    value: controller.selectedService.value.isNotEmpty
                        ? controller.selectedService.value
                        : 'PKG Verification', // ✅ ป้องกัน `null`
                    onChanged: (value) =>
                    controller.selectedService.value = value!,
                    items: [
                      'PKG Verification',
                      'Warranty Claim',
                      'Installation Service',
                      'Repair Service',
                    ].map((service) {
                      return DropdownMenuItem(
                        value: service,
                        child: Text(service),
                      );
                    }).toList(),
                  ),
                  const SizedBox(height: 20),

                  // Submit Button
                  Center(
                    child: ElevatedButton(
                      onPressed: () async {
                        if (_formKey.currentState!.validate()) {
                          await controller.notifyTelegram();

                          // แสดง Snackbar ก่อน
                          Get.snackbar(
                            "สำเร็จ", "บันทึกข้อมูลลูกค้าเรียบร้อยแล้ว!",
                            snackPosition: SnackPosition.BOTTOM,
                          );

                          // เพิ่ม Pop-up Dialog หลัง Snackbar

                        }
                      },
                      child: const Text('ยืนยันข้อมูล'),
                    ),
                  ),
/// QR CODE ///
                  // const SizedBox(height: 20),
                  // Center(
                  //   child: Column(
                  //     children: [
                  //       // ✅ Toggle สำหรับเลือกโหมดการสแกน
                  //       Obx(() => Row(
                  //         mainAxisAlignment: MainAxisAlignment.center,
                  //         children: [
                  //           ChoiceChip(
                  //             label: Text("ยืนยันการทำงาน"),
                  //             selected: controller.scanMode.value == "work",
                  //             onSelected: (selected) {
                  //               if (selected) controller.scanMode.value = "work";
                  //             },
                  //           ),
                  //           const SizedBox(width: 10),
                  //           ChoiceChip(
                  //             label: Text("ยืนยันพนักงาน"),
                  //             selected: controller.scanMode.value == "employee",
                  //             onSelected: (selected) {
                  //               if (selected) controller.scanMode.value = "employee";
                  //             },
                  //           ),
                  //         ],
                  //       )),
                  //       const SizedBox(height: 20),
                  //
                  //       Obx(() => controller.isScanning.value
                  //           ? SizedBox(
                  //         height: 300,
                  //         child: MobileScanner(
                  //           controller: controller.scannerController,
                  //           onDetect: (capture) {
                  //             if (controller.scanMode.value == "work") {
                  //               controller.handleBarcodeScan(capture);
                  //
                  //             } else {
                  //               controller.verifyCustomer(capture);
                  //             }
                  //           },
                  //         ),
                  //       )
                  //           : ElevatedButton(
                  //         onPressed: () {
                  //           controller.hasScanned.value = false; // รีเซ็ตค่าเมื่อกดปุ่มใหม่
                  //           controller.startScanning();
                  //         },
                  //         child: const Text('Scan QR Code'),
                  //       )),
                  //       const SizedBox(height: 20),
                  //
                  //       // Stop Scanning Button
                  //       Obx(() => controller.isScanning.value
                  //           ? ElevatedButton(
                  //         onPressed: controller.stopScanning,
                  //         child: const Text('Stop Scanning'),
                  //       )
                  //           : Container()),
                  //       const SizedBox(height: 20),
                  //     ],
                  //   ),
                  // )

                ],
              ),
            )),
          ],
        ),
      ),
    );
  }
}
