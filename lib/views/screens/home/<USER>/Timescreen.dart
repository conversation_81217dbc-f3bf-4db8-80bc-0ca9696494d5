import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mapp_ms24/controllers/internal/Tacontroller/timePageController.dart';
import 'package:mapp_ms24/controllers/internal/daysWork/daysWorkController.dart';
import 'package:mapp_ms24/controllers/utils/color.dart';
import 'package:mapp_ms24/views/screens/Widget/Widget.dart';

import 'package:smooth_page_indicator/smooth_page_indicator.dart';

import '../../../../controllers/internal/LeaveController/LeaveApprovalController.dart';
import '../../../../controllers/utils/widget.dart';
import '../../Widget/Timeprograssbar.dart';
import '../../Widget/progressbar.dart';

final PageController pageController2 = PageController();

DaysWorkController daysWorkController = Get.put(DaysWorkController());
TimeController timeController = Get.put(TimeController());


Widget buildTimeScreen(BuildContext context) {
  return GetBuilder<TimeController>(builder: (TimeController) {
    return SizedBox(
      width: 393.w,
      child: SingleChildScrollView(
        child: Column(
          children: [
            buildTASTATUS(
                context,
                daysWorkController.daysOfmonthWorkModel?.late_day ,
                daysWorkController.daysOfmonthWorkModel?.sick_leave,
                daysWorkController.daysOfmonthWorkModel?.personal_leave,
                daysWorkController.daysOfmonthWorkModel?.forgot_time,
                timeController.taScoreModel?.percent),
            buildTAMISSING(context, timeController.lostDayModel?.LostDay),
            buildTACHART(context, timeController.taScoreModel?.percent,
                timeController.taScoreModel?.updateDate)
          ],
        ),
      ),
    );
  });
}

Widget buildTASTATUS(
    BuildContext context, late, sick, person_leave, forget_time, score) {
  DateTime now = DateTime.now();
  String monthName = DateFormat('MMMM').format(now);

  return Row(
    mainAxisAlignment: MainAxisAlignment.spaceBetween,
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppWidget.normalText(context, 'ui_ta_status'.tr, 14.sp,
              Theme.of(context).colorScheme.scrim, FontWeight.w400),
          AppWidget.normalText(
              context, '$score', 22.sp, Theme.of(context).colorScheme.secondaryContainer, FontWeight.w700),
          AppWidget.normalText(
              context, monthName, 12.sp, Theme.of(context).colorScheme.secondaryFixedDim, FontWeight.w500),
        ],
      ),
      SizedBox(
        height: 74.h,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            AppWidget.normalText(
                context, '$late', 14.sp, Theme.of(context).colorScheme.secondaryContainer, FontWeight.w500),
            AppWidget.normalText(context, 'ui_ta_late'.tr, 12.sp,
                Theme.of(context).colorScheme.secondaryFixedDim, FontWeight.w400),
          ],
        ),
      ),
      Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          AppWidget.normalText(
              context, '$sick', 14.sp, Theme.of(context).colorScheme.secondaryContainer, FontWeight.w500),
          AppWidget.normalText(context, 'ui_ta_sick'.tr, 12.sp, Theme.of(context).colorScheme.secondaryFixedDim,
              FontWeight.w400),
        ],
      ),
      Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          AppWidget.normalText(context, '$person_leave', 14.sp,
              Theme.of(context).colorScheme.secondaryContainer, FontWeight.w500),
          AppWidget.normalText(context, 'ui_ta_levae'.tr, 12.sp,
              Theme.of(context).colorScheme.secondaryFixedDim, FontWeight.w400),
          AppWidget.normalText(context, 'ui_ta_totaltime'.tr, 12.sp,
              Theme.of(context).colorScheme.scrim, FontWeight.w500),
          AppWidget.normalText(
              context,
              daysWorkController.daysWorkModel != null
          ? '${daysWorkController.daysWorkModel!.personal_leave}/6'
            : '0', // กรณี daysWorkModel เป็น null
              12.sp,
              Theme.of(context).colorScheme.tertiary,
              FontWeight.w500),
        ],
      ),
      Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          AppWidget.normalText(context, '$forget_time', 14.sp,
              Theme.of(context).colorScheme.secondaryContainer, FontWeight.w500),
          AppWidget.normalText(context, 'ui_ta_missing'.tr, 12.sp,
              Theme.of(context).colorScheme.secondaryFixedDim, FontWeight.w400),
          AppWidget.normalText(context, 'ui_dateUpdate'.tr, 12.sp,
              Theme.of(context).colorScheme.scrim, FontWeight.w400),
          AppWidget.normalText(context,
              timeController.taScoreModel != null
                  ? timeController.taScoreModel?.updateDate : '0',
              12.sp, Theme.of(context).colorScheme.tertiary, FontWeight.w500),
        ],
      ),
    ],
  );
}

Widget buildTAMISSING(BuildContext context, data) {
  final PageController smoothController = PageController();
  return Padding(
    padding: EdgeInsets.symmetric(vertical: 5.h),
    child: Column(
      children: [
        SingleChildScrollView(
          physics: NeverScrollableScrollPhysics(),
          scrollDirection: Axis.horizontal,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                children: [
                  Row(
                    children: [
                      SizedBox(
                        height: 20.h,
                        child: AppWidget.normalText(context, 'ui_ta_missing'.tr,
                            14.sp, Theme.of(context).colorScheme.scrim, FontWeight.w400),
                      ),
                      SizedBox(
                        width: 5.w,
                      ),
                      Container(
                        margin: EdgeInsets.only(top: 5.h),
                        height: 0.5.h,
                        width: Get.width,
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.onBackground.withOpacity(0.2),
                          border: Border.all(
                            width: 0.5.w,
                            color: Theme.of(context).colorScheme.onBackground.withOpacity(0.2),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
        (data != null && data.isNotEmpty)
            ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: SizedBox(
                      height: 58.h,
                      child: PageView.builder(
                        controller: smoothController,
                        itemCount: (data.length / 4).ceil(),
                        itemBuilder: (context, pageIndex) {
                          return Row(
                            children: List.generate(
                              4,
                              (index) {
                                final itemIndex = pageIndex * 4 + index;
                                if (itemIndex < data.length) {
                                  return Padding(
                                    padding: EdgeInsets.only(left: 5.w),
                                    child: data.isNotEmpty
                                        ? buildListItem(
                                            context,
                                            data[itemIndex].getDayTH +
                                                    ' ' +
                                                    data[itemIndex].date ??
                                                "",
                                            data[itemIndex].status ?? "",
                                            10.sp,
                                            FontWeight.w600,
                                          )
                                        : buildEmtpy(
                                            context, 'ui_tamissing_emtpy'.tr),
                                  );
                                } else {
                                  // If data is not available for the slot, show an empty Container
                                  return Container();
                                }
                              },
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ],
              )
            : buildEmtpy(context, 'ui_tamissing_emtpy'.tr),
        (data != null && data.isNotEmpty)
            ? SmoothPageIndicator(
                controller: smoothController,
                count: (data.length / 4).ceil(),
                effect: SwapEffect(
                  dotHeight: 4.h,
                  dotWidth: 4.w,
                  dotColor: const Color(0xFFFEE095).withOpacity(0.5),
                  activeDotColor: const Color(0xFFFEE095),
                ),
              )
            : Container(), // ถ้า data ว่าง ให้แสดง Container เปล่า
      ],
    ),
  );
}

Widget buildListItem(BuildContext context, String title, String subtitle,
    double fontSize, FontWeight fontWeight) {
  return Container(
    width: 80.w,
    height: 57.h,
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(10.r),
      border: Border.all(
        color: Theme.of(context).colorScheme.shadow,
        width: 1.w,
      ),
      gradient: LinearGradient(
        begin: Alignment.centerLeft,
        end: Alignment.centerRight,
        colors: [
          Theme.of(context).colorScheme.primary,
          Theme.of(context).colorScheme.primary,
        ],
      ),
    ),
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AppWidget.normalText(
              context,
              title,
              14.sp,
              Theme.of(context).colorScheme.secondaryContainer,
              FontWeight.w400,
            ),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AppWidget.normalText(
              context,
              subtitle,
              12.5.sp,
              Theme.of(context).colorScheme.secondaryFixedDim,
              FontWeight.w500,
            ),
          ],
        )
      ],
    ),
  );
}

Widget buildTACHART(BuildContext context, percent, updateDate) {
  final pagecontroller = PageController();

  return GetBuilder<DaysWorkController>(builder: (dayControl) {
    return Column(
      children: [
        Container(
          height: 0.5.h,
          decoration: BoxDecoration(
            color: const Color(0xFFFCF6E4).withOpacity(0.2),
            border: Border.all(
              width: 0.5.w,
              color: const Color(0xFFFCF6E4).withOpacity(0.2),
            ),
          ),
        ),
        Container(
          height: 300.h,
          width: 358.w,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 130.w,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(vertical: 10.h),
                          child: AppWidget.normalText(
                              context,
                              'ui_ta_total_average'.tr,
                              14.sp,
                              Theme.of(context).colorScheme.scrim,
                              FontWeight.w400),
                        ),
                        Container(
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              AppWidget.normalText(context, '1 ST ', 12.sp,
                                  Theme.of(context).colorScheme.secondaryFixed, FontWeight.w400),
                              AppWidget.normalText(context, 'JAN-JUN', 12.sp,
                                  Theme.of(context).colorScheme.secondaryFixed, FontWeight.w400)
                            ],
                          ),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 10.h,
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(vertical: 5.w),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          AppWidget.normalText(context, '$percent', 22.sp,
                              Theme.of(context).colorScheme.secondaryContainer, FontWeight.w700),
                          AppWidget.normalText(context, 'ui_dateUpdate'.tr,
                              12.sp, Theme.of(context).colorScheme.scrim, FontWeight.w400),
                          AppWidget.normalText(
                              context,
                              // int.parse(timeController.taScoreModel!.TAStatus.toString()),
                              '$updateDate',
                              12.sp,
                              Theme.of(context).colorScheme.tertiary,
                              FontWeight.w500),
                          InkWell(
                            onTap: (){},
                            child: Container(
                                padding: EdgeInsets.symmetric(vertical: 10.h),
                                height: 60.h,
                                width: 64.w,
                                child:
                                    Image.asset('assets/Timmi/TimmiLovely.png')),
                          ),
                          // ? Image.asset('assets/Timmi/TimmiLovely.png')
                          // : Image.asset(
                          //     'assets/Timmi/Timmi Worring.png')),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                width: 5.w,
              ),
              Container(
                padding: const EdgeInsets.all(10),
                width: 0.5.w,
                height: 240.h,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.onBackground.withOpacity(0.2),
                  border: Border.all(
                    width: 0.5.w,
                    color: Theme.of(context).colorScheme.onBackground.withOpacity(0.2),
                  ),
                ),
              ),
              Align(
                alignment: Alignment.centerLeft,
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(left: 2.0),
                      child: SizedBox(
                        width: 216.w,
                        height: 260.h,
                        child: PageView(
                          controller: pagecontroller,
                          children: [
                            Container(
                              height: Get.height,
                              padding: EdgeInsets.symmetric(horizontal: 2),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  buildStamped(
                                      context,
                                      dayControl.daysWorkModel != null ?
                                      dayControl.daysWorkModel?.working_day : "0.0",
                                      dayControl.daysWorkModel != null ?
                                      dayControl.daysWorkModel?.late_day : "0.0"),
                                  buildABSENCED(context,
                                      dayControl.daysWorkModel != null ?
                                      dayControl.daysWorkModel?.personal_leave : "0.0"),
                                ],
                              ),
                            ),
                            Container(
                              height: Get.height,
                              width: double.infinity,
                              padding: EdgeInsets.symmetric(horizontal: 2),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  buildSicked(context,dayControl.daysWorkModel !=null ?
                                      dayControl.daysWorkModel!.sick_leave: "0.0"),
                                  buildVacation(context,dayControl.daysWorkModel !=null ?
                                      dayControl.daysWorkModel!.vacation_leave:"0.0"),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: SmoothPageIndicator(
                        controller: pagecontroller,
                        count: 2,
                        effect: SwapEffect(
                            dotColor: const Color(0xFFFEE095).withOpacity(0.5),
                            // Change dot color
                            activeDotColor: const Color(0xFFFEE095),
                            // Change active dot color
                            dotHeight: 4.h,
                            // Change dot height
                            dotWidth: 4.w,
                            spacing: 8.w // Change dot width
                            // Change spacing between dots
                            ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  });
}

Widget buildABSENCED(context, value) {
  // double value = 9;
  double valueCheck = double.parse(value);
  double max = 6;
  return SizedBox(
    height: Get.height,
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.symmetric(vertical: 10.h),
          child: AppWidget.normalText(context, 'ui_ta_levae'.tr, 14.sp,
              Theme.of(context).colorScheme.scrim, FontWeight.w400),
        ),
        SizedBox(
          height: 180.h,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          AppWidget.normalText(context, "${value} ", 14.sp,
                              value == max ? Theme.of(context).colorScheme.tertiaryContainer : Theme.of(context).colorScheme.secondaryContainer, FontWeight.w400),
                          valueCheck >= max
                              ? AppWidget.normalText(context, "ui_chart_max".tr,
                                  14.sp, Theme.of(context).colorScheme.tertiaryContainer, FontWeight.w400)
                              : AppWidget.normalText(context, "", 14.sp,
                                  const Color(0xFFFF3E3E), FontWeight.w400),
                        ],
                      ),
                      AppWidget.normalText(
                          context,
                          'ui_ta_levaechart_general'.tr,
                          12.sp,
                          Theme.of(context).colorScheme.secondaryFixedDim,
                          FontWeight.w500),
                      AppWidget.normalText(context, 'ui_ta_alltotal'.tr, 12.sp,
                          Theme.of(context).colorScheme.scrim, FontWeight.w400),
                      AppWidget.normalText(context, '${value}/${max.toInt()}',
                          12.sp, value == max ? Theme.of(context).colorScheme.tertiaryContainer : Theme.of(context).colorScheme.secondaryFixedDim, FontWeight.w500),
                    ],
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      AppWidget.normalText(
                          context,
                          '${valueCheck >= max ? valueCheck - max : '0'}',
                          14.sp,
                          Theme.of(context).colorScheme.secondaryContainer,
                          FontWeight.w400),
                      AppWidget.normalText(context, 'ui_ta_levaechart_over'.tr,
                          12.sp, Theme.of(context).colorScheme.secondaryFixedDim, FontWeight.w500),
                      AppWidget.normalText(context, 'ui_ta_alltotal'.tr, 12.sp,
                          Theme.of(context).colorScheme.secondaryFixed, FontWeight.w500),
                      AppWidget.normalText(
                          context,timeController.taScoreModel !=null ?
                          timeController.taScoreModel?.percent : "0.0",
                          12.sp,
                          Theme.of(context).colorScheme.secondaryFixed,
                          FontWeight.w500)
                    ],
                  )
                ],
              ),
              SizedBox(
                width: 30.w,
                height: Get.height.h,
                child: buildProgressbar(
                  context,
                  value,
                  max,
                ),
              ),
            ],
          ),
        ),
      ],
    ),
  );
}

Widget buildStamped(context, value, late) {
  // double value = 234;
  double max = 365;
  return Padding(
    padding: const EdgeInsets.only(left: 4.0),
    child: SizedBox(
      height: Get.height,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.symmetric(vertical: 10.h),
            child: AppWidget.normalText(context, 'ui_ta_stampedchart'.tr, 14.sp,
                Theme.of(context).colorScheme.scrim, FontWeight.w400),
          ),
          SizedBox(
            height: 180.h,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        AppWidget.normalText(context, '${value}', 14.sp,
                            Theme.of(context).colorScheme.secondaryContainer, FontWeight.w400),
                        AppWidget.normalText(context, 'ui_days'.tr, 12.sp,
                            Theme.of(context).colorScheme.secondaryFixedDim, FontWeight.w500),
                        AppWidget.normalText(context, 'ui_ta_alltotal'.tr,
                            12.sp, Theme.of(context).colorScheme.scrim, FontWeight.w400),
                        AppWidget.normalText(context, '${value}/${max.toInt()}',
                            12.sp, Theme.of(context).colorScheme.secondaryFixedDim, FontWeight.w500),
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        AppWidget.normalText(context, '$late', 14.sp,
                            Theme.of(context).colorScheme.secondaryContainer, FontWeight.w400),
                        AppWidget.normalText(context, 'ui_ta_late'.tr, 12.sp,
                            Theme.of(context).colorScheme.secondaryFixedDim, FontWeight.w400),
                        AppWidget.normalText(context, 'ui_ta_alltotal'.tr,
                            12.sp, Theme.of(context).colorScheme.scrim, FontWeight.w500),
                        AppWidget.normalText(context, '$late', 12.sp,
                            Theme.of(context).colorScheme.secondaryFixedDim.withOpacity(0.6), FontWeight.w500)
                      ],
                    )
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 10.0),
                  child: SizedBox(
                    width: 30.w,
                    height: Get.height.h,
                    child: buildProgressbar(
                      context,
                      value,
                      max,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}

Widget buildSicked(context, value) {
  // double value = 12;
  var checkValue = double.parse(value);
  double max = 30;
  return Padding(
    padding: const EdgeInsets.only(left: 5),
    child: SizedBox(
      height: Get.height,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.symmetric(vertical: 10.h),
            child: AppWidget.normalText(context, 'ui_chart_sicked'.tr, 14.sp,
                Theme.of(context).colorScheme.scrim, FontWeight.w400),
          ),
          SizedBox(
            height: 180.h,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        AppWidget.normalText(context, '$value', 14.sp,
                            Theme.of(context).colorScheme.secondaryContainer, FontWeight.w400),
                        AppWidget.normalText(context, 'ui_day'.tr, 12.sp,
                            Theme.of(context).colorScheme.secondaryFixedDim, FontWeight.w500),
                        AppWidget.normalText(
                            context,
                            'ui_chart_vacation_totaltime'.tr,
                            12.sp,
                            Theme.of(context).colorScheme.scrim,
                            FontWeight.w500),
                        AppWidget.normalText(context, '$value/$max', 12.sp,
                            Theme.of(context).colorScheme.secondaryFixedDim, FontWeight.w500),
                      ],
                    ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 10.0),
                  child: SizedBox(
                    width: 30.w,
                    height: Get.height.h,
                    child: buildProgressbar(
                      context,
                      value,
                      max,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}

Widget buildVacation(context, value) {
  // double value = 0;
  var checkValue = double.parse(value);
  double max = 9;
  return SizedBox(
    height: Get.height,
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.symmetric(vertical: 10.h),
          child: AppWidget.normalText(
            context,
            'ui_chart_vacation'.tr,
            14.sp,
            Theme.of(context).colorScheme.scrim,
            FontWeight.w400,
          ),
        ),
        SizedBox(
          height: 180.h,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      AppWidget.normalText(
                        context,
                        '$value',
                        14.sp,
                        value == max
                            ? Theme.of(context).colorScheme.tertiaryContainer
                            : Theme.of(context).colorScheme.secondaryContainer,
                        FontWeight.w400,
                      ),
                      AppWidget.normalText(
                        context,
                        'ui_day'.tr,
                        12.sp,
                        Theme.of(context).colorScheme.secondaryFixedDim,
                        FontWeight.w400,
                      ),
                      AppWidget.normalText(
                        context,
                        'ui_chart_vacation_totaltime'.tr,
                        12.sp,
                        Theme.of(context).colorScheme.scrim,
                        FontWeight.w400,
                      ),
                      AppWidget.normalText(
                        context,
                        value == max
                            ? '${value}/${max} ${'ui_chart_max'.tr}'
                            : '${value}/${max}',
                        12.sp,
                        value == max ? Theme.of(context).colorScheme.tertiaryContainer : Theme.of(context).colorScheme.secondaryFixedDim,
                        FontWeight.w400,
                      ),
                    ],
                  ),
                ],
              ),
              SizedBox(
                width: 30.w,
                height: Get.height.h,
                child: buildProgressbarCount(
                  context,
                  checkValue,
                  max,
                ),
              ),
            ],
          ),
        ),
      ],
    ),
  );
}
