import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mapp_ms24/controllers/utils/widget.dart';

import '../../../Widget/Widget.dart';

class StandBenefit extends StatefulWidget {
  const StandBenefit({super.key});

  @override
  State<StandBenefit> createState() => _StandBenefitState();
}

class _StandBenefitState extends State<StandBenefit> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: Get.width,
        height: Get.height,
        decoration: BoxDecoration(
            gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
              const Color(0xFF1F1C2F).withOpacity(1),
              const Color(0xFF0D0C14).withOpacity(1),
            ])),
        child: Padding(
          padding: const EdgeInsets.only(top: 40.0),
          child: Column(
            children: [
              buildAppBarWithIcon(context, Icons.person, 'สวัสดิการพื้นฐาน'),
              buildBody()
            ],
          ),
        ),
      ),
    );
  }

  Widget buildBody() {
    return Container(
      height: 700.h,
      padding: EdgeInsets.symmetric(vertical: 20.h, horizontal: 10.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AppWidget.normalText(context, 'ui_detail'.tr, 14.sp,
                      const Color(0xFFF6F6F6), FontWeight.w400)
                ],
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        buildDot(15.w, 15.h, const Color(0xFFFEE095)),
                        SizedBox(
                          width: 10.w,
                        ),
                        AppWidget.normalText(context, 'ui_welfareout'.tr, 16.sp,
                            const Color(0xFFFEE095), FontWeight.w400)
                      ],
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(
                          horizontal: 30.h, vertical: 10.h),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          AppWidget.normalText(
                              context,
                              '- ${'ui_welfareout7'.tr}',
                              14.sp,
                              const Color(0xFFF6F6F6),
                              FontWeight.w400),
                          AppWidget.normalText(
                              context,
                              '- ${'ui_welfareout5'.tr}',
                              14.sp,
                              const Color(0xFFF6F6F6),
                              FontWeight.w400),
                          AppWidget.normalText(
                              context,
                              '- ${'ui_welfareout11'.tr}',
                              14.sp,
                              const Color(0xFFF6F6F6),
                              FontWeight.w400),
                          AppWidget.normalText(
                              context,
                              '- ${'ui_welfareout12'.tr}',
                              14.sp,
                              const Color(0xFFF6F6F6),
                              FontWeight.w400)
                        ],
                      ),
                    )
                  ],
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 20.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        buildDot(15.w, 15.h, const Color(0xFFFEE095)),
                        SizedBox(
                          width: 10.w,
                        ),
                        AppWidget.normalText(context, 'ui_helpbudget'.tr, 16.sp,
                            const Color(0xFFFEE095), FontWeight.w400)
                      ],
                    ),
                    Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 30.h, vertical: 5.h),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          AppWidget.normalText(
                              context,
                              '- ${'ui_welfareout3'.tr}',
                              14.sp,
                              const Color(0xFFF6F6F6),
                              FontWeight.w400),
                          AppWidget.normalText(
                              context,
                              '- ${'ui_welfareout1'.tr}',
                              14.sp,
                              const Color(0xFFF6F6F6),
                              FontWeight.w400),
                          AppWidget.normalText(
                              context,
                              '- ${'ui_welfareout2'.tr}',
                              14.sp,
                              const Color(0xFFF6F6F6),
                              FontWeight.w400),
                          AppWidget.normalText(
                              context,
                              '- ${'ui_welfareout4'.tr}',
                              14.sp,
                              const Color(0xFFF6F6F6),
                              FontWeight.w400)
                        ],
                      ),
                    )
                  ],
                ),
              ),
            ],
          ),
          InkWell(
            onTap: () {
              Get.toNamed('StandartForm');
            },
            child: Container(
                padding: EdgeInsets.symmetric(horizontal: 20.w),
                child: AppWidget.normalText(context, 'btn_nextbasic'.tr, 16.sp,
                    const Color(0xFFF6F6F6), FontWeight.w400)),
          )
        ],
      ),
    );
  }
}
