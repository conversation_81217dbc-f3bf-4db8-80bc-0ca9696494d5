import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../controllers/internal/BenefitController /BenefitController.dart';
import '../../../../../controllers/utils/widget.dart';
import '../../../Widget/Widget.dart';

class HealthBenefit extends StatefulWidget {
  const HealthBenefit({super.key});

  @override
  State<HealthBenefit> createState() => _HealthBenefitState();
}

class _HealthBenefitState extends State<HealthBenefit> {
  BenefitController benCrt = Get.find<BenefitController>();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: Get.width,
        height: Get.height,
        decoration: BoxDecoration(
            gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
              const Color(0xFF1F1C2F).withOpacity(1),
              const Color(0xFF0D0C14).withOpacity(1),
            ])),
        child: Padding(

          padding: const EdgeInsets.only(top: 20.0),
          child: Column(
            children: [
              buildAppBarWithIcon(context, Icons.person, 'ui_headhealth'.tr),
              buildBody()
            ],
          ),
        ),
      ),
    );
  }

  Widget buildBody() {
    return Container(
      height: 700.h,
      width: Get.width,
      padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 20.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 10.w),
                child: AppWidget.normalText(context, 'ui_selectinsurance'.tr,
                    12.sp, const Color(0xFFF6F6F6), FontWeight.w400),
              ),
              SizedBox(height: 10.h,),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  InkWell(
                    child: buildIPD(),
                    onTap: () {
                      setState(() {
                        if (benCrt.PD.value.obs == false.obs) {
                          benCrt.CheckPD();
                        }
                      });
                    },
                  ),
                  SizedBox(width: 20.w,),
                  InkWell(
                    child: buildOPD(),
                    onTap: () {
                      setState(() {
                        if (benCrt.PD.value.obs == true.obs) {
                          benCrt.CheckPD();
                        }
                      });
                    },
                  )
                ],
              ),
              SizedBox(height: 10.h,),
              Row(
                children: [
                  AppWidget.normalText(
                      context,
                      benCrt.PD.value ? 'ui_conditionIPD'.tr : 'ui_conditionOPD'.tr,
                      16.sp,
                      const Color(0xFFF6F6F6),
                      FontWeight.w400),
                ],
              ),
              SizedBox(height: 10.h,),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  buildDot(20.h,20.w,const Color(0xFFFEE095)),
                  SizedBox(width: 10.w,),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      AppWidget.normalText(
                          context,
                          'ui_messageIPD_thai'.tr,
                          12.sp,
                          const Color(0xFFF6F6F6),
                          FontWeight.w400),

                    ],
                  ),
                ],
              )
            ],
          ),
          const Spacer(),
          KeyButton()
        ],
      ),
    );
  }

  Widget buildIPD() {
    return Container(
      height: 70.h,
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(10)),
        color: Color(0xFF0D0C14),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          AppWidget.normalText(context, 'btn_selectIPD'.tr, 12.sp,
              const Color(0xFFF6F6F6), FontWeight.w400),
        ],
      ),
    );
  }

  Widget buildOPD() {
    return Container(
      height: 70.h,
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(10)),
        color: Color(0xFF0D0C14),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          AppWidget.normalText(context, 'btn_selectOPD'.tr, 12.sp,
              const Color(0xFFF6F6F6), FontWeight.w400),

        ],
      ),
    );
  }

  Widget KeyButton() {
    return Container(
     child: buttonSave(context, 'btn_statusWithdrow'.tr, () async {
       Get.toNamed('InsuranceStatusScreen');
     }),

    );
  }
}
