import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:mapp_ms24/controllers/internal/HealthController/HealthController.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';
import 'package:mapp_ms24/views/screens/home/<USER>';
import '../../../../../controllers/internal/BenefitController /BenefitController.dart';
import '../../../../../controllers/utils/widget.dart';
import '../../../Widget/Widget.dart';

class HealthForm extends StatefulWidget {
  const HealthForm({super.key});

  @override
  State<HealthForm> createState() => _HealthFormState();
}

class _HealthFormState extends State<HealthForm> {
  BenefitController benCrt = Get.find<BenefitController>();
  HealthController healthController = Get.find<HealthController>();
  ProfileController profile = Get.find<ProfileController>();
  @override
  void initState() {
    super.initState();
    benCrt.information();
    benCrt.ClearData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: Get.width,
        height: Get.height,
        decoration: BoxDecoration(
            gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
              const Color(0xFF1F1C2F).withOpacity(1),
              const Color(0xFF0D0C14).withOpacity(1),
            ])),
        child: Padding(
          padding: const EdgeInsets.only(top: 20.0),
          child: Stack(
            children: [
              SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.only(top: 100.h),
                  child: Column(
                    children: [
                      buildBody(),
                      // buildChoose(context, 'btn_save'.tr,  () async {}),
                      buttonSAVE(context, 100.h, 0.0, 'btn_save'.tr),
                    ],
                  ),
                ),
              ),
              buildAppBarWithIcon(
                  context, Icons.person, 'ui_headhealthfrom4'.tr),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildBody() {
    return buildForm1(); // Replace with your first page widget
  }

  Widget buildForm1() {
    return Column(
      children: [
        Container(
          margin: EdgeInsets.only(left: 35.w, bottom: 10.h),
          alignment: Alignment.topLeft,
          child: AppWidget.normalText(context, "ui_headtype".tr.toString(),
              14.sp, const Color(0xFFF6F6F6), FontWeight.w400),
        ),
        buildDropdown(context, 'dr_select'.tr.obs, benCrt.listPatientType,
            benCrt.updateSelectedPatientType, benCrt.selectedPatient),
        Container(
          alignment: Alignment.topLeft,
          margin: EdgeInsets.only(
              top: mediaQuery(context, "h", 23),
              left: mediaQuery(context, "w", 67)),
          child: Text(
            "ui_headhealthamountnet".tr.toString(),
            style: TextStyle(
              fontFamily: 'SukhumvitSet-SemiBold',
              fontSize: mediaQuery(context, "h", 28),
              color: const Color(0xfffcf6e4),
              letterSpacing: mediaQuery(context, "h", 1.12),
              fontWeight: FontWeight.w700,
            ),
            textAlign: TextAlign.left,
          ),
        ),
        Container(
          margin: EdgeInsets.only(
              top: mediaQuery(context, "h", 16),
              left: mediaQuery(context, "w", 44),
              right: mediaQuery(context, "w", 44)),
          child: Container(
            width: mediaQuery(context, "w", 760),
            height: mediaQuery(context, "h", 120),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(mediaQuery(context, "h", 20)),
              color: const Color(0xff1f1c2f),
              boxShadow: [
                BoxShadow(
                  color: const Color(0x4d000000),
                  offset: Offset(0, 2),
                  blurRadius: 20,
                ),
              ],
            ),
            child: Container(
              alignment: Alignment.centerLeft,
              margin: EdgeInsets.only(left: mediaQuery(context, "w", 47)),
              child: new TextField(
                cursorColor: Colors.black,
                controller: benCrt.TotalExpensesController,
                style: new TextStyle(
                  color: Color(0xffFCF6E4),
                  fontSize: mediaQuery(context, "h", 28),
                  fontFamily: "SukhumvitSet-Text",
                ),
                decoration: new InputDecoration(
                  counterText: '',
                  labelText: "tf_amountroom".tr.toString(),
                  labelStyle: TextStyle(
                    color: Color(0xffFCF6E4),
                    fontSize: mediaQuery(context, "h", 28),
                    fontFamily: "SukhumvitSet-Text",
                  ),
//                  icon: Image.asset(pictel),
                  enabledBorder: const UnderlineInputBorder(
                    borderSide: const BorderSide(color: Colors.transparent),
                  ),
                  focusedBorder: const UnderlineInputBorder(
                    borderSide: const BorderSide(color: Colors.transparent),
                  ),
                ),
              ),
            ),
          ),
        ),
        Container(
          alignment: Alignment.topLeft,
          margin: EdgeInsets.only(
              top: mediaQuery(context, "h", 23),
              left: mediaQuery(context, "w", 67)),
          child: Text(
            "ui_surgerycost".tr.toString(),
            style: TextStyle(
              fontFamily: 'SukhumvitSet-SemiBold',
              fontSize: mediaQuery(context, "h", 28),
              color: const Color(0xfffcf6e4),
              letterSpacing: mediaQuery(context, "h", 1.12),
              fontWeight: FontWeight.w700,
            ),
            textAlign: TextAlign.left,
          ),
        ),
        Container(
          margin: EdgeInsets.only(
              top: mediaQuery(context, "h", 16),
              left: mediaQuery(context, "w", 44),
              right: mediaQuery(context, "w", 44)),
          child: Container(
            width: mediaQuery(context, "w", 760),
            height: mediaQuery(context, "h", 120),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(mediaQuery(context, "h", 20)),
              color: const Color(0xff1f1c2f),
              boxShadow: [
                BoxShadow(
                  color: const Color(0x4d000000),
                  offset: Offset(0, 2),
                  blurRadius: 20,
                ),
              ],
            ),
            child: Container(
              alignment: Alignment.centerLeft,
              margin: EdgeInsets.only(left: mediaQuery(context, "w", 47)),
              child: new TextField(
                cursorColor: Colors.black,
                controller: benCrt.OperateController,
                style: new TextStyle(
                  color: Color(0xffFCF6E4),
                  fontSize: mediaQuery(context, "h", 28),
                  fontFamily: "SukhumvitSet-Text",
                ),
                decoration: new InputDecoration(
                  counterText: '',
                  labelText: "tf_amountroom".tr.toString(),
                  labelStyle: TextStyle(
                    color: Color(0xffFCF6E4),
                    fontSize: mediaQuery(context, "h", 28),
                    fontFamily: "SukhumvitSet-Text",
                  ),
//                  icon: Image.asset(pictel),
                  enabledBorder: const UnderlineInputBorder(
                    borderSide: const BorderSide(color: Colors.transparent),
                  ),
                  focusedBorder: const UnderlineInputBorder(
                    borderSide: const BorderSide(color: Colors.transparent),
                  ),
                ),
              ),
            ),
          ),
        ),
        buildFormUpload(),
      ],
    );
  }

  Widget buildFormUpload() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 10.0.h),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AppWidget.normalText(context, 'ui_doc'.tr, 14.sp,
                const Color(0xFFF6F6F6), FontWeight.w400),
            Container(
              margin: EdgeInsets.only(top: 10.h),
              height: 55.h,
              width: Get.width,
              decoration: BoxDecoration(
                color: const Color(0xFF302C49),
                borderRadius: BorderRadius.circular(8.r),
                boxShadow: const [
                  BoxShadow(
                    color: Color(0x40000000),
                    offset: Offset(0, 4),
                    blurRadius: 4,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.all(10.0),
                child: InkWell(
                  onTap: () {
                    healthController.selectimage(context);
                    setState(() {});
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      healthController.dataLinkImage.length > 0
                          ? AppWidget.normalText(
                              context,
                              healthController.dataLinkImage,
                              14.sp,
                              const Color(0xFFF6F6F6).withOpacity(0.6),
                              FontWeight.w400,
                            )
                          : AppWidget.normalText(
                              context,
                              'btn_uploadimmage'.tr,
                              14.sp,
                              const Color(0xFFF6F6F6).withOpacity(0.6),
                              FontWeight.w400,
                            ),
                      SizedBox(
                        height: 20.h,
                        width: 20.w,
                        child: Image.asset('assets/ADD/Image.png'),
                      )
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildChoose(
      BuildContext context, String title, Future Function()? onTapCallback) {
    return Container(
      margin: EdgeInsets.only(top: 130.h),
      alignment: Alignment.topCenter,
      child: GestureDetector(
        onTap: () async {
          if (onTapCallback != null) {
            // Invoke the callback if provided
            onTapCallback();
          }
        },
        child: Container(
          alignment: Alignment.center,
          width: 135.w,
          height: 50.h,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(50.0),
            color: const Color(0xff7f420c),
            boxShadow: const [
              BoxShadow(
                color: Color(0x4d000000),
                offset: Offset(0, 2),
                blurRadius: 20,
              ),
            ],
          ),
          child: AppWidget.boldText(
              context, title, 14.sp, const Color(0xFFF6F6F6), FontWeight.w400),
        ),
      ),
    );
  }

  UploadImage(context) {
    Stack(
      children: [
        Container(
          margin: EdgeInsets.only(top: 20.h, left: 33.w),
          child: AppWidget.normalText(context, "ui_doc".tr.toString(), 12.sp,
              const Color(0xFFF6F6F6), FontWeight.w400),
        ),
        Container(
          margin: EdgeInsets.only(top: 6.h, left: 22.w, right: 22.w),
          child: GestureDetector(
            onTap: () {
              // healthController.selectimage(context);
            },
            child: Container(
              alignment: Alignment.center,
              width: 300.w,
              height: 60.h,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.h),
                color: const Color(0xff1f1c2f),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0x4d000000),
                    offset: Offset(0, 2),
                    blurRadius: 20,
                  ),
                ],
              ),
              child: Stack(
                children: <Widget>[
                  Container(
                    alignment: Alignment.centerLeft,
                    margin: EdgeInsets.only(left: 23.w),
                    child: AppWidget.normalText(
                        context,
                        "btn_imgUpload".tr.toString(),
                        12.sp,
                        const Color(0xFFF6F6F6),
                        FontWeight.w400),
                  ),
                  Container(
                    alignment: Alignment.centerRight,
                    margin: EdgeInsets.only(right: 14.w),
                    child: SvgPicture.string(
                      '<svg viewBox="713.0 1315.0 33.0 44.0" ><path transform="translate(713.0, 1315.0)" d="M 19.25 11.6875 L 19.25 0 L 2.0625 0 C 0.9195312261581421 0 0 0.9195312261581421 0 2.0625 L 0 41.9375 C 0 43.08046722412109 0.9195312261581421 44 2.0625 44 L 30.9375 44 C 32.08046722412109 44 33 43.08046722412109 33 41.9375 L 33 13.75 L 21.3125 13.75 C 20.17812538146973 13.75 19.25 12.82187461853027 19.25 11.6875 Z M 24.85140609741211 30.25085830688477 L 19.25 30.25085830688477 L 19.25 37.12585830688477 C 19.25 37.88554763793945 18.63468742370605 38.50085830688477 17.875 38.50085830688477 L 15.125 38.50085830688477 C 14.36531257629395 38.50085830688477 13.75 37.88554763793945 13.75 37.12585830688477 L 13.75 30.25085830688477 L 8.148593902587891 30.25085830688477 C 6.921406269073486 30.25085830688477 6.308671951293945 28.76499938964844 7.180078029632568 27.89960861206055 L 15.46617221832275 19.67539024353027 C 16.03765678405762 19.10734367370605 16.96062469482422 19.10734367370605 17.5321102142334 19.67539024353027 L 25.81820297241211 27.89960861206055 C 26.69046783447266 28.76499938964844 26.07859420776367 30.25085830688477 24.85140609741211 30.25085830688477 Z M 32.3984375 9.0234375 L 23.98515701293945 0.6015625 C 23.59843826293945 0.21484375 23.07421875 0 22.52421951293945 0 L 22 0 L 22 11 L 33 11 L 33 10.47578144073486 C 33 9.934374809265137 32.78515625 9.41015625 32.3984375 9.0234375 Z" fill="#fcf6e4" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                      allowDrawingOutsideViewBox: true,
                      height: 22.h,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  List<Widget> ListIMG(context) {
    List<Widget> list = [];
    for (var i = 0; i < healthController.dataLinkImage.length.toInt(); i++) {
      list.add(Container(
        height: 10.h,
        width: 15.w,
      ));
      list.add(
        Container(
          width: 200.w,
          height: 35.h,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.h),
            color: const Color(0xff302c49),
          ),
          child: Stack(
            children: <Widget>[
              Container(
                alignment: Alignment.centerLeft,
                margin: EdgeInsets.only(left: 12.w, right: 12.w),
                child: AppWidget.normalText(context, 'btn_selectOPD'.tr, 12.sp,
                    const Color(0xFFF6F6F6), FontWeight.w400),
              ),
              Container(
                alignment: Alignment.centerRight,
                margin: EdgeInsets.only(right: 10.w),
                child: GestureDetector(
                  onTap: () {
                    healthController.dataLinkImage.removeAt(i);
                  },
                  child: SvgPicture.string(
                    '<svg viewBox="282.0 1321.0 33.0 33.0" ><path transform="translate(277.83, 1316.83)" d="M 20.66666603088379 4.166666507720947 C 11.5421667098999 4.166666507720947 4.166666507720947 11.54216575622559 4.166666507720947 20.66666412353516 C 4.166666507720947 29.79116439819336 11.54216575622559 37.16666412353516 20.66666603088379 37.16666412353516 C 29.79116821289063 37.16666412353516 37.16666793823242 29.79116821289063 37.16666793823242 20.66666793823242 C 37.16666793823242 11.5421667098999 29.79116439819336 4.166666507720947 20.66666603088379 4.166666507720947 Z M 28.91666603088379 26.59016418457031 L 26.59016609191895 28.91666412353516 L 20.66666603088379 22.********** L 14.74316596984863 28.91666412353516 L 12.41666603088379 26.59016418457031 L 18.34016609191895 20.66666412353516 L 12.41666603088379 14.74316501617432 L 14.74316596984863 12.41666507720947 L 20.66666603088379 18.34016418457031 L 26.59016609191895 12.41666507720947 L 28.91666603088379 14.74316501617432 L 22.********** 20.66666412353516 L 28.91666603088379 26.59016418457031 Z" fill="#fcf6e4" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                    allowDrawingOutsideViewBox: true,
                    height: 25.h,
                  ),
                ),
              )
            ],
          ),
        ),
      );
      list.add(Container(
        height: 5.h,
        width: 5.w,
      ));
    }
    return list;
  }

  Widget buttonSAVE(context, height, bottom, title) {
    return Container(
      margin: EdgeInsets.only(top: height, bottom: bottom),
      child: Stack(
        children: <Widget>[
          Container(
            alignment: Alignment.topCenter,
            child: GestureDetector(
              onTap: () {
                if (benCrt.selectedPatient.value.toString() == '' ||
                    benCrt.OperateController.text == '' ||
                    benCrt.TotalExpensesController.text == '') {
                  Fluttertoast.showToast(
                      msg: "กรุณาใส่ข้อมูลให้ครบถ้วน",
                      toastLength: Toast.LENGTH_SHORT,
                      gravity: ToastGravity.TOP,
                      backgroundColor: Colors.red,
                      textColor: Colors.black,
                      fontSize: 16.0);
                } else {
                  ConfirmInformation(context);
                }
              },
              child: Container(
                alignment: Alignment.center,
                height: mediaQuery(context, "h", 110),
                width: mediaQuery(context, "w", 250),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(51.0),
                  color: const Color(0xff7f420c),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0x4d000000),
                      offset: Offset(0, 2),
                      blurRadius: 20,
                    ),
                  ],
                ),
                child: AppWidget.boldText(context, title, 14.sp,
                    const Color(0xFFF6F6F6), FontWeight.w400),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void ConfirmInformation(BuildContext context) {
    showDialog(
        context: context,
        builder: (BuildContext context) {
          return Container(
            child: BackdropFilter(
              filter: ImageFilter.blur(
                sigmaX: 2.5,
                sigmaY: 2.5,
              ),
              child: AlertDialog(
                backgroundColor: Color(0xff5F569B),
                titlePadding: EdgeInsets.all(1.0),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.all(
                    Radius.circular(
                      mediaQuery(context, 'h', 90),
                    ),
                  ),
                ),
                title: Container(
                  width: mediaQuery(context, 'w', 450),
                  height: mediaQuery(context, 'h', 600),
                  decoration: BoxDecoration(
                    borderRadius:
                        BorderRadius.circular(mediaQuery(context, "h", 90)),
                    color: const Color(0xff1f1c2f),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0x1a000000),
                        offset: Offset(0, 1),
                        blurRadius: 2,
                      ),
                    ],
                  ),
                  child: Container(
                    margin: EdgeInsets.only(top: mediaQuery(context, 'h', 50)),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: <Widget>[
                        Container(
                            child: AppWidget.boldText(
                                context,
                                "btn_details".tr.toString(),
                                14.sp,
                                const Color(0xffFEE095),
                                FontWeight.w400)),
                        Container(
                            padding: EdgeInsets.only(
                                top: mediaQuery(context, 'h', 30)),
                            child: AppWidget.boldText(
                                context,
                                "btn_headhealth".tr.toString(),
                                14.sp,
                                const Color.fromRGBO(246, 246, 246, 0.9),
                                FontWeight.w500)),
                        Container(
                          // padding: EdgeInsets.only(
                          //     top: mediaQuery(context, 'h', 30)),
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  AppWidget.boldText(
                                      context,
                                      'ui_headtype'.tr.toString() + ' :  ',
                                      14.sp,
                                      const Color.fromRGBO(246, 246, 246, 0.9),
                                      FontWeight.w500),
                                  SizedBox(width: mediaQuery(context, "w", 10)),
                                  AppWidget.boldText(
                                      context,
                                      benCrt.selectedPatient.value.toString(),
                                      14.sp,
                                      const Color.fromRGBO(246, 246, 246, 0.9),
                                      FontWeight.w500),
                                ],
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  AppWidget.boldText(
                                      context,
                                      'จำนวนเงินรักษารวม :  ',
                                      14.sp,
                                      const Color.fromRGBO(246, 246, 246, 0.9),
                                      FontWeight.w500),
                                  SizedBox(width: mediaQuery(context, "w", 10)),
                                  AppWidget.boldText(
                                      context,
                                      benCrt.TotalExpensesController.text
                                          .toString(),
                                      14.sp,
                                      const Color.fromRGBO(246, 246, 246, 0.9),
                                      FontWeight.w500),
                                  SizedBox(width: mediaQuery(context, "w", 10)),
                                  AppWidget.boldText(
                                      context,
                                      benCrt.monetary.toString(),
                                      14.sp,
                                      const Color.fromRGBO(246, 246, 246, 0.9),
                                      FontWeight.w500),
                                ],
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  AppWidget.boldText(
                                      context,
                                      'จำนวนเงินผ่าตัด :  ',
                                      14.sp,
                                      const Color.fromRGBO(246, 246, 246, 0.9),
                                      FontWeight.w500),
                                  SizedBox(width: mediaQuery(context, "w", 10)),
                                  AppWidget.boldText(
                                      context,
                                      benCrt.OperateController.text.toString(),
                                      14.sp,
                                      const Color.fromRGBO(246, 246, 246, 0.9),
                                      FontWeight.w500),
                                  SizedBox(width: mediaQuery(context, "w", 10)),
                                  AppWidget.boldText(
                                      context,
                                      benCrt.monetary.toString(),
                                      14.sp,
                                      const Color.fromRGBO(246, 246, 246, 0.9),
                                      FontWeight.w500),
                                ],
                              ),
                              SizedBox(width: mediaQuery(context, "w", 10)),
                              AppWidget.boldText(
                                  context,
                                  "btn_details6".tr.toString(),
                                  14.sp,
                                  const Color.fromRGBO(246, 246, 246, 0.9),
                                  FontWeight.w500),
                              Padding(
                                padding: EdgeInsets.only(
                                    top: mediaQuery(context, 'h', 80)),
                                child: Material(
                                  elevation: 5.0,
                                  borderRadius: BorderRadius.circular(20.0),
                                  color: Color(0xff7f420c),
                                  child: MaterialButton(
                                      minWidth:
                                          MediaQuery.of(context).size.width /
                                              2.5,
                                      padding: EdgeInsets.fromLTRB(
                                          mediaQuery(context, "w", 10.0),
                                          mediaQuery(context, "h", 10.0),
                                          mediaQuery(context, "w", 10.0),
                                          mediaQuery(context, "h", 15.0)),
                                      child: AppWidget.boldText(
                                          context,
                                          "btn_likeCreditConfirmok"
                                              .tr
                                              .toString(),
                                          14.sp,
                                          const Color.fromRGBO(
                                              246, 246, 246, 0.9),
                                          FontWeight.w500),
                                      onPressed: () async {
                                        Navigator.of(context).pop();
                                        benCrt.WithdrawHealthBenefits(context);
                                        // benCrt.test(context);
                                        // SaveData(context);
                                      }),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        });
  }

  Widget SaveData(BuildContext context) {
    return Obx(() {
      if (benCrt.HealthWelfareStatus == 1) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          showDialog(
            barrierDismissible: false,
            context: context,
            builder: (BuildContext context) {
              return Container(
                child: BackdropFilter(
                  filter: ImageFilter.blur(
                    sigmaX: 2.5,
                    sigmaY: 2.5,
                  ),
                  child: AlertDialog(
                    backgroundColor: const Color(0xff5F569B),
                    titlePadding: EdgeInsets.all(1.0),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.all(
                        Radius.circular(
                          mediaQuery(context, 'h', 90),
                        ),
                      ),
                    ),
                    title: Container(
                      width: mediaQuery(context, 'w', 630),
                      height: mediaQuery(context, 'h', 700),
                      decoration: BoxDecoration(
                        borderRadius:
                            BorderRadius.circular(mediaQuery(context, "h", 90)),
                        color: const Color(0xff1f1c2f),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0x1a000000),
                            offset: Offset(0, 1),
                            blurRadius: 2,
                          ),
                        ],
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Container(
                            alignment: Alignment.center,
                            padding: EdgeInsets.only(
                              top: mediaQuery(context, 'h', 32),
                            ),
                            child: AppWidget.boldText(
                              context,
                              "เบิกสวัสดิการเรียบร้อยแล้ว".tr.toString(),
                              16.sp,
                              const Color(0xffFEE095),
                              FontWeight.w500,
                            ),
                          ),
                          SizedBox(height: mediaQuery(context, 'h', 20)),
                          Padding(
                            padding: EdgeInsets.only(
                                left: mediaQuery(context, 'w', 120)),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    AppWidget.boldText(
                                      context,
                                      "คุณ:  ".tr.toString(),
                                      14.sp,
                                      Color.fromRGBO(246, 246, 246, 0.9),
                                      FontWeight.w500,
                                    ),
                                    AppWidget.boldText(
                                      context,
                                      profile.responseMember!.full_name_th
                                          .toString(),
                                      14.sp,
                                      Color.fromRGBO(246, 246, 246, 0.9),
                                      FontWeight.w500,
                                    ),
                                  ],
                                ),
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    AppWidget.boldText(
                                      context,
                                      'ประเภทการเคลม :',
                                      14.sp,
                                      Color.fromRGBO(246, 246, 246, 0.9),
                                      FontWeight.w500,
                                    ),
                                    AppWidget.boldText(
                                      context,
                                      benCrt.selectedPatient.value.toString(),
                                      14.sp,
                                      Color.fromRGBO(246, 246, 246, 0.9),
                                      FontWeight.w500,
                                    ),
                                  ],
                                ),
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    AppWidget.boldText(
                                      context,
                                      'จำนวนเงินรักษารวม :  ',
                                      14.sp,
                                      Color.fromRGBO(246, 246, 246, 0.9),
                                      FontWeight.w500,
                                    ),
                                    AppWidget.boldText(
                                      context,
                                      benCrt.InputSum.value.toString(),
                                      14.sp,
                                      Color.fromRGBO(246, 246, 246, 0.9),
                                      FontWeight.w500,
                                    ),
                                    SizedBox(
                                      width: mediaQuery(context, "w", 10),
                                    ),
                                    AppWidget.boldText(
                                      context,
                                      benCrt.monetary.toString(),
                                      14.sp,
                                      Color.fromRGBO(246, 246, 246, 0.9),
                                      FontWeight.w500,
                                    ),
                                  ],
                                ),
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    AppWidget.boldText(
                                      context,
                                      'จำนวนเงินผ่าตัด :  ',
                                      14.sp,
                                      Color.fromRGBO(246, 246, 246, 0.9),
                                      FontWeight.w500,
                                    ),
                                    AppWidget.boldText(
                                      context,
                                      benCrt.InputRocveDay.value.toString(),
                                      14.sp,
                                      Color.fromRGBO(246, 246, 246, 0.9),
                                      FontWeight.w500,
                                    ),
                                    SizedBox(
                                      width: mediaQuery(context, "w", 10),
                                    ),
                                    AppWidget.boldText(
                                      context,
                                      benCrt.monetary.toString(),
                                      14.sp,
                                      Color.fromRGBO(246, 246, 246, 0.9),
                                      FontWeight.w500,
                                    ),
                                  ],
                                ),
                                AppWidget.boldText(
                                  context,
                                  "หากไม่มีการแจ้งกลับให้แก้ไขข้อมูล",
                                  14.sp,
                                  Color.fromRGBO(246, 246, 246, 0.9),
                                  FontWeight.w500,
                                ),
                                AppWidget.boldText(
                                  context,
                                  "ทีม PAO จะดำเนินการโอนเงินให้คุณ",
                                  14.sp,
                                  Color.fromRGBO(246, 246, 246, 0.9),
                                  FontWeight.w500,
                                ),
                                AppWidget.boldText(
                                  context,
                                  "ภายใน 2 วันทำการ",
                                  14.sp,
                                  Color.fromRGBO(246, 246, 246, 0.9),
                                  FontWeight.w500,
                                ),
                                AppWidget.boldText(
                                  context,
                                  "***RAFCO :: นำส่งเอกสารตัวจริงที่ทีมบัญชีเพื่อทำการเบิก ***",
                                  14.sp,
                                  Color.fromRGBO(246, 246, 246, 0.9),
                                  FontWeight.w500,
                                ),
                              ],
                            ),
                          ),
                          Center(
                            child: Padding(
                              padding: EdgeInsets.only(
                                  top: mediaQuery(context, 'h', 50)),
                              child: GestureDetector(
                                onTap: () {
                                  benCrt.HealthWelfareStatus = 0.obs;
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => HomeScreen(),
                                    ),
                                  );
                                },
                                child: Container(
                                  alignment: Alignment.center,
                                  width: mediaQuery(context, "w", 350),
                                  height: mediaQuery(context, "h", 100),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(
                                        mediaQuery(context, "h", 40)),
                                    color: const Color(0xffFEE095),
                                    boxShadow: [
                                      BoxShadow(
                                        color: const Color(0x1a000000),
                                        offset: Offset(0, 1),
                                        blurRadius: 2,
                                      ),
                                    ],
                                  ),
                                  child: AppWidget.boldText(
                                    context,
                                    "btn_GTCok".tr.toString(),
                                    14.sp,
                                    const Color(0xff5F569B),
                                    FontWeight.w500,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          );
        });
      }
      return Container();
    });
  }
}
