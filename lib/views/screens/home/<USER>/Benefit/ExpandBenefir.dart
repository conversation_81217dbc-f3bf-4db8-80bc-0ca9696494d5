import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:mapp_ms24/controllers/internal/BenefitController%20/BenefitController.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/controllers/utils/widget.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';

import '../../../Widget/Widget.dart';

class ExpandBenefitScreen extends StatefulWidget {
  const ExpandBenefitScreen({super.key});

  @override
  State<ExpandBenefitScreen> createState() => _ExpandBenefitScreenState();
}

class _ExpandBenefitScreenState extends State<ExpandBenefitScreen> {
  @override
  BenefitController benCrt = Get.find<BenefitController>();
  ProfileController profile = Get.find<ProfileController>();
  bool checking = true;
  @override
  void initState() {
    super.initState();

    initializePage();
  }

  void initializePage() async {
    await benCrt.welfare(profile.responseMember!.id);
    await benCrt.doreloadbenefits(context);
    await profile.configApp();
    checking = false;
    setState(() {});
  }

  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            width: Get.width,
            height: Get.height,
            decoration: BoxDecoration(
                gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                  const Color(0xFF1F1C2F).withOpacity(1),
                  const Color(0xFF0D0C14).withOpacity(1),
                ])),
            child: Padding(
              padding: const EdgeInsets.only(top: 20.0),
              child: Column(
                children: [
                  buildAppBarWithIcon(
                      context, Icons.person, 'ui_headFlexibleWithdraw'.tr),

                  buildBody(),
                  // buildList(),
                ],
              ),
            ),
          ),
          Container(
            alignment: Alignment.topLeft,
            margin: EdgeInsets.only(
                top: mediaQuery(context, "h", 1000),
                left: mediaQuery(context, "w", 71)),
            child: Text(
              "ui_history".tr.toString(),
              style: TextStyle(
                fontFamily: 'SukhumvitSet-Medium',
                fontSize: mediaQuery(context, "h", 28),
                color: const Color(0xfffcf6e4),
                letterSpacing: mediaQuery(context, "h", 1.12),
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Container(
            alignment: Alignment.topLeft,
            margin: EdgeInsets.only(
                top: mediaQuery(context, "h", 1050),
                left: mediaQuery(context, "w", 44),
                right: mediaQuery(context, "w", 44)),
            child: ListView(
              padding: EdgeInsets.only(top: 0),
              children: ListHistory(),
            ),
          ),
          checking
              ? Container(
                  width: MediaQuery.of(context).size.width,
                  height: MediaQuery.of(context).size.height,
                  color: Colors.black12,
                  child: Center(
                    child: SizedBox(
                      width: 50.w,
                      child: const LoadingIndicator(
                        indicatorType: Indicator.lineSpinFadeLoader,
                        colors: [Colors.white],
                      ),
                    ),
                  ))
              : Container(),
        ],
      ),
    );
  }

  Widget buildBody() {
    return Column(
      children: [
        Container(
          margin: EdgeInsets.only(top: 20.h),
          decoration: const BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(10)),
            color: Color(0xFF0D0C14),
          ),
          height: 70.h,
          width: 170.w,
          child: Container(
            alignment: Alignment.center,
            margin: EdgeInsets.only(left: 25.w, right: 15.w),
            child: AppWidget.normalText(context, profile.date_benefitsresilient,
                12.sp, const Color(0xFFF6F6F6), FontWeight.w400),
          ),
        ),
        Column(
          children: [
            Container(
              margin: EdgeInsets.only(top: 40.h),
              child: Column(
                children: [
                  AppWidget.normalText(context, 'ui_amount'.tr, 14.sp,
                      const Color(0xFFF6F6F6), FontWeight.w500),
                  AppWidget.normalText(
                      context,
                      double.parse(benCrt.start_benefitsresilient!)
                          .toStringAsFixed(2)
                          .replaceAllMapped(
                              new RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                              (Match m) => '${m[1]},'),
                      16.sp,
                      const Color(0xFF63C3DC),
                      FontWeight.w500)
                ],
              ),
            ),
            Container(
              margin: EdgeInsets.only(top: 10.h),
              child: Column(
                children: [
                  AppWidget.normalText(context, 'ui_amount1'.tr, 14.sp,
                      const Color(0xFFF6F6F6), FontWeight.w500),
                  AppWidget.normalText(
                      context,
                      double.parse(benCrt.benefitsbalance!)
                          .toStringAsFixed(2)
                          .replaceAllMapped(
                              new RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                              (Match m) => '${m[1]},'),
                      16.sp,
                      const Color(0xFFFEE095),
                      FontWeight.w400),
                  buttonSave(context, 'btn_apply'.tr, () async {
                    Get.toNamed('ExBenifitForm');
                  })
                ],
              ),
            ),
          ],
        )
      ],
    );
  }

  List<Widget> ListHistory() {
    List<Widget> list = [];
    for (var i = 0; i < benCrt.databenefits.length.toInt(); i++) {
      list.add(
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(mediaQuery(context, "h", 20)),
            color: const Color(0xff1f1c2f),
            boxShadow: [
              BoxShadow(
                color: const Color(0x4d000000),
                offset: Offset(0, 2),
                blurRadius: 20,
              ),
            ],
          ),
          child: Stack(
            children: <Widget>[
              Container(
                height: mediaQuery(context, "h", 140),
                alignment: Alignment.centerLeft,
                child: Container(
                  width: mediaQuery(context, "h", 40),
                  height: mediaQuery(context, "h", 40),
                  margin: EdgeInsets.only(left: mediaQuery(context, "w", 46)),
                  child: Stack(
                    children: <Widget>[
                      SvgPicture.string(
                        '<svg viewBox="3.0 3.0 30.0 30.0" ><path  d="M 33 18 C 33 26.28427124023438 26.28427124023438 33 18 33 C 9.715729713439941 33 3.000001907348633 26.28427124023438 3 18.00000381469727 C 3 9.715730667114258 9.715730667114258 3 18.00000190734863 3.000001907348633 C 26.28427124023438 3.000001907348633 33 9.715732574462891 33 18.00000381469727 Z" fill="none" stroke="#ff3e3e" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" /></svg>',
                        allowDrawingOutsideViewBox: true,
                        fit: BoxFit.fill,
                      ),
                      Container(
                        alignment: Alignment.center,
                        child: SvgPicture.string(
                          '<svg viewBox="12.0 18.0 12.0 1.0" ><path  d="M 12 18 L 24 18" fill="none" stroke="#ff3e3e" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" /></svg>',
                          allowDrawingOutsideViewBox: true,
                          fit: BoxFit.fill,
                          width: mediaQuery(context, "w", 12),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Container(
                height: mediaQuery(context, "h", 140),
                alignment: Alignment.centerLeft,
                padding: EdgeInsets.only(left: mediaQuery(context, "w", 101)),
                child: Text(
                  "ui_recive".tr.toString(),
                  style: TextStyle(
                    fontFamily: 'SukhumvitSet-Medium',
                    fontSize: mediaQuery(context, "h", 28),
                    color: const Color(0xfffcf6e4),
                    letterSpacing: mediaQuery(context, "h", 1.12),
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              Container(
                height: mediaQuery(context, "h", 140),
                padding: EdgeInsets.only(
                    top: mediaQuery(context, "h", 31),
                    left: mediaQuery(context, "w", 500)),
                child: Column(
                  children: <Widget>[
                    Text(
                      convertDateTime(
                          benCrt.databenefits[i]["payDate"].toString(),
                          "dd/MM/YYYY"),
                      style: TextStyle(
                        fontFamily: 'Sarabun',
                        fontSize: mediaQuery(context, "h", 20),
                        color: const Color(0xfffcf6e4),
                        letterSpacing: mediaQuery(context, "h", 2),
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    Container(
                      height: mediaQuery(context, "h", 15),
                    ),
                    Text(
                      benCrt.databenefits[i]["summary"]
                              .toString()
                              .replaceAllMapped(
                                  new RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                                  (Match m) => '${m[1]},') +
                          ".00",
                      style: TextStyle(
                        fontFamily: 'Sarabun',
                        fontSize: mediaQuery(context, "type", 28),
                        color: const Color(0xffff3e3e),
                        letterSpacing:
                            mediaQuery(context, "h", 2.8000000000000003),
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      );
      list.add(Container(
        height: mediaQuery(context, "h", 20),
      ));
    }
    return list;
  }
}
