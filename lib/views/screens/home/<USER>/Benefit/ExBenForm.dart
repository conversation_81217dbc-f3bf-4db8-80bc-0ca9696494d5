import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/controllers/internal/Profile/userController.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';
import 'package:mapp_ms24/views/screens/home/<USER>';

import '../../../../../controllers/internal/BenefitController /BenefitController.dart';
import '../../../../../controllers/utils/widget.dart';
import '../../../Widget/Widget.dart';

class ExBenifitForm extends StatefulWidget {
  const ExBenifitForm({super.key});

  @override
  State<ExBenifitForm> createState() => _ExBenifitFormState();
}

class _ExBenifitFormState extends State<ExBenifitForm> {
  BenefitController benCrt = Get.find<BenefitController>();
  ProfileController profileCtr = Get.find<ProfileController>();
  TextEditingController _withdrawController = TextEditingController();
  var amount = "0";
  List dataLinkImage = [];

  @override
  void initState() {
    super.initState();
    benCrt.information();
    benCrt.FlexibleWelfareStatus = 0;

  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: Get.width,
        height: 852.h,
        decoration: BoxDecoration(
            gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  const Color(0xFF1F1C2F).withOpacity(1),
                  const Color(0xFF0D0C14).withOpacity(1),
                ])),
        child: Container(
          margin: EdgeInsets.only(top: 20.h),
          child: Stack(
            children: [
              buildBody(),
              buildAppBarWithIcon(
                  context, Icons.person, 'ui_headFlexibleWithdraw'.tr),

            ],
          ),
        ),
      ),
    );
  }

  Widget buildBody() {
    return Padding(
      padding: const EdgeInsets.only(top: 100.0),
      child: SingleChildScrollView(
        child: Column(
          children: [
            buildMoney(context, 'ui_amount'.tr),
            buildbody()
          ],
        ),
      ),
    );
  }

  Widget buildDropdown(BuildContext context,
      RxString title,
      RxString dec,
      List<RxString> items,
      void Function(String value) updateSelectedCEWType,
      RxString selectedValue, // New parameter for storing the selected value
      ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppWidget.normalTextRX(
          context,
          title,
          14.sp,
          const Color(0xFFF6F6F6),
          FontWeight.w400,
        ),
        SizedBox(
          height: 10.h,
        ),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 10.w),
          height: 60.h,
          width: Get.width,
          decoration: BoxDecoration(
            color: const Color(0xFF302C49),
            borderRadius: BorderRadius.circular(8.r),
            boxShadow: const [
              BoxShadow(
                color: Color(0x40000000),
                offset: Offset(0, 4),
                blurRadius: 4,
                spreadRadius: 0,
              ),
            ],
          ),
          child: DropdownButton<String>(
            borderRadius: BorderRadius.circular(8.r),
            dropdownColor: const Color(0xFF302C49),
            hint: Row(
              children: [
                AppWidget.normalTextRX(
                  context,
                  selectedValue.isNotEmpty ? selectedValue : dec,
                  14.sp,
                  const Color(0xFFF6F6F6).withOpacity(0.6),
                  FontWeight.w400,
                ),
              ],
            ),
            isExpanded: true,
            underline: const SizedBox(),
            icon: SizedBox(
              width: 24.w,
              height: 24.h,
              child: Image.asset('assets/ADD/Expand_down_light.png'),
            ),
            style: TextStyle(
              fontFamily: 'SukhumvitSet-Text',
              fontSize: 14.sp,
              color: const Color(0xFFF6F6F6),
              fontWeight: FontWeight.w400,
            ),
            onChanged: (String? value) {
              setState(() {
                updateSelectedCEWType(value!);
              });
            },
            items: items.map((RxString rxString) {
              String stringValue =
                  rxString.value; // Extract the string value from RxString
              return DropdownMenuItem<String>(
                value: stringValue,
                child: AppWidget.normalTextRX(
                  context,
                  stringValue.obs,
                  14.sp,
                  const Color(0xFFF6F6F6),
                  FontWeight.w400,
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget buildbody() {
    return Stack(
      children: [
        Container(
          margin: EdgeInsets.only(
              top: mediaQuery(context, "h", 30),
              left: mediaQuery(context, "w", 81)),
          child: Text(
            "ui_amount2".tr.toString(),
            style: TextStyle(
              fontFamily: 'SukhumvitSet-SemiBold',
              fontSize: mediaQuery(context, "h", 28),
              color: const Color(0xfffcf6e4),
              letterSpacing: mediaQuery(context, "h", 1.12),
              fontWeight: FontWeight.w700,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        Container(
          margin: EdgeInsets.only(top: mediaQuery(context, "h", 90)),
          child: Container(
            height: mediaQuery(context, "h", 140),
            width: mediaQuery(context, "w", 740),
            margin: EdgeInsets.only(
                left: mediaQuery(context, "w", 44),
                right: mediaQuery(context, "w", 44)),
            decoration: BoxDecoration(
              borderRadius:
              BorderRadius.circular(mediaQuery(context, "h", 20)),
              color: const Color(0xff1f1c2f),
            ),
            child: Container(
              alignment: Alignment.centerLeft,
              margin: EdgeInsets.only(left: mediaQuery(context, "w", 47)),
              child: new TextField(
                cursorColor: Colors.black,

                onChanged: (value) {
                  setState(() {
                    amount = value;
                  });
                },
                keyboardType: TextInputType.number,
                controller: _withdrawController,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'[.0-9]'))
                ],
//                controller: ctrlUsername,
                style: new TextStyle(
                  color: Color(0xffFCF6E4),
                  fontSize: mediaQuery(context, "h", 28),
                  fontFamily: "SukhumvitSet-Text",
                ),
                decoration: new InputDecoration(
                  counterText: '',
                  labelText: "tf_amount".tr.toString(),
                  labelStyle: TextStyle(
                    color: Color(0xffFCF6E4),
                    fontSize: mediaQuery(context, "h", 28),
                    fontFamily: "SukhumvitSet-Text",
                  ),
                  enabledBorder: const UnderlineInputBorder(
                    borderSide: const BorderSide(color: Colors.transparent),
                  ),
                  focusedBorder: const UnderlineInputBorder(
                    borderSide: const BorderSide(color: Colors.transparent),
                  ),
                ),
              ),
            ),
          ),
        ),
        benCrt.country == false
            ? Stack(
          children: [
            Container(
              margin: EdgeInsets.only(
                  top: mediaQuery(context, "h", 250),
                  left: mediaQuery(context, "w", 70)),
              child: Text(
                "ui_uploadimmage".tr.toString(),
                // "   " +
                // dataLinkImage.length.toString(),
                style: TextStyle(
                  fontFamily: 'SukhumvitSet-SemiBold',
                  fontSize: mediaQuery(context, "h", 28),
                  color: const Color(0xfffcf6e4),
                  letterSpacing: mediaQuery(context, "h", 1.12),
                  fontWeight: FontWeight.w700,
                ),
                textAlign: TextAlign.left,
              ),
            ),
            Container(
              alignment: Alignment.topRight,
              child: GestureDetector(
                onTap: () {
                  // Navigator.push(
                  //     context,
                  //     MaterialPageRoute(
                  //         builder: (context) => benCrt.showImage(imgExBill)));
                },
                child: Row(
                  children: [
                    Expanded(child: Container()),
                    Container(
                      alignment: Alignment.topRight,
                      margin: EdgeInsets.only(
                          top: mediaQuery(context, "h", 450),
                          right: mediaQuery(context, "w", 20)),
                      child: SvgPicture.asset(
                        "assets/images/pkg/more/doc.svg",
                        fit: BoxFit.fill,
                        height: mediaQuery(context, "h", 35),
                      ),
                    ),
                    Container(
                      alignment: Alignment.topRight,
                      margin: EdgeInsets.only(
                          top: mediaQuery(context, "h", 450),
                          right: mediaQuery(context, "w", 67)),
                      child: Text(
                        "ui_exImage".tr.toString(),
                        style: TextStyle(
                          fontFamily: 'SukhumvitSet-SemiBold',
                          fontSize: mediaQuery(context, "h", 28),
                          color: const Color(0xfffcf6e4),
                          letterSpacing: mediaQuery(context, "h", 1.12),
                          fontWeight: FontWeight.w700,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Container(
              margin: EdgeInsets.only(top: mediaQuery(context, "h", 300)),
              child: GestureDetector(
                onTap: () {
                  // getpermissionCamera();
                },
                child: Container(
                  height: mediaQuery(context, "h", 140),
                  width: mediaQuery(context, "w", 740),
                  alignment: Alignment.center,
                  margin: EdgeInsets.only(
                      left: mediaQuery(context, "w", 44),
                      right: mediaQuery(context, "w", 44)),
                  decoration: BoxDecoration(
                    borderRadius:
                    BorderRadius.circular(mediaQuery(context, "h", 20)),
                    color: const Color(0xff1f1c2f),
                  ),
                  child: benCrt.dataLinkImage.length > 0
                      ? Stack(
                    children: <Widget>[
                      Container(
                        margin: EdgeInsets.only(
                            right: mediaQuery(context, "w", 100)),
                        child: ListView(
                          scrollDirection: Axis.horizontal,
                          children: <Widget>[
                            Row(
                              children: ListIMG(),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        alignment: Alignment.centerRight,
                        margin: EdgeInsets.only(
                            right: mediaQuery(context, "w", 36.8)),
                        child: GestureDetector(
                            onTap: () {
                              setState(() {
                                benCrt.dataLinkImage = [];
                              });
                            },
                            child: SvgPicture.asset(
                              "assets/images/pkg/feedback/Icon-material-delete.svg",
                              height: mediaQuery(context, "h", 44),
                            )),
                      ),
                    ],
                  )
                      : Stack(
                    children: <Widget>[
                      Container(
                        alignment: Alignment.centerLeft,
                        margin: EdgeInsets.only(
                            left: mediaQuery(context, "w", 47)),
                        child: Text(
                          "btn_imgUpload".tr.toString(),
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Text',
                            fontSize: mediaQuery(context, "h", 28),
                            color: const Color(0xccfcf6e4),
                            letterSpacing: mediaQuery(
                                context, "h", 1.4000000000000001),
                          ),
                          textAlign: TextAlign.left,
                        ),
                      ),
                      Container(
                        alignment: Alignment.centerRight,
                        margin: EdgeInsets.only(
                            right: mediaQuery(context, "w", 38)),
                        child: SvgPicture.string(
                          '<svg viewBox="713.0 1315.0 33.0 44.0" ><path transform="translate(713.0, 1315.0)" d="M 19.25 11.6875 L 19.25 0 L 2.0625 0 C 0.9195312261581421 0 0 0.9195312261581421 0 2.0625 L 0 41.9375 C 0 43.08046722412109 0.9195312261581421 44 2.0625 44 L 30.9375 44 C 32.08046722412109 44 33 43.08046722412109 33 41.9375 L 33 13.75 L 21.3125 13.75 C 20.17812538146973 13.75 19.25 12.82187461853027 19.25 11.6875 Z M 24.85140609741211 30.25085830688477 L 19.25 30.25085830688477 L 19.25 37.12585830688477 C 19.25 37.88554763793945 18.63468742370605 38.50085830688477 17.875 38.50085830688477 L 15.125 38.50085830688477 C 14.36531257629395 38.50085830688477 13.75 37.88554763793945 13.75 37.12585830688477 L 13.75 30.25085830688477 L 8.148593902587891 30.25085830688477 C 6.921406269073486 30.25085830688477 6.308671951293945 28.76499938964844 7.180078029632568 27.89960861206055 L 15.46617221832275 19.67539024353027 C 16.03765678405762 19.10734367370605 16.96062469482422 19.10734367370605 17.5321102142334 19.67539024353027 L 25.81820297241211 27.89960861206055 C 26.69046783447266 28.76499938964844 26.07859420776367 30.25085830688477 24.85140609741211 30.25085830688477 Z M 32.3984375 9.0234375 L 23.98515701293945 0.6015625 C 23.59843826293945 0.21484375 23.07421875 0 22.52421951293945 0 L 22 0 L 22 11 L 33 11 L 33 10.47578144073486 C 33 9.934374809265137 32.78515625 9.41015625 32.3984375 9.0234375 Z" fill="#fcf6e4" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                          allowDrawingOutsideViewBox: true,
                          height: mediaQuery(context, "h", 44),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        )
            : Container(),
        Container(
          alignment: Alignment.bottomCenter,
          margin: EdgeInsets.only(top: mediaQuery(context, "h", 900)),
          child: GestureDetector(
            onTap: () async {
              if (double.parse(amount) != 0 || _withdrawController.text != '') {
                if (double.parse(amount) <=
                    double.parse(benCrt.benefitsbalance!.toString())) {
                  if (profileCtr.responseMember!.welfare_country == "ไทย") {
                    ConfirmInformation(context);
                  } else {
                    if (benCrt.dataLinkImage.length != 0) {
                      ConfirmInformation(context);
                    } else {
                      Fluttertoast.showToast(
                          msg: "กรุณาเพิ่มรูปภาพ",
                          toastLength: Toast.LENGTH_SHORT,
                          gravity: ToastGravity.TOP,
                          backgroundColor: Colors.red,
                          textColor: Colors.black,
                          fontSize: 16.0);
                    }
                  }
                } else {
                  Fluttertoast.showToast(
                      msg: "คุณใส่จำนวนเงิน เกินวงเงินคงเหลือ",
                      toastLength: Toast.LENGTH_SHORT,
                      gravity: ToastGravity.TOP,
                      backgroundColor: Colors.red,
                      textColor: Colors.black,
                      fontSize: 16.0);
                }
              } else {
                Fluttertoast.showToast(
                    msg: "กรุณาใส่จำนวนเงินที่ต้องการเบิก",
                    toastLength: Toast.LENGTH_SHORT,
                    gravity: ToastGravity.TOP,
                    backgroundColor: Colors.red,
                    textColor: Colors.black,
                    fontSize: 16.0);
              }
            },
            child: Container(
              width: mediaQuery(context, "w", 273),
              height: mediaQuery(context, "h", 101),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(51.0),
                color: const Color(0xff7f420c),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0x4d000000),
                    offset: Offset(0, 2),
                    blurRadius: 20,
                  ),
                ],
              ),
              child: Text(
                "btn_save".tr.toString(),
                style: TextStyle(
                  fontFamily: 'SukhumvitSet-Bold',
                  fontSize: mediaQuery(context, "h", 30),
                  color: const Color(0xfffcf6e4),
                  letterSpacing: mediaQuery(context, "h", 0.8999999999999999),
                  fontWeight: FontWeight.w700,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ),

      ],
    );
  }

  Widget buildMoney(context, money) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 10.0.h, horizontal: 20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Container(
                  height: 60.h,
                  decoration: BoxDecoration(
                    color: const Color(0xFF302C49),
                    borderRadius: BorderRadius.circular(13.sp),
                    boxShadow: const [
                      BoxShadow(
                        color: Color(0x40000000),
                        offset: Offset(0, 4),
                        blurRadius: 4,
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Container(
                          margin: EdgeInsets.only(left: 20.w, right: 20.w),
                          width: Get.width,
                          height: Get.height,
                          child: Center(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment
                                    .spaceBetween,
                                children: [
                                  AppWidget.normalText(
                                    context,
                                    'ui_amountWithdrow'.tr,
                                    14.sp,
                                    const Color(0xFFF6F6F6),
                                    FontWeight.w500,
                                  ),
                                  AppWidget.normalText(
                                    context,
                                    double.parse(benCrt.benefitsbalance!)
                                        .toStringAsFixed(2)
                                        .replaceAllMapped(
                                        new RegExp(
                                            r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                                            (Match m) => '${m[1]},'),
                                    16.sp,
                                    const Color(0xffb5e8ff),
                                    FontWeight.w500,
                                  ),
                                ],
                              )),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget buildFormUpload(context, topic, title, dec) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 10.0.h),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                AppWidget.normalText(
                  context,
                  topic,
                  14.sp,
                  const Color(0xFFF6F6F6),
                  FontWeight.w500,
                ),
                Row(
                  children: [
                    const SizedBox(
                      child: Icon(Icons.document_scanner),
                    ),
                    SizedBox(width: 5.w),
                    AppWidget.normalText(
                      context,
                      dec,
                      14.sp,
                      const Color(0xFFF6F6F6),
                      FontWeight.w500,
                    ),
                  ],
                )
              ],
            ),
            SizedBox(height: 5.h),
            Container(
                padding: EdgeInsets.symmetric(horizontal: 10.w),
                height: 60.h,
                width: Get.width,
                decoration: BoxDecoration(
                  color: const Color(0xFF302C49),
                  borderRadius: BorderRadius.circular(8.r),
                  boxShadow: const [
                    BoxShadow(
                      color: Color(0x40000000),
                      offset: Offset(0, 4),
                      blurRadius: 4,
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    AppWidget.normalText(
                      context,
                      title,
                      14.sp,
                      const Color(0xFFF6F6F6),
                      FontWeight.w500,
                    ),
                    const SizedBox(
                      child: Icon(Icons.document_scanner),
                    ),
                  ],
                )),
          ],
        ),
      ),
    );
  }

  List<Widget> ListIMG() {
    List<Widget> list = [];
    for (var i = 0; i < dataLinkImage.length.toInt(); i++) {
      list.add(Container(
        height: mediaQuery(context, "h", 10),
        width: mediaQuery(context, "h", 25),
      ));
      list.add(
        Container(
          width: mediaQuery(context, "w", 265),
          height: mediaQuery(context, "h", 65),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(mediaQuery(context, "h", 15)),
            color: const Color(0xff302c49),
          ),
          child: Stack(
            children: <Widget>[
              Container(
                alignment: Alignment.centerLeft,
                margin: EdgeInsets.only(
                    left: mediaQuery(context, "w", 22),
                    right: mediaQuery(context, "w", 22)),
                child: Text(
                  cuttext(benCrt.dataLinkImage[i].toString(), 12),
                  style: TextStyle(
                    fontFamily: 'SukhumvitSet-Text',
                    fontSize: mediaQuery(context, "h", 28),
                    color: const Color(0xccfcf6e4),
                    letterSpacing: mediaQuery(context, "h", 1.4000000000000001),
                  ),
                  textAlign: TextAlign.left,
                ),
              ),
              Container(
                alignment: Alignment.centerRight,
                margin: EdgeInsets.only(right: mediaQuery(context, "w", 19)),
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      benCrt.dataLinkImage.removeAt(i);
                    });
                  },
                  child: SvgPicture.string(
                    '<svg viewBox="282.0 1321.0 33.0 33.0" ><path transform="translate(277.83, 1316.83)" d="M 20.66666603088379 4.166666507720947 C 11.5421667098999 4.166666507720947 4.166666507720947 11.54216575622559 4.166666507720947 20.66666412353516 C 4.166666507720947 29.79116439819336 11.54216575622559 37.16666412353516 20.66666603088379 37.16666412353516 C 29.79116821289063 37.16666412353516 37.16666793823242 29.79116821289063 37.16666793823242 20.66666793823242 C 37.16666793823242 11.5421667098999 29.79116439819336 4.166666507720947 20.66666603088379 4.166666507720947 Z M 28.91666603088379 26.59016418457031 L 26.59016609191895 28.91666412353516 L 20.66666603088379 22.9931640625 L 14.74316596984863 28.91666412353516 L 12.41666603088379 26.59016418457031 L 18.34016609191895 20.66666412353516 L 12.41666603088379 14.74316501617432 L 14.74316596984863 12.41666507720947 L 20.66666603088379 18.34016418457031 L 26.59016609191895 12.41666507720947 L 28.91666603088379 14.74316501617432 L 22.9931640625 20.66666412353516 L 28.91666603088379 26.59016418457031 Z" fill="#fcf6e4" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                    allowDrawingOutsideViewBox: true,
                    height: mediaQuery(context, "h", 33),
                  ),
                ),
              )
            ],
          ),
        ),
      );
      list.add(Container(
        height: mediaQuery(context, "h", 10),
        width: mediaQuery(context, "h", 15),
      ));
    }
    return list;
  }


  void ConfirmInformation(BuildContext context) {
    showDialog(
        context: context,
        builder: (BuildContext context) {
          return Container(
            child: BackdropFilter(
              filter: ImageFilter.blur(
                sigmaX: 2.5,
                sigmaY: 2.5,
              ),
              child: AlertDialog(
                backgroundColor: Color(0xff5F569B),
                titlePadding: EdgeInsets.all(1.0),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.all(
                    Radius.circular(
                      mediaQuery(context, 'h', 90),
                    ),
                  ),
                ),
                title: Container(
                  width: mediaQuery(context, 'w', 450),
                  height: mediaQuery(context, 'h', 450),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(
                        mediaQuery(context, "h", 90)),
                    color: const Color(0xff1f1c2f),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0x1a000000),
                        offset: Offset(0, 1),
                        blurRadius: 2,
                      ),
                    ],
                  ),
                  child: Container(
                    margin: EdgeInsets.only(
                        top: mediaQuery(context, 'h', 50)),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: <Widget>[
                        Container(
                            child: AppWidget.boldText(
                                context, "btn_details".tr.toString(), 14.sp,
                                const Color(0xffFEE095), FontWeight.w400)
                        ),
                        Container(
                            padding: EdgeInsets.only(
                                top: mediaQuery(context, 'h', 30)),
                            child: AppWidget.boldText(
                                context, "btn_details2".tr.toString(), 14.sp,
                                const Color.fromRGBO(246, 246, 246, 0.9),
                                FontWeight.w500)
                        ),
                        Container(
                          // padding: EdgeInsets.only(
                          //     top: mediaQuery(context, 'h', 30)),
                          child: Column(
                            children: [
                              Container(

                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children:
                                  [
                                    AppWidget.boldText(
                                        context, "btn_details3".tr.toString(),
                                        14.sp, const Color.fromRGBO(
                                        246, 246, 246, 0.9), FontWeight.w500),
                                    SizedBox(
                                        width: mediaQuery(context, "w", 10)),
                                    AppWidget.boldText(
                                        context, _withdrawController.text,
                                        14.sp, const Color.fromRGBO(
                                        246, 246, 246, 0.9), FontWeight.w500),
                                    SizedBox(
                                        width: mediaQuery(context, "w", 10)),
                                    AppWidget.boldText(
                                        context, benCrt.monetary.toString(),
                                        14.sp, const Color.fromRGBO(
                                        246, 246, 246, 0.9), FontWeight.w500),
                                    SizedBox(
                                        width: mediaQuery(context, "w", 10)),
                                    AppWidget.boldText(
                                        context, "btn_details6".tr.toString(),
                                        14.sp, const Color.fromRGBO(
                                        246, 246, 246, 0.9), FontWeight.w500),
                                  ],
                                ),
                              ),
                              Padding(
                                padding: EdgeInsets.only(
                                    top: mediaQuery(context, 'h', 80)),
                                child: Material(
                                  elevation: 5.0,
                                  borderRadius: BorderRadius.circular(20.0),
                                  color: Color(0xff7f420c),
                                  child: MaterialButton(
                                      minWidth:
                                      MediaQuery
                                          .of(context)
                                          .size
                                          .width /
                                          2.5,
                                      padding: EdgeInsets.fromLTRB(
                                          mediaQuery(context, "w", 10.0),
                                          mediaQuery(context, "h", 10.0),
                                          mediaQuery(context, "w", 10.0),
                                          mediaQuery(context, "h", 15.0)),
                                      child: AppWidget.boldText(context,
                                          "btn_likeCreditConfirmok".tr
                                              .toString(), 14.sp,
                                          const Color.fromRGBO(
                                              246, 246, 246, 0.9),
                                          FontWeight.w500),
                                      onPressed: () async {
                                        Navigator.of(context).pop();
                                        benCrt.RecordWithdrawal(context, amount,
                                            benCrt.dataLinkImage);
                                        // benCrt.RecordWithdrawal(context,amount,benCrt.dataLinkImage);
                                      }),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                ),
              ),
            ),
          );
        });
  }

  void SaveData() {
    if (benCrt.FlexibleWelfareStatus == 1) {
      showDialog(
          barrierDismissible: false,
          context: context,
          builder: (BuildContext context) {
            return Container(
              child: BackdropFilter(
                filter: ImageFilter.blur(
                  sigmaX: 2.5,
                  sigmaY: 2.5,
                ),
                child: AlertDialog(
                  backgroundColor: Color(0xff5F569B),
                  titlePadding: EdgeInsets.all(1.0),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.all(
                      Radius.circular(
                        mediaQuery(context, 'h', 90),
                      ),
                    ),
                  ),
                  title: Container(
                    width: mediaQuery(context, 'w', 630),
                    height: mediaQuery(context, 'h', 700),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(
                          mediaQuery(context, "h", 90)),
                      color: const Color(0xff1f1c2f),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0x1a000000),
                          offset: Offset(0, 1),
                          blurRadius: 2,
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Container(
                          alignment: Alignment.center,
                          padding: EdgeInsets.only(
                            top: mediaQuery(context, 'h', 32),
                          ),
                          child: AppWidget.boldText(
                              context, "เบิกสวัสดิการเรียบร้อยแล้ว".tr
                              .toString(), 16.sp, const Color(0xffFEE095),
                              FontWeight.w500),
                        ),
                        SizedBox(
                          height: mediaQuery(context, 'h', 20),
                        ),
                        Padding(
                          padding: EdgeInsets.only(
                              left: mediaQuery(context, 'w', 120)),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  AppWidget.boldText(
                                      context, "คุณ:  ".tr.toString(), 14.sp,
                                      Color.fromRGBO(246, 246, 246, 0.9),
                                      FontWeight.w500),
                                  AppWidget.boldText(context,
                                      profileCtr.responseMember!.full_name_th
                                          .toString(), 14.sp,
                                      Color.fromRGBO(246, 246, 246, 0.9),
                                      FontWeight.w500),
                                ],
                              ),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  AppWidget.boldText(context, 'BU: ', 14.sp,
                                      Color.fromRGBO(246, 246, 246, 0.9),
                                      FontWeight.w500),
                                  AppWidget.boldText(context,
                                      profileCtr.responseMember!
                                          .company_management.toString(), 14.sp,
                                      Color.fromRGBO(246, 246, 246, 0.9),
                                      FontWeight.w500),
                                ],
                              ),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  AppWidget.boldText(
                                      context, 'จำนวนเงิน :  '.toString(),
                                      14.sp, Color.fromRGBO(246, 246, 246, 0.9),
                                      FontWeight.w500),
                                  AppWidget.boldText(
                                      context, _withdrawController.text, 14.sp,
                                      Color.fromRGBO(246, 246, 246, 0.9),
                                      FontWeight.w500),
                                  SizedBox(
                                    width: mediaQuery(context, "w", 10),),
                                  AppWidget.boldText(
                                      context, benCrt.monetary.toString(),
                                      14.sp, Color.fromRGBO(246, 246, 246, 0.9),
                                      FontWeight.w500),
                                ],
                              ),
                              AppWidget.boldText(
                                  context, "ทีม PAO จะดำเนินการโอนเงินให้คุณ",
                                  14.sp, Color.fromRGBO(246, 246, 246, 0.9),
                                  FontWeight.w500),
                              AppWidget.boldText(
                                  context, "ภายใน 2 วันทำการ", 14.sp,
                                  Color.fromRGBO(246, 246, 246, 0.9),
                                  FontWeight.w500),
                              AppWidget.boldText(context,
                                  "***RAFCO :: นำส่งเอกสารตัวจริงที่ทีมบัญชีเพื่อทำการเบิก ***",
                                  14.sp, Color.fromRGBO(246, 246, 246, 0.9),
                                  FontWeight.w500),
                            ],
                          ),
                        ),
                        Center(
                          child: Padding(
                            padding: EdgeInsets.only(
                                top: mediaQuery(context, 'h', 50)),
                            child: GestureDetector(
                              onTap: () {
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) => HomeScreen()));
                              },
                              child: Container(
                                alignment: Alignment.center,
                                width: mediaQuery(context, "w", 350),
                                height: mediaQuery(context, "h", 100),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(
                                      mediaQuery(context, "h", 40)),
                                  color: const Color(0xffFEE095),
                                  boxShadow: [
                                    BoxShadow(
                                      color: const Color(0x1a000000),
                                      offset: Offset(0, 1),
                                      blurRadius: 2,
                                    ),
                                  ],
                                ),
                                child: AppWidget.boldText(
                                    context, "btn_GTCok".tr.toString(), 14.sp,
                                    Color(0xff5F569B), FontWeight.w500),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),

                  ),
                ),
              ),
            );
          });
    } else {
      Container();
    }
  }
}

