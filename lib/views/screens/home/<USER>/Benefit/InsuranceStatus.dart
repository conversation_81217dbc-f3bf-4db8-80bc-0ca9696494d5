import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:mapp_ms24/controllers/internal/BenefitController%20/BenefitController.dart';
import 'package:mapp_ms24/controllers/internal/HealthController/HealthController.dart';
import 'package:mapp_ms24/controllers/utils/widget.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';

import '../../../Widget/Widget.dart';

class InsuranceStatusScreen extends StatefulWidget {
  const InsuranceStatusScreen({super.key});

  @override
  State<InsuranceStatusScreen> createState() => _InsuranceStatusScreenState();
}

class _InsuranceStatusScreenState extends State<InsuranceStatusScreen> {
  double sizeH = 110.h;
  bool checking = true;

  BenefitController benCrt = Get.find<BenefitController>();
  void initState() {
    super.initState();

    initializePage();
  }

  void initializePage() async {
    await benCrt.loadhealtaBlance();
    await benCrt.seachConfigApp();
    checking = false;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            width: Get.width,
            height: Get.height,
            decoration: BoxDecoration(
                gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                  const Color(0xFF1F1C2F).withOpacity(1),
                  const Color(0xFF0D0C14).withOpacity(1),
                ])),
            child: Padding(
              padding: const EdgeInsets.only(top: 20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  buildAppBarWithIcon(
                      context, Icons.person, 'ui_headhealthfrom4'.tr),
                  buildBody(),
                  buildSum(),
                  buildWidrawList(),
                  buildButtom()
                ],
              ),
            ),
          ),
          checking
              ? Container(
                  width: MediaQuery.of(context).size.width,
                  height: MediaQuery.of(context).size.height,
                  color: Colors.black12,
                  child: Center(
                    child: SizedBox(
                      width: 50.w,
                      child: const LoadingIndicator(
                        indicatorType: Indicator.lineSpinFadeLoader,
                        colors: [Colors.white],
                      ),
                    ),
                  ))
              : Container(),
        ],
      ),
    );
  }

  Widget buildBody() {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: SizedBox(
        height: 150.h,
        width: Get.width,
        child: Column(
          children: [
            Stack(
              children: [
                Align(
                  alignment: Alignment.topRight,
                  child: Container(
                    height: 60.h,
                    width: 140.w,
                    decoration: const BoxDecoration(
                      color: Color(0xFF934D0F),
                      borderRadius: BorderRadius.all(Radius.circular(10)),
                    ),
                  ),
                ),
                SizedBox(
                  height: 150.h,
                  width: Get.width,
                  child: Align(
                    alignment: Alignment.bottomCenter,
                    child: Container(
                      height: 110.h,
                      width: Get.width,
                      decoration: const BoxDecoration(
                        color: Color(0xFF302C49),
                        borderRadius: BorderRadius.all(Radius.circular(10)),
                      ),
                    ),
                  ),
                ),
                Align(
                  alignment: Alignment.topRight,
                  child: SizedBox(
                    height: 150.h,
                    width: 140.w,
                    child: Row(
                      children: [
                        Expanded(
                          flex: 1,
                          child: Column(
                            children: [
                              Expanded(
                                  flex: 1,
                                  child: Align(
                                      alignment: Alignment.center,
                                      child: AppWidget.normalText(
                                          context,
                                          'ui_Used'.tr,
                                          14.sp,
                                          const Color(0xFFF6F6F6),
                                          FontWeight.w400))),
                              Expanded(
                                  flex: 1,
                                  child: Align(
                                    alignment: Alignment.center,
                                    child: AppWidget.normalText(
                                        context,
                                        benCrt.numhealthIPD
                                            .toStringAsFixed(2)
                                            .replaceAllMapped(
                                                new RegExp(
                                                    r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                                                (Match m) => '${m[1]},'),
                                        14.sp,
                                        Colors.red,
                                        FontWeight.w400),
                                  )),
                              Expanded(
                                  flex: 1,
                                  child: Align(
                                    alignment: Alignment.center,
                                    child: AppWidget.normalText(
                                        context,
                                        benCrt.numhealthOPD
                                            .toStringAsFixed(2)
                                            .replaceAllMapped(
                                                new RegExp(
                                                    r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                                                (Match m) => '${m[1]},'),
                                        14.sp,
                                        const Color(0xFFF6F6F6),
                                        FontWeight.w400),
                                  )),
                            ],
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: Column(
                            children: [
                              Expanded(
                                  flex: 1,
                                  child: Align(
                                      alignment: Alignment.center,
                                      child: AppWidget.normalText(
                                          context,
                                          'ui_Balance'.tr,
                                          14.sp,
                                          const Color(0xFFF6F6F6),
                                          FontWeight.w400))),
                              Expanded(
                                  flex: 1,
                                  child: Align(
                                    alignment: Alignment.center,
                                    child: AppWidget.normalText(
                                        context,
                                        (int.parse(benCrt.numIPD) -
                                                benCrt.numhealthIPD)
                                            .toStringAsFixed(2)
                                            .replaceAllMapped(
                                                new RegExp(
                                                    r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                                                (Match m) => '${m[1]},'),
                                        14.sp,
                                        const Color(0xFFF6F6F6),
                                        FontWeight.w400),
                                  )),
                              Expanded(
                                  flex: 1,
                                  child: Align(
                                    alignment: Alignment.center,
                                    child: AppWidget.normalText(
                                        context,
                                        (int.parse(benCrt.numOPD) -
                                                benCrt.numhealthOPD)
                                            .toStringAsFixed(2)
                                            .replaceAllMapped(
                                                new RegExp(
                                                    r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                                                (Match m) => '${m[1]},'),
                                        14.sp,
                                        const Color(0xFFF6F6F6),
                                        FontWeight.w400),
                                  )),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 10.0),
                  child: Align(
                    alignment: Alignment.topLeft,
                    child: SizedBox(
                      height: 150.h,
                      width: 200.w,
                      child: Row(
                        children: [
                          Expanded(
                            flex: 1,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                    flex: 1,
                                    child: Align(
                                        alignment: Alignment.centerLeft,
                                        child: AppWidget.normalText(
                                            context,
                                            'ui_statusWithdrawPKG'.tr,
                                            14.sp,
                                            const Color(0xFFF6F6F6),
                                            FontWeight.w400))),
                                Expanded(
                                    flex: 1,
                                    child: Align(
                                      alignment: Alignment.centerLeft,
                                      child: AppWidget.normalText(
                                          context,
                                          'ui_IPD'.tr,
                                          14.sp,
                                          const Color(0xFFF6F6F6),
                                          FontWeight.w400),
                                    )),
                                Expanded(
                                    flex: 1,
                                    child: Align(
                                      alignment: Alignment.centerLeft,
                                      child: AppWidget.normalText(
                                          context,
                                          'ui_OPD'.tr,
                                          14.sp,
                                          const Color(0xFFF6F6F6),
                                          FontWeight.w400),
                                    )),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget buildSum() {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Container(
        height: 60.h,
        width: Get.width,
        decoration: const BoxDecoration(
          color: Color(0xFF302C49),
          borderRadius: BorderRadius.all(Radius.circular(10)),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              AppWidget.normalText(context, 'ui_amountWithdraw'.tr, 14.sp,
                  const Color(0xFFF6F6F6), FontWeight.w400),
              AppWidget.normalText(
                  context,
                  benCrt.amounthealt.toStringAsFixed(2).replaceAllMapped(
                      new RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                      (Match m) => '${m[1]},'),
                  18.sp,
                  const Color(0xFFF6F6F6),
                  FontWeight.w400)
            ],
          ),
        ),
      ),
    );
  }

  Widget buildWidrawList() {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: SizedBox(
        width: Get.width,
        height: 300.h,
        child: Stack(
          children: [
            Container(
              alignment: Alignment.topLeft,
              margin: EdgeInsets.only(left: mediaQuery(context, "w", 10)),
              child: AppWidget.normalText(context, 'ui_listWithdraw'.tr, 14.sp,
                  const Color(0xFFF6F6F6), FontWeight.w400),
            ),
            Container(
              alignment: Alignment.topLeft,
              margin: EdgeInsets.only(
                  top: mediaQuery(context, "h", 60),
                  left: mediaQuery(context, "w", 10),
                  right: mediaQuery(context, "w", 10)),
              child: ListView(
                padding: EdgeInsets.only(top: 0),
                children: ListSpecial(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildButtom() {
    return InkWell(
      onTap: () {
        Get.toNamed('HealthForm');
      },
      child: Container(
          height: 50.h,
          width: 110.w,
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.all(Radius.circular(25)),
            color: const Color(0xFF934D0F),
            border: Border.all(
              width: 1.w,
              color: const Color(0xFF934D0F),
            ),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              AppWidget.normalText(
                context,
                'btn_key'.tr,
                14.sp,
                const Color(0xFFF6F6F6),
                FontWeight.w400,
              ),
            ],
          )),
    );
  }

  List<Widget> ListSpecial() {
    List<Widget> list = [];
    for (var i = 0; i < benCrt.dataHistoryHelp.length.toInt(); i++) {
      list.add(
        Container(
          height: mediaQuery(context, "h", 140),
          width: mediaQuery(context, "w", 740),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(mediaQuery(context, "h", 20)),
            color: const Color(0xff1f1c2f),
            boxShadow: [
              BoxShadow(
                color: const Color(0x4d000000),
                offset: Offset(0, 2),
                blurRadius: 20,
              ),
            ],
          ),
          child: Stack(
            children: <Widget>[
              Container(
                alignment: Alignment.centerLeft,
                margin: EdgeInsets.only(left: mediaQuery(context, "w", 55)),
                child: Stack(
                  children: <Widget>[
                    Container(
                      height: mediaQuery(context, "h", 30),
                      width: mediaQuery(context, "h", 30),
                      child: SvgPicture.string(
                        '<svg viewBox="3.0 3.0 30.0 30.0" ><path  d="M 33 18 C 33 26.28427124023438 26.28427124023438 33 18 33 C 9.715729713439941 33 3.000001907348633 26.28427124023438 3 18.00000381469727 C 3 9.715730667114258 9.715730667114258 3 18.00000190734863 3.000001907348633 C 26.28427124023438 3.000001907348633 33 9.715732574462891 33 18.00000381469727 Z" fill="none" stroke="#ff3e3e" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" /></svg>',
                        allowDrawingOutsideViewBox: true,
                        fit: BoxFit.fill,
                      ),
                    ),
                    Container(
                      height: mediaQuery(context, "h", 30),
                      width: mediaQuery(context, "h", 30),
                      alignment: Alignment.center,
                      child: SvgPicture.string(
                        '<svg viewBox="12.0 18.0 12.0 1.0" ><path  d="M 12 18 L 24 18" fill="none" stroke="#ff3e3e" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" /></svg>',
                        allowDrawingOutsideViewBox: true,
                        fit: BoxFit.fill,
                        width: mediaQuery(context, "w", 12),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                alignment: Alignment.centerLeft,
                margin: EdgeInsets.only(left: mediaQuery(context, "w", 99)),
                child: AppWidget.normalText(
                    context,
                    benCrt.dataHistoryHelp[i]["type"].toString() == "ผู้ป่วยนอก"
                        ? "ui_claimtypeOPD".tr.toString()
                        : "ui_claimtypeIPD".tr.toString(),
                    14.sp,
                    const Color(0xFFF6F6F6),
                    FontWeight.w400),
              ),
              Container(
                alignment: Alignment.centerRight,
                margin: EdgeInsets.only(right: mediaQuery(context, "w", 40)),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    Container(
                      alignment: Alignment.centerRight,
                      margin:
                          EdgeInsets.only(left: mediaQuery(context, "w", 314)),
                      child: AppWidget.normalText(
                          context,
                          convertDateTime(
                              benCrt.dataHistoryHelp[i]["date_c"].toString(),
                              "dd/MM/YYYY"),
                          10.sp,
                          const Color(0xFFF6F6F6),
                          FontWeight.w400),
                    ),
                    Container(
                      height: mediaQuery(context, "h", 23),
                    ),
                    Container(
                      alignment: Alignment.centerRight,
                      margin:
                          EdgeInsets.only(left: mediaQuery(context, "w", 341)),
                      child: AppWidget.normalText(
                          context,
                          benCrt.dataHistoryHelp[i]["reimbursable"].toString(),
                          13.sp,
                          const Color(0xffff3e3e),
                          FontWeight.w400),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
      list.add(Container(
        height: mediaQuery(context, "h", 20),
      ));
    }
    return list;
  }
}
