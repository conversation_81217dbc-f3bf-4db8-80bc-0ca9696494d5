import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_utils/src/extensions/internacionalization.dart';
import 'package:mapp_ms24/controllers/internal/BenefitController%20/BenefitController.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/controllers/utils/widget.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';


class pkgWelfareFlexibleConfirm extends StatefulWidget {
  final String withdraw;
  const pkgWelfareFlexibleConfirm({Key? key, required this.withdraw}) ;
  @override
  _pkgWelfareFlexibleConfirmState createState() =>
      _pkgWelfareFlexibleConfirmState(this.withdraw);
}

class _pkgWelfareFlexibleConfirmState extends State<pkgWelfareFlexibleConfirm> {
  final withdraw;
  _pkgWelfareFlexibleConfirmState(this.withdraw);

  ProfileController profileCtr = Get.put(ProfileController());
 BenefitController benCrt = Get.find<BenefitController>();


  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () {
        Navigator.of(context).pop();
        Navigator.of(context).pop();
        Navigator.of(context).pop();
        return Future.value(true);
//        Navigator.push(
//            context, MaterialPageRoute(builder: (context) => home()));
      },
      child: Scaffold(
        body: Stack(
          children: <Widget>[
            Center(
                child: Container(
              color: Color(0xff302C49),
            )),
            Container(
              width: mediaQuery(context, "w", 828),
              height: mediaQuery(context, "h", 290),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                  bottomRight: Radius.circular(mediaQuery(context, "h", 40)),
                  bottomLeft: Radius.circular(mediaQuery(context, "h", 40)),
                ),
                color: const Color(0xff1f1c2f),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0x4d000000),
                    offset: Offset(0, 2),
                    blurRadius: 20,
                  ),
                ],
              ),
            ),
            Container(
              margin: EdgeInsets.only(
                  top: mediaQuery(context, "h", 154),
                  left: mediaQuery(context, "w", 67)),
              child: GestureDetector(
                onTap: () {
                  Navigator.of(context).pop();
                  Navigator.of(context).pop();
                  Navigator.of(context).pop();
//              Navigator.push(
//                  context, MaterialPageRoute(builder: (context) => home()));
                },
                child: Container(
                  color: Colors.transparent,
                  alignment: Alignment.center,
                  height: mediaQuery(context, "h", 70),
                  width: mediaQuery(context, "h", 70),
                  child: SvgPicture.string(
                    '<svg viewBox="67.0 154.0 21.7 38.0" ><path transform="translate(55.75, 147.81)" d="M 17.79955863952637 25.18868064880371 L 32.17768859863281 10.82186222076416 C 33.24105834960938 9.758491516113281 33.24105834960938 8.038998603820801 32.17768859863281 6.986940383911133 C 31.11431694030762 5.92357063293457 29.39482688903809 5.934882164001465 28.33145332336426 6.986940383911133 L 12.0415210723877 23.26556205749512 C 11.01208686828613 24.29499435424805 10.98946189880371 25.94661521911621 11.96233177185059 27.00998306274414 L 28.32014274597168 43.40172958374023 C 28.85183143615723 43.93341445922852 29.55320167541504 44.19359970092773 30.24326133728027 44.19359970092773 C 30.93332099914551 44.19359970092773 31.63469123840332 43.93341445922852 32.1663818359375 43.40172958374023 C 33.22975158691406 42.33835601806641 33.22975158691406 40.61886596679688 32.1663818359375 39.56680679321289 L 17.79955863952637 25.18868064880371 Z" fill="#fcf6e4" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                    allowDrawingOutsideViewBox: true,
                    height: mediaQuery(context, "h", 38),
                  ),
                ),
              ),
            ),
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    AppWidget.boldText(
                        context, "คุณ:  ".tr.toString(), 14.sp,
                        Color.fromRGBO(246, 246, 246, 0.9),
                        FontWeight.w500),
                    AppWidget.boldText(context,
                        profileCtr.responseMember!.full_name_th
                            .toString(), 14.sp,
                        Color.fromRGBO(246, 246, 246, 0.9),
                        FontWeight.w500),
                  ],
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    AppWidget.boldText(context, 'BU: ', 14.sp,
                        Color.fromRGBO(246, 246, 246, 0.9),
                        FontWeight.w500),
                    AppWidget.boldText(context,
                        profileCtr.responseMember!
                            .company_management.toString(), 14.sp,
                        Color.fromRGBO(246, 246, 246, 0.9),
                        FontWeight.w500),
                  ],
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    AppWidget.boldText(
                        context, 'จำนวนเงิน :  '.toString(),
                        14.sp, Color.fromRGBO(246, 246, 246, 0.9),
                        FontWeight.w500),
                    AppWidget.boldText(
                        context, withdraw, 14.sp,
                        Color.fromRGBO(246, 246, 246, 0.9),
                        FontWeight.w500),
                    SizedBox(
                      width: mediaQuery(context, "w", 10),),
                    AppWidget.boldText(
                        context, benCrt.monetary.toString(),
                        14.sp, Color.fromRGBO(246, 246, 246, 0.9),
                        FontWeight.w500),
                  ],
                ),
                AppWidget.boldText(
                    context, "ทีม PAO จะดำเนินการโอนเงินให้คุณ",
                    14.sp, Color.fromRGBO(246, 246, 246, 0.9),
                    FontWeight.w500),
                AppWidget.boldText(
                    context, "ภายใน 2 วันทำการ", 14.sp,
                    Color.fromRGBO(246, 246, 246, 0.9),
                    FontWeight.w500),
                AppWidget.boldText(context,
                    "***RAFCO :: นำส่งเอกสารตัวจริงที่ทีมบัญชีเพื่อทำการเบิก ***",
                    14.sp, Color.fromRGBO(246, 246, 246, 0.9),
                    FontWeight.w500),
              ],
            ),
//        Container(
//          alignment: Alignment.topCenter,
//          margin: EdgeInsets.only(top: mediaQuery(context, "h", 461)),
//          child: Text(
//            '*รอใส่รายละเอียด',
//            style: TextStyle(
//              fontFamily: 'SukhumvitSet-SemiBold',
//              fontSize: mediaQuery(context, "h", 28),
//              color: const Color(0xfffee095),
//              letterSpacing: 1.12,
//              fontWeight: FontWeight.w700,
//            ),
//            textAlign: TextAlign.center,
//          ),
//        ),
          ],
        ),
      ),
    );
  }
}
