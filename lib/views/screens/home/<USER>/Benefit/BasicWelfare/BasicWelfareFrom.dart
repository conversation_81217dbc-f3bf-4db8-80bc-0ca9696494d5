import 'package:date_picker_plus/date_picker_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:get/get_utils/get_utils.dart';
import 'package:intl/intl.dart';
import 'package:mapp_ms24/controllers/internal/BenefitController%20/BenefitController.dart';
import 'package:mapp_ms24/controllers/internal/FinaceController/NDAdoc/NDAcontroller.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/controllers/utils/color.dart';
import 'package:mapp_ms24/views/screens/Widget/Widget.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';
import 'package:get/get.dart';

import '../../../../../../controllers/utils/widget.dart';

class Welfarefrom extends StatefulWidget {
  Welfarefrom({super.key});

  @override
  State<Welfarefrom> createState() => _WelfarefromState();
}

class _WelfarefromState extends State<Welfarefrom> {
  int currentIndex = 0;
  final NumberFormat currencyFormat = NumberFormat("#,###");
  var isChecked = false.obs;

  BenefitController benCrt = Get.find<BenefitController>();

  ProfileController profile = Get.find<ProfileController>();

  TextEditingController WithdrawalAmount = TextEditingController();
  TextEditingController Withdrawal_sur_Amount = TextEditingController();

  String _inputValue = '';

  bool shouldRepaint(CustomPainter oldDelegate) {
    return false;
  }

  DateTime now = DateTime.now();

  DateFormat formatter = DateFormat('dd/MM/yyyy');

  @override
  void initState() {
    currentIndex = 0;
    initializePage();
    benCrt.initializeCamera();
  }

  void initializePage() async {
    await profile.configApp();
    await benCrt.welfare(profile.responseMember!.id);
    await benCrt.doreloadbenefits(context);
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Stack(
      children: [
        Container(
          height: Get.height,
          width: Get.width,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Theme.of(context).colorScheme.primary,
                Theme.of(context).colorScheme.onPrimary,
              ],
              begin: Alignment.topCenter, // เริ่มต้น Gradient ที่มุมบนซ้าย
              end: Alignment.bottomCenter,
            ),
          ),
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.only(top: 10),
              child: Column(
                children: [
                  buildAppBar(context, 'btn_apply'.tr),
                  Container(
                    margin: EdgeInsets.only(top: 20.h, left: 20.w),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        buildButton(context, 0, 'ยืดหยุ่น'),
                        SizedBox(width: 5.w),
                        buildButton(context, 1, 'สุขภาพ'),
                        SizedBox(width: 5.w),
                        buildButton(context, 2,
                            'พื้นฐาน'), // Add as many buttons as needed
                      ],
                    ),
                  ),
                  Obx(() {
                    if (benCrt.currentIndex.value == 0) {
                      return FlexibleWidget(context);
                    } else if (benCrt.currentIndex.value == 1) {
                      return HealthWidget(
                          context); // Widget เมื่อกดปุ่ม 'สุขภาพ'
                    } else if (benCrt.currentIndex.value == 2) {
                      return BasicWidget(
                          context); // Widget เมื่อกดปุ่ม 'พื้นฐาน'
                    } else {
                      return FlexibleWidget(context);
                    }
                  }),
                ],
              ),
            ),
          ),
        ),
      ],
    ));
  }

  Widget buildAppBar(context, title) {
    return Container(
      margin: EdgeInsets.only(top: 50.h),
      child: Stack(
        children: [
          Center(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Align(
                  alignment: Alignment.centerRight,
                  child: InkWell(
                    onTap: () {
                      // Get.offAndToNamed('home');
                      Get.back();
                    },
                    child: SizedBox(
                      height: 34.h,
                      width: 34.w,
                      child: Center(
                        child: Container(
                          height: 14.h,
                          width: 9.w,
                          child: Image.asset(
                            'assets/ADD/back_Vector.png',
                            color: Theme.of(context).colorScheme.onSecondary,
                            scale: 1,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Center(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                AppWidget.normalText(
                  context,
                  title,
                  18.sp,
                  Theme.of(context).colorScheme.onSecondary,
                  FontWeight.w500,
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget buildButton(context, int index, String text) {
    return Obx(() {
      bool isSelected = benCrt.currentIndex.value == index;
      return InkWell(
        onTap: () {
          clearWithdrawalAmount();
          benCrt.cleardata();
          benCrt.currentIndex.value =
              index; // อัพเดทค่า currentIndex ใน controller
        },
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 10.w),
          height: 35.h,
          width: 90.w,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.r),
            border: Border.all(
              color: benCrt.currentIndex.value == index
                  ? Theme.of(context).colorScheme.surfaceContainer
                  : Theme.of(context).colorScheme.surfaceContainer,
              width: 1.0.w,
            ),
            color: benCrt.currentIndex.value == index
                ? Theme.of(context).colorScheme.surfaceBright
                : Theme.of(context).colorScheme.surface.withOpacity(0.1),
          ),
          child: Center(
            child: Text(
              text,
              style: TextStyle(
                color: isSelected
                    ? Theme.of(context).colorScheme.primaryContainer
                    : Theme.of(context).colorScheme.surfaceTint,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      );
    });
  }

  Widget buildButtonConfirm(BuildContext context, onTap, isDisabled) {
    return Container(
      height: 80.h,
      child: Align(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10.0),
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 10.h),
            width: double.infinity.w,
            child: InkWell(
              onTap: onTap,
              child: Container(
                height: 52.h,
                width: Get.width,
                decoration: BoxDecoration(
                  color: isDisabled
                      ? Theme.of(context)
                          .colorScheme
                          .secondaryContainer
                          .withOpacity(0.2)
                      : Theme.of(context)
                          .colorScheme
                          .secondaryContainer
                          .withOpacity(0.6),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isDisabled
                        ? Theme.of(context)
                            .colorScheme
                            .secondaryContainer
                            .withOpacity(0.2)
                        : Theme.of(context)
                            .colorScheme
                            .secondaryContainer
                            .withOpacity(1),
                    width: 1.5.w,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    AppWidget.normalText(
                      context,
                      'btn_savebasic'.tr,
                      16.sp,
                      isDisabled
                          ? Theme.of(context)
                              .colorScheme
                              .onSecondary
                              .withOpacity(0.2)
                          : Theme.of(context)
                              .colorScheme
                              .onSecondary
                              .withOpacity(1),
                      FontWeight.w900,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget FlexibleWidget(context) {
    return Stack(
      children: [
        Column(
          children: [
            Container(
              margin: EdgeInsets.only(top: 20.h, left: 20.h, right: 20.h),
              height: 140.h,
              width: Get.width,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Theme.of(context).colorScheme.primary.withOpacity(0.1),
                      Theme.of(context).colorScheme.onPrimary.withOpacity(0.3),
                    ]),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color:
                      Theme.of(context).colorScheme.secondary.withOpacity(0.2),
                  width: 1.0.w,
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(left: 20.w, top: 10.h),
                        child: AppWidget.normalText(
                            context,
                            'ui_amountWithdrow'.tr.toString(),
                            14.sp,
                            Theme.of(context).colorScheme.onSecondary,
                            FontWeight.w400),
                      ),
                      Padding(
                        padding: EdgeInsets.only(right: 20.w, top: 10.h),
                        child: AppWidget.normalText(
                            context,
                            'ui_recive'.tr.toString(),
                            14.sp,
                            Theme.of(context).colorScheme.onSecondary,
                            FontWeight.w400),
                      ),
                    ],
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(left: 20.w, top: 10.h),
                        child: AppWidget.normalText(
                            context,
                            double.parse(benCrt.benefitsbalance!)
                                .toStringAsFixed(2)
                                .replaceAllMapped(
                                    new RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                                    (Match m) => '${m[1]},'),
                            14.sp,
                            Theme.of(context).colorScheme.secondaryContainer,
                            FontWeight.w400),
                      ),
                      Padding(
                        padding: EdgeInsets.only(right: 20.w, top: 10.h),
                        child: AppWidget.normalText(
                            context,
                            "-" +
                                double.parse(benCrt.totalAlreadyWithdrawn!
                                        .toString())
                                    .toStringAsFixed(2)
                                    .replaceAllMapped(
                                        new RegExp(
                                            r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                                        (Match m) => '${m[1]},'),
                            14.sp,
                            Theme.of(context).colorScheme.secondaryFixed,
                            FontWeight.w400),
                      ),
                    ],
                  ),
                  Container(
                    margin: EdgeInsets.only(left: 20.h, right: 20.h),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        AppWidget.normalText(
                            context,
                            "/" +
                                double.parse(benCrt.start_benefitsresilient!)
                                    .toStringAsFixed(2)
                                    .replaceAllMapped(
                                        new RegExp(
                                            r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                                        (Match m) => '${m[1]},'),
                            14.sp,
                            Theme.of(context).colorScheme.secondaryContainer,
                            FontWeight.w400),
                        SizedBox(
                          height: 10.h,
                        ),
                        Image.asset('assets/ADD/Line 523.png', width: 350.w),
                        SizedBox(
                          height: 10.h,
                        ),
                        AppWidget.normalText(
                            context,
                            profile.date_benefitsresilient.toString(),
                            12.sp,
                            Theme.of(context).colorScheme.tertiary,
                            FontWeight.w400),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Container(
              margin: EdgeInsets.only(top: 20.h, left: 20.h, right: 20.h),
              height: 60.h,
              width: Get.width,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(10.r),
                border: Border.all(
                  color: Colors.grey.shade300,
                  width: 1.0.w,
                ),
              ),
              child: Container(
                margin: EdgeInsets.only(left: 10.h, right: 10.h),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Left text
                    AppWidget.normalText(
                      context,
                      'ui_amount2'.tr.toString(),
                      14.sp,
                      Theme.of(context)
                          .colorScheme
                          .onSecondary
                          .withOpacity(0.7),
                      FontWeight.w400,
                    ),
                    // Right input field or clickable text
                    Container(
                      width: 90.w,
                      child: TextField(
                        cursorColor: Colors.black,

                        textAlign: TextAlign.end,
                        keyboardType: TextInputType.number,
                        controller:
                            WithdrawalAmount, // Attach controller to TextField
                        style: new TextStyle(
                          color: Theme.of(context).colorScheme.onSecondary,
                          fontSize: 14.h,
                          fontFamily: "SukhumvitSet-Bold",
                        ),
                        onChanged: (value) {
                          String numericValue = value.replaceAll(
                              RegExp(r'[^0-9]'), ''); // ลบอักขระที่ไม่ใช่ตัวเลข
                          benCrt.withdrawalAmountText.value =
                              numericValue; // เก็บค่าเป็นตัวเลขล้วน

                          if (numericValue.isNotEmpty) {
                            WithdrawalAmount.value = TextEditingValue(
                              text: currencyFormat.format(int.parse(
                                  numericValue)), // แปลงเป็นรูปแบบเงิน
                              selection: TextSelection.collapsed(
                                  offset: currencyFormat
                                      .format(int.parse(numericValue))
                                      .length),
                            );
                          }
                        },

                        inputFormatters: [
                          FilteringTextInputFormatter.allow(
                              RegExp(r'[0-9]')), // อนุญาตเฉพาะตัวเลข
                          ThousandsSeparatorInputFormatter(), // เพิ่มคอมม่าหลังตัวเลข
                        ],
                        decoration: InputDecoration(
                          labelText: 'tf_amount'.tr.toString(),
                          counterText: '',
                          labelStyle: TextStyle(
                              color: Theme.of(context)
                                  .colorScheme
                                  .secondaryFixedDim,
                              fontSize: 12.h,
                              fontFamily: "SukhumvitSet-Bold"),
                          enabledBorder: const UnderlineInputBorder(
                            borderSide:
                                const BorderSide(color: Colors.transparent),
                          ),
                          focusedBorder: const UnderlineInputBorder(
                            borderSide:
                                const BorderSide(color: Colors.transparent),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Container(
              alignment: Alignment.topLeft,
              margin: EdgeInsets.only(
                  top: mediaQuery(context, "h", 20),
                  left: mediaQuery(context, "w", 71),
                  bottom: mediaQuery(context, "h", 25)),
              child: AppWidget.normalText(
                  context,
                  "ui_history".tr.toString(),
                  12.sp,
                  Theme.of(context).colorScheme.tertiary,
                  FontWeight.w400),
            ),
            Container(
              width: Get.width,
              height: mediaQuery(context, "h", 400),
              child: benCrt.databenefits.isEmpty
                  ? Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                            height: 20.h,
                            width: 20.w,
                            child:
                                Image.asset('assets/Exclamation-Circle.png')),
                        Container(
                          child: AppWidget.normalText(
                              context,
                              "ui_history_emtpy".tr.toString(),
                              12.sp,
                              Theme.of(context)
                                  .colorScheme
                                  .onSecondary
                                  .withOpacity(0.9),
                              FontWeight.w400),
                        ),
                      ],
                    )
                  : ListView(
                      shrinkWrap: true,
                      padding: EdgeInsets.only(top: 0),
                      children:
                          ListHistory(context), // ฟังก์ชันแสดงรายการประวัติ
                    ),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 20, right: 20, top: 20),
              child: Liner(context),
            ),
            buildDetail(context, profile.responseMember!.number_bank,
                formatter.format(now)),
            Obx(
              () => buildButtonConfirm(context, () {
                if (benCrt.withdrawalAmountText.value == '' ||
                    benCrt.withdrawalAmountText.value == '0') {
                  Get.snackbar('ui_error'.tr, 'กรุณากรอกข้อมูลให้ครบถ้วน'.tr);
                } else {
                  confirmDetails(context, profile.responseMember!.number_bank,
                      formatter.format(now), () {
                    benCrt.RecordWithdrawal(
                        context, benCrt.withdrawalAmountText.value, '-');
                  },
                      benCrt.withdrawalAmountText.value == '' ||
                              benCrt.withdrawalAmountText.value == '0'
                          ? false
                          : true);
                }
              },
                  benCrt.withdrawalAmountText.value == '' ||
                      benCrt.withdrawalAmountText.value == '0'),
            )
          ],
        ),
      ],
    );
  }

  Future<void> clearWithdrawalAmount() async {
    await benCrt.withdrawalAmountText.value.isEmpty; // อัปเดตค่าใน RxString
    await WithdrawalAmount.isBlank; // อัปเดตค่าใน TextField
    WithdrawalAmount.text = '0';
    Withdrawal_sur_Amount.text = '0';
    benCrt.withdrawalAmountText.value = '0';
    benCrt.InputRocveDay.value == '0'.obs;
    benCrt.InputSum.value == '0'.obs;
  }

  Future confirmDetails(context, bankAccount, date, onTap, isDisabled) {
    return showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.all(16),
          height: Get.height * 0.36, // ความสูงของ Bottom Sheet
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // แสดงรายละเอียดบัญชีธนาคารและวันที่
              buildDetail(context, bankAccount, date),
              Spacer(),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 10),
                child: InkWell(
                  onTap: isDisabled
                      ? null
                      : onTap, // ปิดการกดหาก isDisabled เป็น true
                  child: Container(
                    height: 52.h,
                    decoration: BoxDecoration(
                      color: isDisabled
                          ? Theme.of(context)
                              .colorScheme
                              .secondaryContainer
                              .withOpacity(0.2)
                          : Theme.of(context)
                              .colorScheme
                              .secondaryContainer
                              .withOpacity(0.6),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: isDisabled
                            ? Theme.of(context)
                                .colorScheme
                                .secondaryContainer
                                .withOpacity(0.2)
                            : Theme.of(context).colorScheme.secondaryContainer,
                        width: 1.5.w,
                      ),
                    ),
                    child: Center(
                      child: AppWidget.normalText(
                        context,
                        'btn_GTCok'.tr,
                        16.sp,
                        isDisabled
                            ? Theme.of(context)
                                .colorScheme
                                .onSecondary
                                .withOpacity(0.2)
                            : Theme.of(context).colorScheme.onSecondary,
                        FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),
              SizedBox(height: 10.h),
              // ปุ่มยกเลิก
              Center(
                child: GestureDetector(
                  onTap: () => Navigator.pop(context), // ปิด Bottom Sheet
                  child: AppWidget.normalText(
                    context,
                    'btn_cancel'.tr,
                    16.sp,
                    Theme.of(context).colorScheme.onSecondary,
                    FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  //*สวัสดิการพื้นฐาน
  Widget BasicWidget(context) {
    return Stack(
      children: [
        Column(
          children: [
            SizedBox(
              height: 20.h,
            ),
            buildDropdown(
                context,
                'dp_welfareselect'.tr.obs,
                benCrt.testStatus.value,
                benCrt.updateselectedType,
                benCrt.selectedType),
            Padding(
              padding: EdgeInsets.only(top: 10.h, left: 20.w, right: 20.w),
              child: Row(
                children: [
                  AppWidget.normalText(context, '* กรณีลาคลอด '.tr.toString(),
                      12.sp, Color(0xFF4CCAB4), FontWeight.w400),
                  AppWidget.normalText(
                      context,
                      'ให้ระบุวันที่เริ่มทำคลอดวันแรก'.tr.toString(),
                      12.sp,
                      Color(0xFFA78AFE),
                      FontWeight.w400),
                ],
              ),
            ),
            buildDatePicker(context, 'tf_datework'.tr.obs,
                benCrt.updateselectedTime, benCrt.selectedTime),
            buildImage(context),
            buildFormInputBank(context),
            Padding(
              padding: const EdgeInsets.only(left: 20, right: 20, top: 50),
              child: Liner(context),
            ),
            buildDetail(
                context, benCrt.accountNumber.value, formatter.format(now)),
            Obx(() {
              // กำหนดเงื่อนไขว่าฟิลด์ทั้ง 3 ต้องไม่ว่าง
              bool isAllFilled = benCrt.selectedTime.value.isNotEmpty &&
                  benCrt.selectedType.value.isNotEmpty &&
                  benCrt.accountNumber.value.isNotEmpty;

              return buildButtonConfirm(context, () {
                // หากฟิลด์ใดว่าง ก็แสดง snackbar แจ้งให้กรอกข้อมูลให้ครบถ้วน
                if (!isAllFilled) {
                  Get.snackbar('ui_error'.tr, 'กรุณากรอกข้อมูลให้ครบถ้วน'.tr);
                  return;
                }
                // ถ้าข้อมูลครบถ้วนแล้ว ให้เรียกใช้งาน confirmDetails
                confirmDetails(
                    context, benCrt.accountNumber.value, formatter.format(now),
                    () {
                  benCrt.updateToBCTbasic(
                    context,
                    benCrt.selectedTime.value,
                    benCrt.selectedType.value,
                    benCrt.accountNumber.value,
                    ''.obs,
                    ''.obs,
                    'ไม่มีการ์ด'.obs,
                  );
                  Get.snackbar('การทำงานปกติ', 'ใส่ Function ตรงนี้');
                }, true // ส่ง true เมื่อทุกฟิลด์ถูกกรอก
                    );
              },
                  // ปุ่มจะ disable เมื่อมีฟิลด์ใดว่าง (isAllFilled เป็น false)
                  !isAllFilled);
            })
          ],
        ),
      ],
    );
  }

  Widget HealthWidget(context) {
    return Container(
        child: Stack(
      children: [
        Column(
          children: [
            Container(
              margin: EdgeInsets.only(top: 20.h, left: 20.h, right: 20.h),
              height: 130.h,
              width: Get.width,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Theme.of(context).colorScheme.primary.withOpacity(0.1),
                      Theme.of(context).colorScheme.onPrimary.withOpacity(0.3),
                    ]),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color:
                      Theme.of(context).colorScheme.secondary.withOpacity(0.2),
                  width: 1.0.w,
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        width: 120.w,
                        margin:
                            EdgeInsets.only(left: 20.w, top: 10.h, right: 10.w),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            AppWidget.normalText(
                                context,
                                'ui_OPD'.tr.toString(),
                                12.sp,
                                Theme.of(context).colorScheme.secondaryFixedDim,
                                FontWeight.w400),
                            AppWidget.normalText(
                                context,
                                'ui_amountWithdrow'.tr.toString(),
                                12.sp,
                                Theme.of(context).colorScheme.onSecondary,
                                FontWeight.w400),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                AppWidget.normalText(
                                    context,
                                    profile.OPD_Total.toString()
                                        .replaceAllMapped(
                                            new RegExp(
                                                r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                                            (Match m) => '${m[1]},'),
                                    16.sp,
                                    Theme.of(context)
                                        .colorScheme
                                        .secondaryContainer,
                                    FontWeight.w400),
                                AppWidget.normalText(
                                    context,
                                    '-0',
                                    12.sp,
                                    Theme.of(context)
                                        .colorScheme
                                        .secondaryFixed,
                                    FontWeight.w400),
                              ],
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                AppWidget.normalText(
                                    context,
                                    '/' +
                                        profile.OPD_Total.toString()
                                            .replaceAllMapped(
                                                new RegExp(
                                                    r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                                                (Match m) => '${m[1]},'),
                                    14.sp,
                                    Theme.of(context)
                                        .colorScheme
                                        .secondaryContainer,
                                    FontWeight.w400),
                                AppWidget.normalText(
                                    context,
                                    'ui_recive'.tr.toString(),
                                    12.sp,
                                    Theme.of(context)
                                        .colorScheme
                                        .secondaryFixed,
                                    FontWeight.w400),
                              ],
                            )
                          ],
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.only(top: 10.h),
                        width:
                            1.w, // กำหนดความกว้างของ Container ที่เก็บเส้นประ
                        height: 100.h, // ความสูงของเส้นประ (ความหนา)
                        child: CustomPaint(
                          painter:
                              DottedLinePainterVertical(), // ใช้ CustomPainter ในการวาดเส้น
                        ),
                      ),
                      Container(
                        width: 120.w,
                        margin: EdgeInsets.only(right: 20.w, top: 10.h),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            AppWidget.normalText(
                                context,
                                'ui_IPD'.tr.toString(),
                                12.sp,
                                Theme.of(context).colorScheme.secondaryFixedDim,
                                FontWeight.w400),
                            AppWidget.normalText(
                                context,
                                'ui_amountWithdrow'.tr.toString(),
                                12.sp,
                                Theme.of(context).colorScheme.onSecondary,
                                FontWeight.w400),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                AppWidget.normalText(
                                    context,
                                    profile.IPD_Total.toString()
                                        .replaceAllMapped(
                                            new RegExp(
                                                r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                                            (Match m) => '${m[1]},'),
                                    16.sp,
                                    Theme.of(context)
                                        .colorScheme
                                        .secondaryContainer,
                                    FontWeight.w400),
                                AppWidget.normalText(
                                    context,
                                    '-0',
                                    12.sp,
                                    Theme.of(context)
                                        .colorScheme
                                        .secondaryFixed,
                                    FontWeight.w400),
                              ],
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                AppWidget.normalText(
                                    context,
                                    '/' +
                                        profile.IPD_Total.toString()
                                            .replaceAllMapped(
                                                new RegExp(
                                                    r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                                                (Match m) => '${m[1]},'),
                                    14.sp,
                                    Theme.of(context)
                                        .colorScheme
                                        .secondaryContainer,
                                    FontWeight.w400),
                                AppWidget.normalText(
                                    context,
                                    'ui_recive'.tr.toString(),
                                    12.sp,
                                    Theme.of(context)
                                        .colorScheme
                                        .secondaryFixed,
                                    FontWeight.w400),
                              ],
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            SizedBox(
              height: 10.h,
            ),
            buildDropdown(context, 'ui_selectinsurance'.tr.obs,
                ['dp_out'.tr.obs, 'dp_in'.tr.obs], (value) {
              clearInputs();
              benCrt.cleardata();
              benCrt.selectedPatient.value = value; // อัปเดตค่าที่เลือก
            }, benCrt.selectedPatient),
            Column(
              children: [
                benCrt.selectedPatient.value == ''
                    ? Container(
                        child: Column(
                        children: [
                          Container(
                            alignment: Alignment.topLeft,
                            margin: EdgeInsets.only(
                                top: mediaQuery(context, "h", 20),
                                left: mediaQuery(context, "w", 71),
                                bottom: mediaQuery(context, "h", 25)),
                            child: AppWidget.normalText(
                                context,
                                "ui_history".tr.toString(),
                                12.sp,
                                Theme.of(context).colorScheme.tertiary,
                                FontWeight.w400),
                          ),
                          Container(
                            width: Get.width,
                            height: mediaQuery(context, "h", 400),
                            child: benCrt.databenefits.isEmpty
                                ? Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      SizedBox(
                                          height: 20.h,
                                          width: 20.w,
                                          child: Image.asset(
                                              'assets/Exclamation-Circle.png')),
                                      Container(
                                        child: AppWidget.normalText(
                                            context,
                                            "ui_history_emtpy".tr.toString(),
                                            12.sp,
                                            Theme.of(context)
                                                .colorScheme
                                                .onSecondary
                                                .withOpacity(0.9),
                                            FontWeight.w400),
                                      ),
                                    ],
                                  )
                                : ListView(
                                    shrinkWrap: true,
                                    padding: EdgeInsets.only(top: 0),
                                    children: ListHistory(
                                        context), // ฟังก์ชันแสดงรายการประวัติ
                                  ),
                          ),
                        ],
                      ))
                    : Column(
                        children: [
                          Obx(() {
                            return benCrt.selectedPatient.value == 'dp_in'.tr
                                ? Column(
                                    children: [
                                      Container(
                                        margin: EdgeInsets.only(
                                            top: 10.h, left: 20.h, right: 20.h),
                                        height: 60.h,
                                        width: Get.width,
                                        decoration: BoxDecoration(
                                          color: Theme.of(context)
                                              .colorScheme
                                              .primary
                                              .withOpacity(0.1),
                                          borderRadius:
                                              BorderRadius.circular(10.r),
                                          border: Border.all(
                                            color: Colors.grey.shade300,
                                            width: 1.0.w,
                                          ),
                                        ),
                                        child: Container(
                                          margin: EdgeInsets.only(
                                              left: 10.h, right: 10.h),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              // Left text
                                              AppWidget.normalText(
                                                context,
                                                'ui_headhealthamountnet'
                                                    .tr
                                                    .toString(),
                                                14.sp,
                                                Theme.of(context)
                                                    .colorScheme
                                                    .onSecondary
                                                    .withOpacity(0.7),
                                                FontWeight.w400,
                                              ),
                                              // Right input field or clickable text
                                              Container(
                                                width: 90.w,
                                                child: TextField(
                                                  onChanged: (value) {
                                                    benCrt
                                                        .updateInputSum(value);
                                                  },
                                                  cursorColor: Colors.black,

                                                  textAlign: TextAlign.end,
                                                  keyboardType:
                                                      TextInputType.number,
                                                  controller:
                                                      WithdrawalAmount, // Attach controller to TextField
                                                  style: new TextStyle(
                                                    color: Theme.of(context)
                                                        .colorScheme
                                                        .onSecondary,
                                                    fontSize: 14.h,
                                                    fontFamily:
                                                        "SukhumvitSet-Bold",
                                                  ),
                                                  inputFormatters: [
                                                    FilteringTextInputFormatter
                                                        .allow(RegExp(
                                                            r'[0-9]')), // อนุญาตเฉพาะตัวเลข
                                                    ThousandsSeparatorInputFormatter(),
                                                  ],
                                                  decoration: InputDecoration(
                                                    labelText: 'tf_amount'
                                                        .tr
                                                        .toString(),
                                                    counterText: '',
                                                    labelStyle: TextStyle(
                                                        color: Theme.of(context)
                                                            .colorScheme
                                                            .secondaryFixedDim,
                                                        fontSize: 12.h,
                                                        fontFamily:
                                                            "SukhumvitSet-Bold"),
                                                    enabledBorder:
                                                        const UnderlineInputBorder(
                                                      borderSide:
                                                          const BorderSide(
                                                              color: Colors
                                                                  .transparent),
                                                    ),
                                                    focusedBorder:
                                                        const UnderlineInputBorder(
                                                      borderSide:
                                                          const BorderSide(
                                                              color: Colors
                                                                  .transparent),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                      Padding(
                                        padding: EdgeInsets.only(
                                            top: 10.h, left: 20.h, right: 20.h),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              children: [
                                                AppWidget.normalText(
                                                  context,
                                                  '* ยอดรวมทั้งหมดที่จ่ายจริง ',
                                                  12.sp,
                                                  Theme.of(context)
                                                      .colorScheme
                                                      .secondaryContainer,
                                                  FontWeight.w400,
                                                ),
                                                AppWidget.normalText(
                                                  context,
                                                  'ตามใบเสร็จ',
                                                  12.sp,
                                                  Theme.of(context)
                                                      .colorScheme
                                                      .secondaryFixedDim,
                                                  FontWeight.w400,
                                                ),
                                              ],
                                            ),
                                            AppWidget.normalText(
                                              context,
                                              '/ กรณีไม่มีค่าผ่าตัด ไม่ต้องกรอกจำนวนเงิน',
                                              12.sp,
                                              Theme.of(context)
                                                  .colorScheme
                                                  .secondaryFixedDim,
                                              FontWeight.w400,
                                            ),
                                          ],
                                        ),
                                      ),
                                      SizedBox(height: 10),
                                      Container(
                                        margin: EdgeInsets.only(
                                            top: 10.h, left: 20.h, right: 20.h),
                                        height: 60.h,
                                        width: Get.width,
                                        decoration: BoxDecoration(
                                          color: Theme.of(context)
                                              .colorScheme
                                              .primary
                                              .withOpacity(0.1),
                                          borderRadius:
                                              BorderRadius.circular(10.r),
                                          border: Border.all(
                                            color: Colors.grey.shade300,
                                            width: 1.0.w,
                                          ),
                                        ),
                                        child: Container(
                                          margin: EdgeInsets.only(
                                              left: 10.h, right: 10.h),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Row(
                                                children: [
                                                  AppWidget.normalText(
                                                    context,
                                                    'ค่าผ่าตัด'.tr.toString(),
                                                    14.sp,
                                                    Theme.of(context)
                                                        .colorScheme
                                                        .onSecondary
                                                        .withOpacity(0.7),
                                                    FontWeight.w600,
                                                  ),
                                                  AppWidget.normalText(
                                                    context,
                                                    '(จ่ายสูงสุด 5,000 บาท)'
                                                        .tr
                                                        .toString(),
                                                    14.sp,
                                                    Theme.of(context)
                                                        .colorScheme
                                                        .onSecondary
                                                        .withOpacity(0.7),
                                                    FontWeight.w400,
                                                  ),
                                                ],
                                              ),
                                              // Left text

                                              // Right input field or clickable text
                                              Container(
                                                width: 90.w,
                                                child: TextField(
                                                  onChanged: (value) {
                                                    benCrt.InputRocveDay.value =
                                                        Withdrawal_sur_Amount
                                                            .text;
                                                    benCrt.updateInputRocveDay(
                                                        value);
                                                  },
                                                  cursorColor: Colors.black,
                                                  textAlign: TextAlign.end,
                                                  keyboardType:
                                                      TextInputType.number,
                                                  controller:
                                                      Withdrawal_sur_Amount, // Attach controller to TextField
                                                  style: new TextStyle(
                                                    color: Theme.of(context)
                                                        .colorScheme
                                                        .onSecondary,
                                                    fontSize: 14.h,
                                                    fontFamily:
                                                        "SukhumvitSet-Bold",
                                                  ),
                                                  inputFormatters: [
                                                    FilteringTextInputFormatter
                                                        .allow(RegExp(
                                                            r'[0-9]')), // อนุญาตเฉพาะตัวเลข
                                                    ThousandsSeparatorInputFormatter(),
                                                  ],
                                                  decoration: InputDecoration(
                                                    labelText: 'tf_amount'
                                                        .tr
                                                        .toString(),
                                                    counterText: '',
                                                    labelStyle: TextStyle(
                                                        color: Theme.of(context)
                                                            .colorScheme
                                                            .secondaryFixedDim,
                                                        fontSize: 12.h,
                                                        fontFamily:
                                                            "SukhumvitSet-Bold"),
                                                    enabledBorder:
                                                        const UnderlineInputBorder(
                                                      borderSide:
                                                          const BorderSide(
                                                              color: Colors
                                                                  .transparent),
                                                    ),
                                                    focusedBorder:
                                                        const UnderlineInputBorder(
                                                      borderSide:
                                                          const BorderSide(
                                                              color: Colors
                                                                  .transparent),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  )
                                : Container(
                                    margin: EdgeInsets.only(
                                        top: 10.h, left: 20.h, right: 20.h),
                                    height: 60.h,
                                    width: Get.width,
                                    decoration: BoxDecoration(
                                      color: Theme.of(context)
                                          .colorScheme
                                          .primary
                                          .withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(10.r),
                                      border: Border.all(
                                        color: Colors.grey.shade300,
                                        width: 1.0.w,
                                      ),
                                    ),
                                    child: Container(
                                      margin: EdgeInsets.only(
                                          left: 10.h, right: 10.h),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          // Left text
                                          AppWidget.normalText(
                                            context,
                                            'ui_headhealthamountnet'
                                                .tr
                                                .toString(),
                                            14.sp,
                                            Theme.of(context)
                                                .colorScheme
                                                .onSecondary
                                                .withOpacity(0.7),
                                            FontWeight.w400,
                                          ),
                                          // Right input field or clickable text
                                          Container(
                                            width: 90.w,
                                            child: TextField(
                                              cursorColor: Colors.black,
                                              onChanged: (value) {
                                                benCrt.InputSum.value =
                                                    WithdrawalAmount.text;
                                                benCrt.updateInputSum(value);
                                              },

                                              textAlign: TextAlign.end,
                                              keyboardType:
                                                  TextInputType.number,
                                              controller:
                                                  WithdrawalAmount, // Attach controller to TextField
                                              style: new TextStyle(
                                                color: Theme.of(context)
                                                    .colorScheme
                                                    .onSecondary,
                                                fontSize: 14.h,
                                                fontFamily: "SukhumvitSet-Bold",
                                              ),
                                              inputFormatters: [
                                                FilteringTextInputFormatter
                                                    .allow(RegExp(
                                                        r'[0-9]')), // อนุญาตเฉพาะตัวเลข
                                                ThousandsSeparatorInputFormatter(),
                                              ],
                                              decoration: InputDecoration(
                                                labelText:
                                                    'tf_amount'.tr.toString(),
                                                counterText: '',
                                                labelStyle: TextStyle(
                                                    color: Theme.of(context)
                                                        .colorScheme
                                                        .secondaryFixedDim,
                                                    fontSize: 12.h,
                                                    fontFamily:
                                                        "SukhumvitSet-Bold"),
                                                enabledBorder:
                                                    const UnderlineInputBorder(
                                                  borderSide: const BorderSide(
                                                      color:
                                                          Colors.transparent),
                                                ),
                                                focusedBorder:
                                                    const UnderlineInputBorder(
                                                  borderSide: const BorderSide(
                                                      color:
                                                          Colors.transparent),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  );
                          }),
                          SizedBox(height: 10),
                          buildImage(context),
                        ],
                      ),
                Padding(
                  padding: const EdgeInsets.only(left: 20, right: 20, top: 30),
                  child: Liner(context),
                ),
                buildDetail(context, profile.responseMember!.number_bank,
                    formatter.format(now)),
                Obx(() => buildButtonConfirm(
                      context,
                      () {
                        // ตรวจสอบเงื่อนไขตามประเภทของผู้ป่วย
                        if (benCrt.selectedPatient.value == 'dp_in'.tr) {
                          // ถ้า dp_in ให้เช็คเฉพาะ InputSum
                          if (benCrt.InputSum.value == '' ||
                              benCrt.InputSum.value == '0') {
                            Get.snackbar(
                                'ui_error'.tr, 'กรุณากรอกข้อมูลให้ครบถ้วน'.tr);
                            return;
                          }
                        } else if (benCrt.selectedPatient.value ==
                            'dp_out'.tr) {
                          // ถ้า dp_out ให้เช็คทั้ง InputRocveDay และ InputSum
                          if ((benCrt.InputRocveDay.value == '' ||
                                  benCrt.InputRocveDay.value == '0') &&
                              (benCrt.InputSum.value == '' ||
                                  benCrt.InputSum.value == '0')) {
                            Get.snackbar(
                                'ui_error'.tr, 'กรุณากรอกข้อมูลให้ครบถ้วน'.tr);
                            return;
                          }
                        }

                        // ถ้าเงื่อนไขครบถ้วน ให้ดำเนินการต่อ
                        confirmDetails(
                            context,
                            profile.responseMember!.number_bank,
                            formatter.format(now), () {
                          benCrt.WithdrawHealthBenefits(context);
                          Get.snackbar('การทำงานปกติ', 'ใส่ Function ตรงนี้');
                        }, true // ส่ง true เมื่อผ่านเงื่อนไข
                            );
                      },
                      // กำหนดสถานะปุ่ม (isDisabled) ตามประเภทผู้ป่วย
                      benCrt.selectedPatient.value == 'dp_out'.tr
                          ? !(benCrt.InputSum.value != '' &&
                              benCrt.InputSum.value != '0')
                          : benCrt.selectedPatient.value == 'dp_in'.tr
                              ? !((benCrt.InputRocveDay.value != '' &&
                                      benCrt.InputRocveDay.value != '0') &&
                                  (benCrt.InputSum.value != '' &&
                                      benCrt.InputSum.value != '0'))
                              : true,
                    ))
              ],
            ),
          ],
        ),
      ],
    ));
  }

  List<Widget> ListHistory(context) {
    List<Widget> list = [];
    for (var i = 0; i < benCrt.databenefits.length.toInt(); i++) {
      list.add(
        Stack(
          children: <Widget>[
            Row(
              children: [
                Container(
                  child: Container(
                    width: mediaQuery(context, "w", 40),
                    height: mediaQuery(context, "h", 20),
                    margin: EdgeInsets.only(
                        left: mediaQuery(context, "w", 46),
                        top: mediaQuery(context, "h", 20)),
                    child: Stack(
                      children: <Widget>[
                        SvgPicture.string(
                          '<svg width="6" height="6" viewBox="0 0 6 6" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="3" cy="3" r="3" fill="#FF88C1"/></svg>',
                          allowDrawingOutsideViewBox: true,
                          width: 10.w,
                          fit: BoxFit.fill,
                        ),
                      ],
                    ),
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(top: 10.h),
                  child: AppWidget.normalText(
                      context,
                      "ui_recive".tr.toString(),
                      14.sp,
                      Theme.of(context).colorScheme.onSecondary,
                      FontWeight.w400),
                ),
                SizedBox(width: 10.w),
                Padding(
                  padding: EdgeInsets.only(top: 10.h),
                  child: AppWidget.normalText(
                      context,
                      convertDateTime(
                          benCrt.databenefits[i]["payDate"].toString(),
                          "dd/MM/YYYY"),
                      14.sp,
                      Theme.of(context).colorScheme.tertiary,
                      FontWeight.w400),
                ),
                Padding(
                    padding: EdgeInsets.only(top: 10.h, left: 150.w),
                    child: AppWidget.normalText(
                        context,
                        "-" +
                            benCrt.databenefits[i]["summary"]
                                .toString()
                                .replaceAllMapped(
                                    new RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                                    (Match m) => '${m[1]},'),
                        14.sp,
                        Theme.of(context).colorScheme.secondaryFixed,
                        FontWeight.w400)),
              ],
            ),
            Container(
              margin: EdgeInsets.only(
                  left: mediaQuery(context, "w", 50),
                  top: mediaQuery(context, "h", 90),
                  right: mediaQuery(context, "w", 50)),
              width: Get.width, // กำหนดความกว้างของ Container ที่เก็บเส้นประ
              height: 1, // ความสูงของเส้นประ (ความหนา)
              child: CustomPaint(
                painter: DottedLinePainter(), // ใช้ CustomPainter ในการวาดเส้น
              ),
            ),
          ],
        ),
      );
      list.add(Container(
        height: mediaQuery(context, "h", 10),
      ));
    }
    return list;
  }

  // Widget builFormnoti(
  Widget buildDetail(context, bankAccount, date) {
    return benCrt.currentIndex.value == 0
        ? Container(
            padding: EdgeInsets.only(left: 20.w, right: 20.w),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: 32.h,
                      child: AppWidget.normalText(
                        context,
                        'btn_disbursement'.tr,
                        14.sp,
                        Theme.of(context).colorScheme.onSecondary,
                        FontWeight.w400,
                      ),
                    ),
                    SizedBox(
                      height: 10.h,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        AppWidget.normalText(
                          context,
                          'btn_WelfareList'.tr,
                          14.sp,
                          Theme.of(context)
                              .colorScheme
                              .onSecondary
                              .withOpacity(0.9),
                          FontWeight.w400,
                        ),
                        AppWidget.normalTextRX(
                          context,
                          benCrt.selectedType != '' &&
                                  benCrt.selectedType != null
                              ? benCrt.selectedType.toString().obs
                              : '-'.obs,
                          14.sp,
                          Theme.of(context)
                              .colorScheme
                              .onSecondary
                              .withOpacity(0.9),
                          FontWeight.w400,
                        ),
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        AppWidget.normalText(
                          context,
                          'btn_amount_disbursement'.tr,
                          14.sp,
                          const Color(0xFFA596FF),
                          FontWeight.w400,
                        ),
                        Row(
                          children: [
                            Obx(() {
                              // เช็คว่าค่า withdrawalAmountText ว่างหรือไม่
                              String displayValue =
                                  benCrt.withdrawalAmountText.value.isNotEmpty
                                      ? benCrt.withdrawalAmountText.value
                                      : '0';

                              return AppWidget.normalTextRX(
                                context,
                                displayValue.obs, // แสดงผลลัพธ์ที่เช็คแล้ว
                                14.sp,
                                const Color(0xFFA596FF),
                                FontWeight.w400,
                              );
                            }),
                            AppWidget.normalText(
                              context,
                              ' ' + 'ui_Bath'.tr,
                              14.sp,
                              Theme.of(context)
                                  .colorScheme
                                  .onSecondary
                                  .withOpacity(0.9),
                              FontWeight.w400,
                            )
                          ],
                        )
                      ],
                    ),
                    SizedBox(
                      height: 10.h,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        AppWidget.normalText(
                          context,
                          'btn_transfer_account'.tr,
                          14.sp,
                          Theme.of(context)
                              .colorScheme
                              .onSecondary
                              .withOpacity(0.9),
                          FontWeight.w400,
                        ),
                        Row(
                          children: [
                            benCrt.currentIndex.value == 2
                                ? AppWidget.normalTextRX(
                                    context,
                                    benCrt.accountNumber.value.isNotEmpty &&
                                            bankAccount != '' &&
                                            bankAccount != null
                                        ? bankAccount.toString().obs
                                        : '-'.obs,
                                    14.sp,
                                    Theme.of(context)
                                        .colorScheme
                                        .onSecondary
                                        .withOpacity(0.9),
                                    FontWeight.w400,
                                  )
                                : AppWidget.normalTextRX(
                                    context,
                                    benCrt.profile.responseMember
                                                    ?.number_bank !=
                                                null &&
                                            bankAccount != '' &&
                                            bankAccount != null
                                        ? bankAccount.toString().obs
                                        : '-'.obs,
                                    14.sp,
                                    Theme.of(context)
                                        .colorScheme
                                        .onSecondary
                                        .withOpacity(0.9),
                                    FontWeight.w400,
                                  ),
                          ],
                        )
                      ],
                    ),
                    SizedBox(
                      height: 10.h,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        AppWidget.normalText(
                          context,
                          'btn_recording_date'.tr,
                          14.sp,
                          Theme.of(context)
                              .colorScheme
                              .onSecondary
                              .withOpacity(0.7),
                          FontWeight.w400,
                        ),
                        AppWidget.normalText(
                          context,
                          date != null || date != '' && date != '0'
                              ? date.toString()
                              : '-'.obs,
                          14.sp,
                          Theme.of(context)
                              .colorScheme
                              .onSecondary
                              .withOpacity(0.7),
                          FontWeight.w400,
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          )
        : benCrt.currentIndex.value == 1
            ? Container(
                padding: EdgeInsets.only(left: 20.w, right: 20.w),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: 32.h,
                          child: AppWidget.normalText(
                            context,
                            'btn_disbursement'.tr,
                            14.sp,
                            Theme.of(context).colorScheme.onSecondary,
                            FontWeight.w400,
                          ),
                        ),
                        SizedBox(
                          height: 10.h,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            AppWidget.normalText(
                              context,
                              'btn_WelfareList'.tr,
                              14.sp,
                              Theme.of(context)
                                  .colorScheme
                                  .onSecondary
                                  .withOpacity(0.9),
                              FontWeight.w400,
                            ),
                            AppWidget.normalTextRX(
                              context,
                              benCrt.selectedPatient != '' &&
                                      benCrt.selectedPatient != null
                                  ? benCrt.selectedPatient.toString().obs
                                  : '-'.obs,
                              14.sp,
                              Theme.of(context)
                                  .colorScheme
                                  .onSecondary
                                  .withOpacity(0.9),
                              FontWeight.w400,
                            ),
                          ],
                        ),
                        SizedBox(
                          height: 10.h,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            AppWidget.normalText(
                              context,
                              'btn_amount_disbursement'.tr,
                              14.sp,
                              const Color(0xFFA596FF),
                              FontWeight.w400,
                            ),
                            Row(
                              children: [
                                Obx(() {
                                  String displayValue;

                                  if (benCrt.selectedPatient != 'dp_out'.tr) {
                                    // เช็คว่าค่า InputRocveDay ว่างหรือไม่
                                    displayValue =
                                        benCrt.InputRocveDay.value.isNotEmpty
                                            ? benCrt.InputRocveDay.value
                                            : '0';
                                  } else if (benCrt.selectedPatient ==
                                      'dp_out'.tr) {
                                    // เช็คว่าค่า InputSum ว่างหรือไม่
                                    displayValue =
                                        benCrt.InputSum.value.isNotEmpty
                                            ? benCrt.InputSum.value
                                            : '0';
                                  } else {
                                    displayValue =
                                        '0'; // ถ้าทั้งสองเงื่อนไขไม่เข้า ให้แสดง 0
                                  }

                                  return AppWidget.normalTextRX(
                                    context,
                                    displayValue.obs, // แสดงค่าที่ได้จากการเช็ค
                                    14.sp,
                                    const Color(0xFFA596FF),
                                    FontWeight.w400,
                                  );
                                }),
                                AppWidget.normalText(
                                  context,
                                  ' ' + 'ui_Bath'.tr,
                                  14.sp,
                                  Theme.of(context)
                                      .colorScheme
                                      .onSecondary
                                      .withOpacity(0.9),
                                  FontWeight.w400,
                                ),
                              ],
                            )
                          ],
                        ),
                        SizedBox(
                          height: 10.h,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            AppWidget.normalText(
                              context,
                              'btn_transfer_account'.tr,
                              14.sp,
                              Theme.of(context)
                                  .colorScheme
                                  .onSecondary
                                  .withOpacity(0.9),
                              FontWeight.w400,
                            ),
                            Row(
                              children: [
                                AppWidget.normalTextRX(
                                  context,
                                  benCrt.AccountNumberController.value !=
                                              null &&
                                          bankAccount != '' &&
                                          bankAccount != null
                                      ? bankAccount.toString().obs
                                      : '-'.obs,
                                  14.sp,
                                  Theme.of(context)
                                      .colorScheme
                                      .onSecondary
                                      .withOpacity(0.9),
                                  FontWeight.w400,
                                ),
                              ],
                            )
                          ],
                        ),
                        SizedBox(
                          height: 10.h,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            AppWidget.normalText(
                              context,
                              'btn_recording_date'.tr,
                              14.sp,
                              Theme.of(context)
                                  .colorScheme
                                  .onSecondary
                                  .withOpacity(0.7),
                              FontWeight.w400,
                            ),
                            AppWidget.normalText(
                              context,
                              date != null || date != '' && date != '0'
                                  ? date.toString()
                                  : '-'.obs,
                              14.sp,
                              Theme.of(context)
                                  .colorScheme
                                  .onSecondary
                                  .withOpacity(0.7),
                              FontWeight.w400,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              )
            : benCrt.currentIndex.value == 2
                ? Container(
                    padding: EdgeInsets.only(left: 20.w, right: 20.w),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(
                              height: 32.h,
                              child: AppWidget.normalText(
                                context,
                                'btn_disbursement'.tr,
                                14.sp,
                                Theme.of(context).colorScheme.onSecondary,
                                FontWeight.w400,
                              ),
                            ),
                            SizedBox(
                              height: 10.h,
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                AppWidget.normalText(
                                  context,
                                  'btn_WelfareList'.tr,
                                  14.sp,
                                  Theme.of(context)
                                      .colorScheme
                                      .onSecondary
                                      .withOpacity(0.9),
                                  FontWeight.w400,
                                ),
                                Expanded(
                                  child: AppWidget.normalTextRX(
                                    context,
                                    benCrt.selectedType != '' &&
                                            benCrt.selectedType != null
                                        ? benCrt.selectedType.toString().obs
                                        : '-'.obs,
                                    14.sp,
                                    Theme.of(context)
                                        .colorScheme
                                        .onSecondary
                                        .withOpacity(0.9),
                                    FontWeight.w400,
                                    // จัดตำแหน่งข้อความไปด้านขวา
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(
                              height: 10.h,
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                AppWidget.normalText(
                                  context,
                                  'ui_GTCtransfermoney'.tr,
                                  14.sp,
                                  const Color(0xFFA596FF),
                                  FontWeight.w400,
                                ),
                                Row(
                                  children: [
                                    Obx(() {
                                      // ตรวจสอบว่ามีการเลือกประเภทหรือยัง
                                      String displayValue = (benCrt
                                                  .selectedType.value.isEmpty ||
                                              benCrt.selectedType.value == null)
                                          ? '0' // ถ้ายังไม่ได้เลือกประเภท ให้แสดงค่า 0
                                          : benCrt.reciveBasic
                                              .value; // ถ้าเลือกแล้ว แสดงค่า reciveBasic

                                      return AppWidget.normalTextRX(
                                        context,
                                        displayValue
                                            .obs, // แสดงค่าที่ตรวจสอบแล้ว
                                        14.sp,
                                        const Color(0xFFA596FF),
                                        FontWeight.w400,
                                      );
                                    }),
                                    AppWidget.normalText(
                                      context,
                                      " " + "ui_Bath".tr,
                                      14.sp,
                                      Theme.of(context)
                                          .colorScheme
                                          .onSecondary
                                          .withOpacity(0.7),
                                      FontWeight.w400,
                                    ),
                                  ],
                                )
                              ],
                            ),
                            SizedBox(
                              height: 10.h,
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                AppWidget.normalText(
                                  context,
                                  'btn_transfer_account'.tr,
                                  14.sp,
                                  Theme.of(context)
                                      .colorScheme
                                      .onSecondary
                                      .withOpacity(0.7),
                                  FontWeight.w400,
                                ),
                                Row(
                                  children: [
                                    AppWidget.normalTextRX(
                                      context,
                                      benCrt.AccountNumberController.value !=
                                                  null &&
                                              bankAccount != ''
                                          ? bankAccount.toString().obs
                                          : '-'.obs,
                                      14.sp,
                                      Theme.of(context)
                                          .colorScheme
                                          .onSecondary
                                          .withOpacity(0.7),
                                      FontWeight.w400,
                                    ),
                                  ],
                                )
                              ],
                            ),
                            SizedBox(
                              height: 10.h,
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                AppWidget.normalText(
                                  context,
                                  'btn_recording_date'.tr,
                                  14.sp,
                                  Theme.of(context)
                                      .colorScheme
                                      .onSecondary
                                      .withOpacity(0.7),
                                  FontWeight.w400,
                                ),
                                AppWidget.normalText(
                                  context,
                                  date != null || date != '' && date != '0'
                                      ? date.toString()
                                      : '-'.obs,
                                  14.sp,
                                  Theme.of(context)
                                      .colorScheme
                                      .onSecondary
                                      .withOpacity(0.7),
                                  FontWeight.w400,
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  )
                : Container();
  }

  Widget iconUpload() {
    return Container(
      width: 103, // ความกว้าง
      height: 28, // ความสูง
      padding: const EdgeInsets.fromLTRB(
          10, 4, 4, 4), // Padding: top, right, bottom, left
      decoration: BoxDecoration(
        color: const Color(0xFFFFDD77), // สีพื้นหลัง (background)
        borderRadius: BorderRadius.all(Radius.circular(20)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            'Upload',
            style: TextStyle(
              fontSize: 14,
              overflow: TextOverflow.ellipsis,
              color: const Color(0xFFA596FF), // สีข้อความ
            ),
          ),
          Container(
            width: 20, // ความกว้าง 20px
            height: 20, // ความสูง 20px
            decoration: BoxDecoration(
              color: Color(0xFFFA4862), // ความโปร่งใส 0px
              borderRadius: BorderRadius.all(Radius.circular(20)),
            ),
            child: Center(
              child: SizedBox(
                  height: 12.h,
                  width: 12.w,
                  child: Image.asset('assets/close.png')),
            ), // เพิ่มช่องว่างภายใน (หากจำเป็น)
          )
        ],
      ),
    );
  }

//********************************************************สวัสดิการพื้นฐาน********************************************************
  Widget buildImage(context) {
    return Container(
      margin: EdgeInsets.only(top: 20.h, left: 20.w, right: 20.w),
      child: benCrt.selectedPatient.value == 'dp_in'.tr
          ? Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                GestureDetector(
                  onTap: () {
                    showModalBottomSheet(
                      context: context,
                      shape: RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.vertical(top: Radius.circular(20)),
                      ),
                      builder: (BuildContext context) {
                        return Container(
                          padding: EdgeInsets.all(16.0),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              showButtonSheetFunc('อัพโหลดรูปจากเครื่อง'.tr,
                                  'assets/image-add.png', () async {
                                await benCrt.getImageDirectory();
                              }),
                              SizedBox(height: 16.h),
                              showButtonSheetFunc(
                                  'ถ่ายรูป'.tr, 'assets/Camera_light.png',
                                  () async {
                                Get.toNamed('CameraScreen');
                              })
                            ],
                          ),
                        );
                      },
                    );
                  },
                  child: Container(
                    height: 50,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Container(
                      padding: EdgeInsets.only(
                          top: 5.h, bottom: 5.h, left: 20.w, right: 15.w),
                      height: 50.h,
                      width: 353.w,
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primary,
                        borderRadius: BorderRadius.circular(8.r),
                        border: Border.all(
                          color: Theme.of(context)
                              .colorScheme
                              .onSecondaryContainer
                              .withOpacity(1),
                          width: 1.w,
                        ),
                      ),
                      child: Obx(() {
                        return Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            benCrt.imgName.value == ""
                                ? AppWidget.normalText(
                                    context,
                                    'ui_imgUpload'.tr,
                                    14.sp,
                                    Theme.of(context).colorScheme.onSecondary,
                                    FontWeight.w400,
                                  )
                                : ContainImg(benCrt.imgName.value),
                            SizedBox(
                              height: 18.h,
                              width: 18.w,
                              child: Image.asset('assets/ADD/Image.png'),
                            ),
                          ],
                        );
                      }),
                    ),
                  ),
                ),
                // Additional fields or buttons go here
              ],
            )
          : benCrt.selectedPatient.value == 'dp_out'.tr
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        AppWidget.normalText(
                          context,
                          '* ยอดรวมทั้งหมดที่จ่ายจริง ',
                          12.sp,
                          Theme.of(context).colorScheme.secondaryContainer,
                          FontWeight.w400,
                        ),
                        AppWidget.normalText(
                          context,
                          'ตามใบเสร็จ',
                          12.sp,
                          Theme.of(context).colorScheme.secondaryFixedDim,
                          FontWeight.w400,
                        ),
                      ],
                    ),
                    SizedBox(height: 16),
                    GestureDetector(
                      onTap: () {
                        showModalBottomSheet(
                          context: context,
                          shape: RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.vertical(top: Radius.circular(20)),
                          ),
                          builder: (BuildContext context) {
                            return Container(
                              padding: EdgeInsets.all(16.0),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  showButtonSheetFunc('อัพโหลดรูปจากเครื่อง'.tr,
                                      'assets/image-add.png', () async {
                                    await benCrt.getImageDirectory();
                                  }),
                                  SizedBox(height: 16.h),
                                  showButtonSheetFunc(
                                      'ถ่ายรูป'.tr, 'assets/Camera_light.png',
                                      () async {
                                    Get.toNamed('CameraScreen');
                                  })
                                ],
                              ),
                            );
                          },
                        );
                      },
                      child: Container(
                        height: 50,
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Container(
                          padding: EdgeInsets.only(
                              top: 5.h, bottom: 5.h, left: 20.w, right: 15.w),
                          height: 50.h,
                          width: 353.w,
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.primary,
                            borderRadius: BorderRadius.circular(8.r),
                            border: Border.all(
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSecondaryContainer
                                  .withOpacity(1),
                              width: 1.w,
                            ),
                          ),
                          child: Obx(() {
                            return Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                benCrt.imgName.value == ""
                                    ? AppWidget.normalText(
                                        context,
                                        'ui_imgUpload'.tr,
                                        14.sp,
                                        Theme.of(context)
                                            .colorScheme
                                            .onSecondary,
                                        FontWeight.w400,
                                      )
                                    : ContainImg(benCrt.imgName.value),
                                SizedBox(
                                  height: 18.h,
                                  width: 18.w,
                                  child: Image.asset('assets/ADD/Image.png'),
                                ),
                              ],
                            );
                          }),
                        ),
                      ),
                    ),
                    // Additional fields or buttons go here
                  ],
                )
              : Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AppWidget.normalText(
                      context,
                      'แนบรูปภาพ การ์ดงานพิธีต่างๆ / ใบเกิด',
                      14.sp,
                      Theme.of(context).colorScheme.onSecondary,
                      FontWeight.w400,
                    ),
                    SizedBox(height: 8),
                    Row(
                      children: [
                        Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              AppWidget.normalText(
                                context,
                                '* กรณีถ้ายังไม่มีข้อมูลรูปภาพการ์ดงานพิธี',
                                12.sp,
                                Theme.of(context)
                                    .colorScheme
                                    .secondaryContainer,
                                FontWeight.w400,
                              ),
                              AppWidget.normalText(
                                context,
                                'ให้คลิกเลือก “ ไม่มี ” ที่ปุ่มด้านขวา',
                                12.sp,
                                Theme.of(context).colorScheme.secondaryFixedDim,
                                FontWeight.w400,
                              ),
                            ]),
                        SizedBox(width: 60.w),
                        Obx(() => GestureDetector(
                              onTap: () {
                                benCrt.isChecked.value =
                                    !benCrt.isChecked.value;
                              },
                              child: Container(
                                width: 20,
                                height: 20,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                      color: benCrt.isChecked.value
                                          ? Theme.of(context)
                                              .colorScheme
                                              .secondaryContainer
                                          : AppColors.Grayish),
                                  color: benCrt.isChecked.value
                                      ? Theme.of(context)
                                          .colorScheme
                                          .secondaryContainer
                                      : Theme.of(context).colorScheme.primary,
                                ),
                                child: benCrt.isChecked.value
                                    ? Icon(
                                        Icons.check,
                                        color: Theme.of(context)
                                            .colorScheme
                                            .primary,
                                        size: 16,
                                      )
                                    : null,
                              ),
                            )),
                        SizedBox(width: 8.w),
                        AppWidget.normalText(
                          context,
                          'ไม่มี'.tr,
                          14.sp,
                          Theme.of(context).colorScheme.onSecondary,
                          FontWeight.w400,
                        ),
                      ],
                    ),
                    SizedBox(height: 16),
                    GestureDetector(
                      onTap: () {
                        showModalBottomSheet(
                          context: context,
                          shape: RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.vertical(top: Radius.circular(20)),
                          ),
                          builder: (BuildContext context) {
                            return Container(
                              padding: EdgeInsets.all(16.0),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  showButtonSheetFunc('อัพโหลดรูปจากเครื่อง'.tr,
                                      'assets/image-add.png', () async {
                                    await benCrt.getImageDirectory();
                                  }),
                                  SizedBox(height: 16.h),
                                  showButtonSheetFunc(
                                      'ถ่ายรูป'.tr, 'assets/Camera_light.png',
                                      () async {
                                    Get.toNamed('CameraScreen');
                                  })
                                ],
                              ),
                            );
                          },
                        );
                      },
                      child: Container(
                        height: 50,
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Container(
                          padding: EdgeInsets.only(
                              top: 5.h, bottom: 5.h, left: 20.w, right: 15.w),
                          height: 50.h,
                          width: 353.w,
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.primary,
                            borderRadius: BorderRadius.circular(8.r),
                            border: Border.all(
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSecondaryContainer
                                  .withOpacity(1),
                              width: 1.w,
                            ),
                          ),
                          child: Obx(() {
                            return Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                benCrt.imgName.value == ""
                                    ? AppWidget.normalText(
                                        context,
                                        'ui_imgUpload'.tr,
                                        14.sp,
                                        Theme.of(context)
                                            .colorScheme
                                            .onSecondary,
                                        FontWeight.w400,
                                      )
                                    : ContainImg(benCrt.imgName.value),
                                SizedBox(
                                  height: 18.h,
                                  width: 18.w,
                                  child: Image.asset('assets/ADD/Image.png'),
                                ),
                              ],
                            );
                          }),
                        ),
                      ),
                    ),

                    SizedBox(height: 16),
                    // Additional fields or buttons go here
                  ],
                ),
    );
  }

  Widget ContainImg(String value) {
    return Container(
      width: 103.0.w, // ความกว้าง 103px
      height: 28.0.h, // ความสูง 28px
      padding: EdgeInsets.fromLTRB(10, 4, 4, 4), // padding: 4px 4px 4px 10px
      decoration: BoxDecoration(
        color: Color(0xFFFFDD77), // background color: #FFDD77
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.0), // border-radius: 20px 0px 0px 0px
          bottomLeft: Radius.circular(20.0),
          topRight: Radius.circular(20.0),
          bottomRight: Radius.circular(20.0),
        ),
      ),
      child: Center(
          child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          SizedBox(
            width: 63.w,
            height: 18.h,
            child: Text(
              value, // แสดงชื่อภาพที่เลือก
              style: TextStyle(color: Colors.black),
              overflow:
                  TextOverflow.ellipsis, // ถ้าข้อความยาวเกินจะแสดงเป็น ...
              maxLines: 1, // กำหนดให้แสดงผลเพียง 1 บรรทัด
            ),
          ),
          SizedBox(
              width: 20.w,
              height: 20.h,
              child: InkWell(
                  onTap: () {
                    setState(() {
                      benCrt.cleardata();
                    });
                  },
                  child: Image.asset('assets/ADD/X_button.png'))),
        ],
      )),
    );
  }

  Widget buildButtonIcon(context, text) {
    return InkWell(
      onTap: () {},
      borderRadius: BorderRadius.circular(20),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 10.w),
        height: 35.h,
        width: 90.w,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.r),
            border:
                Border.all(color: Theme.of(context).colorScheme.surfaceBright)),
        child: Text(
          text,
          style: TextStyle(
            color: Theme.of(context).colorScheme.surfaceTint,
            fontSize: 16,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget buildDatePicker(
    BuildContext context,
    RxString title,
    void Function(String selectedDate) updateSelectedDate,
    RxString selectedValue,
  ) {
    DateTime selectedDate = selectedValue.isNotEmpty
        ? DateFormat('dd/MM/yyyy').parse(selectedValue.value)
        : DateTime.now();

    return Container(
      margin: EdgeInsets.only(top: 10.h, left: 20.w, right: 20.w),
      height: 58.h,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary,
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: const [
          BoxShadow(
            color: Color(0x40000000),
            offset: Offset(0, 4),
            blurRadius: 4,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 10.0.w),
        child: Obx(() {
          return Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(
                width: 24.w,
                height: 24.h,
                child: Image.asset('assets/ADD/Calender.png'),
              ),
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  AppWidget.normalText(
                    context,
                    selectedValue.isNotEmpty
                        ? selectedValue.value
                        : '-/-/${DateTime.now().year}',
                    selectedValue.isNotEmpty ? 12.sp : 14.sp,
                    Theme.of(context).colorScheme.onSecondary,
                    FontWeight.w700,
                  ),
                ],
              ),
              InkWell(
                onTap: () async {
                  await showDialog(
                    context: context,
                    builder: (BuildContext context) {
                      return AlertDialog(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        content: Container(
                          height: 550.h,
                          width: Get.width,
                          child: buildCalendar(
                            context,
                            selectedDate.toString().obs,
                            (RxString selectedDate) {
                              // Handle the selected date here

                              // You may want to close the dialog or perform other actions.
                              updateSelectedDate(selectedDate.value);
                            },
                          ),
                        ),
                      );
                    },
                  );
                },
                child: SizedBox(
                  width: 24.w,
                  height: 24.h,
                  child: Image.asset('assets/ADD/Expand_down_light.png',
                      color: Theme.of(context).colorScheme.onSecondary),
                ),
              ),
            ],
          );
        }),
      ),
    );
  }

  Widget buildCalendar(
    BuildContext context,
    RxString Svalue,
    Function(RxString value) updateSelectedCEWType,
  ) {
    DateTime currentDate = DateTime.now();
    DateTime maxDate = currentDate.add(const Duration(days: 365));
    return SizedBox(
      width: Get.width,
      height: 500,
      child: Column(
        children: [
          DatePicker(
            ///สีเดือนปี
            leadingDateTextStyle: TextStyle(
              fontFamily: 'SukhumvitSet-Text',
              fontSize: 14.sp,
              color: Theme.of(context).colorScheme.secondaryContainer,
              fontWeight: FontWeight.w700,
            ),
            initialPickerType: PickerType.days,

            ///สีวัน
            daysNameTextStyle: TextStyle(
              fontFamily: 'SukhumvitSet-Text',
              fontSize: 12.sp,
              color: const Color(0xFFA596FF),
              fontWeight: FontWeight.w600,
            ),

            ///สีตัวเลข
            enabledCellTextStyle: TextStyle(
              fontFamily: 'SukhumvitSet-Text',
              fontSize: 14.sp,
              color: Theme.of(context).colorScheme.onSecondary,
              fontWeight: FontWeight.w600,
            ),

            ///สีตัวเลขตอนเลือก
            selectedCellTextStyle: TextStyle(
              fontFamily: 'SukhumvitSet-Text',
              fontSize: 14.sp,
              color: Theme.of(context).colorScheme.onSecondary,
              fontWeight: FontWeight.w600,
            ),

            ///สีเครื่องหมายเลื่อนเดือน
            slidersColor: Theme.of(context).colorScheme.onSecondary,

            ///สีวันที่เลือก
            selectedCellDecoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(6.0.r)),
              color: Theme.of(context)
                  .colorScheme
                  .secondaryContainer
                  .withOpacity(0.6),
              border: Border.all(
                color: Theme.of(context)
                    .colorScheme
                    .secondaryContainer
                    .withOpacity(1),
                width: 1.0.w,
              ),
            ),

            ///สีวันที่ปัจจุบันและวันที่เลือก
            currentDateTextStyle: TextStyle(
              fontFamily: 'SukhumvitSet-Text',
              fontSize: 14.sp,
              color: Theme.of(context).colorScheme.onSecondary,
              fontWeight: FontWeight.w600,
            ),
            currentDateDecoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(6.0.r)),
              color: Colors.transparent,
              border: Border.all(
                color: Colors.transparent,
                width: 1.0.w,
              ),
            ),

            ///สีวันที่ปัจจุบันและวันที่เลือก

            splashColor: Theme.of(context).colorScheme.onSecondary,
            initialDate: DateTime.now(),
            minDate: DateTime(2021, 1, 1),
            maxDate: maxDate,
            slidersSize: 14,
            onDateChanged: (value) {
              updateSelectedCEWType(DateFormat('dd/MM/yyyy').format(value).obs);
            },
          ),
          InkWell(
            onTap: () {
              updateSelectedCEWType(
                  DateTime.parse(Svalue.value).toString().obs);

              Get.back();
            },
            child: Container(
              height: 50.h,
              width: 317.w,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.all(Radius.circular(20)),
                color: Theme.of(context)
                    .colorScheme
                    .secondaryContainer
                    .withOpacity(0.6),
                border: Border.all(
                  width: 1.w,
                  color: Theme.of(context).colorScheme.secondaryContainer,
                ),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AppWidget.normalText(
                    context,
                    'ui_NitroSelect'.tr,
                    14.sp,
                    Theme.of(context).colorScheme.onSecondary,
                    FontWeight.w400,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildFormInputBank(context) {
    return Padding(
      padding: EdgeInsets.only(left: 20.w, right: 20.w, top: 10.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppWidget.normalText(
            context,
            'ui_bank'.tr,
            14.sp,
            Theme.of(context).colorScheme.onSecondary,
            FontWeight.w400,
          ),
          Container(
            height: mediaQuery(context, "h", 100),
            margin: EdgeInsets.only(top: 10.h, bottom: 10.h),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(mediaQuery(context, "h", 20)),
              color: Theme.of(context).colorScheme.primary,
              border: Border.all(
                color:
                    Theme.of(context).colorScheme.surfaceTint.withOpacity(0.2),
                width: 1.0.w,
              ),
            ),
            child: Container(
              alignment: Alignment.centerLeft,
              margin: EdgeInsets.only(left: mediaQuery(context, "w", 47)),
              child: TextField(
                cursorColor: Colors.black,
                onChanged: (value) {
                  // อัปเดตค่าใน controller พร้อมกับตำแหน่งของ cursor
                  final cursorPos = benCrt.AccountNumberController.selection;
                  benCrt.AccountNumberController.value =
                      benCrt.AccountNumberController.value.copyWith(
                    text: value,
                    selection: cursorPos, // รักษาตำแหน่ง cursor
                  );
                  benCrt.updateAccountNumber(
                      benCrt.AccountNumberController.value.text);
                },
                keyboardType: TextInputType.number,
                controller: benCrt.AccountNumberController,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(
                      RegExp(r'[0-9]')), // อนุญาตเฉพาะตัวเลข
                  LengthLimitingTextInputFormatter(
                      10), // จำกัดความยาวไม่เกิน 10 หลัก
                ],
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onSecondary,
                  fontSize: mediaQuery(context, "h", 28),
                  fontFamily: "SukhumvitSet-Text",
                ),
                decoration: InputDecoration(
                  counterText: '',
                  labelText: "tf_numbank".tr.toString(),
                  labelStyle: TextStyle(
                    color: Theme.of(context).colorScheme.onSecondary,
                    fontSize: mediaQuery(context, "h", 28),
                    fontFamily: "SukhumvitSet-Text",
                  ),
                  enabledBorder: const UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.transparent),
                  ),
                  focusedBorder: const UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.transparent),
                  ),
                ),
              ),
            ),
          ),
          AppWidget.normalText(
            context,
            'ui_remarkbank'.tr,
            12.sp,
            Theme.of(context).colorScheme.secondaryFixed,
            FontWeight.w400,
          ),
        ],
      ),
    );
  }

  void clearInputs() {
    benCrt.InputRocveDay.value = '0';
    benCrt.InputSum.value = '0';

    Withdrawal_sur_Amount.clear();
    WithdrawalAmount.clear();
  }

  Widget showButtonSheetFunc(
      String text, String imagePath, VoidCallback onPressed) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: 353.w, // Responsive width
        height: 52.h, // Responsive height
        decoration: BoxDecoration(
          color: const Color(0xFF4CCAB4), // Background color
          borderRadius: BorderRadius.all(Radius.circular(30.0)),
          border: const Border(
            top: BorderSide(
              color: Color(0xFF4CCAB4), // Border color
              width: 1, // Border width
            ),
            right: BorderSide.none, // No border on the right
            left: BorderSide.none, // No border on the left
            bottom: BorderSide.none, // No border on the bottom
          ),
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 10.w), // Responsive padding
          child: Center(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  height: 18.h,
                  width: 18.w,
                  child: Image.asset(imagePath),
                ),
                SizedBox(width: 4.w),
                Text(
                  text,
                  style: TextStyle(
                    color: Colors.black, // Text color
                    fontSize: 14.sp, // Responsive text size
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class DottedLinePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    var paint = Paint()
      ..color = AppColors.BluePurple.withOpacity(0.5) // สีของเส้นประ
      ..strokeWidth = 1 // ความหนาของเส้น
      ..style = PaintingStyle.stroke;

    // กำหนดความยาวของเส้นประและระยะห่าง
    double dashWidth = 2, dashSpace = 2;
    double startX = 0;

    // วาดเส้นประในแนวนอนตามความกว้างของขนาดที่กำหนด
    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX, 0),
        Offset(startX + dashWidth, 0),
        paint,
      );
      startX += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return false;
  }
}

class DottedLinePainterVertical extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppColors.BluePurple.withOpacity(0.3) // สีของเส้นปะ
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1; // ความหนาของเส้นปะ

    final dashHeight = 3; // ความสูงของแต่ละเส้นปะ
    final dashSpace = 0.5; // ระยะห่างระหว่างแต่ละเส้นปะ

    double startY = 0;

    while (startY < size.height) {
      canvas.drawLine(
        Offset(size.width / 2, startY), // เริ่มจากกึ่งกลางของความกว้าง
        Offset(size.width / 2, startY + dashHeight),
        paint,
      );
      startY += dashHeight + dashSpace; // เพิ่มระยะห่าง
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false; // ไม่ต้องการให้วาดใหม่ทุกครั้ง
  }
}

class ThousandsSeparatorInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    String newText = newValue.text.replaceAll(',', ''); // ลบคอมม่าเก่าออกก่อน
    String formattedText = _addCommas(newText);

    return TextEditingValue(
      text: formattedText,
      selection: TextSelection.collapsed(offset: formattedText.length),
    );
  }

  String _addCommas(String value) {
    if (value.isEmpty) return value;
    return value.replaceAllMapped(
      RegExp(r'(\d)(?=(\d{3})+$)'),
      (Match match) => '${match.group(1)},',
    );
  }
}
