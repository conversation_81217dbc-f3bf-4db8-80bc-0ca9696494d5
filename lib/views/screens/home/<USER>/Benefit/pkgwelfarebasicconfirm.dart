import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get_utils/src/extensions/internacionalization.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';


class pkgwelfarebasicconfirm extends StatefulWidget {
  @override
  _pkgwelfarebasicconfirmState createState() => _pkgwelfarebasicconfirmState();
}

class _pkgwelfarebasicconfirmState extends State<pkgwelfarebasicconfirm> {
  // static FirebaseAnalytics analytics = FirebaseAnalytics();
  // static FirebaseAnalyticsObserver observer =
  //     FirebaseAnalyticsObserver(analytics: analytics);

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    //
    // analytics.setCurrentScreen(screenName: "pkgwelfarebasicconfirm");
    // insertOpenMenu("pkgwelfarebasicconfirm");
    // checkinternet(context);
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () {
        Navigator.of(context).pop();
        Navigator.of(context).pop();
        Navigator.of(context).pop();
        Navigator.of(context).pop();
        return Future.value(true);
      },
      child: Scaffold(
        body: Stack(
          children: <Widget>[
            Center(
                child: Container(
              color: Color(0xff302C49),
            )),
            Container(
              width: mediaQuery(context, "w", 828),
              height: mediaQuery(context, "h", 290),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                  bottomRight: Radius.circular(mediaQuery(context, "h", 40)),
                  bottomLeft: Radius.circular(mediaQuery(context, "h", 40)),
                ),
                color: const Color(0xff1f1c2f),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0x4d000000),
                    offset: Offset(0, 2),
                    blurRadius: 20,
                  ),
                ],
              ),
            ),
            Container(
              margin: EdgeInsets.only(
                  top: mediaQuery(context, "h", 154),
                  left: mediaQuery(context, "w", 67)),
              child: GestureDetector(
                onTap: () {
                  Navigator.of(context).pop();
                  Navigator.of(context).pop();
                  Navigator.of(context).pop();
                  Navigator.of(context).pop();
                },
                child: Container(
                  color: Colors.transparent,
                  alignment: Alignment.center,
                  height: mediaQuery(context, "h", 70),
                  width: mediaQuery(context, "h", 70),
                  child: SvgPicture.string(
                    '<svg viewBox="67.0 154.0 21.7 38.0" ><path transform="translate(55.75, 147.81)" d="M 17.79955863952637 25.18868064880371 L 32.17768859863281 10.82186222076416 C 33.24105834960938 9.758491516113281 33.24105834960938 8.038998603820801 32.17768859863281 6.986940383911133 C 31.11431694030762 5.92357063293457 29.39482688903809 5.934882164001465 28.33145332336426 6.986940383911133 L 12.0415210723877 23.26556205749512 C 11.01208686828613 24.29499435424805 10.98946189880371 25.94661521911621 11.96233177185059 27.00998306274414 L 28.32014274597168 43.40172958374023 C 28.85183143615723 43.93341445922852 29.55320167541504 44.19359970092773 30.24326133728027 44.19359970092773 C 30.93332099914551 44.19359970092773 31.63469123840332 43.93341445922852 32.1663818359375 43.40172958374023 C 33.22975158691406 42.33835601806641 33.22975158691406 40.61886596679688 32.1663818359375 39.56680679321289 L 17.79955863952637 25.18868064880371 Z" fill="#fcf6e4" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                    allowDrawingOutsideViewBox: true,
                    height: mediaQuery(context, "h", 38),
                  ),
                ),
              ),
            ),
            Container(
              height: mediaQuery(context, "h", 290),
              margin: EdgeInsets.only(top: mediaQuery(context, "h", 50)),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  SvgPicture.string(
                    '<svg viewBox="392.0 391.0 45.0 36.0" ><path transform="translate(392.0, 391.0)" d="M 43.75547027587891 19.06171798706055 L 35.65547180175781 15.89765548706055 C 35.36719131469727 15.78515529632568 34.76953506469727 15.63749885559082 34.09453582763672 15.89765548706055 L 25.99453544616699 19.06171798706055 C 25.24219131469727 19.35703086853027 24.75000381469727 20.04609298706055 24.75000381469727 20.8125 C 24.75000381469727 28.65937423706055 29.58047294616699 34.08750152587891 34.09453582763672 35.85234451293945 C 34.76953506469727 36.11249923706055 35.36016082763672 35.96484375 35.65547180175781 35.85234451293945 C 39.26250076293945 34.44609451293945 45 29.56640625 45 20.8125 C 45 20.04609298706055 44.5078125 19.35703086853027 43.75547027587891 19.06171798706055 Z M 34.875 32.51250076293945 L 34.875 19.21640586853027 L 41.58984375 21.83906173706055 C 41.19609451293945 27.96327972412109 37.30781173706055 31.359375 34.875 32.51249694824219 Z M 15.75 18 C 20.72109413146973 18 24.75 13.97109413146973 24.75 9 C 24.75 4.028905868530273 20.72109413146973 0 15.75 0 C 10.77890586853027 0 6.75 4.028906345367432 6.75 9 C 6.75 13.97109413146973 10.77890586853027 18 15.75 18 Z M 22.5 20.8125 C 22.5 20.63671875 22.55624961853027 20.47500038146973 22.57734298706055 20.30624961853027 C 22.40156173706055 20.29921913146973 22.23281097412109 20.25 22.04999923706055 20.25 L 20.87578010559082 20.25 C 19.31484222412109 20.96718788146973 17.57812309265137 21.375 15.74999904632568 21.375 C 13.921875 21.375 12.19218635559082 20.96718788146973 10.62421798706055 20.25 L 9.44999885559082 20.25 C 4.232812404632568 20.25 0 24.48281288146973 0 29.70000076293945 L 0 32.625 C 0 34.48828125 1.51171875 36 3.375 36 L 28.125 36 C 28.60312461853027 36 29.06015586853027 35.89453125 29.47500038146973 35.71875 C 25.67812538146973 32.70234298706055 22.5 27.51328086853027 22.5 20.8125 Z" fill="#fcf6e4" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                    allowDrawingOutsideViewBox: true,
                    fit: BoxFit.fill,
                    height: mediaQuery(context, "h", 36),
                  ),
                  Container(
                    height: mediaQuery(context, "h", 28),
                  ),
                  Text(
                    "ui_headBasicform".tr.toString(),
                    style: TextStyle(
                      fontFamily: 'SukhumvitSet-SemiBold',
                      fontSize: mediaQuery(context, "h", 30),
                      color: const Color(0xfffcf6e4),
                      letterSpacing: mediaQuery(context, "h", 1.2),
                      fontWeight: FontWeight.w700,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            Container(
              alignment: Alignment.topCenter,
              margin: EdgeInsets.only(top: mediaQuery(context, "h", 357)),
              child: Text(
                'คุณได้ส่งเอกสาร เบิกสวัสดิการพื้นฐานแล้ว\nกรุณารอ 2 วันทำการ',
                style: TextStyle(
                  fontFamily: 'SukhumvitSet-SemiBold',
                  fontSize: mediaQuery(context, "h", 28),
                  color: const Color(0xfffcf6e4),
                  letterSpacing: mediaQuery(context, "h", 1.12),
                  fontWeight: FontWeight.w700,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
