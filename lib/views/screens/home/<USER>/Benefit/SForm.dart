import 'package:date_picker_plus/date_picker_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:intl/intl.dart';
import 'package:mapp_ms24/controllers/internal/HealthController/HealthController.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';

import '../../../../../controllers/internal/BenefitController /BenefitController.dart';
import '../../../../../controllers/utils/widget.dart';
import '../../../Widget/Widget.dart';

class StandartForm extends StatefulWidget {
  const StandartForm({super.key});

  @override
  State<StandartForm> createState() => _StandartFormState();
}

class _StandartFormState extends State<StandartForm> {
  BenefitController beneCtr = Get.find<BenefitController>();
  HealthController healthCtr = Get.find<HealthController>();

  @override
  void initState() {
    super.initState();

    beneCtr.ClearData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: Get.width,
        height: Get.height,
        decoration: BoxDecoration(
            gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
              const Color(0xFF1F1C2F).withOpacity(1),
              const Color(0xFF0D0C14).withOpacity(1),
            ])),
        child: Padding(
          padding: const EdgeInsets.only(top: 40.0),
          child: Stack(
            children: [
              buildIndex(),
              buildAppBarWithIcon(context, Icons.person, 'ui_headBasic'.tr),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildIndex() {
    return Obx(() {
      return Padding(
        padding: const EdgeInsets.only(top: 100.0),
        child: IndexedStack(
          index: beneCtr.Check.value, // Use the value of the RxInt as the index
          children: [
            buildBody1(),
            buildBody2(),
          ],
        ),
      );
    });
  }

  Widget buildBody2() {
    return SingleChildScrollView(
      child: Column(
        children: [
          buildFormCard(),
          buildFormInputBank(),
          buildFormEventDate(),
          buildChoose(),
        ],
      ),
    );
  }

  Widget buildBody1() {
    return SingleChildScrollView(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          buildFormStatus(),
          buildFormDate(),
          buildFormBenefit(),
          buildFormUpload(),
          buildChoose(),
        ],
      ),
    );
  }

  Widget buildFormCard() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 10.0.h),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AppWidget.normalText(context, 'ui_card'.tr, 14.sp,
                const Color(0xFFF6F6F6), FontWeight.w400),
            buildDropdown(context, 'dp_cardselect'.tr.obs, beneCtr.listcards,
                beneCtr.updateSelectedCards, beneCtr.SelectedCards)
          ],
        ),
      ),
    );
  }

  Widget buildFormInputBank() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 10.0.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(
                top: mediaQuery(context, "h", 30),
                left: mediaQuery(context, "w", 81)),
            child: Text(
              '${'ui_bank'.tr}\n${'ui_remarkbank'.tr}',
              style: TextStyle(
                fontFamily: 'SukhumvitSet-SemiBold',
                fontSize: mediaQuery(context, "h", 28),
                color: const Color(0xfffcf6e4),
                letterSpacing: mediaQuery(context, "h", 1.12),
                fontWeight: FontWeight.w700,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: mediaQuery(context, "h", 90)),
            child: Container(
              height: mediaQuery(context, "h", 140),
              width: mediaQuery(context, "w", 740),
              margin: EdgeInsets.only(
                  left: mediaQuery(context, "w", 44),
                  right: mediaQuery(context, "w", 44)),
              decoration: BoxDecoration(
                borderRadius:
                    BorderRadius.circular(mediaQuery(context, "h", 20)),
                color: const Color(0xff1f1c2f),
              ),
              child: Container(
                alignment: Alignment.centerLeft,
                margin: EdgeInsets.only(left: mediaQuery(context, "w", 47)),
                child: new TextField(
                  cursorColor: Colors.black,
                  onChanged: (value) {
                    setState(() {
                      beneCtr.AccountNumberController.text = value;
                    });
                  },
                  keyboardType: TextInputType.number,
                  controller: beneCtr.AccountNumberController,
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'[.0-9]'))
                  ],
//                controller: ctrlUsername,
                  style: new TextStyle(
                    color: Color(0xffFCF6E4),
                    fontSize: mediaQuery(context, "h", 28),
                    fontFamily: "SukhumvitSet-Text",
                  ),
                  decoration: new InputDecoration(
                    counterText: '',
                    labelText: "tf_numbank".tr.toString(),
                    labelStyle: TextStyle(
                      color: Color(0xffFCF6E4),
                      fontSize: mediaQuery(context, "h", 28),
                      fontFamily: "SukhumvitSet-Text",
                    ),
                    enabledBorder: const UnderlineInputBorder(
                      borderSide: const BorderSide(color: Colors.transparent),
                    ),
                    focusedBorder: const UnderlineInputBorder(
                      borderSide: const BorderSide(color: Colors.transparent),
                    ),
                  ),
                ),
              ),
            ),
          ),
          // buildFormInput(
          //     context,
          //     '${'ui_bank'.tr}\n${'ui_remarkbank'.tr}',
          //     'tf_numbank'.tr,
          //     InputType.number,
          //     beneCtr.UpdateAccountnumberInput,
          //    beneCtr.InputAccountNumber.toString()),
          // buildFormInput(context, 'ui_remarkbasic'.tr, 'tf_typingnote'.tr, InputType.text,
          //     beneCtr.updateselectStatus, beneCtr.InputNote.toString())
          Container(
            margin: EdgeInsets.only(
                top: mediaQuery(context, "h", 23),
                left: mediaQuery(context, "w", 67)),
            child: Text(
              "ui_remarkbasic".tr.toString(),
              style: TextStyle(
                fontFamily: 'SukhumvitSet-SemiBold',
                fontSize: mediaQuery(context, "h", 28),
                color: const Color(0xfffcf6e4),
                letterSpacing: mediaQuery(context, "h", 1.12),
                fontWeight: FontWeight.w700,
              ),
              textAlign: TextAlign.left,
            ),
          ),
          Container(
            margin: EdgeInsets.only(
                top: mediaQuery(context, "h", 16),
                left: mediaQuery(context, "w", 44),
                right: mediaQuery(context, "w", 44)),
            child: Container(
              width: mediaQuery(context, "w", 740),
              height: mediaQuery(context, "h", 140),
              decoration: BoxDecoration(
                borderRadius:
                    BorderRadius.circular(mediaQuery(context, "h", 20)),
                color: const Color(0xff1f1c2f),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0x4d000000),
                    offset: Offset(0, 2),
                    blurRadius: 20,
                  ),
                ],
              ),
              child: Container(
                alignment: Alignment.centerLeft,
                margin: EdgeInsets.only(left: mediaQuery(context, "w", 47)),
                child: new TextField(
                  cursorColor: Colors.black,

                  controller: beneCtr.NoteController,
//                controller: ctrlUsername,
                  style: new TextStyle(
                    color: Color(0xffFCF6E4),
                    fontSize: mediaQuery(context, "h", 28),
                    fontFamily: "SukhumvitSet-Text",
                  ),
                  decoration: new InputDecoration(
                    counterText: '',
//                errorText: _validate1 ? 'กรุณากรอกข้อมูล' : _checktext1 ? 'กรุณากรอกข้อมูลให้ครบ 4 หลัก' : null,
//                      fillColor: Colors.red,
//                hintText: "phone",
                    labelText: "tf_typingnote".tr.toString(),
                    labelStyle: TextStyle(
                      color: Color(0xffFCF6E4),
                      fontSize: mediaQuery(context, "h", 28),
                      fontFamily: "SukhumvitSet-Text",
                    ),
//                  icon: Image.asset(pictel),
                    enabledBorder: const UnderlineInputBorder(
                      borderSide: const BorderSide(color: Colors.transparent),
                    ),
                    focusedBorder: const UnderlineInputBorder(
                      borderSide: const BorderSide(color: Colors.transparent),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildFormEventDate() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 10.0.h),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AppWidget.normalText(context, 'ui_monk'.tr, 14.sp,
                const Color(0xFFF6F6F6), FontWeight.w400),
            AppWidget.normalText(context, 'ui_remarkmonk'.tr, 12.sp,
                const Color(0xfffee095), FontWeight.w400),
            SizedBox(height: 10.h),
            buildDatePicker(context, 'tf_datework'.tr.obs,
                beneCtr.Updatereturnworkdate, beneCtr.selectedDate)
          ],
        ),
      ),
    );
  }

  Widget buildFormStatus() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(left: 13.w),
            child: AppWidget.normalText(context, 'ui_status'.tr, 15.sp,
                const Color(0xFFF6F6F6), FontWeight.w500),
          ),
          buildDropdown(context, 'dp_select'.tr.obs, beneCtr.testStatusMember,
              beneCtr.updateselectStatus, beneCtr.selectStatus)
        ],
      ),
    );
  }

  Widget buildFormBenefit() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 10.0.h),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AppWidget.normalText(context, 'ui_selectwelfare'.tr, 14.sp,
                const Color(0xFFF6F6F6), FontWeight.w400),
            AppWidget.normalText(context, 'ui_remarkwelfare'.tr, 12.sp,
                const Color(0xFFFEE095), FontWeight.w400),
            buildDropdown(context, 'dp_select'.tr.obs, beneCtr.testStatus,
                beneCtr.updateselectedType, beneCtr.selectedType)
          ],
        ),
      ),
    );
  }

  Widget buildFormUpload() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 10.0.h),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AppWidget.normalText(context, 'ui_link'.tr, 14.sp,
                const Color(0xFFF6F6F6), FontWeight.w400),
            Container(
              height: 60.h,
              width: Get.width,
              decoration: BoxDecoration(
                color: const Color(0xFF302C49),
                borderRadius: BorderRadius.circular(8.r),
                boxShadow: const [
                  BoxShadow(
                    color: Color(0x40000000),
                    offset: Offset(0, 4),
                    blurRadius: 4,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.all(10.0),
                child: InkWell(
                  onTap: () {
                    healthCtr.selectimage(context);
                    setState(() {});
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      healthCtr.dataLinkImage.length > 0
                          ? AppWidget.normalText(
                              context,
                              healthCtr.dataLinkImage,
                              14.sp,
                              const Color(0xFFF6F6F6).withOpacity(0.6),
                              FontWeight.w400,
                            )
                          : AppWidget.normalText(
                              context,
                              'btn_uploadimmage'.tr,
                              14.sp,
                              const Color(0xFFF6F6F6).withOpacity(0.6),
                              FontWeight.w400,
                            ),
                      SizedBox(
                        height: 20.h,
                        width: 20.w,
                        child: Image.asset('assets/ADD/Image.png'),
                      )
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildFormDate() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 10.0.h),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AppWidget.normalText(context, 'ui_datework'.tr, 14.sp,
                const Color(0xFFF6F6F6), FontWeight.w400),
            AppWidget.normalText(context, 'ui_remarkdatework'.tr, 12.sp,
                const Color(0xFFFEE095), FontWeight.w400),
            buildDatePicker(context, 'tf_datework'.tr.obs,
                beneCtr.updateselectedTime, beneCtr.selectedTime)
          ],
        ),
      ),
    );
  }

  Widget buildDropdown(
    BuildContext context,
    RxString title,
    List<RxString> items,
    void Function(String value) updateSelectedCEWType,
    RxString selectedValue, // New parameter for storing the selected value
  ) {
    return Container(
      height: 60.h,
      width: Get.width,
      decoration: BoxDecoration(
        color: const Color(0xFF302C49),
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: const [
          BoxShadow(
            color: Color(0x40000000),
            offset: Offset(0, 4),
            blurRadius: 4,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
        child: DropdownButton<String>(
          borderRadius: BorderRadius.circular(8.r),
          dropdownColor: const Color(0xFF302C49),
          hint: Row(
            children: [
              SizedBox(
                width: 10.w,
              ),
              AppWidget.normalTextRX(
                context,
                selectedValue.isNotEmpty ? selectedValue : title,
                14.sp,
                const Color(0xFFF6F6F6).withOpacity(0.6),
                FontWeight.w400,
              ),
            ],
          ),
          isExpanded: true,
          underline: const SizedBox(),
          icon: SizedBox(
            width: 24.w,
            height: 24.h,
            child: Image.asset('assets/ADD/Expand_down_light.png'),
          ),
          style: TextStyle(
            fontFamily: 'SukhumvitSet-Text',
            fontSize: 14.sp,
            color: const Color(0xFFF6F6F6),
            fontWeight: FontWeight.w400,
          ),
          onChanged: (String? value) {
            // Store selected value
            setState(() {
              updateSelectedCEWType(value!);
            });
            // Handle dropdown value change
          },
          items: items.map((RxString rxString) {
            String stringValue =
                rxString.value; // Extract the string value from RxString
            return DropdownMenuItem<String>(
              value: stringValue,
              child: AppWidget.normalTextRX(
                context,
                stringValue.obs,
                14.sp,
                const Color(0xFFF6F6F6),
                FontWeight.w400,
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget buildChoose() {
    return Padding(
      padding: EdgeInsets.only(top: 30.h),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        child: Stack(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                InkWell(
                  onTap: () {
                    if (beneCtr.Check == 0) {
                      setState(() {
                        Get.back();
                      });
                    } else if (beneCtr.Check == 1) {
                      setState(() {
                        beneCtr.Check = 0.obs;
                      });
                    }
                  },
                  child: AppWidget.normalTextRX(
                    context,
                    'btn_before'.tr.obs,
                    14.sp,
                    const Color(0xFFF6F6F6),
                    FontWeight.w400,
                  ),
                ),
                beneCtr.Check == 0.obs
                    ? InkWell(
                        onTap: () {
                          if (beneCtr.selectStatus == '' ||
                              beneCtr.selectStatus == 'เลือก' ||
                              beneCtr.selectedTime == '' ||
                              beneCtr.selectedType == '' ||
                              beneCtr.selectedType == 'ui_type'.tr.obs.value ||
                              beneCtr.selectedTime ==
                                  'ui_datework'.tr.obs.value ||
                              beneCtr.selectStatus ==
                                  'ui_status'.tr.obs.value) {
                            Fluttertoast.showToast(
                                msg: "กรุณาใส่ข้อมูลให้ครบถ้วน",
                                toastLength: Toast.LENGTH_SHORT,
                                gravity: ToastGravity.TOP,
                                backgroundColor: Colors.red,
                                textColor: Colors.black,
                                fontSize: 16.0);
                          } else {
                            setState(() {
                              beneCtr.Check = 1.obs;
                            });
                          }
                        },
                        child: AppWidget.normalTextRX(
                          context,
                          'btn_next2'.tr.obs,
                          14.sp,
                          const Color(0xFFF6F6F6),
                          FontWeight.w400,
                        ),
                      )
                    : Container()
              ],
            ),
            beneCtr.Check == 1.obs
                ? Center(
                    child: buttonSAVE(context, 0.h, 150.h, 'btn_savefrom'.tr))
                : Container()
          ],
        ),
      ),
    );
  }

  Widget buildDatePicker(
    BuildContext context,
    RxString title,
    void Function(String selectedDate) updateSelectedDate,
    RxString selectedValue,
  ) {
    DateTime selectedDate = selectedValue.isNotEmpty
        ? DateFormat('dd/MM/yy').parse(selectedValue.value)
        : DateTime.now();

    return Container(
      height: 58.h,
      decoration: BoxDecoration(
        color: const Color(0xFF302C49),
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: const [
          BoxShadow(
            color: Color(0x40000000),
            offset: Offset(0, 4),
            blurRadius: 4,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 10.0.w),
        child: Obx(() {
          return Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(
                width: 24.w,
                height: 24.h,
                child: Image.asset('assets/ADD/Calender.png'),
              ),
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  AppWidget.normalText(
                    context,
                    selectedValue.isNotEmpty
                        ? selectedValue.value
                        : 'tf_datework'.tr,
                    selectedValue.isNotEmpty ? 12.sp : 14.sp,
                    const Color(0xFFF6F6F6),
                    FontWeight.w700,
                  ),
                ],
              ),
              InkWell(
                onTap: () async {
                  await showDialog(
                    context: context,
                    builder: (BuildContext context) {
                      return AlertDialog(
                        backgroundColor: const Color(0xFF302C49),
                        content: Container(
                          height: 550.h,
                          width: Get.width,
                          child: buildCalendar(
                            context,
                            selectedDate.toString().obs,
                            (RxString selectedDate) {
                              // Handle the selected date here

                              // You may want to close the dialog or perform other actions.
                              setState(() {
                                updateSelectedDate(selectedDate.value);
                              });
                            },
                          ),
                        ),
                      );
                    },
                  );
                },
                child: SizedBox(
                  width: 24.w,
                  height: 24.h,
                  child: Image.asset('assets/ADD/Expand_down_light.png'),
                ),
              ),
            ],
          );
        }),
      ),
    );
  }

  Widget buildCalendar(
    BuildContext context,
    RxString Svalue,
    Function(RxString value) updateSelectedCEWType,
  ) {
    return SizedBox(
      width: Get.width,
      height: Get.height,
      child: Column(
        children: [
          DatePicker(
            leadingDateTextStyle: TextStyle(
              fontFamily: 'SukhumvitSet-Text',
              fontSize: 14.sp,
              color: const Color(0xFFF6F6F6),
              fontWeight: FontWeight.w700,
            ),
            initialPickerType: PickerType.days,
            daysNameTextStyle: TextStyle(
              fontFamily: 'SukhumvitSet-Text',
              fontSize: 12.sp,
              color: const Color(0xFFA596FF),
              fontWeight: FontWeight.w600,
            ),
            enabledCellTextStyle: TextStyle(
              fontFamily: 'SukhumvitSet-Text',
              fontSize: 14.sp,
              color: const Color(0xFFF6F6F6),
              fontWeight: FontWeight.w600,
            ),
            selectedCellTextStyle: TextStyle(
              fontFamily: 'SukhumvitSet-Text',
              fontSize: 14.sp,
              color: const Color(0xFFF6F6F6),
              fontWeight: FontWeight.w600,
            ),
            slidersColor: const Color(0xFFF6F6F6),
            selectedCellDecoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(6.0.r)),
              color: const Color(0xFF5F569B),
              border: Border.all(
                color: const Color(0xFFA596FF),
                width: 1.0.w,
              ),
            ),
            currentDateTextStyle: TextStyle(
              fontFamily: 'SukhumvitSet-Text',
              fontSize: 14.sp,
              color: const Color(0xFFF6F6F6),
              fontWeight: FontWeight.w600,
            ),
            currentDateDecoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(6.0.r)),
              color: const Color(0xFF302C49),
              border: Border.all(
                color: const Color(0xFF5F569B),
                width: 1.0.w,
              ),
            ),
            splashColor: const Color(0xFFF6F6F6),
            initialDate: DateTime.now(),
            minDate: DateTime(2021, 1, 1),
            maxDate: DateTime.now().add(Duration(days: 365)),
            slidersSize: 14,
            onDateChanged: (value) {
              updateSelectedCEWType(DateFormat('dd/MM/yy').format(value).obs);
            },
          ),
          Padding(
            padding: const EdgeInsets.all(10),
            child: InkWell(
              onTap: () {
                setState(() {
                  updateSelectedCEWType(
                      DateTime.parse(Svalue.value).toString().obs);
                });
                Get.back();
              },
              child: Container(
                height: 50.h,
                width: 317.w,
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.all(Radius.circular(10)),
                  // color: Color(0xFF5F569B),

                  border: Border.all(
                    width: 1.w,
                    color: const Color(0xFFA596FF),
                  ),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    AppWidget.normalText(
                      context,
                      'dp_select'.tr.toString(),
                      14.sp,
                      const Color(0xFFF6F6F6),
                      FontWeight.w400,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buttonSAVE(context, height, bottom, title) {
    return Container(
      margin: EdgeInsets.only(top: height, bottom: bottom),
      child: Stack(
        children: <Widget>[
          Container(
            alignment: Alignment.topCenter,
            child: GestureDetector(
              onTap: () {
                if (beneCtr.selectedType == '' ||
                    beneCtr.NoteController.text == '' ||
                    beneCtr.AccountNumberController.text == '') {
                  Fluttertoast.showToast(
                      msg: "กรุณาใส่ข้อมูลให้ครบถ้วน",
                      toastLength: Toast.LENGTH_SHORT,
                      gravity: ToastGravity.TOP,
                      backgroundColor: Colors.red,
                      textColor: Colors.black,
                      fontSize: 16.0);
                } else {
                  setState(() {
                    // beneCtr.test(context,beneCtr.selectedDate,beneCtr.selectedType,beneCtr.NoteController.text,beneCtr.AccountNumberController.text,beneCtr.selectedDate,beneCtr.SelectedCards);

                    beneCtr.updateToBCTbasic(
                        context,
                        beneCtr.selectedDate,
                        beneCtr.selectedType,
                        beneCtr.NoteController.text,
                        beneCtr.AccountNumberController.text,
                        beneCtr.selectedDate,
                        beneCtr.SelectedCards);
                    beneCtr.Check = 1.obs;
                  });
                }
              },
              child: Container(
                alignment: Alignment.center,
                height: mediaQuery(context, "h", 110),
                width: mediaQuery(context, "w", 250),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(51.0),
                  color: const Color(0xff7f420c),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0x4d000000),
                      offset: Offset(0, 2),
                      blurRadius: 20,
                    ),
                  ],
                ),
                child: AppWidget.boldText(context, title, 14.sp,
                    const Color(0xFFF6F6F6), FontWeight.w400),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
