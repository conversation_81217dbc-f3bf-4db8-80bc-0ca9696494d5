import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:mapp_ms24/controllers/internal/BenefitController%20/BenefitController.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/views/screens/Widget/Widget.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/Benefit/BasicWelfare/BasicWelfareFrom.dart';

import '../../../../../controllers/utils/widget.dart';
import '../../../Widget/library.dart';

class BenefitScreen extends StatefulWidget {
  const BenefitScreen({super.key});

  @override
  State<BenefitScreen> createState() => _BenefitScreenState();
}

class _BenefitScreenState extends State<BenefitScreen> {
  BenefitController benCrt = Get.find<BenefitController>();
  ProfileController profile = Get.find<ProfileController>();
  bool checking = true;
  @override
  void initState() {
    super.initState();
    initializePage();
  }

  void initializePage() async {
    await profile.configApp();
    await benCrt.welfare(profile.responseMember!.id);
    await benCrt.doreloadbenefits(context);
    checking = false;
    setState(() {});
  }

  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            width: Get.width,
            height: Get.height,
            decoration: BoxDecoration(
                gradient: LinearGradient(
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                    colors: [
                  Theme.of(context).colorScheme.primary,
                  Theme.of(context).colorScheme.onPrimary,
                ])),
            child: Padding(
              padding: const EdgeInsets.only(top: 20.0),
              child: Column(
                children: [
                  buildAppBar(context, 'ui_headFlexibleWithdraw'.tr),
                  buildbody(),
                  SizedBox(
                    height: 20.h,
                  ),
                  Cashwithdrawal_list(),
                  // buildList(),
                ],
              ),
            ),
          ),
          // checking
          //     ? Container(
          //     width: MediaQuery.of(context).size.width,
          //     height: MediaQuery.of(context).size.height,
          //     color: Colors.black12,
          //     child: Center(
          //       child: SizedBox(
          //         width: 50.w,
          //         child: const LoadingIndicator(
          //           indicatorType: Indicator.lineSpinFadeLoader,
          //           colors: [Colors.white],
          //         ),
          //       ),
          //     ))
          //     : Container(),
        ],
      ),
    );
  }

  Widget buildbody() {
    return Stack(
      children: [
        Row(
          children: [
            Column(
              children: [
                Container(
                  margin: EdgeInsets.only(top: 20.h, left: 25.h),
                  height: 360.h,
                  width: 168.w,
                  decoration: BoxDecoration(
                      gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Theme.of(context)
                                .colorScheme
                                .inversePrimary
                                .withOpacity(0.1),
                            Theme.of(context)
                                .colorScheme
                                .inversePrimary
                                .withOpacity(0.3),
                          ]),
                      borderRadius: BorderRadius.circular(20)),
                  child: Column(
                    children: [
                      Column(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          SizedBox(
                            height: 5.h,
                          ),
                          AppWidget.normalText(
                              context,
                              'btn_health'.tr.toString(),
                              16.sp,
                              Theme.of(context).colorScheme.onSecondary,
                              FontWeight.w400),
                          SizedBox(
                            height: 15.h,
                          ),
                          AppWidget.normalText(
                              context,
                              'OPD' + ' ' + 'ui_Balance'.tr.toString(),
                              12.sp,
                              Theme.of(context).colorScheme.onSecondary,
                              FontWeight.w400),
                          SizedBox(
                            height: 5.h,
                          ),
                          AppWidget.normalText(
                              context,
                              profile.OPD_Total.toString().replaceAllMapped(
                                  new RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                                  (Match m) => '${m[1]},'),
                              18.sp,
                              Theme.of(context).colorScheme.secondaryContainer,
                              FontWeight.w700),
                          AppWidget.normalText(
                              context,
                              '/' +
                                  profile.OPD_Total.toString().replaceAllMapped(
                                      new RegExp(
                                          r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                                      (Match m) => '${m[1]},'),
                              12.sp,
                              Theme.of(context).colorScheme.secondaryContainer,
                              FontWeight.w400),
                          SizedBox(
                            height: 5.h,
                          ),
                          AppWidget.normalText(
                              context,
                              profile.OPD_Total.toString().replaceAllMapped(
                                  new RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                                  (Match m) => '${m[1]},'),
                              18.sp,
                              Theme.of(context).colorScheme.secondaryFixed,
                              FontWeight.w700),
                          SizedBox(height: 5.h),
                          Image.asset(
                            'assets/Line523.png',
                            width: 150.w,
                            color: Theme.of(context)
                                .colorScheme
                                .onSecondaryContainer,
                          ),
                          SizedBox(
                            height: 15.h,
                          ),
                          AppWidget.normalText(
                              context,
                              'IPD' + ' ' + 'ui_Balance'.tr.toString(),
                              12.sp,
                              Theme.of(context).colorScheme.onSecondary,
                              FontWeight.w400),
                          SizedBox(
                            height: 5.h,
                          ),
                          AppWidget.normalText(
                              context,
                              profile.IPD_Total.toString().replaceAllMapped(
                                  new RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                                  (Match m) => '${m[1]},'),
                              18.sp,
                              Theme.of(context).colorScheme.secondaryContainer,
                              FontWeight.w700),
                          AppWidget.normalText(
                              context,
                              '/' +
                                  profile.IPD_Total.toString().replaceAllMapped(
                                      new RegExp(
                                          r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                                      (Match m) => '${m[1]},'),
                              12.sp,
                              Theme.of(context).colorScheme.secondaryContainer,
                              FontWeight.w400),
                          SizedBox(
                            height: 5.h,
                          ),
                          AppWidget.normalText(
                              context,
                              profile.OPD_Total.toString().replaceAllMapped(
                                  new RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                                  (Match m) => '${m[1]},'),
                              18.sp,
                              Theme.of(context).colorScheme.secondaryFixed,
                              FontWeight.w700),
                          SizedBox(
                            height: 30.h,
                          ),
                        ],
                      ),
                      GestureDetector(
                        onTap: () {
                          Get.to(Welfarefrom());
                        },
                        child: Container(
                          alignment: Alignment.center,
                          width: 140.w,
                          height: 30.h,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(
                                mediaQuery(context, "h", 30)),
                            color: Theme.of(context)
                                .colorScheme
                                .secondaryContainer
                                .withOpacity(0.7),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Image.asset('assets/add-square.png', width: 18.w),
                              SizedBox(
                                width: 5.w,
                              ),
                              AppWidget.normalText(
                                  context,
                                  'ui_withdraw_money'.tr.toString(),
                                  14.sp,
                                  Theme.of(context)
                                      .colorScheme
                                      .tertiaryFixedDim,
                                  FontWeight.w700),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            Column(
              children: [
                Container(
                  margin: EdgeInsets.only(top: 20.h, left: 20.h),
                  height: 148.h,
                  width: 168.w,
                  decoration: BoxDecoration(
                      gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Theme.of(context)
                                .colorScheme
                                .inversePrimary
                                .withOpacity(0.1),
                            Theme.of(context)
                                .colorScheme
                                .inversePrimary
                                .withOpacity(0.3),
                          ]),
                      borderRadius: BorderRadius.circular(20)),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      AppWidget.normalText(
                          context,
                          'btn_basic'.tr.toString(),
                          16.sp,
                          Theme.of(context).colorScheme.onSecondary,
                          FontWeight.w400),
                      SizedBox(
                        height: 10.h,
                      ),
                      AppWidget.normalText(
                          context,
                          "ui_recive".tr.toString(),
                          12.sp,
                          Theme.of(context).colorScheme.onSecondary,
                          FontWeight.w700),
                      SizedBox(
                        height: 5.h,
                      ),
                      AppWidget.normalText(
                          context,
                          "0",
                          18.sp,
                          Theme.of(context).colorScheme.secondaryContainer,
                          FontWeight.w700),
                      SizedBox(
                        height: 5.h,
                      ),
                      GestureDetector(
                        onTap: () {
                          Get.to(Welfarefrom());
                        },
                        child: Container(
                          alignment: Alignment.center,
                          width: 140.w,
                          height: 30.h,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(
                                mediaQuery(context, "h", 30)),
                            color: Theme.of(context)
                                .colorScheme
                                .secondaryContainer
                                .withOpacity(0.7),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Image.asset('assets/add-square.png', width: 18.w),
                              SizedBox(
                                width: 5.w,
                              ),
                              AppWidget.normalText(
                                  context,
                                  'ui_withdraw_money'.tr.toString(),
                                  14.sp,
                                  Theme.of(context)
                                      .colorScheme
                                      .tertiaryFixedDim,
                                  FontWeight.w700),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(top: 20.h, left: 20.h),
                  height: 196.h,
                  width: 168.w,
                  decoration: BoxDecoration(
                      gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Theme.of(context)
                                .colorScheme
                                .inversePrimary
                                .withOpacity(0.1),
                            Theme.of(context)
                                .colorScheme
                                .inversePrimary
                                .withOpacity(0.3),
                          ]),
                      borderRadius: BorderRadius.circular(20)),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      AppWidget.normalText(
                          context,
                          'btn_flexible'.tr.toString(),
                          16.sp,
                          Theme.of(context).colorScheme.onSecondary,
                          FontWeight.w400),
                      SizedBox(
                        height: 10.h,
                      ),
                      AppWidget.normalText(
                          context,
                          "ui_Balance".tr.toString(),
                          12.sp,
                          Theme.of(context).colorScheme.onSecondary,
                          FontWeight.w700),
                      SizedBox(
                        height: 5.h,
                      ),
                      AppWidget.normalText(
                          context,
                          double.parse(benCrt.benefitsbalance!)
                              .toStringAsFixed(2)
                              .replaceAllMapped(
                                  new RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                                  (Match m) => '${m[1]},'),
                          18.sp,
                          Theme.of(context).colorScheme.secondaryContainer,
                          FontWeight.w700),
                      AppWidget.normalText(
                          context,
                          "/" +
                              double.parse(benCrt.start_benefitsresilient!)
                                  .toStringAsFixed(2)
                                  .replaceAllMapped(
                                      new RegExp(
                                          r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                                      (Match m) => '${m[1]},'),
                          12.sp,
                          Theme.of(context).colorScheme.secondaryContainer,
                          FontWeight.w500),
                      SizedBox(
                        height: 5.h,
                      ),
                      AppWidget.normalText(
                          context,
                          "-" +
                              double.parse(
                                      benCrt.totalAlreadyWithdrawn!.toString())
                                  .toStringAsFixed(2)
                                  .replaceAllMapped(
                                      new RegExp(
                                          r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                                      (Match m) => '${m[1]},'),
                          18.sp,
                          Theme.of(context).colorScheme.secondaryFixed,
                          FontWeight.w700),
                      SizedBox(
                        height: 5.h,
                      ),
                      GestureDetector(
                        onTap: () {
                          Get.to(Welfarefrom());
                        },
                        child: Container(
                          alignment: Alignment.center,
                          width: 140.w,
                          height: 30.h,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(
                                mediaQuery(context, "h", 30)),
                            color: Theme.of(context)
                                .colorScheme
                                .secondaryContainer
                                .withOpacity(0.7),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Image.asset('assets/add-square.png', width: 18.w),
                              SizedBox(
                                width: 5.w,
                              ),
                              AppWidget.normalText(
                                  context,
                                  'ui_withdraw_money'.tr.toString(),
                                  14.sp,
                                  Theme.of(context)
                                      .colorScheme
                                      .tertiaryFixedDim,
                                  FontWeight.w700),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Widget Cashwithdrawal_list() {
    return Stack(
      children: [
        Column(
          children: [
            Container(
              alignment: Alignment.topLeft,
              margin: EdgeInsets.only(
                  top: mediaQuery(context, "h", 20),
                  left: mediaQuery(context, "w", 71),
                  bottom: mediaQuery(context, "h", 25)),
              child: AppWidget.normalText(
                  context,
                  "ui_history_all".tr.toString(),
                  12.sp,
                  Theme.of(context).colorScheme.tertiary,
                  FontWeight.w400),
            ),
            benCrt.databenefits.isEmpty
                ? Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                          height: 20.h,
                          width: 20.w,
                          child: Image.asset('assets/Exclamation-Circle.png')),
                      Container(
                        child: AppWidget.normalText(
                            context,
                            "ui_history_emtpy".tr.toString(),
                            12.sp,
                            Theme.of(context)
                                .colorScheme
                                .onSecondary
                                .withOpacity(0.9),
                            FontWeight.w400),
                      ),
                    ],
                  )
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      AppWidget.normalText(
                          context,
                          'ui_listWithdraw'.tr.toString(),
                          14.sp,
                          Theme.of(context).colorScheme.onSecondary,
                          FontWeight.w400),
                    ],
                  )
          ],
        )
      ],
    );
  }
}
