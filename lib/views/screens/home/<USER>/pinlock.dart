import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:mapp_ms24/models/LocalStorageModel.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/unlockTwoFA.dart';
import 'package:mapp_ms24/configs/webauthn_service.dart';

import '../../../../controllers/login_controller/login.dart';
import '../../../../controllers/utils/widget.dart';

class PinlockScreen extends StatefulWidget {
  const PinlockScreen({Key? key}) : super(key: key);

  @override
  State<PinlockScreen> createState() => _PinlockScreenState();
}

class _PinlockScreenState extends State<PinlockScreen> {
  String enteredPin = "";
  final box = GetStorage();
  var stepPin = '';
  CenterController centerCon = Get.find<CenterController>();
  bool _isWebAuthnSupported = false;
  bool _isWebAuthnRegistered = false;
  bool _isAuthenticating = false;
  String? _webAuthnCredentialId;

  @override
  void initState() {
    super.initState();
    stepPin = box.read('stepPin') ?? '';
    _initializeWebAuthn();
  }

  Future<void> _initializeWebAuthn() async {
    await _checkWebAuthnSupport();
    await _checkWebAuthnRegistration();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (mounted) {
        await _autoAuthenticate();
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (mounted && stepPin == 'done') {
        await _autoAuthenticate();
      }
    });
  }

  bool _showError = false;
  bool _isAutoAuthInProgress = false;

  Future<void> _autoAuthenticate() async {
    if (_isAutoAuthInProgress) return;
    if (!mounted ||
        !_isWebAuthnSupported ||
        !_isWebAuthnRegistered ||
        _webAuthnCredentialId == null ||
        _webAuthnCredentialId!.isEmpty) {
      return;
    }

    _isAutoAuthInProgress = true;

    try {
      setState(() {
        _isAuthenticating = true;
      });
      await _authenticateWebAuthn(isAutoAuth: true);
    } catch (e) {
      return;
    } finally {
      _isAutoAuthInProgress = false;
      if (mounted) {
        setState(() {
          _isAuthenticating = false;
        });
      }
    }
  }

  Future<void> _checkWebAuthnSupport() async {
    final isSupported = WebAuthnService.isWebAuthnSupported();
    setState(() {
      _isWebAuthnSupported = isSupported;
    });
  }

  Future<void> _checkWebAuthnRegistration() async {
    final credentialId = box.read(LocalStorage.webAuthnCredentialId);
    final isRegistered = credentialId != null && credentialId.isNotEmpty;
    setState(() {
      _isWebAuthnRegistered = isRegistered;
      _webAuthnCredentialId = credentialId;
    });
  }

  Future<void> _authenticate() async {
    if (!_isWebAuthnSupported) {
      Get.snackbar(
          'Error', 'Biometric authentication is not supported on this device');
      return;
    }

    setState(() {
      _isAuthenticating = true;
    });

    try {
      if (!_isWebAuthnRegistered) {
        // First time - register the credential
        Get.snackbar('Info', 'Setting up biometric authentication...');
        await _registerWebAuthn();
      } else {
        // Authenticate with existing credential
        Get.snackbar('Info', 'Authenticating with biometrics...');
        await _authenticateWebAuthn();
      }
    } catch (e) {
      // Show more user-friendly error messages
      String errorMessage = 'Authentication failed';
      if (e.toString().contains('NotAllowedError')) {
        errorMessage = 'Authentication was cancelled or timed out';
      } else if (e.toString().contains('NotSupportedError')) {
        errorMessage = 'Biometric authentication is not available';
      } else if (e.toString().contains('SecurityError')) {
        errorMessage = 'Security error - please try again';
      }
      Get.snackbar('Error', errorMessage);
    } finally {
      if (mounted) {
        setState(() {
          _isAuthenticating = false;
        });
      }
    }
  }

  Future<void> _registerWebAuthn() async {
    try {
      final result = await WebAuthnService.registerCredential();
      if (result['success'] == true) {
        final credentialId = result['credentialId'];
        box.write(LocalStorage.webAuthnCredentialId, credentialId);

        setState(() {
          _isWebAuthnRegistered = true;
          _webAuthnCredentialId = credentialId;
        });

        Get.snackbar(
            'Success', 'Biometric authentication registered successfully');

        _proceedToHome();
      } else {
        throw Exception(result['error'] ?? 'Registration failed');
      }
    } catch (e) {
      throw Exception('Failed to register biometric authentication: $e');
    }
  }

  Future<void> _authenticateWebAuthn({bool isAutoAuth = false}) async {
    try {
      if (_webAuthnCredentialId == null) {
        throw Exception('No credential ID found');
      }

      final result =
          await WebAuthnService.authenticateCredential(_webAuthnCredentialId!);
      if (result['success'] == true) {
        if (!isAutoAuth) {
          Get.snackbar('Success', 'Authentication successful');
        }
        _proceedToHome();
      } else {
        throw Exception(result['error'] ?? 'Authentication failed');
      }
    } catch (e) {
      throw Exception('Authentication failed: $e');
    }
  }

  void _proceedToHome() {
    Get.snackbar('Success', 'Welcome! Redirecting to home...',
        duration: const Duration(milliseconds: 1500));

    Future.delayed(const Duration(milliseconds: 500), () {
      enteredPin = "";
      Get.toNamed('home');
    });
  }

  void _checkPin(String enteredPin, String stepPin) {
    String storedPin = box.read(LocalStorage.pinCode);

    if (storedPin != enteredPin && enteredPin.length > 5) {
      setState(() {});

      Timer(const Duration(seconds: 10), () {
        setState(() {
          _showError = false;
        });
      });
    }
  }

  void _onKeyPressed(String key) {
    setState(() {
      if (key == "delete") {
        if (enteredPin.isNotEmpty) {
          enteredPin = enteredPin.substring(0, enteredPin.length - 1);
        }
      } else {
        enteredPin += key;

        setState(() {});

        if (enteredPin.length > 5) {
          if (stepPin == "done" && stepPin != "create") {
            if (box.read(LocalStorage.pinCode) == enteredPin) {
              Future.delayed(const Duration(milliseconds: 500), () {
                enteredPin = "";
                Get.toNamed('home');
              });
            } else {
              _checkPin(enteredPin, stepPin);
              enteredPin = "";
              Get.snackbar('Error', 'Pin code not match');
              setState(() {});
            }
          }
          if (stepPin == "create" || stepPin == "") {
            box.write(LocalStorage.stepPin, 'confirm');
            box.write(LocalStorage.pinCode, enteredPin);

            stepPin = box.read('stepPin');
            enteredPin = "";
            setState(() {
              Get.to(() => UnlockTwoFA());
            });
          } else if (stepPin == "confirm") {
            if (box.read(LocalStorage.pinCode) == enteredPin) {
              box.write(LocalStorage.stepPin, 'done');
              _checkPin(enteredPin, stepPin);
              stepPin = box.read('stepPin');
              enteredPin = "";
              Get.toNamed('home');
            } else {
              enteredPin = "";
              setState(() {});
            }
          }
        }
      }
    });
  }

  Widget _buildDigit(int index) {
    bool isPinEntered = index < enteredPin.length;
    bool isFocused = index == 0 && enteredPin.isEmpty;

    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Container(
        width: 20.w,
        height: 20.h,
        decoration: BoxDecoration(
          color: isPinEntered
              ? Theme.of(context).colorScheme.secondaryContainer
              : Colors.transparent,
          shape: BoxShape.circle,
          border: Border.all(
            width: 2.w,
            color: isFocused
                ? Theme.of(context).colorScheme.onSecondary
                : isPinEntered
                    ? Theme.of(context).colorScheme.secondaryContainer
                    : Theme.of(context).colorScheme.onSecondary,
          ),
        ),
      ),
    );
  }

  Widget _buildPinDigit(String digit) {
    return Padding(
      padding: const EdgeInsets.all(5.0),
      child: GestureDetector(
        onTap: () {
          _onKeyPressed(digit);
        },
        child: Container(
          width: 100.w,
          height: 50.h,
          decoration: BoxDecoration(
            color: Colors.transparent,
            shape: BoxShape.rectangle,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
                width: 1.w,
                color: Get.isDarkMode
                    ? Theme.of(context).colorScheme.tertiary.withOpacity(1)
                    : Theme.of(context).colorScheme.tertiary.withOpacity(0.3)),
          ),
          child: Center(
            child: Text(
              digit,
              style: TextStyle(
                  fontSize: 22.sp,
                  color: Theme.of(context).colorScheme.onSecondary),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBiomatic() {
    // Don't show anything if WebAuthn is not supported
    if (!_isWebAuthnSupported) {
      return SizedBox(
        width: 100.w,
        height: 60.h,
      );
    }

    return Padding(
      padding: const EdgeInsets.all(5.0),
      child: GestureDetector(
        onTap: _isAuthenticating
            ? null
            : () {
                _authenticate();
              },
        child: Container(
          width: 100.w,
          height: 60.h,
          decoration: BoxDecoration(
            shape: BoxShape.rectangle,
            borderRadius: BorderRadius.circular(20),
            color: _isAuthenticating
                ? Theme.of(context).colorScheme.surface.withOpacity(0.7)
                : null,
            border: _isWebAuthnRegistered
                ? Border.all(
                    color: Colors.green.withOpacity(0.3),
                    width: 2,
                  )
                : Border.all(
                    color: Colors.blue.withOpacity(0.3),
                    width: 1,
                  ),
          ),
          child: Center(
            child: _isAuthenticating
                ? Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 20.w,
                        height: 20.h,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Theme.of(context).colorScheme.onSecondary,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        _isWebAuthnRegistered ? 'Auth...' : 'Setup...',
                        style: TextStyle(
                          fontSize: 8.sp,
                          color: Theme.of(context).colorScheme.onSecondary,
                        ),
                      ),
                    ],
                  )
                : Stack(
                    alignment: Alignment.center,
                    children: [
                      Image.asset(
                        'assets/Fingerprint.png',
                        color: _isWebAuthnRegistered
                            ? Colors.green
                            : Theme.of(context).colorScheme.onSecondary,
                        height: 110.h,
                        width: 52.w,
                      ),
                    ],
                  ),
          ),
        ),
      ),
    );
  }

  Widget _buildDeleteButton() {
    return Padding(
      padding: const EdgeInsets.all(5.0),
      child: GestureDetector(
        onTap: () {
          _onKeyPressed("delete");
        },
        child: Container(
          width: 100.w,
          height: 60.h,
          decoration: BoxDecoration(
            shape: BoxShape.rectangle,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Center(
            child: Image.asset(
              'assets/Close_button.png',
              height: 46.h,
              width: 46.w,
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // พื้นหลัง gradient
          Container(
            height: double.infinity,
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).colorScheme.primary,
                  Theme.of(context).colorScheme.onPrimary,
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
          ),
          SingleChildScrollView(
            child: SizedBox(
              height: MediaQuery.of(context).size.height,
              width: double.infinity,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Expanded(
                        flex: 0,
                        child: Column(
                          children: [
                            SizedBox(
                              height: 50.h,
                              width: 150.w,
                              child: Image.asset('assets/Logo.png'),
                            ),
                            Container(
                              padding: const EdgeInsets.all(15),
                              child: AppWidget.spacenormalText(
                                context,
                                stepPin == '' || stepPin == 'create'
                                    ? 'ui_create_passcode'.tr
                                    : stepPin == 'confirm'
                                        ? 'ui_confirm_passcode'.tr
                                        : 'ui_passcode'.tr,
                                16.sp,
                                Colors.white,
                                FontWeight.w400,
                                letterSpacing: 1.4000000000000001.h,
                              ),
                            ),
                            _showError
                                ? Text(
                                    'ui_wrong'.tr,
                                    style: const TextStyle(
                                      color: Colors.red,
                                      fontSize: 18.0,
                                    ),
                                  )
                                : Container(),

                            // Show auto-authentication status
                            if (_isAuthenticating &&
                                stepPin == 'done' &&
                                _isWebAuthnRegistered)
                              Container(
                                padding: EdgeInsets.symmetric(vertical: 10.h),
                                child: Column(
                                  children: [
                                    const CircularProgressIndicator(
                                      strokeWidth: 2,
                                      color: Colors.white,
                                    ),
                                    SizedBox(height: 8.h),
                                    Text(
                                      'Authenticating with biometrics...',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 12.sp,
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                            Padding(
                              padding: EdgeInsets.symmetric(vertical: 20.h),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  for (int i = 0; i < 6; i++) _buildDigit(i),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 30.h),
                  // แถวปุ่มตัวเลข
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      for (int i = 1; i <= 3; i++) _buildPinDigit("$i"),
                    ],
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      for (int i = 4; i <= 6; i++) _buildPinDigit("$i"),
                    ],
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      for (int i = 7; i <= 9; i++) _buildPinDigit("$i"),
                    ],
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _buildBiomatic(),
                      _buildPinDigit("0"),
                      _buildDeleteButton(),
                    ],
                  ),
                  SizedBox(height: 40.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      GestureDetector(
                        onTap: () {
                          box.remove(LocalStorage.pinCode);
                          box.write(LocalStorage.stepPin, 'create');
                          centerCon.logout();
                          Get.reloadAll();
                        },
                        child: Row(
                          children: [
                            AppWidget.spacenormalText(
                              context,
                              'ui_lostpin1'.tr,
                              14.sp,
                              Theme.of(context).colorScheme.onSecondary,
                              FontWeight.w400,
                              letterSpacing: 1.4000000000000001.h,
                            ),
                            SizedBox(width: 10.w),
                            AppWidget.spacenormalText(
                              context,
                              'ui_lostpin2'.tr,
                              14.sp,
                              Theme.of(context).colorScheme.onSecondary,
                              FontWeight.w400,
                              letterSpacing: 1.4.h,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 40.h),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildBody() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          height: 50.h,
          width: 150.w,
          child: Image.asset('assets/Logo.png'),
        ),
        Container(
          padding: const EdgeInsets.all(15),
          child: AppWidget.spacenormalText(
            context,
            stepPin == '' || stepPin == 'create'
                ? 'ui_create_passcode'.tr
                : stepPin == 'confirm'
                    ? 'ui_confirm_passcode'.tr
                    : 'ui_passcode'.tr,
            16.sp,
            Colors.white,
            FontWeight.w400,
            letterSpacing: 1.4000000000000001.h,
          ),
        ),
        _showError
            ? Text(
                'ui_wrong'.tr,
                style: const TextStyle(
                  color: Colors.red,
                  fontSize: 18.0,
                ),
              )
            : Container(),
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(vertical: 20.h),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  for (int i = 0; i < 6; i++) _buildDigit(i),
                ],
              ),
            ),
          ],
        ),
        Padding(
          padding: EdgeInsets.only(top: 150.h),
          child: SizedBox(
            width: Get.width,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                for (int i = 1; i <= 3; i++) _buildPinDigit("$i"),
              ],
            ),
          ),
        ),
        SizedBox(
          width: Get.width,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              for (int i = 4; i <= 6; i++) _buildPinDigit("$i"),
            ],
          ),
        ),
        SizedBox(
          width: Get.width,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              for (int i = 7; i <= 9; i++) _buildPinDigit("$i"),
            ],
          ),
        ),
        SizedBox(
          width: Get.width,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildBiomatic(),
              _buildPinDigit("0"),
              _buildDeleteButton(),
            ],
          ),
        ),
        SizedBox(
          height: 60.h,
        ),
        GestureDetector(
          onTap: () {
            box.remove(LocalStorage.pinCode);
            box.write(LocalStorage.stepPin, 'create');

            centerCon.logout();
            Get.reloadAll();
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              AppWidget.spacenormalText(context, 'ui_lostpin1'.tr, 14.sp,
                  Theme.of(context).colorScheme.secondary, FontWeight.w400,
                  letterSpacing: 1.4000000000000001.h),
              SizedBox(
                width: 10.w,
              ),
              AppWidget.spacenormalText(
                context,
                'ui_lostpin2'.tr,
                14.sp,
                Theme.of(context).colorScheme.secondary,
                FontWeight.w400,
                letterSpacing: 1.4.h,
              )
            ],
          ),
        )
      ],
    );
  }
}
