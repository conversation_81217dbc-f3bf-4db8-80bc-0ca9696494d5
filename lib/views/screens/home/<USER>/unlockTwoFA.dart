import 'dart:convert';
import 'dart:io';
import 'dart:ui';

import 'package:ags_authrest2/ags_authrest.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:mapp_ms24/controllers/utils/widget.dart';
import 'package:mapp_ms24/views/screens/Widget/LoadingWidget.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';
import 'package:mapp_ms24/views/screens/home/<USER>';
import 'package:mapp_ms24/views/screens/home/<USER>/yubikey.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:mqtt_client/mqtt_server_client.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';

import '../../../../controllers/login_controller/login.dart';

class UnlockTwoFA extends StatefulWidget {
  UnlockTwoFA({Key? key}) : super(key: key);
  // final ChromeSafariBrowser browser = new ChromeSafariBrowser();

  @override
  _UnlockTwoFAState createState() => _UnlockTwoFAState();
}

class _UnlockTwoFAState extends State<UnlockTwoFA> {
  @override
  CenterController centerCon = Get.find<CenterController>();
  String? authn_id;
  String? app_id;
  String? uinqId;
  String? mac_address;
  var uuid = Uuid();
  bool variable = false;
  String? initialLink;

  void initState() {
    super.initState();
    // analytics.setCurrentScreen(screenName: "unlockTwoFA");
    insertOpenMenu("unlockTwoFA");

    // checkinternet(context);
    // WidgetsFlutterBinding.ensureInitialized();
    // if (Platform.isAndroid) {
    //   // AndroidInAppWebViewController.setWebContentsDebuggingEnabled(true);
    // }
  }

  int statusAlertLogout = 0;
  int statusAlertfailed = 0;

  @override
  Widget build(BuildContext context) {
    bool isDarkTheme = Theme.of(context).brightness == Brightness.dark;
    return Scaffold(
      body: Stack(
        children: [
          Center(
              child: Container(
            color: Theme.of(context).colorScheme.primary,
          )),
          Container(
            margin: EdgeInsets.only(
                top: mediaQuery(context, "h", 100),
                left: mediaQuery(context, "w", 67)),
            child: GestureDetector(
              onTap: () {
                setState(() {
                  statusAlertLogout = 1;
                });
              },
              child: Container(
                color: Colors.transparent,
                alignment: Alignment.center,
                height: mediaQuery(context, "h", 70),
                width: mediaQuery(context, "h", 220),
                child: RichText(
                  text: TextSpan(
                    style: TextStyle(
                      fontFamily: 'SukhumvitSet-Text',
                      fontSize: mediaQuery(context, "h", 26),
                      color: const Color(0xffF6F6F6),
                      letterSpacing: mediaQuery(context, "h", 1.12),
                    ),
                    children: [
                      WidgetSpan(
                        child: SvgPicture.string(
                          '<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M4.75736 4.75736C3.91824 5.59647 3.3468 6.66557 3.11529 7.82946C2.88378 8.99334 3.0026 10.1997 3.45672 11.2961C3.91085 12.3925 4.67988 13.3295 5.66658 13.9888C6.65327 14.6481 7.81331 15 9 15C10.1867 15 11.3467 14.6481 12.3334 13.9888C13.3201 13.3295 14.0891 12.3925 14.5433 11.2961C14.9974 10.1997 15.1162 8.99334 14.8847 7.82946C14.6532 6.66557 14.0818 5.59647 13.2426 4.75736" stroke="#5F569B" stroke-width="2" stroke-linecap="round"/><path d="M9 6L9 3" stroke="#5F569B" stroke-width="2" stroke-linecap="round"/></svg>',
                          allowDrawingOutsideViewBox: true,
                          color: const Color(0xff7295FF),
                          height: mediaQuery(context, "h", 48),
                        ),
                      ),
                      TextSpan(
                        text: '  ',
                      ),
                      TextSpan(
                          text: 'ui_logout'.tr.toString(),
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.onSecondary,
                          )),
                    ],
                  ),
                ),
              ),
            ),
          ),
          Container(
            alignment: Alignment.topCenter,
            margin: EdgeInsets.only(
              top: mediaQuery(context, "h", 600),
            ),
            child: Image.asset(
              isDarkTheme
                  ? 'assets/images/yubikey/Logo_dark.png' // รูปภาพสำหรับธีมมืด
                  : 'assets/images/yubikey/Logo_light.png', // รูปภาพสำหรับธีมสว่าง
              width: mediaQuery(context, "w", 250),
            ),
          ),
          Container(
              // margin: EdgeInsets.only(
              //     top: mediaQuery(context, "w", 50),
              //     left: mediaQuery(context, "w", 10),
              //     right: mediaQuery(context, "w", 10)),
              padding: EdgeInsets.only(bottom: mediaQuery(context, "h", 240)),
              alignment: Alignment.bottomCenter,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AppWidget.normalText(
                    context,
                    'ui_please_unlock'.tr,
                    12.sp,
                    Theme.of(context).colorScheme.scrim,
                    FontWeight.w600,
                  ),
                  SizedBox(
                    width: 5,
                  ),
                  AppWidget.normalText(
                    context,
                    'ui_to_sign_in'.tr,
                    12.sp,
                    Theme.of(context).colorScheme.scrim,
                    FontWeight.w500,
                  ),
                ],
              )),
          Container(
              alignment: Alignment.bottomCenter,
              padding: EdgeInsets.only(bottom: mediaQuery(context, "h", 110)),
              child: GestureDetector(
                onTap: () async {
                  Yubikey.useYubi(context);
                },
                child: Container(
                  alignment: Alignment.center,
                  height: mediaQuery(context, "h", 100),
                  margin: EdgeInsets.only(
                    right: mediaQuery(context, "h", 55),
                    left: mediaQuery(context, "h", 55),
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.0),
                    color: const Color(0xff4CCAB4).withOpacity(0.6),
                    border: Border.all(
                      width: 1.0,
                      color: const Color(0xff4CCAB4).withOpacity(1),
                    ),
                  ),
                  child: Text(
                    "ui_unlock".tr.toString(),
                    style: TextStyle(
                      fontFamily: 'SukhumvitSet-Bold',
                      fontSize: mediaQuery(context, "h", 33),
                      color: Theme.of(context).colorScheme.onSecondary,
                      letterSpacing: mediaQuery(context, "h", 1.5),
                      fontWeight: FontWeight.w700,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              )),
          alertLogout(),
          alertfailed(),
        ],
      ),
    );
  }

  Future<MqttServerClient> listeningEvent(topic) async {
    String hosting = "wss://iac55c5c.ala.us-east-1.emqxsl.com/mqtt";
    String Uname = "webconnect";
    String Pass = "gtm5gvh_hzw5PNH4qpj";
    // String topic = dotenv.env['topicAcleda'].toString();
    var pong = 0;

    Loader.show(context);
    MqttServerClient client = MqttServerClient(hosting, 'flutter_client');

    client.keepAlivePeriod = 60;
    client.logging(on: true);
    client.onConnected = onConnected;
    client.onDisconnected = onDisconnected;
    client.onSubscribed = onSubscribed;
    client.useWebSocket = true;
    client.port = 8084;

    final connMessage = MqttConnectMessage()
        .authenticateAs(Uname, Pass)
        .withWillTopic('willtopic')
        .withWillMessage('Will message')
        .startClean()
        .withWillQos(MqttQos.atLeastOnce);
    client.connectionMessage = connMessage;
    try {
      await client.connect();
    } catch (e) {
      client.disconnect();
    }
    String topicMq = 'mqttpkg/' + topic['uinqId'];
    client.subscribe(topicMq, MqttQos.atMostOnce);
    client.updates!.listen((List<MqttReceivedMessage<MqttMessage?>>? c) async {
      final recMess = c![0].payload as MqttPublishMessage;
      final pt =
          MqttPublishPayload.bytesToStringAsString(recMess.payload.message);
      var auth = Ags_restauth();
      auth.SECERT_JWT = 'Eca)J.L@T9_vbgZ5MDpXUC';
      auth.R_USER = 'yubikeypkg';

      var authDecode = jsonDecode(jsonDecode(auth.decrypBodyStr(pt)));

      if (authDecode['status'] == 'success') {
        client.disconnect();
        SharedPreferences prefs = await SharedPreferences.getInstance();
        prefs.setString('session', authDecode['session'].toString() + '000');
        Loader.hide(context);
        return setState(() {
          Navigator.pushReplacement(
              context, MaterialPageRoute(builder: (context) => HomeScreen()));
        });
      } else {
        setState(() {
          statusAlertfailed = 1;
        });
        Loader.hide(context);
      }
    });
    return client;
  }

  Future<void> onConnected() async {
    print(
        'EXAMPLE::OnConnected client callback - Client connection was sucessful');
  }

  Future<void> onDisconnected() async {
    print('EXAMPLE::OnDisconnected client callback - Client disconnection');
  }

  Future<void> onSubscribed(String topic) async {
    print('EXAMPLE::Subscription confirmed for topic $topic');
  }

  Widget alertLogout() {
    if (statusAlertLogout == 1) {
      return Stack(
        children: <Widget>[
          ClipRect(
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 1.97, sigmaY: 1.97),
              child: Container(
                width: mediaQuery(context, "w", 828),
                height: mediaQuery(context, "h", 1792),
                decoration: BoxDecoration(
                  color: const Color(0x00bebebe),
                ),
              ),
            ),
          ),
          Container(
            alignment: Alignment.topCenter,
            margin: EdgeInsets.only(top: mediaQuery(context, "h", 405)),
            child: Container(
              width: mediaQuery(context, "w", 520),
              height: mediaQuery(context, "h", 600),
              decoration: BoxDecoration(
                borderRadius:
                    BorderRadius.circular(mediaQuery(context, "h", 80)),
                color: Theme.of(context).colorScheme.primary,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.5),
                    offset: Offset(0, 2),
                    blurRadius: 2,
                  ),
                ],
              ),
              child: Stack(
                children: <Widget>[
                  Container(
                      alignment: Alignment.topCenter,
                      margin:
                          EdgeInsets.only(top: mediaQuery(context, "h", 82)),
                      child: SvgPicture.asset(
                        "assets/images/home/<USER>",
                        height: mediaQuery(context, "h", 50),
                      )),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 155)),
                    child: Text(
                      "ui_logoutalert".tr.toString(),
                      style: TextStyle(
                        fontFamily: 'Sukhumvit Set',
                        fontSize: 16.sp,
                        color: Theme.of(context).colorScheme.onSecondary,
                        letterSpacing: mediaQuery(context, "h", 1.32),
                        fontWeight: FontWeight.w700,
                        height: 0.9393939393939394,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 210)),
                    child: SizedBox(
                      width: 368.0,
                      child: Text(
                        "ui_logoutCF".tr.toString(),
                        style: TextStyle(
                          fontFamily: 'Sukhumvit Set',
                          fontSize: 12.sp,
                          color: Theme.of(context).colorScheme.onSecondary,
                          letterSpacing: mediaQuery(context, "h", 1.12),
                          fontWeight: FontWeight.w500,
                          height: 1.5357142857142858,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 335)),
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          statusAlertLogout = 0;
                        });
                      },
                      child: Container(
                        alignment: Alignment.center,
                        width: mediaQuery(context, "w", 250),
                        height: mediaQuery(context, "h", 90),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(
                              mediaQuery(context, "h", 30)),
                          color: Theme.of(context)
                              .colorScheme
                              .secondaryFixed
                              .withOpacity(0.4),
                          border: Border.all(
                              width: 1.0,
                              color: Theme.of(context).colorScheme.primary),
                          boxShadow: [
                            BoxShadow(
                              color: Theme.of(context)
                                  .colorScheme
                                  .primary
                                  .withOpacity(0.5),
                              offset: Offset(0, 1),
                              blurRadius: 2,
                            ),
                          ],
                        ),
                        child: Text(
                          "btn_cancel".tr.toString(),
                          style: TextStyle(
                            fontFamily: 'Sukhumvit Set',
                            fontSize: 12.sp,
                            color: Theme.of(context).colorScheme.onSecondary,
                            letterSpacing: mediaQuery(context, "h", 1.2),
                            fontWeight: FontWeight.w700,
                            // height: 1.0333333333333334,
                            shadows: [
                              Shadow(
                                color: const Color(0x26000000),
                                offset: Offset(0, 1),
                                blurRadius: 1,
                              )
                            ],
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 440)),
                    child: GestureDetector(
                      onTap: () async {
                        await centerCon.logout();
                        setState(() {});
                      },
                      child: Container(
                        alignment: Alignment.center,
                        width: mediaQuery(context, "w", 250),
                        height: mediaQuery(context, "h", 90),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(
                              mediaQuery(context, "h", 30)),
                          color: Theme.of(context)
                              .colorScheme
                              .secondaryContainer
                              .withOpacity(0.6),
                        ),
                        child: Text(
                          "btn_logout".tr.toString(),
                          style: TextStyle(
                            fontFamily: 'Sukhumvit Set',
                            fontSize: 12.sp,
                            color: Theme.of(context).colorScheme.onSecondary,
                            letterSpacing: mediaQuery(context, "h", 1.2),
                            fontWeight: FontWeight.w700,
                            // height: 1,
                            shadows: [
                              Shadow(
                                color: const Color(0x26000000),
                                offset: Offset(0, 1),
                                blurRadius: 1,
                              )
                            ],
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      );
    } else {
      return Container();
    }
  }

  Widget alertfailed() {
    if (statusAlertfailed == 1) {
      return Stack(
        children: <Widget>[
          ClipRect(
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 1.97, sigmaY: 1.97),
              child: Container(
                width: mediaQuery(context, "w", 828),
                height: mediaQuery(context, "h", 1792),
                decoration: BoxDecoration(
                  color: const Color(0x00bebebe),
                ),
              ),
            ),
          ),
          Container(
            alignment: Alignment.topCenter,
            margin: EdgeInsets.only(top: mediaQuery(context, "h", 405)),
            child: Container(
              width: mediaQuery(context, "w", 520),
              height: mediaQuery(context, "h", 600),
              decoration: BoxDecoration(
                borderRadius:
                    BorderRadius.circular(mediaQuery(context, "h", 80)),
                color: const Color(0xfffcf6e4),
              ),
              child: Stack(
                children: <Widget>[
                  Container(
                      alignment: Alignment.topCenter,
                      margin:
                          EdgeInsets.only(top: mediaQuery(context, "h", 82)),
                      child: SvgPicture.asset(
                        "assets/images/home/<USER>",
                        height: mediaQuery(context, "h", 50),
                      )),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 155)),
                    child: Text(
                      "ui_titleAlert2".tr.toString(),
                      style: TextStyle(
                        fontFamily: 'SukhumvitSet-Bold',
                        fontSize: mediaQuery(context, "h", 33),
                        color: const Color(0xff302c49),
                        letterSpacing: mediaQuery(context, "h", 1.32),
                        fontWeight: FontWeight.w700,
                        height: 0.9393939393939394,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 210)),
                    child: SizedBox(
                      width: 368.0,
                      child: Text(
                        "กรุณาลองใหม่อีกครั้ง",
                        style: TextStyle(
                          fontFamily: 'SukhumvitSet-Medium',
                          fontSize: mediaQuery(context, "h", 28),
                          color: const Color(0xff302c49),
                          letterSpacing: mediaQuery(context, "h", 1.12),
                          fontWeight: FontWeight.w500,
                          height: 1.5357142857142858,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                  Container(
                    alignment: Alignment.topCenter,
                    margin: EdgeInsets.only(top: mediaQuery(context, "h", 440)),
                    child: GestureDetector(
                      onTap: () {
                        Navigator.pushReplacement(
                          context,
                          MaterialPageRoute(
                              builder: (context) => UnlockTwoFA()),
                        );
                      },
                      child: Container(
                        alignment: Alignment.center,
                        width: mediaQuery(context, "w", 250),
                        height: mediaQuery(context, "h", 90),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(
                              mediaQuery(context, "h", 30)),
                          color: const Color(0xfe934d0f),
                        ),
                        child: Text(
                          "btn_sign_2".tr.toString(),
                          style: TextStyle(
                            fontFamily: 'SukhumvitSet-Bold',
                            fontSize: mediaQuery(context, "h", 30),
                            color: const Color(0xfeffffff),
                            letterSpacing: mediaQuery(context, "h", 1.2),
                            fontWeight: FontWeight.w700,
                            // height: 1,
                            shadows: [
                              Shadow(
                                color: const Color(0x26000000),
                                offset: Offset(0, 1),
                                blurRadius: 1,
                              )
                            ],
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      );
    } else {
      return Container();
    }
  }
}
