//
// import 'package:flutter/material.dart';
// import 'package:video_player/video_player.dart';
//
// class playVideo extends StatefulWidget {
//   final linkVideo;
//
//   playVideo(this.linkVideo);
//
//   @override
//   _playVideoState createState() => _playVideoState(this.linkVideo);
// }
//
// class _playVideoState extends State<playVideo> {
//   final linkVideo;
//
//   _playVideoState(this.linkVideo);
//
//   late VideoPlayerController _videoPlayerController;
//   bool startedPlaying = false;
//
//   @override
//   void initState() {
//     super.initState();
//
//     _videoPlayerController =
//     // VideoPlayerController.asset('assets/Butterfly-209.mp4');
//     // VideoPlayerController.network(
//     //     "https://s3-ap-southeast-1.amazonaws.com/storage-pkg/icon/icon1627004541cew.mov");
//     VideoPlayerController.network(linkVideo.toString());
//     _videoPlayerController.addListener(() {
//       if (startedPlaying && !_videoPlayerController.value.isPlaying) {
//         Navigator.pop(context);
//       }
//     });
//   }
//
//   @override
//   void dispose() {
//     _videoPlayerController.dispose();
//     super.dispose();
//   }
//
//   Future<bool> started() async {
//     await _videoPlayerController.initialize();
//     await _videoPlayerController.play();
//     startedPlaying = true;
//     return true;
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: Stack(
//         children: [
//           Center(
//             child: FutureBuilder<bool>(
//               future: started(),
//               builder: (BuildContext context, AsyncSnapshot<bool> snapshot) {
//                 if (snapshot.data == true) {
//                   return AspectRatio(
//                     aspectRatio: _videoPlayerController.value.aspectRatio,
//                     child: VideoPlayer(_videoPlayerController),
//                   );
//                 } else {
//                   return const Text('waiting for video to load');
//                 }
//               },
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
