
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';
import 'package:photo_view/photo_view.dart';

class showImage extends StatefulWidget {
  final linkImage;

  showImage(this.linkImage);
  @override
  _showImageState createState() => _showImageState(this.linkImage);
}

class _showImageState extends State<showImage> {
  final linkImage;

  _showImageState(this.linkImage);

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          PhotoView(
            imageProvider: NetworkImage(linkImage.toString()),
          ),
          Container(
            margin: EdgeInsets.only(
                top: mediaQuery(context, "h", 154),
                left: mediaQuery(context, "w", 67)),
            child: GestureDetector(
              onTap: () {
                Navigator.of(context).pop();
              },
              child: Container(
                color: Colors.transparent,
                alignment: Alignment.center,
                height: mediaQuery(context, "h", 70),
                width: mediaQuery(context, "h", 70),
                child: <PERSON><PERSON>(
                  children: [
                    SvgPicture.string(
                      '<svg viewBox="67.0 154.0 21.7 38.0" ><path transform="translate(55.75, 147.81)" d="M 17.79955863952637 25.18868064880371 L 32.17768859863281 10.82186222076416 C 33.24105834960938 9.758491516113281 33.24105834960938 8.038998603820801 32.17768859863281 6.986940383911133 C 31.11431694030762 5.92357063293457 29.39482688903809 5.934882164001465 28.33145332336426 6.986940383911133 L 12.0415210723877 23.26556205749512 C 11.01208686828613 24.29499435424805 10.98946189880371 25.94661521911621 11.96233177185059 27.00998306274414 L 28.32014274597168 43.40172958374023 C 28.85183143615723 43.93341445922852 29.55320167541504 44.19359970092773 30.24326133728027 44.19359970092773 C 30.93332099914551 44.19359970092773 31.63469123840332 43.93341445922852 32.1663818359375 43.40172958374023 C 33.22975158691406 42.33835601806641 33.22975158691406 40.61886596679688 32.1663818359375 39.56680679321289 L 17.79955863952637 25.18868064880371 Z" fill="#fcf6e4" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                      allowDrawingOutsideViewBox: true,
                      height: mediaQuery(context, "h", 38),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
