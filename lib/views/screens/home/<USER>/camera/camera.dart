import 'dart:convert';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../controllers/internal/BenefitController /BenefitController.dart';
import '../Benefit/BasicWelfare/BasicWelfareFrom.dart';

class CameraScreen extends StatefulWidget {
  @override
  State<CameraScreen> createState() => _CameraScreenState();
}

class _CameraScreenState extends State<CameraScreen> {
  late List<CameraDescription> cameras;
  CameraController? cameraController;
  bool isCameraInitialized = false;
  String capturedImageBase64 = '';
  int selectedCameraIndex = 0;
  BenefitController benCrt = Get.find<BenefitController>();

  @override
  void initState() {
    super.initState();
    initializeCamera();
  }

  Future<void> initializeCamera() async {
    try {
      cameras = await availableCameras();
      if (cameras.isNotEmpty) {
        await setCamera(selectedCameraIndex);
      } else {
        Get.snackbar(
          'Error',
          'No cameras available',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.withOpacity(0.7),
          colorText: Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to initialize camera: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withOpacity(0.7),
        colorText: Colors.white,
      );
    }
  }

  Future<void> setCamera(int cameraIndex) async {
    try {
      final CameraDescription camera = cameras[cameraIndex];
      cameraController = CameraController(camera, ResolutionPreset.high);

      await cameraController!.initialize();
      if (mounted) {
        setState(() {
          isCameraInitialized = true;
        });
      }
    } catch (e) {
      print('Error setting camera: $e');
    }
  }

  Future<void> toggleCamera() async {
    if (cameras.length > 1) {
      final newIndex = selectedCameraIndex == 0 ? 1 : 0;
      setState(() {
        isCameraInitialized = false;
        selectedCameraIndex = newIndex;
      });

      await setCamera(newIndex);
    } else {
      Get.snackbar(
        'Info',
        'No secondary camera available to switch',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.yellow.withOpacity(0.7),
        colorText: Colors.black,
      );
    }
  }

  Future<void> captureImage() async {
    try {
      if (isCameraInitialized && cameraController!.value.isInitialized) {
        final XFile image = await cameraController!.takePicture();
        final bytes = await image.readAsBytes();
        String base64Image = base64Encode(bytes);

        showDialog(
          context: context,
          builder: (_) => AlertDialog(
            title: Text('Confirm Image'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('Do you want to use this image?'),
                SizedBox(height: 10),
                ClipRRect(
                  borderRadius: BorderRadius.circular(10),
                  child: Image.memory(
                    bytes,
                    width: 200,
                    height: 200,
                    fit: BoxFit.cover,
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    benCrt.imgName.value = base64Image;
                  });
                  Navigator.of(context).pop();
                  Get.offAll(Welfarefrom());
                  Get.snackbar(
                    'Success',
                    'Image saved successfully!',
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: Colors.green.withOpacity(0.7),
                    colorText: Colors.white,
                  );
                },
                child: Text('Confirm'),
              ),
            ],
          ),
        );
      } else {
        throw Exception('Camera is not initialized');
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to capture image: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withOpacity(0.7),
        colorText: Colors.white,
      );
    }
  }

  @override
  void dispose() {
    cameraController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          if (isCameraInitialized && cameraController != null)
            SizedBox(
              height: Get.height,
              width: Get.width,
              child: CameraPreview(cameraController!),
            )
          else
            Center(child: CircularProgressIndicator()),
          if (capturedImageBase64.isNotEmpty)
            Align(
              alignment: Alignment.topCenter,
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(10),
                  child: Image.memory(
                    base64Decode(capturedImageBase64),
                    width: 200,
                    height: 200,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
          Positioned(
            top: 50,
            right: 20,
            child: GestureDetector(
              onTap: toggleCamera,
              child: Container(
                padding: EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.7),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.cameraswitch,
                  color: Colors.black,
                  size: 30,
                ),
              ),
            ),
          ),
          Positioned(
            bottom: 30,
            left: 0,
            right: 0,
            child: Center(
              child: GestureDetector(
                onTap: captureImage,
                child: Container(
                  width: 70,
                  height: 70,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.camera_alt,
                    color: Colors.black,
                    size: 30,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
