import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/controllers/internal/wealthController/wealthController.dart';
import 'package:mapp_ms24/controllers/utils/color.dart';
import 'package:mapp_ms24/controllers/utils/widget.dart';
import 'package:mapp_ms24/views/screens/Widget/library.dart';

Widget buildIncome(BuildContext context) {
  return GetBuilder<ProfileController>(
    builder: (profileController) => Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AppWidget.normalText(context, 'btn_Income'.tr, 14.sp,
                Theme.of(context).colorScheme.scrim, FontWeight.w400),
            AppWidget.boldText(
                context,
                double.parse(profileController.incomeModel!.salary)
                    .toStringAsFixed(2)
                    .replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                        (Match m) => '${m[1]},'),
                16.sp,
                Theme.of(context).colorScheme.secondaryContainer,
                FontWeight.w700),
            AppWidget.normalText(context, 'ui_Salary'.tr, 12.sp,
                Theme.of(context).colorScheme.secondaryFixedDim, FontWeight.w500),
          ],
        ),
        SizedBox(
          height: 70.h,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              AppWidget.normalText(
                  context,
                  double.parse(profileController.incomeModel!.IncomeReward).toStringAsFixed(2).replaceAllMapped(
                      new RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                          (Match m) => '${m[1]},'),
                  14.sp,
                  Theme.of(context).colorScheme.secondaryContainer,
                  FontWeight.w500),
              AppWidget.normalText(context, 'ui_Reward'.tr, 12.sp,
                  Theme.of(context).colorScheme.secondaryFixedDim, FontWeight.w500),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(left: 30.0),
          child: SizedBox(
            height: 70.h,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                AppWidget.normalText(context,
                    double.parse(profileController.pfs.toString()).toStringAsFixed(2).replaceAllMapped(
                        new RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                            (Match m) => '${m[1]},'),
                    14.sp,Theme.of(context).colorScheme.secondaryContainer, FontWeight.w500),
                AppWidget.normalText(context, 'ui_PFS'.tr, 12.sp,
                    Theme.of(context).colorScheme.secondaryFixedDim, FontWeight.w500),
              ],
            ),
          ),
        ),
        SizedBox(
          width: 80.w,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              AppWidget.boldText(
                  context,
                  double.parse(profileController.incomeModel!.Incentive!).toStringAsFixed(2).replaceAllMapped(
                      new RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                          (Match m) => '${m[1]},'),
                  14.sp,
                  Theme.of(context).colorScheme.secondaryContainer,
                  FontWeight.w500),
              AppWidget.normalText(context, 'ui_Incentive'.tr, 12.sp,
                  Theme.of(context).colorScheme.secondaryFixedDim, FontWeight.w500),
              AppWidget.normalText(context, 'ui_dateUpdate'.tr, 10.sp,
                  Theme.of(context).colorScheme.tertiaryFixed, FontWeight.w400),
              AppWidget.normalText(
                  context,
                  convertDateTime(
                      profileController.incomeModel!.dateUpdateIncentive,
                      "dd/MM/YYYY"),
                  10.sp,
                  Theme.of(context).colorScheme.tertiary,
                  FontWeight.w400),
            ],
          ),
        ),
      ],
    ),
  );
}

Widget buildCu(BuildContext context) {
  return GetBuilder<WealthController>(
    builder: (wealthControl) => Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AppWidget.boldText(context, 'ui_cu'.tr, 14.sp,
                    Theme.of(context).colorScheme.scrim, FontWeight.w400),
                SizedBox(width: 28.w),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AppWidget.boldText(
                        context,
                        // '0',
                        (wealthControl.cU_Model!.shareBalance + wealthControl.totalShares).toStringAsFixed(2).replaceAllMapped(
                            new RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                                (Match m) => '${m[1]},'),
                        16.sp,
                        Theme.of(context).colorScheme.secondaryContainer,
                        FontWeight.w600),
                    AppWidget.normalText(context, 'ui_TotallyGTC'.tr, 12.sp,
                        Theme.of(context).colorScheme.secondaryFixedDim, FontWeight.w500),
                  ],
                )
              ],
            )
          ],
        ),
        Padding(
          padding: const EdgeInsets.only(left: 18.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              AppWidget.normalText(
                  context,
                  double.parse((wealthControl.savingMoney).toString()).toStringAsFixed(2).replaceAllMapped(
                      new RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                          (Match m) => '${m[1]},'),                  // wealthControl.cU_Model!.saving.toString(),
                  14.sp,
                  Theme.of(context).colorScheme.secondaryContainer,
                  FontWeight.w400),
              AppWidget.normalText(context, 'ui_SavingCU'.tr, 12.sp,
                  Theme.of(context).colorScheme.secondaryFixedDim, FontWeight.w500)
            ],
          ),
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            AppWidget.normalText(
                context, double.parse(wealthControl.debtBalance.toString()).toStringAsFixed(2).replaceAllMapped(
                new RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                    (Match m) => '${m[1]},'), 14.sp,Theme.of(context).colorScheme.secondaryContainer, FontWeight.w400),
            AppWidget.normalText(context, 'ui_DebtCU'.tr, 12.sp,
                Theme.of(context).colorScheme.secondaryFixedDim, FontWeight.w500),
          ],
        ),
      ],
    ),
  );
}

Widget buildMETACu(BuildContext context) {
  return GetBuilder<WealthController>(
    builder: (wealthControl) => Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: 50.h,
                  width: 36.w,
                  child: AppWidget.boldText(
                    context,
                    'ui_MetaCu'.tr,
                    14.sp,
                    Theme.of(context).colorScheme.scrim,
                    FontWeight.w400,
                  ),
                ),
                SizedBox(width: 10.w),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AppWidget.boldText(
                      context,
                      double.tryParse(wealthControl.mcu_Model?.mcu?.toString() ?? '0')!
                          .toStringAsFixed(2)
                          .replaceAllMapped(
                        RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                            (Match m) => '${m[1]},',
                      ),
                      16.sp,
                      Theme.of(context).colorScheme.secondaryContainer,
                      FontWeight.w600,
                    ),


                    AppWidget.normalText(
                      context,
                      'ui_userConditionsCu'.tr,
                      12.sp,
                      Theme.of(context).colorScheme.secondaryFixedDim,
                      FontWeight.w500,
                    ),
                    AppWidget.normalText(
                      context,
                      wealthControl.mcu_Model?.holdMCU != null
                          ? double.parse(wealthControl.mcu_Model!.holdMCU!.toString())
                          .toStringAsFixed(2)
                          .replaceAllMapped(
                        RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                            (Match m) => '${m[1]},',
                      )
                          : '0.00',
                      12.sp,
                      Theme.of(context).colorScheme.secondaryContainer,
                      FontWeight.w400,
                    ),

                    AppWidget.normalText(
                      context,
                      'ui_ConditionsCu'.tr,
                      12.sp,
                      Theme.of(context).colorScheme.secondaryFixedDim,
                      FontWeight.w500,
                    ),
                    AppWidget.normalText(
                      context,
                      'ui_Conditions'.tr,
                      10.sp,
                      Theme.of(context).colorScheme.tertiary,
                      FontWeight.w500,
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            AppWidget.normalText(
              context,
              wealthControl.rewardMCU_Model?.reward_holdmcu != null
                  ? double.parse(wealthControl.rewardMCU_Model!.reward_holdmcu!.toString())
                  .toStringAsFixed(2)
                  .replaceAllMapped(
                RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                    (Match m) => '${m[1]},',
              )
                  : '0.00',
              14.sp,
              Theme.of(context).colorScheme.secondaryContainer,
              FontWeight.w400,
            ),

            AppWidget.normalText(
              context,
              'ui_reCu'.tr,
              12.sp,
              Theme.of(context).colorScheme.secondaryFixedDim,
              FontWeight.w500,
            ),
            AppWidget.normalText(
              context,
              wealthControl.rewardMCU_Model?.reward_mcu != null
                  ? double.parse(wealthControl.rewardMCU_Model!.reward_mcu!.toString())
                  .toStringAsFixed(2)
                  .replaceAllMapped(
                RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                    (Match m) => '${m[1]},',
              )
                  : '0.00',
              14.sp,
              Theme.of(context).colorScheme.secondaryContainer,
              FontWeight.w400,
            ),

            AppWidget.normalText(
              context,
              'ui_reward'.tr,
              12.sp,
              Theme.of(context).colorScheme.secondaryFixedDim,
              FontWeight.w500,
            ),
            AppWidget.normalText(
              context,
              'ui_reward2'.tr,
              12.sp,
              Theme.of(context).colorScheme.tertiary,
              FontWeight.w500,
            ),
          ],
        ),
      ],
    ),
  );
}


Widget buildINSURANCE(BuildContext context) {
  WealthController wealthControl = Get.find();
  return Row(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      AppWidget.normalText(context, 'btn_Insurance_CU'.tr, 14.sp,
          Theme.of(context).colorScheme.scrim, FontWeight.w400),
      const Spacer(),
      AppWidget.normalText(context, 'ui_InsuranceCU'.tr, 12.sp,
          Theme.of(context).colorScheme.secondaryFixedDim, FontWeight.w500),
      SizedBox(
        width: 5.w,
      ),
      AppWidget.normalText(
          context, double.parse(wealthControl.savingCU.toString()).toStringAsFixed(2).replaceAllMapped(
          new RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
              (Match m) => '${m[1]},'), 14.sp, Theme.of(context).colorScheme.secondaryFixed, FontWeight.w500),
    ],
  );
}

Widget buildGTC(BuildContext context) {
  return GetBuilder<WealthController>(
    builder: (wealthControl) => Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AppWidget.boldText(context, 'ui_gtc'.tr, 14.sp,
                    Theme.of(context).colorScheme.scrim, FontWeight.w400),
                SizedBox(width: 20.w),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AppWidget.boldText(
                        context,
                        double.parse(wealthControl.holdingTotal.toString()).toStringAsFixed(2)
                            .replaceAllMapped(
                            RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                                (Match m) => '${m[1]},') ,
                        16.sp,
                        Theme.of(context).colorScheme.secondaryContainer,
                        FontWeight.w600),
                    AppWidget.normalText(context, '${'ui_Unit'.tr} = ฿${double.parse(wealthControl.GTCBalance.toString()).toStringAsFixed(2)
                        .replaceAllMapped(
                        RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                            (Match m) => '${m[1]},')}',
                        12.sp, Theme.of(context).colorScheme.secondaryFixedDim, FontWeight.w500),
                  ],
                )
              ],
            )
          ],
        ),
        Padding(
          padding: const EdgeInsets.only(left: 14.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              AppWidget.normalText(context, wealthControl.BV, 14.sp,
                  Theme.of(context).colorScheme.secondaryContainer, FontWeight.w400),
              AppWidget.normalText(context, 'ui_BV'.tr, 12.sp,
                  Theme.of(context).colorScheme.secondaryFixedDim, FontWeight.w500)
            ],
          ),
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            AppWidget.normalText(context, double.parse(wealthControl.GTCguarantee.toString()).toStringAsFixed(2)
                .replaceAllMapped(
                RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                    (Match m) => '${m[1]},') , 14.sp,
                Theme.of(context).colorScheme.secondaryFixed, FontWeight.w400),
            AppWidget.normalText(context, 'ui_Collateralized'.tr, 12.sp,
                Theme.of(context).colorScheme.secondaryFixedDim, FontWeight.w500),
            AppWidget.normalText(context, 'ui_withcu'.tr, 12.sp,
                Theme.of(context).colorScheme.secondaryContainer, FontWeight.w500)
          ],
        ),
      ],
    ),
  );
}
