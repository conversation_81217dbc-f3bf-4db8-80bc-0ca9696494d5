import 'package:flutter/material.dart';
import 'package:flutter_fortune_wheel/flutter_fortune_wheel.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:screenshot/screenshot.dart';
// import 'package:nativtest/config/Majesticons_icons.dart';
// import 'package:nativtest/config/color_system.dart';
// import 'package:nativtest/controllers/loyalty_controller/lotto_controller.dart';
// import 'package:nativtest/widgets/button_Widget/secondary_button.dart';
// import 'package:nativtest/widgets/confetti_widget.dart';
// import 'package:screenshot/screenshot.dart';

import '../../../configs/Majesticons_icons.dart';
import '../../../configs/color_system.dart';
import '../../../controllers/spin_controller/spin_controller.dart';
import '../Widget/button_Widget/secondary_button.dart';
import '../Widget/confetti.dart';
// import 'package:confetti/src/confetti.dart';

class SpinRewardScreen extends StatelessWidget {
  const SpinRewardScreen({super.key});
  //  late ConfettiController confettiController;

  @override
  Widget build(BuildContext context) {
    SpinController spinController = Get.find();
    return Screenshot(
      controller: spinController.screenshotController,
      child: Scaffold(
          extendBodyBehindAppBar: true,
          appBar: AppBar(
            backgroundColor: transparentColor,
            elevation: 0.0,
            leading: InkWell(
              splashColor: transparentColor,
              highlightColor: transparentColor,
              onTap: () => Get.back(),
              child: Container(
                margin: const EdgeInsets.all(14),
                width: 32,
                height: 32,
                decoration: const ShapeDecoration(
                  color: Color(0x4CB899EC),
                  shape: OvalBorder(),
                ),
                child: const Icon(
                  Icons.close,
                  size: 18,
                  color: whiteColor,
                ),
              ),
            ),
          ),
          body: GetBuilder<SpinController>(
            init: SpinController(),
            builder: (lottoController) {
              return Stack(
                alignment: Alignment.topCenter,
                children: [
                  SizedBox(
                    height: Get.height,
                    width: Get.width,
                    child: SvgPicture.asset(
                      'assets/svgs/bg-daily-lotto.svg',
                      fit: BoxFit.cover,
                    ),
                  ),
                  Align(
                    alignment: Alignment.bottomCenter,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          SizedBox(
                            height: Get.height * 0.11,
                          ),
                          Text(
                            lottoController.spinReward!.amount == 0
                                ? 'Sorry'.tr
                                : 'Congrats!'.tr,
                            style: const TextStyle(
                              color: whiteColor,
                              fontSize: 30,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                          Padding(
                              padding: EdgeInsets.only(
                                  top: Get.height * 0.005,
                                  bottom: Get.height * 0.03),
                              child: Text(
                                lottoController.spinReward!.amount == 0
                                    ? "You didn't get the reward this time."
                                    : '${"ยินดีด้วยคุณได้รับ".tr} ${lottoController.spinReward!.amount} "MSP"',
                                textAlign: TextAlign.center,
                                style: const TextStyle(
                                  color: whiteColor,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                ),
                              )),
                          Container(
                            margin: EdgeInsets.only(
                                top: Get.height * 0.13, bottom: 15),
                            padding: const EdgeInsets.all(30),
                            width: Get.width * 0.4,
                            height: Get.width * 0.4,
                            decoration: const ShapeDecoration(
                              color: whiteColor,
                              shape: OvalBorder(),
                            ),
                            child: lottoController.spinReward!.unit == "Points"
                                ? Image.asset(
                                    'assets/Timmi/TimmiLovely.png',
                                    width: 45)
                                : Container(
                                    decoration: ShapeDecoration(
                                        shape: const OvalBorder(),
                                        image: DecorationImage(
                                            image: AssetImage(lottoController
                                                        .spinReward!.unit ==
                                                    "USDT"
                                                ? 'assets/images/usdt.png'
                                                : 'assets/images/no-reward.png'))),
                                  ),
                          ),
                          lottoController.spinReward!.amount == 0
                              ? const SizedBox.shrink()
                              : Text(
                                  '${lottoController.spinReward!.amount} MSP',
                                  style: const TextStyle(
                                    color: whiteColor,
                                    fontSize: 30,
                                    fontWeight: FontWeight.w700,
                                  ),
                                ),
                          Padding(
                            padding: EdgeInsets.only(
                                top: Get.height * 0.13, bottom: 12),
                            child: SecondaryButton(
                              title: "เยี่ยมไปเลยย!!!!",
                              onPressed: () {
                                  Get.offAllNamed('home');
                              },
                              fontColor: darkBlueColor,
                              backgroundColor: whiteColor,
                            ),
                          ),
                          // SecondaryButton(
                          //   iconData: Majesticons.share_3,
                          //   title: 'Share'.tr,
                          //   onPressed: () {
                          //     // lottoController.shareDailyReward();
                              
                          //   },
                          //   fontColor: whiteColor,
                          //   backgroundColor: lightBlueColor,
                          // ),
                          SizedBox(height: Get.height * 0.06)
                        ],
                      ),
                    ),
                  ),
                  spinController.spinReward!.amount == 0
                      ? const SizedBox.shrink()
                      : Align(
                          alignment: Alignment.topLeft,
                          child: confetti(spinController.confettiController),
                        ),
                  spinController.spinReward!.amount == 0
                      ? const SizedBox.shrink()
                      : Align(
                          alignment: Alignment.topCenter,
                          child: confetti(spinController.confettiController),
                        ),
                  spinController.spinReward!.amount == 0
                      ? const SizedBox.shrink()
                      : Align(
                          alignment: Alignment.topRight,
                          child: confetti(spinController.confettiController),
                        ),
                ],
              );
            },
          )),
    );
  }
}