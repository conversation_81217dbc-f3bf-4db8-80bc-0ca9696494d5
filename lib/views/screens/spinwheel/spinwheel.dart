import 'package:flutter/material.dart';
import 'package:flutter_fortune_wheel/flutter_fortune_wheel.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_timer_countdown/flutter_timer_countdown.dart';
import 'package:get/get.dart';
// import Majesticons Icons
import 'package:mapp_ms24/configs/Majesticons_icons.dart';
import 'package:mapp_ms24/configs/color_system.dart';
import 'package:mapp_ms24/controllers/spin_controller/spin_controller.dart';
import 'package:mapp_ms24/views/screens/Widget/button_Widget/secondary_button.dart';

import '../Widget/library.dart';

class SpinWheelScreen extends StatelessWidget {
  const SpinWheelScreen({super.key});

  @override
  Widget build(BuildContext context) {
    
    return Scaffold(
        extendBodyBehindAppBar: true,
        appBar: AppBar(
          elevation: 0.0,
          backgroundColor: transparentColor,
          leading: InkWell(
            splashColor: transparentColor,
            highlightColor: transparentColor,
            onTap: () => {
            
                Get.offAllNamed('home')
            },
            child: Container(
              margin: const EdgeInsets.all(14),
              width: 32,
              height: 32,
              decoration: const ShapeDecoration(
                color: Color(0x4CB899EC),
                shape: OvalBorder(),
              ),
              child: const Icon(
                Icons.close,
                size: 18,
                color: whiteColor,
              ),
            ),
          ),
        ),
        body: GetBuilder<SpinController>(
          init: SpinController(),
          builder: (lottoController) {
            return Stack(
              children: [
                SizedBox(
                  height: Get.height,
                  width: Get.width,
                  child: SvgPicture.asset(
                    'assets/svgs/bg-daily-lotto.svg',
                    fit: BoxFit.cover,
                  ),
                ),
                Align(
                  alignment: Alignment.center,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        SizedBox(
                          height: Get.height * 0.08,
                        ),
                        Text(
                          'เสี่ยงโชคปีใหม่ PKG'.tr,
                          textAlign: TextAlign.center,
                          style: const TextStyle(
                            color: whiteColor,
                            fontSize: 30,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.only(
                              top: Get.height * 0.005,
                              bottom: Get.height * 0.03),
                          child: Text(
                            'หมุนเพื่อลุ้นรับสูงสุด 50000 MSP'.tr,
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                              color: darkerSecondaryColor,
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                        Container(
                          width: Get.width * 0.4,
                          padding: const EdgeInsets.symmetric(
                              vertical: 5, horizontal: 16),
                          decoration: const BoxDecoration(
                              borderRadius:
                                  BorderRadius.all(Radius.circular(20)),
                              color: lighterPrimaryColor),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(Majesticons.clock,
                                  size: 16, color: whiteColor),
                              const SizedBox(width: 4),
                              // TimerCountdown(
                              //   enableDescriptions: false,
                              //   format:
                              //       CountDownTimerFormat.hoursMinutesSeconds,
                              //   timeTextStyle: const TextStyle(
                              //     color: whiteColor,
                              //     fontSize: 16,
                              //     fontWeight: FontWeight.w500,
                              //   ),
                              //   colonsTextStyle: const TextStyle(
                              //     color: whiteColor,
                              //     fontSize: 16,
                              //     fontWeight: FontWeight.w500,
                              //   ),
                              //   spacerWidth: 4,
                              //   endTime: DateTime.now().hour >= 0 &&
                              //           DateTime.now().hour < 10
                              //       ? DateTime(
                              //           DateTime.now().year,
                              //           DateTime.now().month,
                              //           DateTime.now().day,
                              //           10,
                              //           0,
                              //           0,
                              //           0,
                              //         )
                              //       : DateTime(
                              //           DateTime.now()
                              //               .add(const Duration(days: 1))
                              //               .year,
                              //           DateTime.now()
                              //               .add(const Duration(days: 1))
                              //               .month,
                              //           DateTime.now()
                              //               .add(const Duration(days: 1))
                              //               .day,
                              //           10,
                              //           0,
                              //           0,
                              //           0,
                              //         ),
                              //   onEnd: () {
                              //     Get.back();
                              //   },
                              // ),
                            ],
                          ),
                        ),
                        Stack(
                          alignment: Alignment.center,
                          children: [
                            lottoController.items.isNotEmpty
                                ? Align(
                                    alignment: Alignment.center,
                                    child: Container(
                                      margin: EdgeInsets.only(
                                          top: Get.height * 0.06,
                                          bottom: Get.height * 0.06),
                                      height: Get.height * 0.4,
                                      child: FortuneWheel(
                                        indicators: const <FortuneIndicator>[],
                                        animateFirst: false,
                                        duration:
                                            const Duration(milliseconds: 5000),
                                        selected: lottoController
                                            .dailyLottoController.stream,
                                        items: lottoController.items.value,
                                        onAnimationEnd: () =>
                                            lottoController.checkReward(),
                                      ),
                                    ),
                                  )
                                : const SizedBox.shrink(),
                            SizedBox(
                              height: Get.height * 0.43,
                              width: Get.width * 0.9,
                              child: SvgPicture.asset(
                                  'assets/svgs/illus-border-wheel.svg'),
                            ),
                            Positioned(
                              top: Get.height * 0.03,
                              child: SizedBox(
                                width: 30,
                                height: 30,
                                child: Image.asset(
                                    'assets/images/Polygon_icon.png'),
                              ),
                            ),
                            Align(
                              alignment: Alignment.center,
                              child: InkWell(
                                splashColor: transparentColor,
                                highlightColor: transparentColor,
                                onTap: () => {
                                  if (lottoController.isUserCanSpin.value)
                                    {lottoController.spinTheWheel()}
                                },
                                child: Container(
                                  padding: const EdgeInsets.all(3),
                                  width: 60,
                                  height: 60,
                                  decoration: const ShapeDecoration(
                                    color: whiteColor,
                                    shape: OvalBorder(),
                                  ),
                                  child: Container(
                                    padding: const EdgeInsets.all(3),
                                    width: 60,
                                    height: 60,
                                    decoration: const ShapeDecoration(
                                      color: lightPrimaryColor,
                                      shape: OvalBorder(),
                                    ),
                                    child: Container(
                                      alignment: Alignment.center,
                                      padding: const EdgeInsets.all(1.5),
                                      width: 60,
                                      height: 60,
                                      decoration: const ShapeDecoration(
                                        color: transparentColor,
                                        shape: OvalBorder(
                                            side: BorderSide(
                                                color: lighterPrimaryColor2,
                                                width: 1)),
                                      ),
                                      child: Text(
                                        'GO'.tr,
                                        style: const TextStyle(
                                          color: whiteColor,
                                          fontSize: 20,
                                          fontWeight: FontWeight.w700,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            )
                          ],
                        ),
                        Text(
                          'Note: รางวัลสูงสุด 50000 MSP ต่ำสุด 1000 MSP ขอให้โชคดี.'
                              .tr,
                          textAlign: TextAlign.center,
                          style: const TextStyle(
                            color: darkerSecondaryColor,
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.only(
                              top: Get.height * 0.04,
                              bottom: Get.height * 0.06),
                          child: SecondaryButton(
                            title: 'หมุนเลย'.tr,
                            onPressed: () {

                              if(lottoController.isUserCanSpin.value == true){
                                lottoController.spinTheWheel();
                            
                                
                              }else{
                                 ErrorshowAlertDialog(
            context, 'คุณได้ทำการหมุนรางวัลไปแล้ว', "รอติดตามตอนต่อไป");
                              }
                              
                            },
                            isDisabled: false,
                            fontColor: darkBlueColor,
                            backgroundColor: whiteColor,
                          ),
                        )
                      ],
                    ),
                  ),
                )
              ],
            );
          },
        ));
  }
}