import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:mapp_ms24/controllers/internal/LeaveController/LeaveApprovalController.dart';
import 'package:mapp_ms24/controllers/internal/NotiController/NotifiController.dart';
import 'package:mapp_ms24/controllers/internal/Profile/profileController.dart';
import 'package:mapp_ms24/controllers/internal/wealthController/wealthController.dart';
import 'package:mapp_ms24/controllers/login_controller/login.dart';
import 'package:mapp_ms24/controllers/telegram/loginwithTG.dart';
import 'package:mapp_ms24/models/LocalStorageModel.dart';
import '../../../controllers/internal/authController/AuthController.dart';
import '../../../controllers/utils/widget.dart';

class Loginscreen extends StatefulWidget {
  const Loginscreen({super.key});

  @override
  State<Loginscreen> createState() => _LoginscreenState();
}

class _LoginscreenState extends State<Loginscreen> {
  final LoginwithTG logth = Get.find<LoginwithTG>();
  final authController authCtr = Get.find<authController>();
  final CenterController centerCon = Get.find<CenterController>();
  final wealthrController = Get.find<WealthController>();
  final leaveAndAppController = Get.find<LeaveAndAppController>();
  final notiCTRL = Get.find<NotiController>();
  final profileController = Get.put(ProfileController());
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  bool _isPasswordVisible = false;
  final box = GetStorage();
  late FToast fToast;
  bool policy = false;

  @override
  void initState() {
    super.initState();
    fToast = FToast();
    fToast.init(context);
  }

  Future<void> _handleLogin() async {
    await box.write(LocalStorage.usernameLogin, _usernameController.text);
    await box.write(LocalStorage.passwordLogin, _passwordController.text);
    Get.offAllNamed('introanimate');
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
        body: Stack(
          children: [
            Container(
              height: double.infinity,
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Theme.of(context).colorScheme.primary,
                    Theme.of(context).colorScheme.onPrimary,
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
            ),
            SingleChildScrollView(
              child: SizedBox(
                height: MediaQuery.of(context).size.height,
                width: double.infinity,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        ms24Sign(),
                      ],
                    ),
                    SizedBox(height: 32.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Expanded(
                          flex: 0,
                          child: loginForm(),
                        ),
                      ],
                    ),
                    SizedBox(height: 32.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Expanded(
                          flex: 0,
                          child: loginButton(),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget ms24Sign() {
    bool isDarkTheme = Theme.of(context).brightness == Brightness.dark;
    String logo = isDarkTheme ? 'assets/Logo.png' : 'assets/Logo.png';
    return Stack(
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              alignment: Alignment.center,
              height: 70.h,
              width: 180.w,
              child: Image.asset(logo),
            ),
          ],
        ),
      ],
    );
  }

  Widget loginForm() {
    return Stack(
      children: [
        Container(
          width: MediaQuery.of(context).size.width,
          padding: EdgeInsets.symmetric(horizontal: 24.w),
          child: SingleChildScrollView(
            child: SizedBox(
              width: Get.width,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(height: 12.h),
                  TextField(
                    cursorColor: Colors.black,
                    controller: _usernameController,
                    textInputAction: TextInputAction.next,
                    decoration: InputDecoration(
                      enabledBorder: UnderlineInputBorder(
                        borderSide: BorderSide(
                            color: Theme.of(context).colorScheme.onBackground),
                      ),
                      focusedBorder: UnderlineInputBorder(
                        borderSide: BorderSide(
                            color: Theme.of(context).colorScheme.onBackground),
                      ),
                      hintText: 'fild_username'.tr,
                      hintStyle: TextStyle(
                        fontFamily: 'SukhumvitSet-Text',
                        fontSize: 16.sp,
                        color: Theme.of(context).colorScheme.scrim,
                        fontWeight: FontWeight.w400,
                      ),
                      contentPadding: EdgeInsets.symmetric(vertical: 16.h),
                      alignLabelWithHint: true,
                    ),
                    style: TextStyle(
                      fontFamily: 'SukhumvitSet-Text',
                      fontSize: 16.sp,
                      color: Theme.of(context).colorScheme.scrim,
                      fontWeight: FontWeight.w400,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 12.h),
                  TextField(
                    cursorColor: Colors.black,
                    controller: _passwordController,
                    obscureText: !_isPasswordVisible,
                    onSubmitted: (_) {
                      _handleLogin();
                    },
                    decoration: InputDecoration(
                      enabledBorder: UnderlineInputBorder(
                        borderSide: BorderSide(
                            color: Theme.of(context).colorScheme.onBackground),
                      ),
                      focusedBorder: UnderlineInputBorder(
                        borderSide: BorderSide(
                            color: Theme.of(context).colorScheme.onBackground),
                      ),
                      hintText: 'fild_password'.tr,
                      hintStyle: TextStyle(
                        fontFamily: 'SukhumvitSet-Text',
                        fontSize: 16.sp,
                        color: Theme.of(context).colorScheme.scrim,
                        fontWeight: FontWeight.w400,
                      ),
                      contentPadding: EdgeInsets.only(left: 50.h, top: 16.h),
                      alignLabelWithHint: true,
                      suffixIcon: GestureDetector(
                        onTap: () {
                          setState(() {
                            _isPasswordVisible = !_isPasswordVisible;
                          });
                        },
                        child: Image.asset(
                            Get.isDarkMode
                                ? 'assets/D_View_alt_fill.png'
                                : 'assets/B_View_alt_fill.png',
                            scale: 4.5),
                      ),
                    ),
                    style: TextStyle(
                      fontFamily: 'SukhumvitSet-Text',
                      fontSize: 16.sp,
                      color: Theme.of(context).colorScheme.scrim,
                      fontWeight: FontWeight.w400,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget loginButton() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          InkWell(
            onTap: () {
              _handleLogin();
            },
            child: Container(
              height: 52.h,
              width: 353.w,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.all(Radius.circular(10)),
                gradient: LinearGradient(
                  colors: [
                    Theme.of(context).colorScheme.secondaryContainer,
                    Theme.of(context).colorScheme.secondaryContainer,
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
              child: Center(
                child: AppWidget.normalText(
                  context,
                  'ui_login'.tr,
                  16.sp,
                  Theme.of(context).colorScheme.scrim,
                  FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
