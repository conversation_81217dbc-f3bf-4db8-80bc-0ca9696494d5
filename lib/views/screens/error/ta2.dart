import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

Widget buildErrorTApopup(context) {
  return Column(
    children: [
      ElevatedButton(
        onPressed: () {
          // เรียกใช้ฟังก์ชันเมื่อปุ่มถูกกด
          _showErrorDialog(context);
        },
        child: Text('Show Error Dialog'),
      ),
    ],
  );
}

Future _showErrorDialog(context) {
 return showDialog(
    context: context, // context คือ BuildContext ที่ได้รับจาก widget ที่กำลัง build
    builder: (BuildContext context) {
      return AlertDialog(
        title: Text('Error'),
        content: Container(
          height: 100.h,
          width: 200.w,
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 10.0),
                child: Container(
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.all(Radius.circular(20)),
                      shape: BoxShape.rectangle,
                      border: Border.all(color: Colors.black.withOpacity(0.5),width: 1.0,)
                  ),
                  child: TextButton(
                    onPressed: () {

                      Navigator.of(context).pop();
                    },
                    child: Text('ลงเวลาเช้า'),
                  ),
                ),
              ),

              Padding(
                padding: const EdgeInsets.symmetric(vertical: 10.0),
                child: Container(
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.all(Radius.circular(20)),
                      shape: BoxShape.rectangle,
                      border: Border.all(color: Colors.black.withOpacity(0.5),width: 1.0,)
                  ),
                  child: TextButton(
                    onPressed: () {
                      // ปิด AlertDialog เมื่อปุ่มถูกกด
                      Navigator.of(context).pop();
                    },
                    child: Text('ลงเวลาเย็น'),
                  ),
                ),
              ),
            ],
          ),
        ),

      );
    },
  );
}
