import 'dart:js_interop';
import 'dart:js_util';
import 'dart:convert';
import 'dart:typed_data';

import 'package:get_storage/get_storage.dart';
import 'package:mapp_ms24/models/LocalStorageModel.dart';

@JS()
external JSObject get navigator;

@JS()
external JSObject get window;

class WebAuthnService {
  static bool isWebAuthnSupported() {
    try {
      final hasCredentials = hasProperty(navigator, 'credentials');

      if (!hasCredentials) return false;

      final credentials = getProperty(navigator, 'credentials');
      final hasCreate = hasProperty(credentials, 'create');

      return hasCredentials && hasCreate;
    } catch (e) {
      return false;
    }
  }

  static String _getCurrentDomain() {
    try {
      final location = getProperty(window, 'location');
      final hostname = getProperty(location, 'hostname') as String;

      // For localhost, return as-is
      if (hostname == 'localhost' || hostname == '127.0.0.1') {
        return hostname;
      }

      // For other domains, return the hostname without subdomain if it's a subdomain
      // This ensures compatibility with WebAuthn requirements
      return hostname;
    } catch (e) {
      return 'localhost'; // fallback
    }
  }

  static Future<Map<String, dynamic>> registerCredential() async {
    try {
      if (!isWebAuthnSupported()) {
        throw Exception('WebAuthn is not supported');
      }

      final challenge = _generateChallenge();
      final userId = _generateUserId();

      // Get the actual username from storage
      final box = GetStorage();
      final username =
          box.read(LocalStorage.usernameLogin) ?? '<EMAIL>';

      final options = {
        'publicKey': {
          'challenge': _uint8ListToArrayBuffer(challenge),
          'rp': {
            'name': 'MS24 Beta',
            'id': _getCurrentDomain(), // Use current domain dynamically
          },
          'user': {
            'id': _uint8ListToArrayBuffer(userId),
            'name': username,
            'displayName': username,
          },
          'pubKeyCredParams': [
            {'alg': -7, 'type': 'public-key'}, // ES256
            {'alg': -257, 'type': 'public-key'}, // RS256
          ],
          'authenticatorSelection': {
            'authenticatorAttachment': 'platform',
            'userVerification': 'required',
            'requireResidentKey': false,
          },
          'timeout': 60000,
          'attestation': 'none',
        },
      };

      final jsOptions = jsify(options);
      final credentials = getProperty(navigator, 'credentials');
      final credentialPromise = callMethod(credentials, 'create', [jsOptions]);
      final credential = await promiseToFuture(credentialPromise);
      if (credential == null) {
        throw Exception('Failed to create credential');
      }
      final credentialId = getProperty(credential, 'id') as String;
      return {
        'success': true,
        'credentialId': credentialId,
        'rawId': _arrayBufferToBase64Url(getProperty(credential, 'rawId')),
        'type': getProperty(credential, 'type'),
        'challenge': _uint8ListToBase64Url(challenge),
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  static Future<Map<String, dynamic>> authenticateCredential(
    String credentialId,
  ) async {
    try {
      if (!isWebAuthnSupported()) {
        throw Exception('WebAuthn is not supported');
      }
      final challenge = _generateChallenge();
      final options = {
        'publicKey': {
          'challenge': _uint8ListToArrayBuffer(challenge),
          'allowCredentials': [
            {
              'id': _base64UrlToArrayBuffer(credentialId),
              'type': 'public-key',
              'transports': ['internal'],
            },
          ],
          'timeout': 60000,
          'userVerification': 'required',
        },
      };
      final jsOptions = jsify(options);
      final credentials = getProperty(navigator, 'credentials');
      final credentialPromise = callMethod(credentials, 'get', [jsOptions]);
      final credential = await promiseToFuture(credentialPromise);
      if (credential == null) {
        throw Exception('Authentication failed');
      }
      return {
        'success': true,
        'credentialId': getProperty(credential, 'id'),
        'type': getProperty(credential, 'type'),
        'challenge': _uint8ListToBase64Url(challenge),
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  static Uint8List _generateChallenge() {
    final bytes = Uint8List(32);
    for (int i = 0; i < 32; i++) {
      bytes[i] = (DateTime.now().microsecondsSinceEpoch + i) % 256;
    }
    return bytes;
  }

  static Uint8List _generateUserId() {
    final bytes = Uint8List(16);
    for (int i = 0; i < 16; i++) {
      bytes[i] = (DateTime.now().microsecondsSinceEpoch + i * 17) % 256;
    }
    return bytes;
  }

  static dynamic _uint8ListToArrayBuffer(Uint8List bytes) {
    try {
      final buffer =
          callConstructor(getProperty(window, 'ArrayBuffer'), [bytes.length]);
      final view = callConstructor(getProperty(window, 'Uint8Array'), [buffer]);

      for (int i = 0; i < bytes.length; i++) {
        setProperty(view, i, bytes[i]);
      }

      return buffer;
    } catch (e) {
      throw Exception('Failed to convert bytes to ArrayBuffer: $e');
    }
  }

  static String _arrayBufferToBase64Url(dynamic arrayBuffer) {
    try {
      final view = callConstructor(getProperty(window, 'Uint8Array'), [
        arrayBuffer,
      ]);
      final length = getProperty(view, 'length') as int;
      final bytes = Uint8List(length);

      for (int i = 0; i < length; i++) {
        bytes[i] = getProperty(view, i) as int;
      }

      return _uint8ListToBase64Url(bytes);
    } catch (e) {
      throw Exception('Failed to convert ArrayBuffer to base64url: $e');
    }
  }

  static dynamic _base64UrlToArrayBuffer(String base64Url) {
    try {
      final bytes = _base64UrlToUint8List(base64Url);
      return _uint8ListToArrayBuffer(bytes);
    } catch (e) {
      throw Exception('Failed to convert base64url to ArrayBuffer: $e');
    }
  }

  static String _uint8ListToBase64Url(Uint8List bytes) {
    String base64 = base64Encode(bytes);
    base64 = base64.replaceAll('+', '-');
    base64 = base64.replaceAll('/', '_');
    base64 = base64.replaceAll('=', '');
    return base64;
  }

  static Uint8List _base64UrlToUint8List(String base64Url) {
    String base64 = base64Url.replaceAll('-', '+');
    base64 = base64.replaceAll('_', '/');
    switch (base64.length % 4) {
      case 2:
        base64 += '==';
        break;
      case 3:
        base64 += '=';
        break;
    }

    try {
      return base64Decode(base64);
    } catch (e) {
      throw FormatException('Invalid base64url string: $base64Url');
    }
  }
}
