import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

import '../models/MR_model/MR.dart';
import '../models/MR_model/recommend_MR.dart';

DocumentReference notification =
    FirebaseFirestore.instance.collection('mobileapp').doc('notification');
DocumentReference version =
    FirebaseFirestore.instance.collection('appversion').doc('Prachakij');
CollectionReference repairStatus =
    FirebaseFirestore.instance.collection('repairStatus');
CollectionReference rankMR = FirebaseFirestore.instance.collection('rankMR');
CollectionReference recommendMR = FirebaseFirestore.instance.collection('MR');
CollectionReference config = FirebaseFirestore.instance.collection('config');
CollectionReference poi = FirebaseFirestore.instance.collection('poi');

class FireStore {
  static getPictureURL() async {
    QuerySnapshot snapshot;
    snapshot = await config.where('URL').get();
    List<Map> arr = [];
    for (QueryDocumentSnapshot task in snapshot.docs) {
      dynamic thisTask = task.data();
      thisTask['id'] = task.id;
      arr.add(thisTask);
    }
    Map<String, dynamic> data = {
      'status': true,
      'data': arr,
    };
    return data["data"];
  }

  static getMerchantID() async {
    QuerySnapshot snapshot;
    snapshot = await config.where('minilike').get();
    List<Map> arr = [];
    for (QueryDocumentSnapshot task in snapshot.docs) {
      dynamic thisTask = task.data();
      thisTask['id'] = task.id;
      arr.add(thisTask);
    }
    Map<String, dynamic> data = {
      'status': true,
      'data': arr,
    };
    return data["data"];
  }

  static getInfoLikePoint() async {
    var collectionData = await config.doc('miniLike').get();
    var rawData = collectionData.data();
    return rawData;
  }

  static getRankMR(String rank) async {
    QuerySnapshot snapshot;
    if (rank != "") {
      snapshot = await rankMR.where('rank', isEqualTo: rank).get();
    } else {
      return null;
    }
    List<Map> arr = [];
    for (QueryDocumentSnapshot task in snapshot.docs) {
      dynamic thisTask = task.data();
      thisTask['id'] = task.id;
      arr.add(thisTask);
    }
    Map<String, dynamic> data = {
      'status': true,
      'data': arr,
    };
    ResponseRankMRFB resultRank = ResponseRankMRFB.fromJson(data['data'][0]);
    return resultRank;
  }

  static getRecommend(String recommend) async {
    QuerySnapshot snapshot;
    if (recommend != "") {
      snapshot =
          await recommendMR.where("recommend", isEqualTo: recommend).get();
    } else {
      return null;
    }
    List<Map> arr = [];
    for (QueryDocumentSnapshot task in snapshot.docs) {
      dynamic thisTask = task.data();
      thisTask['id'] = task.id;
      arr.add(thisTask);
    }
    Map<String, dynamic> data = {
      'status': 200,
      'data': arr,
    };
    ResponseRecommend responseRecommend =
        ResponseRecommend.fromJson(data['data'][0]);
    return responseRecommend;
  }

  static getNotificationRead(String mobile) {
    return notification
        .collection(mobile)
        .where('read', isEqualTo: false)
        .get()
        .then((value) {
      var arr = [];
      for (var add in value.docs) {
        Map<String, dynamic> res = add.data();
        res.addAll({"docId": add.id.toString()});
        arr.add(res);
      }

      Map<String, dynamic> data = {
        'status': true,
        'data': arr,
      };

      return data;
    }).catchError((error) {
      if (kDebugMode) {}
      Map<String, dynamic> data = {
        'status': false,
        'data': error,
      };
      return data;
    });
  }

  static getNotificationPayment(String mobile, String partnerTxnUid) {
    return notification
        .collection(mobile)
        .where('type', isEqualTo: 'notificationQR')
        .where('partnerTxnUid', isEqualTo: partnerTxnUid)
        .get()
        .then((value) {
      var arr = [];
      for (var add in value.docs) {
        Map<String, dynamic> res = add.data();
        res.addAll({"docId": add.id.toString()});
        arr.add(res);
      }

      Map<String, dynamic> data = {
        'status': true,
        'data': arr,
      };

      return data;
    }).catchError((error) {
      if (kDebugMode) {}
      Map<String, dynamic> data = {
        'status': false,
        'data': error,
      };
      return data;
    });
  }

  static getNotification(String mobile) {
    return notification
        .collection(mobile)
        .orderBy('create', descending: true)
        .get()
        .then((value) {
      var arr = [];
      for (var add in value.docs) {
        Map<String, dynamic> res = add.data();
        res.addAll({"docId": add.id.toString()});
        arr.add(res);
      }

      Map<String, dynamic> data = {
        'status': true,
        'data': arr,
      };

      return data;
    }).catchError((error) {
      if (kDebugMode) {}
      Map<String, dynamic> data = {
        'status': false,
        'data': error,
      };
      return data;
    });
  }

  static updateReadNotification(String mobile, String docId) {
    Map<String, dynamic> data = {
      'read': true,
    };
    return notification
        .collection(mobile)
        .doc(docId)
        .update(data)
        .then((value) {
      return true;
    }).catchError((error) {
      return false;
    });
  }

  static deleteAllNotification(String mobile) {
    return notification.collection(mobile).get().then((value) {
      for (var item in value.docs) {
        notification.collection(mobile).doc(item.id.toString()).delete();
      }
      return true;
    }).catchError((error) {
      return false;
    });
  }

  static deleteByDocIdNotification(String mobile, String docId) {
    return notification.collection(mobile).doc(docId).delete().then((value) {
      return true;
    }).catchError((error) {
      return false;
    });
  }

  static getCurrentVersion() async {
    var returnVersion = await version.get();
    return returnVersion.data();
  }

  static getIDpoi() async {
    var collectionData = await poi.doc('gafrAW5wZlKywejkJczX').get();
    var rawData = collectionData.data();
    return rawData;
  }
}
