// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:get/get.dart';
// import 'package:mapp_ms24/controllers/utils/color.dart';
// import 'package:mapp_ms24/controllers/utils/theme_models.dart';
//
// ThemeController themer = Get.put(ThemeController());
//
// ThemeData configTheme() {
//   if(themer.themeMode == true){
//     return ThemeData(
//       primaryColor: ColorSystem.primary,
//       accentColor: ColorSystem.accent,
//       scaffoldBackgroundColor: ColorSystem.background,
//       fontFamily: 'Kanit',
//       textTheme: TextTheme(
//         headline1: TextStyle(
//           fontSize: 96.sp,
//           fontWeight: FontWeight.w300,
//           letterSpacing: -1.5,
//         ),
//         headline2: TextStyle(
//           fontSize: 60.sp,
//           fontWeight: FontWeight.w300,
//           letterSpacing: -0.5,
//         ),
//         headline3: TextStyle(
//           fontSize: 48.sp,
//           fontWeight: FontWeight.w400,
//         ),
//         headline4: TextStyle(
//           fontSize: 34.sp,
//           fontWeight: FontWeight.w400,
//           letterSpacing: 0.25,
//         ),
//         headline5: TextStyle(
//           fontSize: 24.sp,
//           fontWeight: FontWeight.w400,
//         ),
//         headline6: TextStyle(
//           fontSize: 20.sp,
//           fontWeight: FontWeight.w500,
//           letterSpacing: 0.15,
//         ),
//         subtitle1: TextStyle(
//           fontSize: 16.sp,
//           fontWeight: FontWeight.w400,
//           letterSpacing: 0.15,
//         ),
//         subtitle2: TextStyle(
//           fontSize: 14.sp,
//           fontWeight: FontWeight.w500,
//           letterSpacing: 0.1,
//         ),
//         bodyText1: TextStyle(
//           fontSize: 16.sp,
//           fontWeight: FontWeight.w400,
//           letterSpacing: 0.5,
//         ),
//         bodyText2: TextStyle(
//           fontSize: 14.sp,
//           fontWeight: FontWeight.w400,
//           letterSpacing: 0.25,
//         ),
//         button: TextStyle(
//           fontSize: 14.sp,
//           fontWeight: FontWeight.w500,
//           letterSpacing: 1.25,
//         ),
//         caption: TextStyle(
//           fontSize: 12.sp,
//           fontWeight: FontWeight.w400,
//           letterSpacing: 0.4,
//         ),
//         overline: TextStyle(
//           fontSize: 10.sp,
//           fontWeight: FontWeight.w400,
//           letterSpacing: 1.5,
//         ),
//       ),
//     );
//   }
// }
