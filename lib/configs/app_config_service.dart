import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'app_config.dart';

class AppConfigService extends GetxService {
  final BuildContext context;
  String endpointAPI = '';
  Environment environment = Environment.dev;
  String appTitle = '';

  AppConfigService({
    required this.context,
  });

  @override
  Future<void> onInit() async {
    super.onInit();

    await Future.delayed(Duration.zero);

    if (kIsWeb) {
      endpointAPI = 'https://agilesoftgroup.com/MS24_uat';
      appTitle = 'MS24 Prod Mode';
      environment = Environment.prod;
    } else {
      final appConfig = AppConfig.of(context);
      endpointAPI = appConfig.endpointAPI;
      environment = appConfig.environment;
      appTitle = appConfig.appTitle;
    }

    await Future.delayed(Duration(milliseconds: 500));
    loadOtherConfigs();
  }

  void loadOtherConfigs() {
    final appConfig = AppConfig.of(context);
    if (appConfig != null) {
    } else {
      print("Error: AppConfig is null in loadOtherConfigs");
    }
  }
}
