import 'package:flutter/material.dart';

// Black and shades
// -- thick
const Color blackColor = Color(0xFF000000);
const Color mainBlack = Color(0xFF0C0C0C);
const Color mainDarkGray = Color(0xFF3D3D3D);
const Color mainGray = Color(0xFF868686);
// -- with white shade
const Color darkerGray = Color(0xFF5F5F5F);
const Color darkGray = Color(0xFF9E9E9E);
const Color gray = Color(0xFFBDBDBD);
const Color lightGray = Color(0xFFCACACA);
const Color lighterGray = Color(0xFFDEDEDE);
const Color lightestGray = Color(0xFFF4F4F4);
const Color mainWhite = Color(0xFFFFFFFF);
const Color whiteColor = Color(0xFFFFFFFF);

// Success - colors
const Color darkerGreen = Color(0xFF1A642D);
const Color darkGreen = Color(0xFF2A9F47);
const Color green = Color(0xFF34C759);
const Color lightGreen = Color(0xFF5DD27A);
const Color lighterGreen = Color(0xFF9AE3AC);
const Color lightestGreen = Color(0xFFF5FCF7);
// Error - colors
const Color darkerRed = Color(0xFF801E18);
const Color darkRed = Color(0xFFCC2F26);
const Color red = Color(0xFFFF3B30);
const Color lightRed = Color(0xFFFF6259);
const Color lighterRed = Color(0xFFFF9D98);
const Color redBgColor = Color(0xFFFFEBEA);
// Warning - colors
const Color darkerYellow = Color(0xFFE6B800);
const Color yellow = Color(0xFFFFCC00);
const Color lightestYellow = Color(0xFFFFFCF2);

// widget colors
const Color dividerColor = Color(0x265D5BEB);
const Color lineColor = Color(0xFFEEEEEE);
const Color transparentColor = Colors.transparent;
const Color shimmerBaseColor = Color(0xFFE0E0E0);
const Color shimmerHighlightColor = Color(0xFFF5F5F5);

// Primary colors
const Color darkerPrimaryColor = Color(0xFF46007F);
const Color darkPrimaryColor = Color(0xFF6F00CB);
const Color primaryColor = Color(0xFF8B00FE);
const Color lightPrimaryColor = Color(0xFFA233FE);
const Color lighterPrimaryColor = Color(0xFFC580FF);
const Color lightestPrimaryColor = Color(0xFFFAF3FF);
const Color lighterPrimaryColor2 = Color(0xFF9685C8);

// Secondary colors
const Color secondaryColor = Color(0xFFFAF3FF);
const Color darkSecondaryColor = Color(0xFFF2E0FF);
const Color darkerSecondaryColor = Color(0xFFEED7FF);

// logo colors
const Color facebookLogo = Color(0xFFF4F9FF);
const Color twitterLogo = Color(0xFFF4F9FF);
const Color appleLogo = Color(0xFFF5F5F5);
const Color googleLogo = Color(0xFFFFF8F8);
const Color lineLogo = Color(0xFFF3FFF8);

const Color darkBlueColor = Color(0xFF0F1330);
const Color darkerBlueColor = Color(0xFF0D1336);
const Color blueGrayColor = Color(0xFF737580);
const Color lightBlueColor = Color(0xFF6352B0);
const Color alertBlue = Color(0xFF1671D9);