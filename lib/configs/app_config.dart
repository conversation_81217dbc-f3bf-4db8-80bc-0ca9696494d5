import 'package:flutter/material.dart';

// 1
enum Environment { dev, prod }

// 2
class AppConfig extends InheritedWidget {
  // 3
  final Environment environment;
  final String appTitle;
  late String endpointAPI;
  // late String walletConfigCollection;
  // late String nativWallet;
  // late String swapUrl;
  // late String swapxapikey;
  // late String configPathFirebase;

  // 4
  AppConfig({
    Key? key,
    required Widget child,
    required this.environment,
    required this.appTitle,
    required this.endpointAPI,
    // required this.walletConfigCollection,
    // required this.nativWallet,
    // required this.swapUrl,
    // required this.swapxapikey,
    // required this.configPathFirebase,
  }) : super(
          key: key,
          child: child,
        );

  // 5
  static AppConfig of(BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<AppConfig>()!;
  }

  // 6
  @override
  bool updateShouldNotify(covariant InheritedWidget oldWidget) => false;
}
