import 'dart:io';

import 'package:firebase_core/firebase_core.dart';

import 'package:flutter/foundation.dart';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:mapp_ms24/configs/theme.dart';
import 'package:mapp_ms24/controllers/firebaseApi.dart';
import 'package:mapp_ms24/controllers/utils/theme_models.dart';
import 'package:mapp_ms24/views/screens/authpages/login.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/Benefit/BenefitScreen.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/Benefit/ExBenForm.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/Benefit/ExpandBenefir.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/Benefit/HealthBenefit.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/Benefit/HealthForm.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/Benefit/InsuranceStatus.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/Benefit/SForm.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/Benefit/Standart.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/Cew/SaveCew.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/Cew/cew.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/Drawer/2FaDetails.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/Drawer/ChangeTheme.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/Drawer/Changelanguage.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/Drawer/EditUser.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/Drawer/ResetPassword.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/Drawer/Safety.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/Drawer/Suggestions.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/Drawer/UserData.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/Drawer/regisEsign.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/FinanceDoc/DetailDoc.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/FinanceDoc/FinaceDoc.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/FinanceDoc/NDAdoc/introNDA.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/FinanceDoc/newdoc.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/Leave/LeaveScreen.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/NotiScreen/Notification.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/QRscan.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/TA/TaScreen.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/Verify.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/WifiDoor/DoorScanScreen.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/Workissues/WorkissuesScreen.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/camera/camera.dart';
import 'package:mapp_ms24/views/screens/home/<USER>/pinlock.dart';
import 'package:mapp_ms24/views/screens/home/<USER>';
import 'package:mapp_ms24/views/screens/home/<USER>';
import 'package:mapp_ms24/views/screens/intro/introanimate.dart';
import 'package:mapp_ms24/views/screens/spinwheel/spin_reward_screen.dart';
import 'package:mapp_ms24/views/screens/spinwheel/spinwheel.dart';
import 'package:mapp_ms24/views/screens/web_only.dart';

import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_web/webview_flutter_web.dart';

import 'appblind.dart';
import 'configs/app_config_service.dart';
import 'configs/color_system.dart';
import 'configs/firebase/firebase_option.dart';
import 'controllers/annoucer_controller.dart';
import 'controllers/internal/notification_controller.dart';
import 'controllers/utils/custom_translations.dart';
import 'controllers/utils/language_selection_controller.dart';
import 'controllers/utils/url.dart';
import 'views/screens/home/<USER>';

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}

int initScreen = 0;

Future<void> initializeGetStorage() async {
  await GetStorage.init(); // เริ่มต้น GetStorage
  GetStorage box = GetStorage();

  // อ่านค่า initScreen หากไม่พบค่าจะใช้ค่าเริ่มต้น 0
  int initScreen = box.read('initScreen') ?? 0;


  if(initScreen == null){
    initScreen = 0;
  }
  // แสดงผลค่าของ initScreen

  // ตั้งค่าใหม่ให้กับ initScreen ใน GetStorage
  await box.write('initScreen', 1);

  // หากต้องการใช้งานค่า initScreen ต่อไปก็สามารถใช้ค่า 1 ได้
}

Future<void> main() async {
  // Ensure Flutter bindings are initialized first
  print('WidgetsFlutterBinding.ensureInitialized');
    WidgetsFlutterBinding.ensureInitialized();

  // Initialize GetStorage
  print('initializeGetStorage');
  await initializeGetStorage();
  // Set system UI overlays and preferred orientation
  print('SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.light);');
   SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.light);
  print('SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);');
   SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);


  // Set custom HTTP overrides
  print('HttpOverrides.global');
    HttpOverrides.global = MyHttpOverrides();

  // Initialize Firebase
  print('Firebase.initializeApp');
  try {
    await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
    final firebaseCtrl = Get.put(FirebaseController());
    await firebaseCtrl.init();
  } catch (e) {
    print('Error initializing Firebase: $e');
  }

  // Retrieve or set the initial screen state

 // if already shown -> 1 else 0

  // Initialize Sentry error tracking
  // await SentryFlutter.init(
  //       (options) {
  //     options.dsn = 'https://<EMAIL>/4508068693475328';
  //     options.tracesSampleRate = 0.1;
  //     options.profilesSampleRate = 0.1;
  //   },
  // );
  FlutterError.onError = (FlutterErrorDetails details) {
    // Handle errors globally
  };
  // Run the app normally


    runApp((const MyApp()));

}


class MyApp extends StatefulWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  DateTime? _backgroundTime;



  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);


  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    print('didChangeAppLifecycleState');

    if (state == AppLifecycleState.paused) {
      print('Background');
      // App is in the background
      if (_backgroundTime != null) {
        final timeInBackground = DateTime.now().difference(_backgroundTime!);
        if (timeInBackground.inSeconds > 60) {
          Get.offAllNamed('PinlockScreen');
        }
      }
    } else if (state == AppLifecycleState.resumed) {
      // App is in the foreground

      _backgroundTime = DateTime.now();
    }
  }

  void configEasyLoading() {
    // define EasyLoading instance
    EasyLoading.instance
      ..loadingStyle = EasyLoadingStyle.light
      ..dismissOnTap = false
      ..userInteractions = false
      ..indicatorWidget = const CircularProgressIndicator(
        color: primaryColor,
        strokeWidth: 2,
      );
  }

  @override
  Widget build(BuildContext context) {

      AppConfigService appConfigService = Get.put(
          AppConfigService(context: context));
      appConfigService.onInit();
      AppUrl appurl = Get.put(AppUrl());
      // ignore: unused_local_variable
      NotifyController notifyController = Get.put(
        NotifyController(),
        permanent: true,
      );

      Annoucer annoucerController = Get.put(Annoucer());
      annoucerController.setLanguageByLocale(context);

      LanguageSelectionController languageSelectionController =
      Get.put(LanguageSelectionController());


      appurl.onInit();
      final ThemeController themeController = Get.put(ThemeController());
      if (kIsWeb) {
        print('telegramIframe');
        WebViewPlatform.instance = WebWebViewPlatform();
        registerIframe('onepage', 'https://ags.im/ZvdL0A'); // ลงทะเบียน URL
      }

      // configEasyLoading();
    return Obx(() => ScreenUtilInit(
      designSize: const Size(393, 852),
      minTextAdapt: true,
      splitScreenMode: true,
      child: GetMaterialApp(
        debugShowCheckedModeBanner: false,
        title: 'MS24',
        translations: CustomTranslations(),
        locale: languageSelectionController.currentLocale,
        fallbackLocale: const Locale('en'),
        theme: lightTheme,
        darkTheme: darkTheme,
        themeMode: themeController.selectedTheme.value == 1
            ? ThemeMode.dark
            : themeController.selectedTheme.value == 2
            ? ThemeMode.light
            : WidgetsBinding.instance.platformDispatcher
            .platformBrightness ==
            Brightness.dark
            ? ThemeMode.dark
            : ThemeMode.light,
        initialBinding: AppBindings(),
        //  builder: EasyLoading.init(),
        builder: EasyLoading.init(
          builder: (context, child) {
            final mediaQueryData = MediaQuery.of(context);

            return MediaQuery(
              data: mediaQueryData.copyWith(textScaleFactor: 1.0),
              child: child!,
            );
          },
        ),

        initialRoute: 'introanimate',
        routes: {
          'home': (context) => const HomeScreen(),
          'login': (context) => const Loginscreen(),
          'introanimate': (context) => const introanimate(),
          'DoorScanScreen': (context) => const DoorScanScreen(),
          'SaveCew': (context) => const SaveCew2(),
          'FinaceScreen': (context) =>  AllSelectDoc(),
          // 'DetailDocScreen': (context) => const DetailDocScreen(),
          'NotiScreen': (context) => const NotiScreen(0),
          'LeaveScreen': (context) => const LeaveScreen(),
          // 'Tascreen': (context) => const Tascreen(),
          'BenefitScreen': (context) => const BenefitScreen(),
          'StandBenefit': (context) => const StandBenefit(),
          'ExpandBenefitScreen': (context) => const ExpandBenefitScreen(),
          'HealthBenefit': (context) => const HealthBenefit(),
          'StandartForm': (context) => const StandartForm(),
          'ExBenifitForm': (context) => const ExBenifitForm(),
          'HealthForm': (context) => const HealthForm(),
          'WorkIssuesScreen': (context) => const WorkIssuesScreen(),
          'IntroNDA': (context) => const IntroNDAScreen(),
          'UserDataScreen': (context) => const UserDataScreen(),
          'EditUserScreen': (context) => const EditUserScreen(),
          'SafetyScreen': (context) => const SafetyScreen(),
          'TwoFAscreem': (context) => const TwoFAscreem(),
          'ResetPassword': (context) => const ResetPassword(),
          'ChangelanguageScreen': (context) => const ChangelanguageScreen(),
          'SuggestionsScreen': (context) => const SuggestionsScreen(),
          'InsuranceStatusScreen': (context) => const InsuranceStatusScreen(),
          'QrScanScreen': (context) => const QrScanScreen(),
          'RegisEsign': (context) =>  regisEsign(),
          'WebViewPage': (context) =>  const WebViewPage(),
          'SpinWheel' : (context) => const SpinWheelScreen(),
          'SpinReward' : (context) => const SpinRewardScreen(),
          'CameraScreen' : (context) =>  CameraScreen(),
          'Soundpage' : (context) =>  SoundPage(),
          'CustomerFormPage' : (context) =>  CustomerFormPage(),
          'ChangeThemeScreen': (context) => ChangethemeScreen(),
          // 'AllSelectDoc': (context) =>  AllSelectDoc(),
          // 'onboard': (context) => const OnBoardingScreens(),
        },
      ),
    ));
  }
}
