class MemberId {
  MemberId({
    this.memberId,
  });

  String? memberId;

  factory MemberId.fromJson(Map<String, dynamic> json) => MemberId(
        memberId: json["memberID"],
      );

  Map<String, dynamic> toJson() => {
        "memberID": memberId,
      };
}

class PocketMember {
  PocketMember({
    this.id,
    this.documentStatus,
    this.memberId,
    this.merchantId,
    this.walletBalance,
    this.pocket,
  });

  String? id;
  bool? documentStatus;
  String? memberId;
  String? merchantId;
  double? walletBalance;
  List<Pocket>? pocket;

  factory PocketMember.fromJson(Map<String, dynamic> json) => PocketMember(
        id: json["id"],
        documentStatus: json["documentStatus"],
        memberId: json["memberID"],
        merchantId: json["merchantID"],
        walletBalance: json["walletBalance"],
        pocket:
            List<Pocket>.from(json["pocket"].map((x) => Pocket.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "documentStatus": documentStatus,
        "memberID": memberId,
        "merchantID": merchantId,
        "walletBalance": walletBalance,
        "pocket": List<dynamic>.from(pocket!.map((x) => x.toJson())),
      };
}

class Pocket {
  Pocket(
      {this.id,
      this.documentStatus,
      this.walletId,
      this.pocketType,
      this.pocketName,
      this.pocketBalance,
      this.merchantId});

  String? id;
  bool? documentStatus;
  String? walletId;
  String? pocketType;
  String? pocketName;
  double? pocketBalance;
  String? merchantId;

  factory Pocket.fromJson(Map<String, dynamic> json) => Pocket(
        id: json["id"],
        documentStatus: json["documentStatus"],
        walletId: json["walletID"],
        pocketType: json["pocketType"],
        pocketName: json["pocketName"],
        pocketBalance: json["pocketBalance"],
        merchantId: json["merchantID"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "documentStatus": documentStatus,
        "walletID": walletId,
        "pocketType": pocketType,
        "pocketName": pocketName,
        "pocketBalance": pocketBalance,
        "merchantId": merchantId,
      };
}

class TeamId {
  TeamId({
    this.teamId = '',
    this.ownerWalletId = '',
    this.ownerWalletName = '',
    this.ownerStatus = '',
  });

  String teamId;
  String ownerWalletId;
  String ownerWalletName;
  String ownerStatus;

  factory TeamId.fromJson(Map<String, dynamic> json) => TeamId(
        teamId: json["teamId"] ?? '',
        ownerWalletId: json["owner_wallet_id"] ?? '',
        ownerWalletName: json["owner_wallet_name"] ?? '',
        ownerStatus: json["owner_status"] ?? '',
      );

  Map<String, dynamic> toJson() => {
        "teamId": teamId,
        "owner_wallet_id": ownerWalletId,
        "owner_wallet_name": ownerWalletName,
        "owner_status": ownerStatus,
      };
}

class PocketTeam {
  PocketTeam(
      {this.team,
      this.walletId,
      this.pocketId,
      this.pocketType,
      this.pocketName,
      this.pocketBalance,
      this.merchantId,
      this.valuePoint = 0
      });

  String? team;
  String? walletId;
  String? pocketId;
  String? pocketType;
  String? pocketName;
  double? pocketBalance;
  String? merchantId;
  int valuePoint;

  factory PocketTeam.fromJson(Map<String, dynamic> json) => PocketTeam(
        team: json["team"],
        walletId: json["walletID"],
        pocketId: json["pocketID"],
        pocketType: json["pocketType"],
        pocketName: json["pocketName"],
        pocketBalance: json["pocketBalance"] ?? 0.0,
        merchantId: json["merchantID"],
        valuePoint: json["valuePoint"],
      );

  Map<String, dynamic> toJson() => {
        "team": team,
        "walletID": walletId,
        "pocketID": pocketId,
        "pocketType": pocketType,
        "pocketName": pocketName,
        "pocketBalance": pocketBalance,
        "merchantId": merchantId,
        "valuePoint": valuePoint,
      };
}
