import 'dart:convert';

// ignore: non_constant_identifier_names
List<MspTier> MspTierFromJson(String jsonStr) =>
    List<MspTier>.from(json.decode(jsonStr).map((x) {
      Map<String, dynamic> tierInfo = {};
      if (x['tierInfo'] is String) {
        String replacedJson = x['tierInfo'].replaceAll("'", '"');
        replacedJson = replacedJson.replaceAll('example', '"example"');
        tierInfo = jsonDecode(replacedJson);
      } else {
        tierInfo = x['tierInfo'];
      }
      return MspTier.fromJson(x, tierInfo);
    }));

class MspTier {
  String tier;
  int points;
  double earnRewardPercent;
  double referralPercent;
  double feePercent;

  MspTier(
      {required this.tier,
      required this.points,
      required this.earnRewardPercent,
      required this.feePercent,
      required this.referralPercent});

  factory MspTier.fromJson(
          Map<String, dynamic> jsonDecoded, Map<String, dynamic> tierInfo) =>
      MspTier(
        tier: jsonDecoded['name'],
        points: jsonDecoded['name'] == 'Bronze' ? 0 : jsonDecoded['lowThreshold'],
        earnRewardPercent: double.parse(
            ((tierInfo.containsKey('earnRewardMultiplier')
                        ? tierInfo['earnRewardMultiplier']
                        : 0) *
                    100)
                .toString()),
        referralPercent: double.parse(
            ((tierInfo.containsKey('referralRewardMultiplier')
                        ? tierInfo['referralRewardMultiplier']
                        : 0) *
                    100)
                .toString()),
        feePercent: double.parse(
            ((tierInfo.containsKey('withdrawFeeReductionMultiplier')
                        ? tierInfo['withdrawFeeReductionMultiplier']
                        : 0) *
                    100)
                .toString()),
      );
}