import 'dart:convert';

List<MspTransaction> loyaltyTransactionFromJson(String jsonStr) => List<MspTransaction>.from(
    json.decode(jsonStr).map((x) => MspTransaction.fromJson(x)));

class MspTransaction {
  String action;
  int amount;
  DateTime dateTime;

  MspTransaction({required this.action, required this.amount, required this.dateTime});

  factory MspTransaction.fromJson(Map<String, dynamic> jsonDecoded) => MspTransaction(
        action: jsonDecoded["actionName"],
        amount: jsonDecoded["pointChange"],
        dateTime: DateTime.fromMillisecondsSinceEpoch(jsonDecoded["timestamp"]),
      );
}