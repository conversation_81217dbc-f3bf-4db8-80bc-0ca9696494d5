class Income_Model {
  String salary = "0.00";
  var Incentive = "0.00";
  var IncomeReward = "0.00";
  String dateUpdateIncentive = "00/00/0000";

  Income_Model({
    required this.salary,
    required this.Incentive,
    required this.IncomeReward,
    required this.dateUpdateIncentive,
  });

  factory Income_Model.fromJson(Map<String, dynamic> json) => Income_Model(
      salary: json["salary"] ?? "0.00",
      Incentive: json["Incentive"] ?? "0.00",
      IncomeReward: json["IncomeReward"] ?? "0.00",
      dateUpdateIncentive: json["dateUpdateIncentive"] ?? 00/00/0000
  );

  Map<String, dynamic> toJson() => {
    "salary": salary,
    "Incentive": Incentive,
    "IncomeReward": IncomeReward,
    "dateUpdateIncentive": dateUpdateIncentive
  };
}