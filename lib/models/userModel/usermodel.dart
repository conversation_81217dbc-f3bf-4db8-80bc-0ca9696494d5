class Member {
  Member({
    required this.id,
  });

  String id;

  factory Member.fromJson(Map<String, dynamic> json) => Member(
    id: json["id"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
  };
}

class ResponseMember {
  ResponseMember({
    this.id,
    this.username,
    this.company_management,
    this.phone_like,
    this.telNumber,
    this.full_name_th,
    this.email,
    this.country,
    this.guarantee_money,
    this.money_first,
    this.deductions,
    this.dates_deductions,
    this.birthDay,
    this.guaranty_status,
    this.cuID,
    this.tel_country,
    this.personalID,
    this.role,
    this.welfare_firsthalf,
    this.welfare_secondhalf,
    this.job_position_id,
    this.interest,
    this.telegram_token,
    this.status,
    this.employment_type,
    this.tokenLine,
    this.nickname,
    this.name_en,
    this.type_NDA,
    this.welfare_daythai,
    this.welfare_dayrplc,
    this.welfare_country,
    this.password,
    this.purpose,
    this.purpose_p,
    this.Annual_Compass_p,
    this.Metrics_p,
    this.status_metrics_p,
    this.Accountability_p,
    this.Adhere,
    this.Abandon,
    this.searching,
    this.p_contract,
    this.name_th,
    this.surname_th,
    this.thprefix,
    this.full_name_en,
    this.datein,
    this.date_added,
    this.number_bank

});

  String? id;
  String? username;
  String? company_management;
  String? phone_like;
  String? telNumber;
  String? full_name_th;
  String? email;
  String? country;
  String? guarantee_money;
  String? money_first;
  String? deductions;
  String? dates_deductions;
  String? birthDay;
  String? guaranty_status;
  String? cuID;
  String? tel_country;
  String? personalID;
  String? role;
  String? welfare_firsthalf;
  String? welfare_secondhalf;
  String? job_position_id;
  String? interest;
  String? telegram_token;
  String? status;
  String? employment_type;
  String? tokenLine;
  String? nickname;
  String? name_en;
  String? type_NDA;
  String? welfare_daythai;
  String? welfare_dayrplc;
  String? welfare_country;
  String? password;
  String? purpose;
  String? purpose_p;
  String? Annual_Compass_p;
  String? Metrics_p;
  String? status_metrics_p;
  String? Accountability_p;
  String? Adhere;
  String? Abandon;
  String? searching;
  String? p_contract;
  String? name_th;
  String? surname_th;
  String? thprefix;
  String? full_name_en;
  String? datein;
  String? date_added;
  String? number_bank;

  factory ResponseMember.fromJson(Map<String, dynamic> json) => ResponseMember(
    id: json["id"] ?? "",
    username: json["userName"] ?? "",
    nickname: json["nickname"] ?? "",
    full_name_th: json["name_th"] + " " + json["surname_th"]?? "",
    full_name_en: json["name_en"] + " " + json["surname_en"]?? "",
    thprefix: json["thprefix"] ?? "",
    company_management: json["company_management"] ?? "",
    password: json["password"] ?? "",
    phone_like: json["phone_like"] ?? "",
    telNumber: json["telNumber"] ?? "",
    email: json["email1"] ?? "",
    country: json["country"] ?? "",
    guarantee_money: json["guarantee_money"].toString() ?? "",
    money_first: json["money_first"].toString() ?? "",
    deductions: json["deductions"].toString() ?? "",
    dates_deductions: json["dates_deductions"] ?? "",
    birthDay: json["birthDay"] ?? "",
    guaranty_status: json["guaranty_status"] ?? "",
    cuID: json["cuID"] ?? "",
    tel_country: json["tel_country"] ?? "",
    personalID: json["personalID"] ?? "",
    role: json["role"] ?? "",
    welfare_firsthalf: json["welfare_firsthalf"].toString() ?? "",
    welfare_secondhalf: json["welfare_secondhalf"].toString() ?? "",
    job_position_id: json["job_position_id"].toString() ?? "",
    interest: json["interest"] ?? "",
    telegram_token: json["telegram_token_1"] ?? "",
    status: json["status"] ?? "",
    employment_type: json["employment_type"] ?? "",
    tokenLine: json["tokenLine"] ?? "",
    name_en: json["name_en"] ?? "",
    type_NDA: json["type_NDA"] ?? "",
    welfare_daythai: json["welfare_daythai"].toString() ?? "",
    welfare_dayrplc: json["welfare_dayrplc"].toString() ?? "",
    welfare_country: json["welfare_country"] ?? "",
    purpose: json["purpose"] ?? "",
    purpose_p: json["purpose_p"] ?? "",
    Annual_Compass_p: json["Annual_Compass_p"] ?? "",
    Metrics_p: json["Metrics_p"] ?? "",
    status_metrics_p: json["status_metrics_p"] ?? "",
    Accountability_p: json["Accountability_p"] ?? "",
    Adhere: json["Adhere"] ?? "",
    Abandon: json["Abandon"] ?? "",
    searching: json["searching"] ?? "",
    p_contract: json["p_contract"] ?? "",
    datein: json["date_in"] ?? "",
    name_th: json["name_th"] ?? "",
    surname_th: json["surname_th"] ?? "",
    date_added: json["date_added"] ?? "",
    number_bank: json["Bank_Acct_Number"] ?? "",
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "username": username,
    "company_management": company_management,
    "phone_like": phone_like,
    "telNumber": telNumber,
    "full_name_th": full_name_th,
    "email": email,
    "country": country,
    "guarantee_money": guarantee_money,
    "money_first": money_first,
    "deductions": deductions,
    "dates_deductions": dates_deductions,
    "birthDay": birthDay,
    "guaranty_status": guaranty_status,
    "cuID": cuID,
    "tel_country": tel_country,
    "personalID": personalID,
    "role": role,
    "welfare_firsthalf": welfare_firsthalf,
    "welfare_secondhalf": welfare_secondhalf,
    "job_position_id": job_position_id,
    "interest": interest,
    "telegram_token": telegram_token,
    "status": status,
    "employment_type": employment_type,
    "tokenLine": tokenLine,
    "nickname": nickname,
    "name_en": name_en,
    "type_NDA": type_NDA,
    "welfare_daythai": welfare_daythai,
    "welfare_dayrplc": welfare_dayrplc,
    "welfare_country": welfare_country,
    "purpose": purpose,
    "purpose_p": purpose_p,
    "Annual_Compass_p": Annual_Compass_p,
    "Metrics_p": Metrics_p,
    "status_metrics_p": status_metrics_p,
    "Accountability_p": Accountability_p,
    "Adhere": Adhere,
    "Abandon": Abandon,
    "searching": searching,
    "p_contract": p_contract,
    "name_th": name_th,
    "surname_th": surname_th,
    "full_name_en": full_name_en,
    "thprefix": thprefix,
    "date_in": datein,
    "date_added": date_added,
    "numberbank": number_bank,
  };
}
//
// class ResponseProfileAndMR {
//   ResponseProfileAndMR({
//     this.id,
//     this.nickname,
//     this.fullname,
//     this.username,
//     this.password,
//     this.phoneFirebase,
//     this.firstname,
//     this.lastname,
//     this.mobile,
//     this.tokenMessage,
//     this.acceptAgreement,
//     this.birthday,
//     this.address,
//     this.addressNumber,
//     this.addressMoo,
//     this.addressRoad,
//     this.addressTumbol,
//     this.addressAmphur,
//     this.addressProvince,
//     this.addressZipcode,
//     this.email,
//     this.lineIdTemp,
//     this.userUrl,
//     this.countLogin,
//     this.createUser,
//     this.idcard,
//     this.displayName,
//     this.userIdLine,
//     this.userIdFb,
//     this.userIdApple,
//     this.linkAccounts,
//     this.linkAccountsConnect,
//     this.profilePicture,
//     this.usernameConnect,
//     this.facebookConnect,
//     this.appleConnect,
//     this.lineConnect,
//     this.roleId,
//     this.idRefOnly,
//     this.idRefOther,
//     this.locationHome,
//     this.mrCode,
//     this.bankNameMR,
//     this.bookBankNoMR,
//     this.bookBankNameMR,
//     this.fullNameMR,
//     this.idCardMR,
//     this.phoneNumberMR,
//     this.careerMR,
//     this.careerNoteMR,
//     this.businessNameMR,
//   });
//
//   int? id;
//   String? nickname;
//   String? fullname;
//   String? username;
//   String? password;
//   String? phoneFirebase;
//   String? firstname;
//   String? lastname;
//   String? mobile;
//   String? tokenMessage;
//   String? acceptAgreement;
//   String? birthday;
//   String? address;
//   String? addressNumber;
//   String? addressMoo;
//   String? addressRoad;
//   String? addressTumbol;
//   String? addressAmphur;
//   String? addressProvince;
//   String? addressZipcode;
//   String? email;
//   String? lineIdTemp;
//   String? userUrl;
//   int? countLogin;
//   String? createUser;
//   String? idcard;
//   String? displayName;
//   String? userIdLine;
//   String? userIdFb;
//   String? userIdApple;
//   int? linkAccounts;
//   String? linkAccountsConnect;
//   String? profilePicture;
//   String? usernameConnect;
//   String? facebookConnect;
//   String? appleConnect;
//   String? lineConnect;
//   String? roleId;
//   String? idRefOnly;
//   String? idRefOther;
//   String? locationHome;
//   String? mrCode;
//   String? bankNameMR;
//   String? bookBankNoMR;
//   String? bookBankNameMR;
//   String? fullNameMR;
//   String? idCardMR;
//   String? phoneNumberMR;
//   String? careerMR;
//   String? careerNoteMR;
//   String? businessNameMR;
//
//   factory ResponseProfileAndMR.fromJson(Map<String, dynamic> json) => ResponseProfileAndMR(
//     id: json["id"] ?? "",
//     nickname: json["nickname"] ?? "",
//     fullname: json["fullname"] ?? "",
//     username: json["username"] ?? "",
//     password: json["password"] ?? "",
//     phoneFirebase: json["phone_firebase"] ?? "",
//     firstname: json["firstname"] ?? "",
//     lastname: json["lastname"] ?? "",
//     mobile: json["mobile"] ?? "",
//     tokenMessage: json["tokenMessage"] ?? "",
//     acceptAgreement: json["accept_agreement"] ?? "",
//     birthday: json["birthday"] ?? "",
//     address: json["address"] ?? "",
//     addressNumber: json["address_number"] ?? "",
//     addressMoo: json["address_moo"] ?? "",
//     addressRoad: json["address_road"] ?? "",
//     addressTumbol: json["address_tumbol"] ?? "",
//     addressAmphur: json["address_amphur"] ?? "",
//     addressProvince: json["address_province"] ?? "",
//     addressZipcode: json["address_zipcode"] ?? "",
//     email: json["email"] ?? "",
//     lineIdTemp: json["line_id_temp"] ?? "",
//     userUrl: json["user_url"] ?? "",
//     countLogin: json["countLogin"] ?? "",
//     createUser: json["create_user"] ?? "",
//     idcard: json["idcard"] ?? "",
//     displayName: json["displayName"] ?? "",
//     userIdLine: json["userID_line"] ?? "",
//     userIdFb: json["userID_fb"] ?? "",
//     userIdApple: json["userID_apple"] ?? "",
//     linkAccounts: json["link_accounts"] ?? "",
//     linkAccountsConnect: json["link_accounts_connect"] ?? "",
//     profilePicture: json["profile_picture"] ?? "",
//     usernameConnect: json["username_connect"] ?? "",
//     facebookConnect: json["facebook_connect"] ?? "",
//     appleConnect: json["apple_connect"] ?? "",
//     lineConnect: json["line_connect"] ?? "",
//     roleId: json["roleId"] ?? "",
//     idRefOnly: json["id_ref_only"] ?? "",
//     idRefOther: json["id_ref_other"] ?? "",
//     locationHome: json["location_home"] ?? "",
//     mrCode: json["mr_code"] ?? "",
//     bankNameMR: json["bank_name"] ?? "",
//     bookBankNoMR: json["book_bank_no"] ?? "",
//     bookBankNameMR: json["book_bank_name"] ?? "",
//     fullNameMR: json["full_name"] ?? "",
//     idCardMR: json["id_card"] ?? "",
//     phoneNumberMR: json["phone_number"] ?? "",
//     careerMR: json["career"] ?? "",
//     careerNoteMR: json["career_note"] ?? "",
//     businessNameMR: json["business_name"] ?? "",
//   );
//
//   Map<String, dynamic> toJson() => {
//     "id": id,
//     "nickname": nickname,
//     "fullname": fullname,
//     "username": username,
//     "password": password,
//     "phone_firebase": phoneFirebase,
//     "firstname": firstname,
//     "lastname": lastname,
//     "mobile": mobile,
//     "tokenMessage": tokenMessage,
//     "accept_agreement": acceptAgreement,
//     "birthday": birthday,
//     "address": address,
//     "address_number": addressNumber,
//     "address_moo": addressMoo,
//     "address_road": addressRoad,
//     "address_tumbol": addressTumbol,
//     "address_amphur": addressAmphur,
//     "address_province": addressProvince,
//     "address_zipcode": addressZipcode,
//     "email": email,
//     "line_id_temp": lineIdTemp,
//     "user_url": userUrl,
//     "countLogin": countLogin,
//     "create_user": createUser,
//     "idcard": idcard,
//     "displayName": displayName,
//     "userID_line": userIdLine,
//     "userID_fb": userIdFb,
//     "userID_apple": userIdApple,
//     "link_accounts": linkAccounts,
//     "link_accounts_connect": linkAccountsConnect,
//     "profile_picture": profilePicture,
//     "username_connect": usernameConnect,
//     "facebook_connect": facebookConnect,
//     "apple_connect": appleConnect,
//     "line_connect": lineConnect,
//     "roleId": roleId,
//     "id_ref_only": idRefOnly,
//     "id_ref_other": idRefOther,
//     "location_home": locationHome,
//     "mr_code": mrCode,
//     "bank_name": bankNameMR,
//     "book_bank_no": bookBankNoMR,
//     "book_bank_name": bookBankNameMR,
//     "full_name": fullNameMR,
//     "id_card": idCardMR,
//     "phone_number": phoneNumberMR,
//     "career": careerMR,
//     "career_note": careerNoteMR,
//     "business_name": businessNameMR,
//   };
// }
//
// class SaveProfile {
//   SaveProfile({
//     required this.id,
//     required this.firstname,
//     required this.lastname,
//     required this.idcard,
//     required this.birthday,
//     required this.address,
//     required this.number,
//     required this.moo,
//     required this.road,
//     required this.tumbol,
//     required this.amphur,
//     required this.province,
//     required this.zipcode,
//     required this.email,
//     required this.location,
//   });
//
//   String id;
//   String firstname;
//   String lastname;
//   String idcard;
//   String birthday;
//   String address;
//   String number;
//   String moo;
//   String road;
//   String tumbol;
//   String amphur;
//   String province;
//   String zipcode;
//   String email;
//   String location;
//
//   factory SaveProfile.fromJson(Map<String, dynamic> json) => SaveProfile(
//     id: json["id"],
//     firstname: json["firstname"],
//     lastname: json["lastname"],
//     idcard: json["idcard"],
//     birthday: json["birthday"],
//     address: json["address"],
//     number: json["number"],
//     moo: json["moo"],
//     road: json["road"],
//     tumbol: json["tumbol"],
//     amphur: json["amphur"],
//     province: json["province"],
//     zipcode: json["zipcode"],
//     email: json["email"],
//     location: json["location"],
//   );
//
//   Map<String, dynamic> toJson() => {
//     "id": id,
//     "firstname": firstname,
//     "lastname": lastname,
//     "idcard": idcard,
//     "birthday": birthday,
//     "address": address,
//     "number": number,
//     "moo": moo,
//     "road": road,
//     "tumbol": tumbol,
//     "amphur": amphur,
//     "province": province,
//     "zipcode": zipcode,
//     "email": email,
//     "location": location,
//   };
// }
