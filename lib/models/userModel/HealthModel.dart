class EexerciseModel {
  int NumberExercises;
  String CountingDays;

  EexerciseModel({
    required this.NumberExercises,
    required this.CountingDays,

  });

  factory EexerciseModel.fromJson(Map<String, dynamic> json) => EexerciseModel(
    NumberExercises: json["NumberOfExercises"] ?? 0,
    CountingDays: json["CountingDays"] ?? 0,
  );

  Map<String, dynamic> toJson() => {
    "NumberExercises": NumberExercises,
    "CountingDays": CountingDays,

  };
}

class ProportionModel {
  String weight;
  String height;

  ProportionModel({
    required this.weight,
    required this.height,

  });

  factory ProportionModel.fromJson(Map<String, dynamic> json) => ProportionModel(
    weight: json["H_1_2"] ?? 0,
    height: json["H_1_3"] ?? 0,
  );

  Map<String, dynamic> toJson() => {
    "weight": weight,
    "height": height,

  };
}
