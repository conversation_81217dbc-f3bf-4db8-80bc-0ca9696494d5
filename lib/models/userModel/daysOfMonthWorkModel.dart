class DaysOfmonthWorkModel {
  String forgot_time;
  String late_day;
  String sick_leave;
  String personal_leave;

  DaysOfmonthWorkModel({
    required this.forgot_time,
    required this.late_day,
    required this.sick_leave,
    required this.personal_leave,
  });

  factory DaysOfmonthWorkModel.fromJson(Map<String, dynamic> json) => DaysOfmonthWorkModel(
    forgot_time: json["forgot_time"] ?? 0,
    late_day: json["late_day"] ?? 0,
    sick_leave: json["sick_leave"] ?? 0,
    personal_leave: json["personal_leave"] ?? 0,
  );

  Map<String, dynamic> toJson() => {
    "forgot_time": forgot_time,
    "late_day": late_day,
    "sick_leave": sick_leave,
    "personal_leave": personal_leave
  };
}


