class BirthdayModel {
  bool Birthday;


  BirthdayModel({
    required this.Birthday,

  });

  factory BirthdayModel.fromJson(Map<String, dynamic> json) => BirthdayModel(
    Birthday: json["Birthday"] ?? 0,

  );

  Map<String, dynamic> toJson() => {
    "Birthday": Birthday,
  };
}

class BitrixModel {
  String ID;
  String TITLE;
  String CREATED_DATE;
  String CREATED_BY_NAME;
  String CREATED_BY_LAST_NAME;
  String GROUP_ID;
  String DEADLINE;
  String REAL_STATUS;
  String ACTIVITY_DATE;
  String RESPONSIBLE_NAME;


  BitrixModel({
    required this.ID,
    required this.TITLE,
    required this.CREATED_DATE,
    required this.CREATED_BY_NAME,
    required this.CREATED_BY_LAST_NAME,
    required this.GROUP_ID,
    required this.DEADLINE,
    required this.REAL_STATUS,
    required this.ACTIVITY_DATE,
    required this.RESPONSIBLE_NAME,
  });

  factory BitrixModel.fromJson(Map<String, dynamic> json) => BitrixModel(
    ID: json["ID"] ?? 0,
    TITLE: json["TITLE"] ?? 0,
    CREATED_DATE: json["CREATED_DATE"] ?? 0,
    CREATED_BY_NAME: json["CREATED_BY_NAME"] ?? 0,
    CREATED_BY_LAST_NAME: json["CREATED_BY_LAST_NAME"] ?? 0,
    GROUP_ID: json["GROUP_ID"] ?? 0,
    DEADLINE: json["DEADLINE"] ?? 0,
    REAL_STATUS: json["REAL_STATUS"] ?? 0,
    ACTIVITY_DATE: json["ACTIVITY_DATE"] ?? 0,
    RESPONSIBLE_NAME: json["RESPONSIBLE_NAME"] ?? 0,


  );

  Map<String, dynamic> toJson() => {
    "ID": ID,
    "TITLE": TITLE,
    "CREATED_DATE": CREATED_DATE,
    "CREATED_BY_NAME": CREATED_BY_NAME,
    "CREATED_BY_LAST_NAME": CREATED_BY_LAST_NAME,
    "GROUP_ID": GROUP_ID,
    "DEADLINE": DEADLINE,
    "REAL_STATUS": REAL_STATUS,
    "ACTIVITY_DATE": ACTIVITY_DATE,
    "RESPONSIBLE_NAME": RESPONSIBLE_NAME,
  };
}

// class BitrixlModelList {
//   final List<BitrixModel>? Bitrix;
//
//   BitrixlModelList({this.Bitrix});
//
//   factory BitrixlModelList.fromJson(List<dynamic> parsedJson){
//     List<BitrixModel>? data = <BitrixModel>[];
//     data = parsedJson.map((i) => BitrixModel.fromJson(i)).toList();
//     return BitrixlModelList(Bitrix: data);
//   }
// }
class BitrixlModelList {
  final List<BitrixModel>? bitrixl;

  BitrixlModelList({this.bitrixl});

  factory BitrixlModelList.fromJson(List<dynamic> parsedJson){
    List<BitrixModel>? data = <BitrixModel>[];
    data = parsedJson.map((i) => BitrixModel.fromJson(i)).toList();
    return BitrixlModelList(bitrixl: data);
  }
}

class TokenBitrixModel {
  String token;


  TokenBitrixModel({
    required this.token
  });

  factory TokenBitrixModel.fromJson(Map<String, dynamic> json) => TokenBitrixModel(
    token: json["access_token"] ?? 0,
  );

  Map<String, dynamic> toJson() => {
    "token": token
  };
}


