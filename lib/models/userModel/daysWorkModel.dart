class DaysWorkModel {
  String working_day;
  String forgot_time;
  String late_day;
  String sick_leave;
  String personal_leave;
  String vacation_leave;
  String beyond_rights;

  DaysWorkModel({
    required this.working_day,
    required this.forgot_time,
    required this.late_day,
    required this.sick_leave,
    required this.personal_leave,
    required this.vacation_leave,
    required this.beyond_rights,
  });

  factory DaysWorkModel.fromJson(Map<String, dynamic> json) => DaysWorkModel(
    working_day: json["working_day"] ?? 0,
    forgot_time: json["forgot_time"] ?? 0,
    late_day: json["late_day"] ?? 0,
    sick_leave: json["sick_leave"] ?? 0,
    personal_leave: json["personal_leave"] ?? 0,
    vacation_leave: json["vacation_leave"] ?? 0,
    beyond_rights: json["beyond_rights"] ?? 0,
  );

  Map<String, dynamic> toJson() => {
    "working_day": working_day,
    "forgot_time": forgot_time,
    "late_day": late_day,
    "sick_leave": sick_leave,
    "personal_leave": personal_leave,
    "vacation_leave": vacation_leave,
    "beyond_rights": beyond_rights,
  };
}

class LostDayModel {
  String date;
  String getDayTH;
  String getDayEN;
  String status;

  LostDayModel({
    required this.date,
    required this.getDayTH,
    required this.getDayEN,
    required this.status,

  });

  factory LostDayModel.fromJson(Map<String, dynamic> json) => LostDayModel(
    date: json["date"] ?? 0,
    getDayTH: json["getDayTH"] ?? 0,
    getDayEN: json["getDayEN"] ?? 0,
    status: json["status"] ?? 0,

  );

  Map<String, dynamic> toJson() => {
    "date": date,
    "getDayTH": getDayTH,
    "getDayEN": getDayEN,
    "status": status,
  };
}

class LostDayModelList {
  final List<LostDayModel>?  LostDay;

  LostDayModelList({this.LostDay});

  factory LostDayModelList.fromJson(List<dynamic> parsedJson){
    List<LostDayModel>? data = <LostDayModel>[];
    data = parsedJson.map((i) => LostDayModel.fromJson(i)).toList();
    return LostDayModelList(LostDay: data);
  }
}

class TaScoreModel {
  String percent;
  String updateDate;


  TaScoreModel({
    required this.percent,
    required this.updateDate,

  });

  factory TaScoreModel.fromJson(Map<String, dynamic> json) => TaScoreModel(
    percent: json["percent"] ?? 0,
    updateDate: json["UpdateDate"] ?? 0,

  );

  Map<String, dynamic> toJson() => {
    "percent": percent,
    "updateDate": updateDate,

  };
}

