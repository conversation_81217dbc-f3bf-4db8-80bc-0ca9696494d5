class AbsenceModel {
  int absenceID;
  String type;
  String description;
  String detail;
  String cancalComment;
  String date;
  String startDate;
  String endDate;
  dynamic sumDate;
  String user_add;
  String uthFirstName;
  String uthLastName;
  String typeID;
  int reasonID;
  String approveID;

  AbsenceModel({
    required this.absenceID,
    required this.type,
    required this.description,
    required this.detail,
    required this.cancalComment,
    required this.date,
    required this.startDate,
    required this.endDate,
    required this.sumDate,
    required this.user_add,
    required this.uthFirstName,
    required this.uthLastName,
    required this.typeID,
    required this.reasonID,
    required this.approveID
  });

  factory AbsenceModel.fromJson(Map<String, dynamic> json) => AbsenceModel(
      absenceID: json["absenceID"],
      type: json["type"],
      description: json["description"],
      detail: json["detail"],
      cancalComment: json["cancalComment"],
      date: json["date"],
      startDate: json["startDate"],
      endDate: json["endDate"],
      sumDate: json["sumDate"],
      user_add: json["user_add"],
      uthFirstName: json["uthFirstName"],
      uthLastName: json["uthLastName"],
      typeID: json["typeID"],
      reasonID: json["reasonID"],
      approveID: json["approveID"]
  );

  Map<String, dynamic> toJson() => {
    "absenceID": absenceID,
    "type": type,
    "description": description,
    "detail": detail,
    "cancalComment": cancalComment,
    "date": date,
    "startDate": startDate,
    "endDate": endDate,
    "sumDate": sumDate,
    "user_add": user_add,
    "uthFirstName": uthFirstName,
    "uthLastName": uthLastName,
    "typeID": typeID,
    "reasonID": reasonID,
    "approveID": approveID
  };
}


class AbsenceModelList {
  final List<AbsenceModel>? data;

  AbsenceModelList({this.data});

  factory AbsenceModelList.fromJson(List<dynamic> parsedJson){
    List<AbsenceModel>? data = <AbsenceModel>[];
    data = parsedJson.map((i) => AbsenceModel.fromJson(i)).toList();
    return AbsenceModelList(data: data);
  }
}

class KeyValueModel {
  String key;
  String value;

  KeyValueModel({required this.key, required this.value});

}

class KeyValueModelApprove {
  String key;
  String value;

  KeyValueModelApprove({required this.key, required this.value});
}
