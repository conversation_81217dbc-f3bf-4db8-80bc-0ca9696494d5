import 'dart:convert';

List<SpinReward> lottoRewardFromJson(String jsonStr) => List<SpinReward>.from(
    json.decode(jsonStr).map((x) => SpinReward.fromJson(x)));

class SpinReward {
  String unit;
  int amount;
  int ratio;

  SpinReward({required this.unit, required this.amount, required this.ratio});

  factory SpinReward.fromJson(Map<String, dynamic> jsonDecoded) => SpinReward(
        unit: jsonDecoded["unit"],
        amount: jsonDecoded["amount"],
        ratio: jsonDecoded["ratio"],
      );
}

