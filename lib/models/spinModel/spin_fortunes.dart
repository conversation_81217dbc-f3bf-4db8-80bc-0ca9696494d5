import 'dart:convert';

List<SpinFortune> spinFortuneFromJson(String jsonStr) => List<SpinFortune>.from(
    json.decode(jsonStr).map((x) => SpinFortune.fromJson(x)));

class SpinFortune{
  String prizeUnit;
  int prizeAmount;
  
  SpinFortune({required this.prizeUnit, required this.prizeAmount});

  factory SpinFortune.fromJson(Map<String, dynamic> jsonDecoded) => SpinFortune(
        prizeUnit: jsonDecoded["prizeUnit"],
        prizeAmount: jsonDecoded["prizeAmount"],
        // ratio: jsonDecoded["ratio"],
      );
}