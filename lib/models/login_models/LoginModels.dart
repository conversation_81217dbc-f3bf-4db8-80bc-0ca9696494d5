// To parse this JSON data, do
//
//     final loginUsername = loginUsername<PERSON>rom<PERSON><PERSON>(jsonString);

import 'dart:convert';

LoginUsername loginUsernameFrom<PERSON><PERSON>(String str) => LoginUsername.fromJson(json.decode(str));

String loginUsernameTo<PERSON>son(LoginUsername data) => json.encode(data.toJson());

class LoginUsername {
  String username;
  String password;

  LoginUsername({
    required this.username,
    required this.password,
  });

  factory LoginUsername.fromJson(Map<String, dynamic> json) => LoginUsername(
    username: json["username"],
    password: json["password"],
  );

  Map<String, dynamic> toJson() => {
    "username": username,
    "password": password,
  };
}
