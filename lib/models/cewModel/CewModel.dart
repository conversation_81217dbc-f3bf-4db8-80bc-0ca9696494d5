class SearchTeamCewModel {
  String name_th;
  String id;

  SearchTeamCewModel({
    required this.name_th,
    required this.id,
  });

  factory SearchTeamCewModel.fromJson(Map<String, dynamic> json) => SearchTeamCewModel(
    name_th: json["name_th"] ?? 0,
    id: json["id"] ?? 0,

  );

  Map<String, dynamic> toJson() => {
    "name": name_th,
    "id": id,
  };
}
class SearchTeamCewModelList {
  final List<SearchTeamCewModel>? TeamCew;

  SearchTeamCewModelList({this.TeamCew});

  factory SearchTeamCewModelList.fromJson(List<dynamic> parsedJson){
    List<SearchTeamCewModel>? data = <SearchTeamCewModel>[];
    data = parsedJson.map((i) => SearchTeamCewModel.fromJson(i)).toList();
    return SearchTeamCewModelList(TeamCew: data);
  }
}

class KeyValueModelTeamRec {
  String key;
  String value;

  KeyValueModelTeamRec({required this.key, required this.value});
}

class KeyValueModelCategory {
  String key;
  String value;

  KeyValueModelCategory({required this.key, required this.value});
}

class KeyValueModelCounty {
  String key;
  String value;

  KeyValueModelCounty({required this.key, required this.value});
}

class KeyValueModelTypecew {
  String key;
  String value;

  KeyValueModelTypecew({required this.key, required this.value});
}


