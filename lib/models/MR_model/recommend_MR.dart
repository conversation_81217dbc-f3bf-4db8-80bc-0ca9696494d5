class ResponseRecommend {
  String? recommend;
  List<String>? productList;
  String? id;
  List<String>? glassCoatingList;
  List<String>? colorList;
  List<String>? accessoryProductList;

  ResponseRecommend({
    this.recommend,
    this.productList,
    this.id,
    this.glassCoatingList,
    this.colorList,
    this.accessoryProductList,
  });

  factory ResponseRecommend.fromJson(Map<String, dynamic> json) => ResponseRecommend(
    recommend: json["recommend"],
    productList: json["productList"] == null ? null : List<String>.from(json["productList"].map((x) => x)),
    id: json["id"],
    glassCoatingList: json["glassCoatingList"] == null ? null : List<String>.from(json["glassCoatingList"].map((x) => x)),
    colorList: json["colorList"] == null ? null : List<String>.from(json["colorList"].map((x) => x)),
    accessoryProductList: json["accessoryProductList"] == null ? null : List<String>.from(json["accessoryProductList"].map((x) => x)),
  );

  Map<String, dynamic> toJson() => {
    "recommend": recommend,
    "productList": List<dynamic>.from(productList!.map((x) => x)),
    "id": id,
    "glassCoatingList": List<dynamic>.from(glassCoatingList!.map((x) => x)),
    "colorList": List<dynamic>.from(colorList!.map((x) => x)),
    "accessoryProductList": List<dynamic>.from(accessoryProductList!.map((x) => x)),
  };
}