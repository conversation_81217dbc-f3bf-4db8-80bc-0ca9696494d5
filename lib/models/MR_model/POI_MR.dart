class ResponsePoi {
  ResponsePoi({
    this.running,
    this.typeApplication,
    this.typeBu,
    this.point,
    this.title,
  });

  int? running;
  String? typeApplication;
  String? typeBu;
  String? point;
  String? title;

  factory ResponsePoi.fromJson(Map<String, dynamic> json) => ResponsePoi(
    running: json["running"],
    typeApplication: json["typeApplication"],
    typeBu: json["type_bu"],
    point: json["point"],
    title: json["titile"],
  );

  Map<String, dynamic> toJson() => {
    "running": running,
    "typeApplication": typeApplication,
    "type_bu": typeBu,
    "point": point,
    "titile": title,
  };
}