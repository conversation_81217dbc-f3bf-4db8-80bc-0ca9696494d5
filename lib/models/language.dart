import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class SupportedLanguage {
  final String title;
  final String subtag;
  final String iconPath;

  SupportedLanguage({
    required this.title,
    required this.subtag,
    required this.iconPath,
  });

  Widget get icon {
    final isSvg = iconPath.toLowerCase().endsWith('.svg');

    return SizedBox(
      height: 24,
      width: 24,
      child: isSvg
          ? SvgPicture.asset(
        iconPath,
        fit: BoxFit.contain,
      )
          : Image.asset(
        iconPath,
        fit: BoxFit.contain,
      ),
    );
  }
}

// list that supports here
SupportedLanguage burmese = SupportedLanguage(
  title: 'ဗမာဘာသာစကား',
  subtag: 'my',
  iconPath: 'assets/svgs/MM.svg',
);
SupportedLanguage english = SupportedLanguage(
  title: 'English',
  subtag: 'en',
  iconPath: 'assets/svgs/GB.svg',
);
SupportedLanguage thai = SupportedLanguage(
  title: 'ภาษาไทย',
  subtag: 'th',
  iconPath: 'assets/svgs/TH.svg',
);
// SupportedLanguage vietnam = SupportedLanguage(
//   title: 'ngôn ngữ tiếng Việt',
//   subtag: 'vn',
//   iconPath: 'assets/svgs/VN.svg',
// );
SupportedLanguage laos = SupportedLanguage(
  title: 'ພາສາລາວ',
  subtag: 'la',
  iconPath: 'assets/svgs/LA.svg',
);
SupportedLanguage khmer = SupportedLanguage(
  title: 'ភាសាខ្មែរ',
  subtag: 'kh',
  iconPath: 'assets/svgs/KH.svg',
);

// list that shows up on setting screen
final List<SupportedLanguage> listOfLanguages = [
  // burmese,
  english,
  thai,
  //vietnam,
  // laos,
  // khmer
];
