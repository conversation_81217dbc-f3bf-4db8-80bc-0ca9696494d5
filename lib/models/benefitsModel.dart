class WelfareModel {
  String welfare_country;
  int welfare_daythai;
  int welfare_dayrplc;
  int welfare_firsthalf;
  int welfare_secondhalf;
  int total_welfare;

  // String password;

  WelfareModel({
    required this.welfare_country,
    required this.welfare_daythai,
    required this.welfare_dayrplc,
    required this.welfare_firsthalf,
    required this.welfare_secondhalf,
    required this.total_welfare,

    // required this.password
  });

  factory WelfareModel.fromJson(Map<String, dynamic> json) => WelfareModel(
      welfare_country: json["welfare_country"] ?? "0",
      welfare_daythai: json["welfare_daythai"] ?? "0",
      welfare_dayrplc: json["welfare_dayrplc"] ?? "0",
      welfare_firsthalf: json["welfare_firsthalf"] ?? "0",
      welfare_secondhalf: json["welfare_secondhalf"] ?? "0",
    total_welfare: json["total_welfare"] ?? "0",


    // password: json["password"],
  );

  Map<String, dynamic> toJson() => {
    "welfare_country": welfare_country,
    "welfare_daythai": welfare_daythai,
    "welfare_dayrplc": welfare_dayrplc,
    "welfare_firsthalf": welfare_firsthalf,
    "welfare_secondhalf": welfare_secondhalf,
    "total_welfare":total_welfare

    // "password": password,
  };
}
