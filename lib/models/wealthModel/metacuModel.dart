class MCU_Model {
  double? mcu = 0.0;
  double? holdMCU = 0.0;

  MCU_Model({
    required this.mcu,
    required this.holdMCU,
  });

  factory MCU_Model.fromJson(Map<String, dynamic> json) =>
      MCU_Model(
          mcu: json["mcu"] ?? 0.0,
          holdMCU: json["holdMCU"] ?? 0.0);

  Map<String, dynamic> toJson() => {"mcu": mcu, "holdMCU": holdMCU};
}

class RewardMCU_Model {
  double reward_holdmcu ;
  double reward_mcu;

  RewardMCU_Model({
    required this.reward_holdmcu,
    required this.reward_mcu,
  });

  factory RewardMCU_Model.fromJson(Map<String, dynamic> json) =>
      RewardMCU_Model(
          reward_holdmcu: json["reward_holdmcu"] ?? 0.0,
          reward_mcu: json["reward_mcu"] ?? 0.0);

  Map<String, dynamic> toJson() => {"reward_holdmcu": reward_holdmcu, "reward_mcu": reward_mcu};
}