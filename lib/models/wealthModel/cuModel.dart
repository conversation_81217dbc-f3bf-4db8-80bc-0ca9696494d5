class CU_Model {
  double shareBalance;
  double saving;
  // String password;

  CU_Model({
    required this.shareBalance,
    required this.saving,
    // required this.password
  });

  factory CU_Model.fromJson(Map<String, dynamic> json) => CU_Model(
    shareBalance: json["shareBalance"] ?? 0.00,
      saving: json["saving"] ?? 0.00
    // password: json["password"],
  );

  Map<String, dynamic> toJson() => {
    "shareBalance": shareBalance,
    "saving": saving
    // "password": password,
  };
}