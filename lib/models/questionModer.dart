class Question_Model {
  String question;
  String selection1;
  String selection2;
  String selection3;
  String answer;


  Question_Model({
    required this.question,
    required this.selection1,
    required this.selection2,
    required this.selection3,
    required this.answer,
  });

  factory Question_Model.fromJson(Map<String, dynamic> json) => Question_Model(
    question: json["question"] ?? 0,
    selection1: json["selection1"] ?? 0,
    selection2: json["selection2"] ?? 0,
    selection3: json["selection3"] ?? 0,
    answer: json["answer"] ?? 0,

  );

  Map<String, dynamic> toJson() => {
    "question": question,
    "selection1": selection1,
    "selection2": selection2,
    "selection3": selection3,
    "answer": answer,
  };
}