// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBPL2dcbGaTyEJ0Sb8iMfrls6T0yrsgfRg',
    appId: '1:1081540488049:web:77696a75cf28f221aca4d0',
    messagingSenderId: '1081540488049',
    projectId: 'ms24-cf18b',
    authDomain: 'ms24-cf18b.firebaseapp.com',
    databaseURL: 'https://ms24-cf18b.firebaseio.com',
    storageBucket: 'ms24-cf18b.appspot.com',
    measurementId: 'G-FLRCKGSYMJ',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAzsyr-AmzH6wRcMvBKF_IucWCpX5lULXw',
    appId: '1:1081540488049:android:fb0a775294781ed0aca4d0',
    messagingSenderId: '1081540488049',
    projectId: 'ms24-cf18b',
    databaseURL: 'https://ms24-cf18b.firebaseio.com',
    storageBucket: 'ms24-cf18b.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCkfI5gvrX2ZhRxEwd7pI6oAudOSoxbcDs',
    appId: '1:1081540488049:ios:a4d0c707e131558caca4d0',
    messagingSenderId: '1081540488049',
    projectId: 'ms24-cf18b',
    databaseURL: 'https://ms24-cf18b.firebaseio.com',
    storageBucket: 'ms24-cf18b.appspot.com',
    androidClientId:
        '1081540488049-huppkvhllpu0424eehtbf4hcbr2nv6qv.apps.googleusercontent.com',
    iosClientId:
        '1081540488049-pahlpe61dd0mtcn63o3v7r72ujcj27p6.apps.googleusercontent.com<',
    iosBundleId: 'com.prachakij.ms24beta',
  );
}
