import 'dart:io';

import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:get_storage/get_storage.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'configs/app_config.dart';
// import 'cybavo/cybavo_config.dart';
import 'firebase_options.dart';
import 'main.dart';

void main() async {
  // define dev environment here
  var configuredApp = AppConfig(
    environment: Environment.dev,
    appTitle: 'This is MS24 dev mode',

    endpointAPI: 'https://agilesoftgroup.com/MS24_uat',
    child: const MyApp(),
  );
  // run init here

  await GetStorage.init();
  GetStorage box = GetStorage();
  WidgetsFlutterBinding.ensureInitialized();

  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  if (kIsWeb) {
    await Firebase.initializeApp(
        options: const FirebaseOptions(
            appId: '1:1081540488049:web:ca0190e45d9255adaca4d0',
            apiKey: 'AIzaSyBPL2dcbGaTyEJ0Sb8iMfrls6T0yrsgfRg',
            messagingSenderId: '1081540488049',
            projectId: 'ms24-cf18b'));
  } else {
    await Firebase.initializeApp();
  }

  print('initScreen: ${box.read('initScreen')}');
  initScreen = box.read('initScreen') ?? 0;
  await box.write('initScreen', 1); //if already shown -> 1 else 0

  await SentryFlutter.init(
        (options) {
      options.dsn = 'https://<EMAIL>/4508068693475328';
      // Set tracesSampleRate to 1.0 to capture 100% of transactions for tracing.
      // We recommend adjusting this value in production.
      options.tracesSampleRate = 1.0;
      // The sampling rate for profiling is relative to tracesSampleRate
      // Setting to 1.0 will profile 100% of sampled transactions:
      options.profilesSampleRate = 1.0;
    },
    appRunner: () => runApp(configuredApp),
  );




}
