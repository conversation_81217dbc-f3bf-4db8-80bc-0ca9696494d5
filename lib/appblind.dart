import 'package:get/get.dart';
import 'package:mapp_ms24/controllers/LikeCreditController.dart';
import 'package:mapp_ms24/controllers/internal/wealthController/wealthController.dart';
import 'package:mapp_ms24/controllers/login_controller/login.dart';
import 'package:mapp_ms24/controllers/telegram/loginwithTG.dart';


import 'configs/app_config_service.dart';
import 'controllers/annoucer_controller.dart';
import 'controllers/firebaseApi.dart';
import 'controllers/googleDriveController.dart';
import 'controllers/internal/BenefitController /BenefitController.dart';
import 'controllers/internal/BitrixController/bitrixController.dart';
import 'controllers/internal/CewController/CewController.dart';
import 'controllers/internal/FinaceController/FinaceController.dart';
import 'controllers/internal/HealthController/HealthController.dart';
import 'controllers/internal/LeaveController/LeaveApprovalController.dart';
import 'controllers/internal/MainMenuController/MenuController.dart';
import 'controllers/internal/NotiController/NotifiController.dart';
import 'controllers/internal/Profile/profileController.dart';
import 'controllers/internal/Profile/userController.dart';
import 'controllers/internal/Tacontroller/Tacontroller.dart';
import 'controllers/internal/WorkIssuse/WorkIssuseController.dart';
import 'controllers/internal/authController/AuthController.dart';
import 'controllers/internal/authController/OTPCobtroller.dart';
import 'controllers/internal/authController/OsController.dart';
import 'controllers/internal/notification_controller.dart';
import 'controllers/record_soundController.dart';
import 'controllers/spin_controller/point_service.dart';
import 'controllers/utils/language_selection_controller.dart';
import 'controllers/webview_tg_controller.dart';
class AppBindings extends Bindings {
  @override
  void dependencies() {


    Get.lazyPut<Tacontroller>(() => Tacontroller());
    Get.lazyPut<FinaceController>(() => FinaceController());
    Get.lazyPut<BenefitController>(() => BenefitController());
    Get.lazyPut<LeaveAndAppController>(() => LeaveAndAppController());

    Get.put(ProfileController(), permanent: true);
    Get.put(LanguageSelectionController(), permanent: true);
    Get.put(Annoucer(), permanent: true);
    Get.put(OsController(), permanent: true);
    Get.put(HealthController(), permanent: true);
    Get.put(WebViewLikePointController(), permanent: true);
    Get.put(NotiController(), permanent: true);
    Get.put(FirebaseController());

    // Lazy loaded controllers
    Get.lazyPut<LoginwithTG>(() => LoginwithTG());
    Get.lazyPut<CewController>(() => CewController());

    Get.lazyPut<MainMenuController>(() => MainMenuController());



    Get.lazyPut<WorkIssuseController>(() => WorkIssuseController());
    Get.lazyPut<authController>(() => authController());
    Get.lazyPut<OtpController>(() => OtpController());
    Get.lazyPut<UserController>(() => UserController());
    Get.lazyPut<CenterController>(() => CenterController());
    Get.lazyPut<LikeCreditController>(() => LikeCreditController());
    Get.lazyPut<WealthController>(() => WealthController());
    Get.lazyPut<BitrixController>(() => BitrixController());
    Get.lazyPut<RecordController>(() => RecordController());
  }
}
