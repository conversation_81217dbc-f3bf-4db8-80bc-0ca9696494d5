// web/firebase-messaging-sw.js
importScripts('https://www.gstatic.com/firebasejs/10.7.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/10.7.0/firebase-messaging-compat.js');

firebase.initializeApp({
 apiKey: "AIzaSyBPL2dcbGaTyEJ0Sb8iMfrls6T0yrsgfRg",
  authDomain: "ms24-cf18b.firebaseapp.com",
  projectId: "ms24-cf18b",
  messagingSenderId: "1081540488049",
  appId: "1:1081540488049:web:ca0190e45d9255adaca4d0"
});

const messaging = firebase.messaging();

messaging.onBackgroundMessage((payload) => {
  console.log('[firebase-messaging-sw.js] Received background message ', payload);
  const { title, body } = payload.notification;
  self.registration.showNotification(title, { body });
});
