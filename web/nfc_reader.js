  async function startNfcScan() {
    if ('NDEFReader' in window) {
      try {
        const nfc = new NDEFReader();
        await nfc.scan(); // เริ่มสแกน

        console.log('NFC scanning started');

        nfc.onreading = (event) => {
          const { message, serialNumber } = event;
          console.log(`🔍 Serial: ${serialNumber}`);

          message.records.forEach((record, index) => {
            const textDecoder = new TextDecoder(record.encoding || 'utf-8');
            console.log(`📦 Record ${index}:`, textDecoder.decode(record.data));
          });
        };

        nfc.onerror = (error) => {
          console.error('⚠️ NFC Error:', error);
        };

      } catch (err) {
        console.error('🚫 ไม่สามารถเริ่มสแกน NFC ได้:', err);
      }
    } else {
      alert('อุปกรณ์นี้ไม่รองรับ Web NFC (ต้อง Android + Chrome)');
    }
  }