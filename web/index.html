<!DOCTYPE html>
<html>
  <head>
    <!-- ⚠️ ถ้าคุณ deploy ไป path ย่อย ให้เปลี่ยนค่า base href -->
    <base href="$FLUTTER_BASE_HREF">

    <meta charset="UTF-8">
    <meta content="IE=Edge" http-equiv="X-UA-Compatible">
    <meta name="description" content="NFC and QR Flutter Web App">

    <!-- iOS meta tags & icons -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-title" content="MS24BETA">
    <link rel="apple-touch-icon" href="icons/Test_Icon.png">

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="icons/Test_Icon.png"/>

    <title>NFC Authentication</title>

    <!-- Manifest -->
    <link rel="manifest" href="manifest.json">

    <!-- External Scripts -->
    <script src="nfc_reader.js"></script>
    <script src="https://unpkg.com/@simplewebauthn/browser@9.0.1/dist/bundle/index.umd.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/qr-code-generator/umd/qrcode.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/qrious/dist/qrious.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jsqr/dist/jsQR.js"></script>
    <script src="https://unpkg.com/@zxing/library@0.19.1" type="application/javascript"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js"></script>


    <!-- Flutter Loader (DO NOT MODIFY) -->
    <script>
      var serviceWorkerVersion = null;
    </script>
    <script src="flutter.js" defer></script>
    <script src="flutter_bootstrap.js" async></script>
  </head>

  <body>
    <!-- Flutter Entrypoint Loader -->
    <script>
      window.addEventListener('load', function(ev) {
        _flutter.loader.loadEntrypoint({
          serviceWorker: {
            serviceWorkerVersion: serviceWorkerVersion,
          },
          onEntrypointLoaded: function(engineInitializer) {
            engineInitializer.initializeEngine().then(function(appRunner) {
              appRunner.runApp();
            });
          }
        });
      });
    </script>

    <!-- Web NFC Script -->
    <script>
      if ("NDEFReader" in window) {
        const reader = new NDEFReader();
        reader.scan().then(() => {
          console.log("NFC Scan started");
          reader.onreading = (event) => {
            const message = event.message;
            console.log("NDEF Message:", message);
          };
        }).catch((error) => {
          console.error("NFC Scan failed:", error);
        });
      } else {
        console.log("Web NFC not supported on this browser");
      }
    </script>

    <script>
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.register('firebase-messaging-sw.js', {
          scope: '/firebase-cloud-messaging-push-scope'
        });
      }
    </script>
  </body>
</html>
