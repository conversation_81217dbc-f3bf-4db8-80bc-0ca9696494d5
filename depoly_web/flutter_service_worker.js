'use strict';
const MANIFEST = 'flutter-app-manifest';
const TEMP = 'flutter-temp-cache';
const CACHE_NAME = 'flutter-app-cache';

const RESOURCES = {"flutter_bootstrap.js": "708b1aafc1279710395ad450def75015",
"version.json": "29a7f0656dcff6c917cd40449d2ef505",
"index.html": "7797c82b8270651d06732544bab30b11",
"/": "7797c82b8270651d06732544bab30b11",
"main.dart.js": "3f32b9b83dd4e2d49625ea59d6409108",
"flutter.js": "f393d3c16b631f36852323de8e583132",
"favicon.png": "d41d8cd98f00b204e9800998ecf8427e",
"icons/Icon-192.png": "d41d8cd98f00b204e9800998ecf8427e",
"icons/Icon-maskable-192.png": "d41d8cd98f00b204e9800998ecf8427e",
"icons/Icon-maskable-512.png": "d41d8cd98f00b204e9800998ecf8427e",
"icons/Icon-512.png": "d41d8cd98f00b204e9800998ecf8427e",
"manifest.json": "ffe64b096a40391d7088412d891480b9",
"assets/shorebird.yaml": "b76bf65578bacfaa60590c9cec198506",
"assets/AssetManifest.json": "8d28507cc1dde8542cd05f4dcda71c78",
"assets/NOTICES": "a33fa326775be0146450667a1f08abe5",
"assets/FontManifest.json": "602f26efbe2309640b81e00a2d3db81a",
"assets/AssetManifest.bin.json": "7f8b34f4001d5df42f4c9fa3b659148f",
"assets/packages/cupertino_icons/assets/CupertinoIcons.ttf": "229d36864e2e7d6f615ff9fba4228cd0",
"assets/packages/phosphor_flutter/lib/fonts/phosphor.ttf": "ae434202ddb6730654adbf02f8f3bc5d",
"assets/packages/flutter_sound_web/howler/howler.js": "2bba823e6b4d71ea019d81d384672823",
"assets/packages/flutter_sound_web/src/flutter_sound_recorder.js": "f7ac74c4e0fd5cd472d86c3fe93883fc",
"assets/packages/flutter_sound_web/src/flutter_sound_player.js": "6bf84579813fd481ec5e24e73927500d",
"assets/packages/flutter_sound_web/src/flutter_sound.js": "aecd83c80bf4faace0bcea4cd47ab307",
"assets/packages/flutter_image_compress_web/assets/pica.min.js": "6208ed6419908c4b04382adc8a3053a2",
"assets/packages/fluttertoast/assets/toastify.js": "56e2c9cedd97f10e7e5f1cebd85d53e3",
"assets/packages/fluttertoast/assets/toastify.css": "a85675050054f179444bc5ad70ffc635",
"assets/packages/flutter_inappwebview/assets/t_rex_runner/t-rex.css": "5a8d0222407e388155d7d1395a75d5b9",
"assets/packages/flutter_inappwebview/assets/t_rex_runner/t-rex.html": "16911fcc170c8af1c5457940bd0bf055",
"assets/shaders/ink_sparkle.frag": "ecc85a2e95f5e9f53123dcaf8cb9b6ce",
"assets/AssetManifest.bin": "da691d3f9dc19cf9e34408779c6a2bf7",
"assets/fonts/MaterialIcons-Regular.otf": "f792d0c447274bada37a209f8acd0a85",
"assets/assets/add-square.png": "31fa879cdbc9bc19fb03ec8dcdf00b8b",
"assets/assets/Happy/Gift-Ribbin.png": "c7afadbae27098930d8f7b7205c25020",
"assets/assets/Exclamation-Circle-D.png": "c8e0c7c3fd1b9901ec3a0710c07ff27e",
"assets/assets/Logo%2520App.png": "6ce6331f3e790218310b19c20bf17443",
"assets/assets/Group.png": "832cc8284172556966083066997c734b",
"assets/assets/textlogo_light.png": "40bbcdb51d0d6d3185ec390578b07d48",
"assets/assets/images/yubikey/success-title.svg": "412376461e8d49a37bcb6c0cb0d8328e",
"assets/assets/images/yubikey/yubikeyN_D.png": "7d8d53474f1548f01f706da5892add39",
"assets/assets/images/yubikey/alert.svg": "8cc8e1910a16ca60bbdad73080050524",
"assets/assets/images/yubikey/yubikeyN_B.png": "34a9e0ac4196c7cb3e46a28a95c28d89",
"assets/assets/images/yubikey/waiting.gif": "4853257d8e69044e12fd7faca4eb8009",
"assets/assets/images/yubikey/Icon_material-group-work.png": "e9ec217fe1b25852bb827c04df8a39d6",
"assets/assets/images/yubikey/error.jpeg": "b312c2f892a2ac4f181cff60a4540de5",
"assets/assets/images/yubikey/yubikey.png": "2a73e6e435d0839a39342206baafa622",
"assets/assets/images/yubikey/Group_4885.svg": "d19bb20bb018c98fb8a1a03a9c6489f3",
"assets/assets/images/yubikey/error.png": "baa417b64014a75c3f8f42e4ace80553",
"assets/assets/images/yubikey/success.png": "c87ba8c858cdcf41583800e00f1a62bc",
"assets/assets/images/yubikey/yubikeyY_B.png": "207de91b3cfce7c335a72a019494f522",
"assets/assets/images/yubikey/yubikey_D.png": "bd0b77396f5e2edb863c709d242cd1c4",
"assets/assets/images/yubikey/yubikeyY_D.png": "9f9e7c55fd09586c911ebca55c13c7d0",
"assets/assets/images/yubikey/yubikey_B.png": "2a73e6e435d0839a39342206baafa622",
"assets/assets/Chat_alt_3_1.png": "e2f043460c6e7ef124deafa82df6e234",
"assets/assets/On_button.png": "b223166e4dd6b704ff4ae44eb7ef7cae",
"assets/assets/Fingerprint.png": "4a26fbaa59e83feec29602630bd6443c",
"assets/assets/PKGmember.json": "44ea01fdecec3b898b3c3c15303ba915",
"assets/assets/Logo.json": "6a2354f935093cd1bb07ec135575983c",
"assets/assets/Line523.png": "a2edd23cba5cc2a148b6766ab61bb3e3",
"assets/assets/ADD/Cloud_duotone.png": "60200ca0a4a4f4690fa24d91a5654970",
"assets/assets/ADD/Timmi_Face_verify2.png": "3b3fe1b85ee215ca242832018ca7bbe4",
"assets/assets/ADD/Line%2520523.png": "4d610f0a9c681470fe07a46a2170208e",
"assets/assets/ADD/Timmi_Face_verify.png": "b039f426f1cae682cf42177ef407e99e",
"assets/assets/ADD/Calender.png": "198918ceaf4bb3e303c6fe0a7e30175b",
"assets/assets/ADD/Exclamation-Circle-Yellow.png": "c033356f2f495202371feac3912beb2f",
"assets/assets/ADD/SD-card.png": "f3d30e77b1f948a5fdde1006a2028a59",
"assets/assets/ADD/door_441897.png": "ff21343220fcffec536da103df8209c1",
"assets/assets/ADD/Share-play.png": "1308dec114e9e7666e6cc8f599eab65e",
"assets/assets/ADD/Door_water_mark.png": "deb219546e05ac02ef3817981f3926e1",
"assets/assets/ADD/Devices.png": "9d527129fad864dee5762f800d6e7e01",
"assets/assets/ADD/TA_B.png": "706f46f8dfce921f0e878bbe24c5c6f6",
"assets/assets/ADD/Transfer-2.png": "8eeb625a8f9316ae8bbf3aa9a4423207",
"assets/assets/ADD/file.png": "000999babd9c612fd14d04643cfb639e",
"assets/assets/ADD/Vector.png": "ea496d37f6fdfb207a30a9b46ce9d24f",
"assets/assets/ADD/esign.png": "aff0e633c0d3c81ada19dcdb99929d63",
"assets/assets/ADD/MS24.png": "4dc79e0c647e9050d061928956a07233",
"assets/assets/ADD/Chat_alt_3%25201.png": "a489713212113d004598ce05fa0cea37",
"assets/assets/ADD/Message-bubble.png": "65868d7c84da655fbcdd93a4637c5be4",
"assets/assets/ADD/TA_D.png": "9c2a37aa8f932154320a1902fa1d6d3f",
"assets/assets/ADD/back_Vector.png": "dc6701a1848c4ab1b5a7ce929f93fdf9",
"assets/assets/ADD/Exclamation-Circle_G.png": "8be4c9ed6076b47e3e307b250b57564d",
"assets/assets/ADD/Expand_down_double.png": "96742ae13aad872f105d3ec1d3a39285",
"assets/assets/ADD/See_detail.png": "035293872e299ba1ff2d4516c0b53d86",
"assets/assets/ADD/Exclamation.png": "5b6be1fd5220b278fd28572992233b59",
"assets/assets/ADD/people-safe-B.png": "4c60b6f35ca5e7d115a064c69c05d366",
"assets/assets/ADD/Lock_D.png": "958454b289cf948e6beb4686ce0e8fd6",
"assets/assets/ADD/Cause_lateness_D.png": "6e33d3c66e74fd9ec007ca6bcf504f59",
"assets/assets/ADD/edit-two-B.png": "20a0f106f21fa199b1c660aa1c381cbd",
"assets/assets/ADD/ubic_D.png": "607c95dec79d0581ecd83676483d7fb2",
"assets/assets/ADD/Expand_up_double%2520.png": "9e4f18a4a6bcb5a641c27e59b306afdd",
"assets/assets/ADD/Expand_down_light.png": "1209df50f42265547eac0f2c7da44bc6",
"assets/assets/ADD/Expand_up_light.png": "c8d5695b35fb8d3ed52c721bda82f14a",
"assets/assets/ADD/Clock-2.png": "2112cbcca1ae5229740fbd7fe7509e8d",
"assets/assets/ADD/people-safe-D.png": "8861bfcb56c371345b5e20de3aba0f34",
"assets/assets/ADD/Lock_B.png": "c219cff4ee6b3ae95abc4c348915e73a",
"assets/assets/ADD/calendar.png": "dd44ec570046052e9dd947956cdccb47",
"assets/assets/ADD/Cause_lateness_B.png": "ea198a273fe632fc9727ac8980f7e524",
"assets/assets/ADD/edit-two-D.png": "0cf06612aff19bfbd9d3cd69113ff41e",
"assets/assets/ADD/ubic_B.png": "01ae79945cfbc24c2f06f32d35bf97d3",
"assets/assets/ADD/human.png": "fb54034b1546707c9147459372597789",
"assets/assets/ADD/Expand_up_double.png": "1b81bf4edad88cf473a4dff62ebb0dbd",
"assets/assets/ADD/Time_stamp_D.png": "0656c3b116497e061fce7b2e7138396c",
"assets/assets/ADD/Stick-note.png": "be6c16572a37f87e28ac09c81a4702b1",
"assets/assets/ADD/Trash.png": "fa0af9680326d5920f0c476ba1043d19",
"assets/assets/ADD/File_dock_search.png": "7a613c549669d08b1560b4fe73753965",
"assets/assets/ADD/pic.png": "2d5cfe8a0039d766b1772659c54a84fb",
"assets/assets/ADD/Exclamation-Circle.png": "3d651427de538a159f74c8afcc8ba600",
"assets/assets/ADD/message-security-D.png": "a5080daa526b61ab165a13a5646f130e",
"assets/assets/ADD/User-Verified.png": "de4a49bac8511ebbb6b7d529546662d3",
"assets/assets/ADD/Time_stamp_B.png": "f9654372aeb7b0ef2a849e711884281e",
"assets/assets/ADD/Exclamation-Circle(1).png": "3d651427de538a159f74c8afcc8ba600",
"assets/assets/ADD/Message.png": "aee12e303c79a7014d68deb3f79ccb39",
"assets/assets/ADD/Crown.png": "5d865925cf74da2773fde8e9aa48126b",
"assets/assets/ADD/X_button.png": "ef5d7c1af0a54b9d6c1c8be609b4402d",
"assets/assets/ADD/Image.png": "6d2ed90bc760ca0a88184bacbe40b06c",
"assets/assets/ADD/Subtract.png": "1522231637694679e00f79e5da0568d5",
"assets/assets/ADD/Sun_duotone.png": "2b08f2ccdee92328ae6da60894f304e5",
"assets/assets/ADD/message-security-B.png": "59a002a6de59165138f3554f014b9571",
"assets/assets/Logo_dark.png": "58f2727f76b5325978893bcec9df8346",
"assets/assets/logo/Like.svg": "3b66dfe165b44f36566afeea0cf774c0",
"assets/assets/logo/Sign_in.png": "050bc7f98b042704233d40fe18752cad",
"assets/assets/logo/gallery.png": "5ba0edacffcc103b2c719e00388602c6",
"assets/assets/logo/likewallet.png": "adb580fc5e5af70239786574d11a01d2",
"assets/assets/logo/doc.png": "938fbaa43e5c555c85877980c5465f63",
"assets/assets/logo/doc.svg": "5f4ba7bf569725b9be7f2c1d09198778",
"assets/assets/logo/pms.png": "c502789ce4e12c34a28ecc0ac6ef639a",
"assets/assets/logo/Scan_QR_Code.png": "84cd9e0aa363d2529007ca90be2030d3",
"assets/assets/logo/Expand_down_double.png": "0d3f849f086a7c7699964f9f6c18a248",
"assets/assets/logo/Like.png": "43cab789c2991ef97f3341ec3c45c479",
"assets/assets/logo/Timmii_Animated.png": "9bd9b7dedf7869edebf419f29339256e",
"assets/assets/logo/smart-door_3679954.png": "152e6a61df72c0eff6fb004ef11c6b8c",
"assets/assets/logo/Menu.png": "a835130c9b1a4576f24210a2fda6e152",
"assets/assets/logo/Group4936.png": "7c2b741a9a4117aa6af0b1dd8bf2d48e",
"assets/assets/logo/Logo.png": "8954f6f94df3fc4d5ab4b62ede11d6df",
"assets/assets/logo/notifications.png": "671b022ef877339f0d6e3b34b48251e2",
"assets/assets/logo/icon_rafco.png": "525b1140ca6e2851ed8a01c1bac967f0",
"assets/assets/logo/icon_aam.png": "********************************",
"assets/assets/logo/MS24_Logo.png": "d1868d7dc0724f071b4c4b428ea6db44",
"assets/assets/logo/icon_rplc.png": "663addf9a9ec6fc2549e32ff2f94fe55",
"assets/assets/logo/Group4936.svg": "0a8c3973d21c7156b7132f78b98d78c4",
"assets/assets/logo/loginwithTG.png": "486195d2a583dee4e4a427abc4d833e0",
"assets/assets/logo/Timmi.png": "645a59899879c31d62a28a1d3fae090b",
"assets/assets/Logo.png": "8954f6f94df3fc4d5ab4b62ede11d6df",
"assets/assets/Wallet_dark.png": "c7bcb6c83744199bcf862f29338c5888",
"assets/assets/Lock_B.png": "c219cff4ee6b3ae95abc4c348915e73a",
"assets/assets/Wallet_light.png": "aefe207c9211f3be762c8acda739aafd",
"assets/assets/Menu/icon.png": "18f3de2cd4e4cc85c24cef19925d0dd9",
"assets/assets/Menu/Calender.png": "dfc7499c6d94774e2988e1d77f788379",
"assets/assets/Menu/Doc.png": "8c2fea9e6f1beaf3fcacb49a8e7f436c",
"assets/assets/Menu/smile.svg": "bff6221830ff4706663cde574ea167be",
"assets/assets/Menu/notifications.png": "f45208a8a5cc59f3c2765c4c43e35fbd",
"assets/assets/Menu/Clock-2.png": "8c580c4b2cdfb3f6a38542ff92f9553c",
"assets/assets/Menu/Love.png": "4dd44ed1ec22e9aa70d623404f87b2a9",
"assets/assets/Close_button.png": "db202ebe88f32119456e977df04f0fae",
"assets/assets/Timmi/Timmi%2520Happy.png": "5bd1e98f4672cc5bbdd3b9a0d5fc22c4",
"assets/assets/Timmi/TimmiLovely.png": "e421d4b453811d1582ea078ec809ebc5",
"assets/assets/Timmi/Timmi%2520Worring.png": "eaa989f6f59af4dc333a320b7d975262",
"assets/assets/bitrix/close.svg": "991d4803ba64c38a5953114d623706bb",
"assets/assets/bitrix/play.jpeg": "cf9461d5abbd87288a5465c3a9a550a5",
"assets/assets/bitrix/Group4936.svg": "5d822ccde220280647aedcb6c2b297e9",
"assets/assets/bitrix/Icon-edit.svg": "ebe35d7bc6b3cc75d613f304efbec8e0",
"assets/assets/Logo_light.png": "f7526549edc6dde86440eb8987f0071f",
"assets/assets/Logo%2520App%2520copy.png": "7052b6d1b2ad308f41da87da8f10966d",
"assets/assets/fonts/sukhumvit-set/SukhumvitSet-Bold.ttf": "cf83ce0aa5e75930d38e0bff88d9426c",
"assets/assets/fonts/sukhumvit-set/SukhumvitSet-Text.ttf": "69fc4f0f2a8869feeec71379c2cf824c",
"assets/assets/fonts/sukhumvit-set/SukhumvitSet-Medium.ttf": "e54f4b9d0df88a9af7636bee55ac9bc1",
"assets/assets/fonts/sukhumvit-set/SukhumvitSet-SemiBold.ttf": "d55ff127a3c864c5d185b86c3d28f0e9",
"assets/assets/Text.json": "32bd8d6090c448c89aad85868e3a7074",
"assets/assets/textlogo_dark.png": "23f92c5825114f33ddbf33a267f1f943",
"assets/assets/Claim/Frame.png": "f60a2e6c9ca37c5ce86e93e5f24d5d86",
"canvaskit/skwasm.js": "694fda5704053957c2594de355805228",
"canvaskit/skwasm.js.symbols": "262f4827a1317abb59d71d6c587a93e2",
"canvaskit/canvaskit.js.symbols": "48c83a2ce573d9692e8d970e288d75f7",
"canvaskit/skwasm.wasm": "9f0c0c02b82a910d12ce0543ec130e60",
"canvaskit/chromium/canvaskit.js.symbols": "a012ed99ccba193cf96bb2643003f6fc",
"canvaskit/chromium/canvaskit.js": "671c6b4f8fcc199dcc551c7bb125f239",
"canvaskit/chromium/canvaskit.wasm": "b1ac05b29c127d86df4bcfbf50dd902a",
"canvaskit/canvaskit.js": "66177750aff65a66cb07bb44b8c6422b",
"canvaskit/canvaskit.wasm": "1f237a213d7370cf95f443d896176460",
"canvaskit/skwasm.worker.js": "89990e8c92bcb123999aa81f7e203b1c"};
// The application shell files that are downloaded before a service worker can
// start.
const CORE = ["main.dart.js",
"index.html",
"flutter_bootstrap.js",
"assets/AssetManifest.bin.json",
"assets/FontManifest.json"];

// During install, the TEMP cache is populated with the application shell files.
self.addEventListener("install", (event) => {
  self.skipWaiting();
  return event.waitUntil(
    caches.open(TEMP).then((cache) => {
      return cache.addAll(
        CORE.map((value) => new Request(value, {'cache': 'reload'})));
    })
  );
});
// During activate, the cache is populated with the temp files downloaded in
// install. If this service worker is upgrading from one with a saved
// MANIFEST, then use this to retain unchanged resource files.
self.addEventListener("activate", function(event) {
  return event.waitUntil(async function() {
    try {
      var contentCache = await caches.open(CACHE_NAME);
      var tempCache = await caches.open(TEMP);
      var manifestCache = await caches.open(MANIFEST);
      var manifest = await manifestCache.match('manifest');
      // When there is no prior manifest, clear the entire cache.
      if (!manifest) {
        await caches.delete(CACHE_NAME);
        contentCache = await caches.open(CACHE_NAME);
        for (var request of await tempCache.keys()) {
          var response = await tempCache.match(request);
          await contentCache.put(request, response);
        }
        await caches.delete(TEMP);
        // Save the manifest to make future upgrades efficient.
        await manifestCache.put('manifest', new Response(JSON.stringify(RESOURCES)));
        // Claim client to enable caching on first launch
        self.clients.claim();
        return;
      }
      var oldManifest = await manifest.json();
      var origin = self.location.origin;
      for (var request of await contentCache.keys()) {
        var key = request.url.substring(origin.length + 1);
        if (key == "") {
          key = "/";
        }
        // If a resource from the old manifest is not in the new cache, or if
        // the MD5 sum has changed, delete it. Otherwise the resource is left
        // in the cache and can be reused by the new service worker.
        if (!RESOURCES[key] || RESOURCES[key] != oldManifest[key]) {
          await contentCache.delete(request);
        }
      }
      // Populate the cache with the app shell TEMP files, potentially overwriting
      // cache files preserved above.
      for (var request of await tempCache.keys()) {
        var response = await tempCache.match(request);
        await contentCache.put(request, response);
      }
      await caches.delete(TEMP);
      // Save the manifest to make future upgrades efficient.
      await manifestCache.put('manifest', new Response(JSON.stringify(RESOURCES)));
      // Claim client to enable caching on first launch
      self.clients.claim();
      return;
    } catch (err) {
      // On an unhandled exception the state of the cache cannot be guaranteed.
      console.error('Failed to upgrade service worker: ' + err);
      await caches.delete(CACHE_NAME);
      await caches.delete(TEMP);
      await caches.delete(MANIFEST);
    }
  }());
});
// The fetch handler redirects requests for RESOURCE files to the service
// worker cache.
self.addEventListener("fetch", (event) => {
  if (event.request.method !== 'GET') {
    return;
  }
  var origin = self.location.origin;
  var key = event.request.url.substring(origin.length + 1);
  // Redirect URLs to the index.html
  if (key.indexOf('?v=') != -1) {
    key = key.split('?v=')[0];
  }
  if (event.request.url == origin || event.request.url.startsWith(origin + '/#') || key == '') {
    key = '/';
  }
  // If the URL is not the RESOURCE list then return to signal that the
  // browser should take over.
  if (!RESOURCES[key]) {
    return;
  }
  // If the URL is the index.html, perform an online-first request.
  if (key == '/') {
    return onlineFirst(event);
  }
  event.respondWith(caches.open(CACHE_NAME)
    .then((cache) =>  {
      return cache.match(event.request).then((response) => {
        // Either respond with the cached resource, or perform a fetch and
        // lazily populate the cache only if the resource was successfully fetched.
        return response || fetch(event.request).then((response) => {
          if (response && Boolean(response.ok)) {
            cache.put(event.request, response.clone());
          }
          return response;
        });
      })
    })
  );
});
self.addEventListener('message', (event) => {
  // SkipWaiting can be used to immediately activate a waiting service worker.
  // This will also require a page refresh triggered by the main worker.
  if (event.data === 'skipWaiting') {
    self.skipWaiting();
    return;
  }
  if (event.data === 'downloadOffline') {
    downloadOffline();
    return;
  }
});
// Download offline will check the RESOURCES for all files not in the cache
// and populate them.
async function downloadOffline() {
  var resources = [];
  var contentCache = await caches.open(CACHE_NAME);
  var currentContent = {};
  for (var request of await contentCache.keys()) {
    var key = request.url.substring(origin.length + 1);
    if (key == "") {
      key = "/";
    }
    currentContent[key] = true;
  }
  for (var resourceKey of Object.keys(RESOURCES)) {
    if (!currentContent[resourceKey]) {
      resources.push(resourceKey);
    }
  }
  return contentCache.addAll(resources);
}
// Attempt to download the resource online before falling back to
// the offline cache.
function onlineFirst(event) {
  return event.respondWith(
    fetch(event.request).then((response) => {
      return caches.open(CACHE_NAME).then((cache) => {
        cache.put(event.request, response.clone());
        return response;
      });
    }).catch((error) => {
      return caches.open(CACHE_NAME).then((cache) => {
        return cache.match(event.request).then((response) => {
          if (response != null) {
            return response;
          }
          throw error;
        });
      });
    })
  );
}
