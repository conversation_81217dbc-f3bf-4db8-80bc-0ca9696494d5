# This file is used to configure the Shorebird updater used by your app.
# Learn more at https://docs.shorebird.dev
# This file should be checked into version control.
 #shorebird release android --flavor aam_dev -t lib/main_dev.dart --flutter-version=3.22.3 


# This is the unique identifier assigned to your app.
# Your app_id is not a secret and is just used to identify your app
# when requesting patches from Shorebird's servers.
app_id: 0778de5e-2396-49ef-981b-677753bf3bdf
flavors:
  dev: 0778de5e-2396-49ef-981b-677753bf3bdf
  prod: 3f36a42e-64d2-462b-890f-5f5dacc02670

# auto_update controls if Shorebird should automatically update in the background on launch.
# If auto_update: false, you will need to use package:shorebird_code_push to trigger updates.
# https://pub.dev/packages/shorebird_code_push
# Uncomment the following line to disable automatic updates.
auto_update: false
