PODS:
  - awesome_notifications (0.0.5):
    - Flutter
    - IosAwnCore (= 0.7.3)
  - camera_avfoundation (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - Firebase/CoreOnly (10.25.0):
    - FirebaseCore (= 10.25.0)
  - Firebase/DynamicLinks (10.25.0):
    - Firebase/CoreOnly
    - FirebaseDynamicLinks (~> 10.25.0)
  - firebase_core (2.32.0):
    - Firebase/CoreOnly (= 10.25.0)
    - Flutter
  - firebase_dynamic_links (5.5.7):
    - Firebase/DynamicLinks (= 10.25.0)
    - firebase_core
    - Flutter
  - FirebaseCore (10.25.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseDynamicLinks (10.25.0):
    - FirebaseCore (~> 10.0)
  - Flutter (1.0.0)
  - flutter_image_compress_common (1.0.0):
    - Flutter
    - Mantle
    - SDWebImage
    - SDWebImageWebPCoder
  - flutter_inappwebview (0.0.1):
    - Flutter
    - flutter_inappwebview/Core (= 0.0.1)
    - OrderedSet (~> 5.0)
  - flutter_inappwebview/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 5.0)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_pdfview (1.0.2):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - image_picker_ios (0.0.1):
    - Flutter
  - IosAwnCore (0.7.3)
  - libwebp (1.3.2):
    - libwebp/demux (= 1.3.2)
    - libwebp/mux (= 1.3.2)
    - libwebp/sharpyuv (= 1.3.2)
    - libwebp/webp (= 1.3.2)
  - libwebp/demux (1.3.2):
    - libwebp/webp
  - libwebp/mux (1.3.2):
    - libwebp/demux
  - libwebp/sharpyuv (1.3.2)
  - libwebp/webp (1.3.2):
    - libwebp/sharpyuv
  - location (0.0.1):
    - Flutter
  - Mantle (2.2.0):
    - Mantle/extobjc (= 2.2.0)
  - Mantle/extobjc (2.2.0)
  - nfc_manager (0.0.1):
    - Flutter
  - OrderedSet (5.0.0)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.1.1):
    - Flutter
  - printing (1.0.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - restart_app (0.0.1):
    - Flutter
  - rive_common (0.0.1):
    - Flutter
  - scan (0.0.1):
    - Flutter
  - SDWebImage (5.19.7):
    - SDWebImage/Core (= 5.19.7)
  - SDWebImage/Core (5.19.7)
  - SDWebImageWebPCoder (0.14.6):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - Sentry/HybridSDK (8.36.0)
  - sentry_flutter (8.9.0):
    - Flutter
    - FlutterMacOS
    - Sentry/HybridSDK (= 8.36.0)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - Toast (4.1.1)
  - uni_links (0.0.1):
    - Flutter
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - awesome_notifications (from `.symlinks/plugins/awesome_notifications/ios`)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_dynamic_links (from `.symlinks/plugins/firebase_dynamic_links/ios`)
  - Flutter (from `Flutter`)
  - flutter_image_compress_common (from `.symlinks/plugins/flutter_image_compress_common/ios`)
  - flutter_inappwebview (from `.symlinks/plugins/flutter_inappwebview/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_pdfview (from `.symlinks/plugins/flutter_pdfview/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - location (from `.symlinks/plugins/location/ios`)
  - nfc_manager (from `.symlinks/plugins/nfc_manager/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - printing (from `.symlinks/plugins/printing/ios`)
  - restart_app (from `.symlinks/plugins/restart_app/ios`)
  - rive_common (from `.symlinks/plugins/rive_common/ios`)
  - scan (from `.symlinks/plugins/scan/ios`)
  - sentry_flutter (from `.symlinks/plugins/sentry_flutter/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - uni_links (from `.symlinks/plugins/uni_links/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseDynamicLinks
    - GoogleUtilities
    - IosAwnCore
    - libwebp
    - Mantle
    - OrderedSet
    - PromisesObjC
    - SDWebImage
    - SDWebImageWebPCoder
    - Sentry
    - Toast

EXTERNAL SOURCES:
  awesome_notifications:
    :path: ".symlinks/plugins/awesome_notifications/ios"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_dynamic_links:
    :path: ".symlinks/plugins/firebase_dynamic_links/ios"
  Flutter:
    :path: Flutter
  flutter_image_compress_common:
    :path: ".symlinks/plugins/flutter_image_compress_common/ios"
  flutter_inappwebview:
    :path: ".symlinks/plugins/flutter_inappwebview/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_pdfview:
    :path: ".symlinks/plugins/flutter_pdfview/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  location:
    :path: ".symlinks/plugins/location/ios"
  nfc_manager:
    :path: ".symlinks/plugins/nfc_manager/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  printing:
    :path: ".symlinks/plugins/printing/ios"
  restart_app:
    :path: ".symlinks/plugins/restart_app/ios"
  rive_common:
    :path: ".symlinks/plugins/rive_common/ios"
  scan:
    :path: ".symlinks/plugins/scan/ios"
  sentry_flutter:
    :path: ".symlinks/plugins/sentry_flutter/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  uni_links:
    :path: ".symlinks/plugins/uni_links/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  awesome_notifications: d63d9a25f126860f9a600850d99772237895b3ba
  camera_avfoundation: dd002b0330f4981e1bbcb46ae9b62829237459a4
  device_info_plus: e5c5da33f982a436e103237c0c85f9031142abed
  Firebase: 0312a2352584f782ea56f66d91606891d4607f06
  firebase_core: a626d00494efa398e7c54f25f1454a64c8abf197
  firebase_dynamic_links: 525e9c1b702d2ed2d9b0dbd342eee1e15a75e62d
  FirebaseCore: 7ec4d0484817f12c3373955bc87762d96842d483
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  FirebaseDynamicLinks: 12c9f5b643943e0565ed28080373f89cbcb914a3
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_image_compress_common: ec1d45c362c9d30a3f6a0426c297f47c52007e3e
  flutter_inappwebview: 3d32228f1304635e7c028b0d4252937730bbc6cf
  flutter_local_notifications: 4cde75091f6327eb8517fa068a0a5950212d2086
  flutter_pdfview: 25f53dd6097661e6395b17de506e6060585946bd
  flutter_secure_storage: 23fc622d89d073675f2eaa109381aefbcf5a49be
  fluttertoast: e9a18c7be5413da53898f660530c56f35edfba9c
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  image_picker_ios: c560581cceedb403a6ff17f2f816d7fea1421fc1
  IosAwnCore: 6494e0e174d49f04f513e8a002187be226889a37
  libwebp: 1786c9f4ff8a279e4dac1e8f385004d5fc253009
  location: d5cf8598915965547c3f36761ae9cc4f4e87d22e
  Mantle: c5aa8794a29a022dfbbfc9799af95f477a69b62d
  nfc_manager: d7da7cb781f7744b94df5fe9dbca904ac4a0939e
  OrderedSet: aaeb196f7fef5a9edf55d89760da9176ad40b93c
  package_info_plus: 6c92f08e1f853dc01228d6f553146438dafcd14e
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: e76247795d700c14ea09e3a2d8855d41ee80a2e6
  printing: 233e1b73bd1f4a05615548e9b5a324c98588640b
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  restart_app: 806659942bf932f6ce51c5372f91ce5e81c8c14a
  rive_common: 8a159d68033a8b073e5853acc50f03aa486a2888
  scan: aea35bb4aa59ccc8839c576a18cd57c7d492cc86
  SDWebImage: 8a6b7b160b4d710e2a22b6900e25301075c34cb3
  SDWebImageWebPCoder: e38c0a70396191361d60c092933e22c20d5b1380
  Sentry: f8374b5415bc38dfb5645941b3ae31230fbeae57
  sentry_flutter: 0eb93e5279eb41e2392212afe1ccd2fecb4f8cbe
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sqflite_darwin: a553b1fd6fe66f53bbb0fe5b4f5bab93f08d7a13
  Toast: 1f5ea13423a1e6674c4abdac5be53587ae481c4e
  uni_links: d97da20c7701486ba192624d99bffaaffcfc298a
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe

PODFILE CHECKSUM: f450e9d85bb4d88548f298f78b33269094b6217d

COCOAPODS: 1.16.2
